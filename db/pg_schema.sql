--
-- PostgreSQL database dump
--

-- Dumped from database version 15.6 (Debian 15.6-1.pgdg120+2)
-- Dumped by pg_dump version 15.6 (Debian 15.6-0+deb12u1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: accessTokens; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.accessTokens (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    accessToken_hash text NOT NULL,
    name text NOT NULL,
    ownerType text,
    ownerUid uuid NOT NULL,
    expiresAt timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone,
    CONSTRAINT accessTokens_ownerType_check CHECK ((ownerType = ANY (ARRAY['user'::text, 'org'::text])))
);


ALTER TABLE public.accessTokens OWNER TO testfiesta;

--
-- Name: app_configs; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.app_configs (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    config json DEFAULT '{}'::json NOT NULL,
    name text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.app_configs OWNER TO testfiesta;

--
-- Name: apps; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.apps (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    link text,
    customFields json,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.apps OWNER TO testfiesta;

--
-- Name: attachments; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.attachments (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    size integer,
    metadata json DEFAULT '{}'::json,
    type text NOT NULL,
    checksum text,
    externalId text,
    source text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.attachments OWNER TO testfiesta;

--
-- Name: audit_logs; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.audit_logs (
    id uuid DEFAULT gen_random_uuid(),
    tablename text NOT NULL,
    action text NOT NULL,
    input json,
    output json NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.audit_logs OWNER TO testfiesta;

--
-- Name: case_attachments; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.case_attachments (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    attachment_uid uuid NOT NULL,
    case_uid uuid NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    deleted_at timestamp with time zone
);


ALTER TABLE public.case_attachments OWNER TO testfiesta;

--
-- Name: comment_attachments; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.comment_attachments (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    attachment_uid uuid NOT NULL,
    comment_uid uuid NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    deleted_at timestamp with time zone
);


ALTER TABLE public.comment_attachments OWNER TO testfiesta;

--
-- Name: comments; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.comments (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    entity_type text,
    entity_uid uuid NOT NULL,
    created_by uuid NOT NULL,
    body text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    deleted_at timestamp with time zone,
    CONSTRAINT comments_entity_type_check CHECK ((entity_type = ANY (ARRAY['case'::text, 'milestone'::text, 'execution'::text, 'suite'::text, 'result'::text, 'run'::text])))
);


ALTER TABLE public.comments OWNER TO testfiesta;

--
-- Name: dashboards; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.dashboards (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    org_uid uuid NOT NULL,
    name text NOT NULL,
    body json NOT NULL,
    created_by uuid NOT NULL,
    editable boolean DEFAULT true,
    system_default boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.dashboards OWNER TO testfiesta;

--
-- Name: data_relationships; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.data_relationships (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    primary_table text NOT NULL,
    primary_key text NOT NULL,
    secondary_table text NOT NULL,
    secondary_key text NOT NULL,
    source text NOT NULL,
    direction text NOT NULL,
    org_uid uuid NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.data_relationships OWNER TO testfiesta;

--
-- Name: execution_attachments; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.execution_attachments (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    attachment_uid uuid NOT NULL,
    execution_uid uuid NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    deleted_at timestamp with time zone
);


ALTER TABLE public.execution_attachments OWNER TO testfiesta;

--
-- Name: execution_step_attachments; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.execution_step_attachments (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    attachment_uid uuid NOT NULL,
    execution_step_uid uuid NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    deleted_at timestamp with time zone
);


ALTER TABLE public.execution_step_attachments OWNER TO testfiesta;

--
-- Name: external_entities; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.external_entities (
    source text NOT NULL,
    source_id text NOT NULL,
    entity_uid uuid NOT NULL,
    entity_type text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone,
    CONSTRAINT external_entities_entity_type_check CHECK ((entity_type = ANY (ARRAY['user'::text, 'org'::text])))
);


ALTER TABLE public.external_entities OWNER TO testfiesta;

--
-- Name: handles; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.handles (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    ownerUid uuid NOT NULL,
    ownerType text,
    current boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT handles_ownerType_check CHECK ((ownerType = ANY (ARRAY['org'::text, 'user'::text])))
);


ALTER TABLE public.handles OWNER TO testfiesta;

--
-- Name: invites; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.invites (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    org_uid uuid NOT NULL,
    inviter_uid uuid NOT NULL,
    role text NOT NULL,
    email text NOT NULL,
    token text NOT NULL,
    expiresAt timestamp with time zone NOT NULL,
    accepted_at timestamp with time zone,
    accepted boolean,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone,
    CONSTRAINT invites_role_check CHECK ((role = ANY (ARRAY['member'::text, 'admin'::text, 'owner'::text])))
);


ALTER TABLE public.invites OWNER TO testfiesta;

--
-- Name: knex_migrations; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.knex_migrations (
    id integer NOT NULL,
    name character varying(255),
    batch integer,
    migration_time timestamp with time zone
);


ALTER TABLE public.knex_migrations OWNER TO testfiesta;

--
-- Name: knex_migrations_id_seq; Type: SEQUENCE; Schema: public; Owner: testfiesta
--

CREATE SEQUENCE public.knex_migrations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.knex_migrations_id_seq OWNER TO testfiesta;

--
-- Name: knex_migrations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: testfiesta
--

ALTER SEQUENCE public.knex_migrations_id_seq OWNED BY public.knex_migrations.id;


--
-- Name: knex_migrations_lock; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.knex_migrations_lock (
    index integer NOT NULL,
    is_locked integer
);


ALTER TABLE public.knex_migrations_lock OWNER TO testfiesta;

--
-- Name: knex_migrations_lock_index_seq; Type: SEQUENCE; Schema: public; Owner: testfiesta
--

CREATE SEQUENCE public.knex_migrations_lock_index_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.knex_migrations_lock_index_seq OWNER TO testfiesta;

--
-- Name: knex_migrations_lock_index_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: testfiesta
--

ALTER SEQUENCE public.knex_migrations_lock_index_seq OWNED BY public.knex_migrations_lock.index;


--
-- Name: milestone_attachments; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.milestone_attachments (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    attachment_uid uuid NOT NULL,
    milestone_uid uuid NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    deleted_at timestamp with time zone
);


ALTER TABLE public.milestone_attachments OWNER TO testfiesta;

--
-- Name: oauth_tokens; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.oauth_tokens (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    accessToken text NOT NULL,
    refresh_token text NOT NULL,
    url text NOT NULL,
    ownerUid uuid,
    retrievalId uuid,
    service text,
    expiresAt timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.oauth_tokens OWNER TO testfiesta;

--
-- Name: org_attachments; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.org_attachments (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    attachment_uid uuid NOT NULL,
    org_uid uuid NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    deleted_at timestamp with time zone
);


ALTER TABLE public.org_attachments OWNER TO testfiesta;

--
-- Name: orgs; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.orgs (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    created_by uuid NOT NULL,
    preferences json DEFAULT '{}'::json NOT NULL,
    stripe_id text,
    avatarUrl text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.orgs OWNER TO testfiesta;

--
-- Name: plan_attachments; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.plan_attachments (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    attachment_uid uuid NOT NULL,
    plan_uid uuid NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    deleted_at timestamp with time zone
);


ALTER TABLE public.plan_attachments OWNER TO testfiesta;

--
-- Name: repo_branches; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.repo_branches (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    repo_uid uuid,
    externalId text NOT NULL,
    name text NOT NULL,
    link text,
    customFields json,
    external_created_at timestamp with time zone,
    external_updated_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.repo_branches OWNER TO testfiesta;

--
-- Name: repos; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.repos (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    externalId text NOT NULL,
    source text NOT NULL,
    name text NOT NULL,
    link text,
    customFields json,
    external_created_at timestamp with time zone,
    external_updated_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.repos OWNER TO testfiesta;

--
-- Name: run_attachments; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.run_attachments (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    attachment_uid uuid NOT NULL,
    run_uid uuid NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    deleted_at timestamp with time zone
);


ALTER TABLE public.run_attachments OWNER TO testfiesta;

--
-- Name: run_suite_attachments; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.run_suite_attachments (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    attachment_uid uuid NOT NULL,
    run_suite_uid uuid NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    deleted_at timestamp with time zone
);


ALTER TABLE public.run_suite_attachments OWNER TO testfiesta;

--
-- Name: sessions; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.sessions (
    sid text NOT NULL,
    sess json NOT NULL,
    userUid uuid,
    expired timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.sessions OWNER TO testfiesta;

--
-- Name: step_attachments; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.step_attachments (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    attachment_uid uuid NOT NULL,
    step_uid uuid NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    deleted_at timestamp with time zone
);


ALTER TABLE public.step_attachments OWNER TO testfiesta;

--
-- Name: storage_profiles; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.storage_profiles (
    uid uuid NOT NULL,
    name text NOT NULL,
    owner_id text NOT NULL,
    type text NOT NULL,
    is_default boolean DEFAULT false NOT NULL,
    provider text NOT NULL,
    credentials json NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.storage_profiles OWNER TO testfiesta;

--
-- Name: subscriptions; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.subscriptions (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    stripe_customer_id uuid NOT NULL,
    stripe_subscription_id text NOT NULL,
    stripe_price_id text NOT NULL,
    qty integer DEFAULT 1 NOT NULL,
    name text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.subscriptions OWNER TO testfiesta;

--
-- Name: suite_attachments; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.suite_attachments (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    attachment_uid uuid NOT NULL,
    suite_uid uuid NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    deleted_at timestamp with time zone
);


ALTER TABLE public.suite_attachments OWNER TO testfiesta;

--
-- Name: super_admins; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.super_admins (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    userUid uuid NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    deleted_at timestamp with time zone
);


ALTER TABLE public.super_admins OWNER TO testfiesta;

--
-- Name: test_cases; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.test_cases (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    project_uid uuid,
    parent_uid uuid,
    suite_uid uuid,
    repo_uid uuid,
    externalId text NOT NULL,
    source text NOT NULL,
    name text NOT NULL,
    link text,
    customFields json,
    external_created_at timestamp with time zone,
    external_updated_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.test_cases OWNER TO testfiesta;

--
-- Name: test_execution_steps; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.test_execution_steps (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    externalId text,
    source text,
    link text,
    customFields json,
    external_created_at timestamp with time zone,
    external_updated_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone,
    test_execution_uid uuid NOT NULL,
    test_step_uid uuid,
    status text
);


ALTER TABLE public.test_execution_steps OWNER TO testfiesta;

--
-- Name: test_executions; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.test_executions (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    externalId text NOT NULL,
    source text NOT NULL,
    link text,
    status text,
    test_case_uid uuid,
    testRunUid uuid,
    repo_branch_uid uuid,
    customFields json,
    completed_at timestamp with time zone,
    external_created_at timestamp with time zone,
    external_updated_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.test_executions OWNER TO testfiesta;

--
-- Name: testFolders; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.testFolders (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    project_uid uuid,
    parent_uid uuid,
    externalId text NOT NULL,
    source text NOT NULL,
    name text NOT NULL,
    link character varying(255),
    customFields json,
    external_created_at timestamp with time zone,
    external_updated_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.testFolders OWNER TO testfiesta;

--
-- Name: test_milestones; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.test_milestones (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    project_uid uuid,
    externalId text NOT NULL,
    source text NOT NULL,
    name text NOT NULL,
    link text,
    customFields json,
    dueAt timestamp with time zone,
    completed_at timestamp with time zone,
    external_created_at timestamp with time zone,
    external_updated_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.test_milestones OWNER TO testfiesta;

--
-- Name: test_plans; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.test_plans (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    project_uid uuid,
    externalId text NOT NULL,
    source text NOT NULL,
    name text,
    link text,
    customFields json,
    completed_at timestamp with time zone,
    external_created_at timestamp with time zone,
    external_updated_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.test_plans OWNER TO testfiesta;

--
-- Name: test_projects; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.test_projects (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    externalId text NOT NULL,
    source text NOT NULL,
    name text NOT NULL,
    link text,
    customFields json,
    external_created_at timestamp with time zone,
    external_updated_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.test_projects OWNER TO testfiesta;

--
-- Name: test_results; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.test_results (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    status text,
    comment text,
    test_execution_uid uuid,
    test_execution_step_uid uuid,
    externalId text,
    source text,
    link text,
    customFields json,
    completed_at timestamp with time zone,
    external_created_at timestamp with time zone,
    external_updated_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone,
    CONSTRAINT test_results_status_check CHECK ((status = ANY (ARRAY['passed'::text, 'failed'::text, 'pending'::text, 'inconclusive'::text, 'skipped'::text])))
);


ALTER TABLE public.test_results OWNER TO testfiesta;

--
-- Name: test_run_suites; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.test_run_suites (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    testRunUid uuid,
    test_suite_uid uuid,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.test_run_suites OWNER TO testfiesta;

--
-- Name: test_runs; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.test_runs (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    externalId text NOT NULL,
    source text NOT NULL,
    name text,
    link text,
    project_uid uuid,
    customFields json,
    completed_at timestamp with time zone,
    external_created_at timestamp with time zone,
    external_updated_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.test_runs OWNER TO testfiesta;

--
-- Name: test_steps; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.test_steps (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    externalId text,
    source text,
    link text,
    test_case_uid uuid,
    description text,
    version integer,
    active boolean DEFAULT true,
    customFields json,
    external_created_at timestamp with time zone,
    external_updated_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.test_steps OWNER TO testfiesta;

--
-- Name: test_suites; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.test_suites (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    project_uid uuid,
    externalId text NOT NULL,
    source text NOT NULL,
    name text NOT NULL,
    link text,
    customFields json,
    external_created_at timestamp with time zone,
    external_updated_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.test_suites OWNER TO testfiesta;

--
-- Name: user_attachments; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.user_attachments (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    attachment_uid uuid NOT NULL,
    userUid uuid NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    deleted_at timestamp with time zone
);


ALTER TABLE public.user_attachments OWNER TO testfiesta;

--
-- Name: users; Type: TABLE; Schema: public; Owner: testfiesta
--

CREATE TABLE public.users (
    uid uuid DEFAULT gen_random_uuid() NOT NULL,
    firstName text NOT NULL,
    lastName text NOT NULL,
    email text,
    password_hash text,
    preferences json DEFAULT '{}'::json NOT NULL,
    last_sign_in_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    last_sign_in_ip text NOT NULL,
    password_reset_token text,
    password_reset_count integer,
    passwordResetAt timestamp with time zone,
    password_reset_created_at timestamp with time zone,
    stripe_id text,
    avatarUrl text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.users OWNER TO testfiesta;

--
-- Name: knex_migrations id; Type: DEFAULT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.knex_migrations ALTER COLUMN id SET DEFAULT nextval('public.knex_migrations_id_seq'::regclass);


--
-- Name: knex_migrations_lock index; Type: DEFAULT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.knex_migrations_lock ALTER COLUMN index SET DEFAULT nextval('public.knex_migrations_lock_index_seq'::regclass);


--
-- Name: accessTokens accessTokens_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.accessTokens
    ADD CONSTRAINT accessTokens_pkey PRIMARY KEY (uid);


--
-- Name: app_configs app_configs_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.app_configs
    ADD CONSTRAINT app_configs_pkey PRIMARY KEY (uid);


--
-- Name: apps apps_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.apps
    ADD CONSTRAINT apps_pkey PRIMARY KEY (uid);


--
-- Name: attachments attachments_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.attachments
    ADD CONSTRAINT attachments_pkey PRIMARY KEY (uid);


--
-- Name: case_attachments case_attachments_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.case_attachments
    ADD CONSTRAINT case_attachments_pkey PRIMARY KEY (uid);


--
-- Name: comment_attachments comment_attachments_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.comment_attachments
    ADD CONSTRAINT comment_attachments_pkey PRIMARY KEY (uid);


--
-- Name: comments comments_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.comments
    ADD CONSTRAINT comments_pkey PRIMARY KEY (uid);


--
-- Name: dashboards dashboards_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.dashboards
    ADD CONSTRAINT dashboards_pkey PRIMARY KEY (uid);


--
-- Name: data_relationships data_relationships_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.data_relationships
    ADD CONSTRAINT data_relationships_pkey PRIMARY KEY (uid);


--
-- Name: execution_attachments execution_attachments_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.execution_attachments
    ADD CONSTRAINT execution_attachments_pkey PRIMARY KEY (uid);


--
-- Name: execution_step_attachments execution_step_attachments_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.execution_step_attachments
    ADD CONSTRAINT execution_step_attachments_pkey PRIMARY KEY (uid);


--
-- Name: external_entities external_entities_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.external_entities
    ADD CONSTRAINT external_entities_pkey PRIMARY KEY (source, entity_type, source_id);


--
-- Name: handles handles_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.handles
    ADD CONSTRAINT handles_pkey PRIMARY KEY (uid);


--
-- Name: invites invites_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.invites
    ADD CONSTRAINT invites_pkey PRIMARY KEY (uid);


--
-- Name: invites invites_token_unique; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.invites
    ADD CONSTRAINT invites_token_unique UNIQUE (token);


--
-- Name: knex_migrations_lock knex_migrations_lock_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.knex_migrations_lock
    ADD CONSTRAINT knex_migrations_lock_pkey PRIMARY KEY (index);


--
-- Name: knex_migrations knex_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.knex_migrations
    ADD CONSTRAINT knex_migrations_pkey PRIMARY KEY (id);


--
-- Name: milestone_attachments milestone_attachments_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.milestone_attachments
    ADD CONSTRAINT milestone_attachments_pkey PRIMARY KEY (uid);


--
-- Name: oauth_tokens oauth_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.oauth_tokens
    ADD CONSTRAINT oauth_tokens_pkey PRIMARY KEY (uid);


--
-- Name: org_attachments org_attachments_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.org_attachments
    ADD CONSTRAINT org_attachments_pkey PRIMARY KEY (uid);


--
-- Name: orgs orgs_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.orgs
    ADD CONSTRAINT orgs_pkey PRIMARY KEY (uid);


--
-- Name: plan_attachments plan_attachments_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.plan_attachments
    ADD CONSTRAINT plan_attachments_pkey PRIMARY KEY (uid);


--
-- Name: repo_branches repo_branches_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.repo_branches
    ADD CONSTRAINT repo_branches_pkey PRIMARY KEY (uid);


--
-- Name: repos repos_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.repos
    ADD CONSTRAINT repos_pkey PRIMARY KEY (uid);


--
-- Name: run_attachments run_attachments_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.run_attachments
    ADD CONSTRAINT run_attachments_pkey PRIMARY KEY (uid);


--
-- Name: run_suite_attachments run_suite_attachments_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.run_suite_attachments
    ADD CONSTRAINT run_suite_attachments_pkey PRIMARY KEY (uid);


--
-- Name: sessions sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.sessions
    ADD CONSTRAINT sessions_pkey PRIMARY KEY (sid);


--
-- Name: step_attachments step_attachments_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.step_attachments
    ADD CONSTRAINT step_attachments_pkey PRIMARY KEY (uid);


--
-- Name: storage_profiles storage_profiles_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.storage_profiles
    ADD CONSTRAINT storage_profiles_pkey PRIMARY KEY (uid);


--
-- Name: storage_profiles storage_profiles_uid_unique; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.storage_profiles
    ADD CONSTRAINT storage_profiles_uid_unique UNIQUE (uid);


--
-- Name: subscriptions subscriptions_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.subscriptions
    ADD CONSTRAINT subscriptions_pkey PRIMARY KEY (uid);


--
-- Name: subscriptions subscriptions_stripe_customer_id_unique; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.subscriptions
    ADD CONSTRAINT subscriptions_stripe_customer_id_unique UNIQUE (stripe_customer_id);


--
-- Name: subscriptions subscriptions_stripe_subscription_id_unique; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.subscriptions
    ADD CONSTRAINT subscriptions_stripe_subscription_id_unique UNIQUE (stripe_subscription_id);


--
-- Name: suite_attachments suite_attachments_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.suite_attachments
    ADD CONSTRAINT suite_attachments_pkey PRIMARY KEY (uid);


--
-- Name: super_admins super_admins_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.super_admins
    ADD CONSTRAINT super_admins_pkey PRIMARY KEY (uid);


--
-- Name: super_admins super_admins_uid_unique; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.super_admins
    ADD CONSTRAINT super_admins_uid_unique UNIQUE (uid);


--
-- Name: super_admins super_admins_userUid_unique; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.super_admins
    ADD CONSTRAINT super_admins_userUid_unique UNIQUE (userUid);


--
-- Name: test_cases test_cases_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_cases
    ADD CONSTRAINT test_cases_pkey PRIMARY KEY (uid);


--
-- Name: test_execution_steps test_execution_steps_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_execution_steps
    ADD CONSTRAINT test_execution_steps_pkey PRIMARY KEY (uid);


--
-- Name: test_executions test_executions_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_executions
    ADD CONSTRAINT test_executions_pkey PRIMARY KEY (uid);


--
-- Name: testFolders testFolders_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.testFolders
    ADD CONSTRAINT testFolders_pkey PRIMARY KEY (uid);


--
-- Name: test_milestones test_milestones_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_milestones
    ADD CONSTRAINT test_milestones_pkey PRIMARY KEY (uid);


--
-- Name: test_plans test_plans_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_plans
    ADD CONSTRAINT test_plans_pkey PRIMARY KEY (uid);


--
-- Name: test_projects test_projects_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_projects
    ADD CONSTRAINT test_projects_pkey PRIMARY KEY (uid);


--
-- Name: test_results test_results_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_results
    ADD CONSTRAINT test_results_pkey PRIMARY KEY (uid);


--
-- Name: test_run_suites test_run_suites_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_run_suites
    ADD CONSTRAINT test_run_suites_pkey PRIMARY KEY (uid);


--
-- Name: test_runs test_runs_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_runs
    ADD CONSTRAINT test_runs_pkey PRIMARY KEY (uid);


--
-- Name: test_steps test_steps_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_steps
    ADD CONSTRAINT test_steps_pkey PRIMARY KEY (uid);


--
-- Name: test_suites test_suites_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_suites
    ADD CONSTRAINT test_suites_pkey PRIMARY KEY (uid);


--
-- Name: user_attachments user_attachments_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.user_attachments
    ADD CONSTRAINT user_attachments_pkey PRIMARY KEY (uid);


--
-- Name: users users_email_unique; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_unique UNIQUE (email);


--
-- Name: users users_password_reset_token_unique; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_password_reset_token_unique UNIQUE (password_reset_token);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (uid);


--
-- Name: users users_stripe_id_unique; Type: CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_stripe_id_unique UNIQUE (stripe_id);


--
-- Name: accessTokens_ownerType_ownerUid_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX accessTokens_ownerType_ownerUid_index ON public.accessTokens USING btree (ownerType, ownerUid);


--
-- Name: comments_entity_type_entity_uid_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX comments_entity_type_entity_uid_index ON public.comments USING btree (entity_type, entity_uid);


--
-- Name: handle_name; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX handle_name ON public.handles USING btree (name);


--
-- Name: handles_ownerUid_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX handles_ownerUid_index ON public.handles USING btree (ownerUid);


--
-- Name: oauth_tokens_retrievalId_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX oauth_tokens_retrievalId_index ON public.oauth_tokens USING btree (retrievalId);


--
-- Name: oauth_tokens_service_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX oauth_tokens_service_index ON public.oauth_tokens USING btree (service);


--
-- Name: orgs_name_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX orgs_name_index ON public.orgs USING btree (name);


--
-- Name: repos_source_externalId_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX repos_source_externalId_index ON public.repos USING btree (source, externalId);


--
-- Name: test_cases_parent_uid_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_cases_parent_uid_index ON public.test_cases USING btree (parent_uid);


--
-- Name: test_cases_project_uid_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_cases_project_uid_index ON public.test_cases USING btree (project_uid);


--
-- Name: test_cases_repo_uid_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_cases_repo_uid_index ON public.test_cases USING btree (repo_uid);


--
-- Name: test_cases_source_externalId_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_cases_source_externalId_index ON public.test_cases USING btree (source, externalId);


--
-- Name: test_cases_suite_uid_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_cases_suite_uid_index ON public.test_cases USING btree (suite_uid);


--
-- Name: test_executions_repo_branch_uid_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_executions_repo_branch_uid_index ON public.test_executions USING btree (repo_branch_uid);


--
-- Name: test_executions_source_externalId_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_executions_source_externalId_index ON public.test_executions USING btree (source, externalId);


--
-- Name: test_executions_test_case_uid_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_executions_test_case_uid_index ON public.test_executions USING btree (test_case_uid);


--
-- Name: test_executions_testRunUid_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_executions_testRunUid_index ON public.test_executions USING btree (testRunUid);


--
-- Name: testFolders_source_externalId_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX testFolders_source_externalId_index ON public.testFolders USING btree (source, externalId);


--
-- Name: test_milestones_project_uid_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_milestones_project_uid_index ON public.test_milestones USING btree (project_uid);


--
-- Name: test_milestones_source_externalId_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_milestones_source_externalId_index ON public.test_milestones USING btree (source, externalId);


--
-- Name: test_plans_project_uid_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_plans_project_uid_index ON public.test_plans USING btree (project_uid);


--
-- Name: test_plans_source_externalId_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_plans_source_externalId_index ON public.test_plans USING btree (source, externalId);


--
-- Name: test_projects_source_externalId_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_projects_source_externalId_index ON public.test_projects USING btree (source, externalId);


--
-- Name: test_results_test_execution_step_uid_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_results_test_execution_step_uid_index ON public.test_results USING btree (test_execution_step_uid);


--
-- Name: test_results_test_execution_uid_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_results_test_execution_uid_index ON public.test_results USING btree (test_execution_uid);


--
-- Name: test_run_suites_testRunUid_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_run_suites_testRunUid_index ON public.test_run_suites USING btree (testRunUid);


--
-- Name: test_run_suites_test_suite_uid_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_run_suites_test_suite_uid_index ON public.test_run_suites USING btree (test_suite_uid);


--
-- Name: test_runs_project_uid_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_runs_project_uid_index ON public.test_runs USING btree (project_uid);


--
-- Name: test_runs_source_externalId_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_runs_source_externalId_index ON public.test_runs USING btree (source, externalId);


--
-- Name: test_steps_test_case_uid_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_steps_test_case_uid_index ON public.test_steps USING btree (test_case_uid);


--
-- Name: test_suites_source_externalId_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX test_suites_source_externalId_index ON public.test_suites USING btree (source, externalId);


--
-- Name: users_password_reset_token_index; Type: INDEX; Schema: public; Owner: testfiesta
--

CREATE INDEX users_password_reset_token_index ON public.users USING btree (password_reset_token);


--
-- Name: case_attachments case_attachments_attachment_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.case_attachments
    ADD CONSTRAINT case_attachments_attachment_uid_foreign FOREIGN KEY (attachment_uid) REFERENCES public.attachments(uid);


--
-- Name: case_attachments case_attachments_case_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.case_attachments
    ADD CONSTRAINT case_attachments_case_uid_foreign FOREIGN KEY (case_uid) REFERENCES public.test_cases(uid);


--
-- Name: comment_attachments comment_attachments_attachment_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.comment_attachments
    ADD CONSTRAINT comment_attachments_attachment_uid_foreign FOREIGN KEY (attachment_uid) REFERENCES public.attachments(uid);


--
-- Name: comment_attachments comment_attachments_comment_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.comment_attachments
    ADD CONSTRAINT comment_attachments_comment_uid_foreign FOREIGN KEY (comment_uid) REFERENCES public.comments(uid);


--
-- Name: comments comments_created_by_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.comments
    ADD CONSTRAINT comments_created_by_foreign FOREIGN KEY (created_by) REFERENCES public.users(uid);


--
-- Name: dashboards dashboards_org_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.dashboards
    ADD CONSTRAINT dashboards_org_uid_foreign FOREIGN KEY (org_uid) REFERENCES public.orgs(uid);


--
-- Name: data_relationships data_relationships_org_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.data_relationships
    ADD CONSTRAINT data_relationships_org_uid_foreign FOREIGN KEY (org_uid) REFERENCES public.orgs(uid);


--
-- Name: execution_attachments execution_attachments_attachment_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.execution_attachments
    ADD CONSTRAINT execution_attachments_attachment_uid_foreign FOREIGN KEY (attachment_uid) REFERENCES public.attachments(uid);


--
-- Name: execution_attachments execution_attachments_execution_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.execution_attachments
    ADD CONSTRAINT execution_attachments_execution_uid_foreign FOREIGN KEY (execution_uid) REFERENCES public.test_executions(uid);


--
-- Name: execution_step_attachments execution_step_attachments_attachment_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.execution_step_attachments
    ADD CONSTRAINT execution_step_attachments_attachment_uid_foreign FOREIGN KEY (attachment_uid) REFERENCES public.attachments(uid);


--
-- Name: execution_step_attachments execution_step_attachments_execution_step_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.execution_step_attachments
    ADD CONSTRAINT execution_step_attachments_execution_step_uid_foreign FOREIGN KEY (execution_step_uid) REFERENCES public.test_execution_steps(uid);


--
-- Name: invites invites_inviter_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.invites
    ADD CONSTRAINT invites_inviter_uid_foreign FOREIGN KEY (inviter_uid) REFERENCES public.users(uid);


--
-- Name: invites invites_org_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.invites
    ADD CONSTRAINT invites_org_uid_foreign FOREIGN KEY (org_uid) REFERENCES public.orgs(uid);


--
-- Name: milestone_attachments milestone_attachments_attachment_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.milestone_attachments
    ADD CONSTRAINT milestone_attachments_attachment_uid_foreign FOREIGN KEY (attachment_uid) REFERENCES public.attachments(uid);


--
-- Name: milestone_attachments milestone_attachments_milestone_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.milestone_attachments
    ADD CONSTRAINT milestone_attachments_milestone_uid_foreign FOREIGN KEY (milestone_uid) REFERENCES public.test_milestones(uid);


--
-- Name: org_attachments org_attachments_attachment_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.org_attachments
    ADD CONSTRAINT org_attachments_attachment_uid_foreign FOREIGN KEY (attachment_uid) REFERENCES public.attachments(uid);


--
-- Name: org_attachments org_attachments_org_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.org_attachments
    ADD CONSTRAINT org_attachments_org_uid_foreign FOREIGN KEY (org_uid) REFERENCES public.orgs(uid);


--
-- Name: orgs orgs_created_by_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.orgs
    ADD CONSTRAINT orgs_created_by_foreign FOREIGN KEY (created_by) REFERENCES public.users(uid);


--
-- Name: plan_attachments plan_attachments_attachment_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.plan_attachments
    ADD CONSTRAINT plan_attachments_attachment_uid_foreign FOREIGN KEY (attachment_uid) REFERENCES public.attachments(uid);


--
-- Name: plan_attachments plan_attachments_plan_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.plan_attachments
    ADD CONSTRAINT plan_attachments_plan_uid_foreign FOREIGN KEY (plan_uid) REFERENCES public.test_plans(uid);


--
-- Name: repo_branches repo_branches_repo_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.repo_branches
    ADD CONSTRAINT repo_branches_repo_uid_foreign FOREIGN KEY (repo_uid) REFERENCES public.repos(uid);


--
-- Name: run_attachments run_attachments_attachment_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.run_attachments
    ADD CONSTRAINT run_attachments_attachment_uid_foreign FOREIGN KEY (attachment_uid) REFERENCES public.attachments(uid);


--
-- Name: run_attachments run_attachments_run_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.run_attachments
    ADD CONSTRAINT run_attachments_run_uid_foreign FOREIGN KEY (run_uid) REFERENCES public.test_runs(uid);


--
-- Name: run_suite_attachments run_suite_attachments_attachment_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.run_suite_attachments
    ADD CONSTRAINT run_suite_attachments_attachment_uid_foreign FOREIGN KEY (attachment_uid) REFERENCES public.attachments(uid);


--
-- Name: run_suite_attachments run_suite_attachments_run_suite_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.run_suite_attachments
    ADD CONSTRAINT run_suite_attachments_run_suite_uid_foreign FOREIGN KEY (run_suite_uid) REFERENCES public.test_run_suites(uid);


--
-- Name: sessions sessions_userUid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.sessions
    ADD CONSTRAINT sessions_userUid_foreign FOREIGN KEY (userUid) REFERENCES public.users(uid);


--
-- Name: step_attachments step_attachments_attachment_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.step_attachments
    ADD CONSTRAINT step_attachments_attachment_uid_foreign FOREIGN KEY (attachment_uid) REFERENCES public.attachments(uid);


--
-- Name: step_attachments step_attachments_step_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.step_attachments
    ADD CONSTRAINT step_attachments_step_uid_foreign FOREIGN KEY (step_uid) REFERENCES public.test_steps(uid);


--
-- Name: suite_attachments suite_attachments_attachment_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.suite_attachments
    ADD CONSTRAINT suite_attachments_attachment_uid_foreign FOREIGN KEY (attachment_uid) REFERENCES public.attachments(uid);


--
-- Name: suite_attachments suite_attachments_suite_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.suite_attachments
    ADD CONSTRAINT suite_attachments_suite_uid_foreign FOREIGN KEY (suite_uid) REFERENCES public.test_suites(uid);


--
-- Name: super_admins super_admins_userUid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.super_admins
    ADD CONSTRAINT super_admins_userUid_foreign FOREIGN KEY (userUid) REFERENCES public.users(uid);


--
-- Name: test_cases test_cases_parent_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_cases
    ADD CONSTRAINT test_cases_parent_uid_foreign FOREIGN KEY (parent_uid) REFERENCES public.testFolders(uid);


--
-- Name: test_cases test_cases_project_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_cases
    ADD CONSTRAINT test_cases_project_uid_foreign FOREIGN KEY (project_uid) REFERENCES public.test_projects(uid);


--
-- Name: test_cases test_cases_repo_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_cases
    ADD CONSTRAINT test_cases_repo_uid_foreign FOREIGN KEY (repo_uid) REFERENCES public.repos(uid);


--
-- Name: test_cases test_cases_suite_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_cases
    ADD CONSTRAINT test_cases_suite_uid_foreign FOREIGN KEY (suite_uid) REFERENCES public.test_suites(uid);


--
-- Name: test_execution_steps test_execution_steps_test_execution_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_execution_steps
    ADD CONSTRAINT test_execution_steps_test_execution_uid_foreign FOREIGN KEY (test_execution_uid) REFERENCES public.test_executions(uid);


--
-- Name: test_execution_steps test_execution_steps_test_step_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_execution_steps
    ADD CONSTRAINT test_execution_steps_test_step_uid_foreign FOREIGN KEY (test_step_uid) REFERENCES public.test_steps(uid);


--
-- Name: test_executions test_executions_repo_branch_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_executions
    ADD CONSTRAINT test_executions_repo_branch_uid_foreign FOREIGN KEY (repo_branch_uid) REFERENCES public.repo_branches(uid);


--
-- Name: test_executions test_executions_test_case_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_executions
    ADD CONSTRAINT test_executions_test_case_uid_foreign FOREIGN KEY (test_case_uid) REFERENCES public.test_cases(uid);


--
-- Name: test_executions test_executions_testRunUid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_executions
    ADD CONSTRAINT test_executions_testRunUid_foreign FOREIGN KEY (testRunUid) REFERENCES public.test_runs(uid);


--
-- Name: testFolders testFolders_parent_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.testFolders
    ADD CONSTRAINT testFolders_parent_uid_foreign FOREIGN KEY (parent_uid) REFERENCES public.testFolders(uid);


--
-- Name: testFolders testFolders_project_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.testFolders
    ADD CONSTRAINT testFolders_project_uid_foreign FOREIGN KEY (project_uid) REFERENCES public.test_projects(uid);


--
-- Name: test_milestones test_milestones_project_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_milestones
    ADD CONSTRAINT test_milestones_project_uid_foreign FOREIGN KEY (project_uid) REFERENCES public.test_projects(uid);


--
-- Name: test_plans test_plans_project_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_plans
    ADD CONSTRAINT test_plans_project_uid_foreign FOREIGN KEY (project_uid) REFERENCES public.test_projects(uid);


--
-- Name: test_results test_results_test_execution_step_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_results
    ADD CONSTRAINT test_results_test_execution_step_uid_foreign FOREIGN KEY (test_execution_step_uid) REFERENCES public.test_executions(uid);


--
-- Name: test_results test_results_test_execution_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_results
    ADD CONSTRAINT test_results_test_execution_uid_foreign FOREIGN KEY (test_execution_uid) REFERENCES public.test_executions(uid);


--
-- Name: test_run_suites test_run_suites_testRunUid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_run_suites
    ADD CONSTRAINT test_run_suites_testRunUid_foreign FOREIGN KEY (testRunUid) REFERENCES public.test_runs(uid);


--
-- Name: test_run_suites test_run_suites_test_suite_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_run_suites
    ADD CONSTRAINT test_run_suites_test_suite_uid_foreign FOREIGN KEY (test_suite_uid) REFERENCES public.test_suites(uid);


--
-- Name: test_runs test_runs_project_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_runs
    ADD CONSTRAINT test_runs_project_uid_foreign FOREIGN KEY (project_uid) REFERENCES public.test_projects(uid);


--
-- Name: test_steps test_steps_test_case_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_steps
    ADD CONSTRAINT test_steps_test_case_uid_foreign FOREIGN KEY (test_case_uid) REFERENCES public.test_cases(uid);


--
-- Name: test_suites test_suites_project_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.test_suites
    ADD CONSTRAINT test_suites_project_uid_foreign FOREIGN KEY (project_uid) REFERENCES public.test_projects(uid);


--
-- Name: user_attachments user_attachments_attachment_uid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.user_attachments
    ADD CONSTRAINT user_attachments_attachment_uid_foreign FOREIGN KEY (attachment_uid) REFERENCES public.attachments(uid);


--
-- Name: user_attachments user_attachments_userUid_foreign; Type: FK CONSTRAINT; Schema: public; Owner: testfiesta
--

ALTER TABLE ONLY public.user_attachments
    ADD CONSTRAINT user_attachments_userUid_foreign FOREIGN KEY (userUid) REFERENCES public.users(uid);


--
-- PostgreSQL database dump complete
--

