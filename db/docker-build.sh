#!/bin/bash


gcloud auth print-access-token | sudo docker login -u oauth2accesstoken --password-stdin https://us-central1-docker.pkg.dev

sudo docker-compose -f serve/bootstrap.docker-compose.yml build

BUILD_SHA=$(git rev-parse HEAD)
# TODO - Set the version from the package file
sudo docker tag testfiesta-backend:latest us-central1-docker.pkg.dev/cicd-0927/testfiesta-backend/testfiesta-backend:$(BUILD_SHA)
sudo docker tag testfiesta-backend:latest us-central1-docker.pkg.dev/cicd-0927/testfiesta-backend/testfiesta-backend:${BUILD_VERSION}

sudo docker push us-central1-docker.pkg.dev/cicd-0927/testfiesta-backend/testfiesta-backend --all-tags
