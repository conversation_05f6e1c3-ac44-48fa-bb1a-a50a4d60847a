require('dotenv').config();

const knex = require('knex');
const fs = require('fs');
const path = require('path');
const env = process.env;

let version = process.env.npm_package_version;
if (version) return version;

const packagePath = path.join(process.cwd(), 'package.json');
version = JSON.parse(fs.readFileSync(packagePath).toString()).version;
const serverVersion = version;

const sharedDB = getDBConnection(
  env.DB_HOST,
  env.DB_USERNAME,
  env.DB_PASSWORD,
  env.DB_DATABASE,
);

/**
 * list of user or org uids
 */
const uids = process.argv.slice(2);

async function migrate() {
  console.log('Pulling tenants for knex migration...');
  const tenants = await sharedDB('tenants')
    .where('tenants.isMaintenance', false)
    .join('dbServers', 'tenants.dbServerUid', 'dbServers.uid')
    .join(
      'appVersions',
      'tenants.backendVersionUid',
      'appVersions.uid',
    )
    .select('tenants.*', 'dbServers.*', 'appVersions.version')
    .where((q) => {
      if (uids.length > 0) {
        q.whereIn('tenants.tenantUid', uids);
      } else {
        q.whereRaw('1=1');
      }
    })
    .where(`appVersions.version`, `v${serverVersion}`);

  for (const tenant of tenants) {
    console.log('migrating tenant', tenant.tenantUid);
    try {
      const db = getDBConnection(
        tenant.host,
        tenant.username,
        tenant.password,
        tenant.tenantUid,
        tenant.port,
      );
      const res = await migrateDB(db);
      console.log('successfully migrated tenant', tenant.tenantUid, res);
      await db.destroy();
    } catch (error) {
      console.log('failed to migrate tenant', error);
    }
  }

  console.log(`successfully migrated ${tenants.length} tenants`);
  process.exit(0);
}

/**
 * Gets the knex db connection for the provided details
 * @param {string} host
 * @param {string} user
 * @param {string} password
 * @param {string} db
 * @param {number} port
 * @returns
 */
function getDBConnection(host, user, password, db, port = 5432) {
  return knex({
    client: 'pg',
    connection: {
      host: host,
      port: port,
      user: user,
      password: password,
      database: db,
    },
  });
}

// migrations path
const migrationsPath = path.join(process.cwd(), 'dist/db/migrations');

console.log(migrationsPath);
/**
 * migrates a databases given the knex connection
 * @param {knex.Knex} db
 */
function migrateDB(db) {
  return db.migrate.latest({
    directory: migrationsPath,
    loadExtensions: ['.js'],
  });
}

migrate();
