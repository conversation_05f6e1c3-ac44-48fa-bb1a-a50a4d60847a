import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
    return knex.schema.dropTable('testRuns');
}

export async function down(knex: Knex): Promise<void> {
    return knex.schema.createTableIfNotExists('testRuns', (table) => {
        table.increments('uid').unique().primary().unsigned();
        table.text('externalId');
        table.text('source');
        table.text('name');
        table.text('priority');
        table.text('status');
        table.text('link');
        table.integer('projectUid').references('uid').inTable('projects').index();
        table.integer('testPlanUid').references('testPlans.uid').nullable();
        table.json('customFields');
        table.timestamp('completedAt');
        table.timestamp('externalCreatedAt');
        table.timestamp('externalUpdatedAt');
        table
            .timestamp('createdAt')
            .notNullable()
            .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
        table
            .timestamp('updatedAt')
            .notNullable()
            .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
        table.timestamp('deletedAt');
        table.index(['source', 'externalId']);
    });
}
