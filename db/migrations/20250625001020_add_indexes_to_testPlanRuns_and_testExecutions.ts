import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.raw(
    'CREATE INDEX testplanruns_deletedat_index ON "testPlanRuns"("deletedAt") WHERE "deletedAt" IS NULL',
  );

  await knex.raw(
    'CREATE INDEX testexecutions_deletedat_index ON "testExecutions"("deletedAt") WHERE "deletedAt" IS NULL',
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.raw('DROP INDEX testplanruns_deletedat_index');
  await knex.raw('DROP INDEX testexecutions_deletedat_index');
}
