import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
    return knex.schema.dropTable('testMilestones');
}

export async function down(knex: Knex): Promise<void> {
    return knex.schema.createTableIfNotExists('testMilestones', (table) => {
        table.increments('uid', { primaryKey: true });
        table.integer('projectUid').references('projects.uid').index();
        table.text('externalId').notNullable();
        table.text('source').notNullable();
        table.text('name').notNullable();
        table.text('link');
        table.json('customFields');
        table.text('description');
        table.timestamp('dueAt');
        table.timestamp('completedAt');
        table.timestamp('externalCreatedAt');
        table.timestamp('externalUpdatedAt');
        table
            .timestamp('createdAt')
            .notNullable()
            .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
        table
            .timestamp('updatedAt')
            .notNullable()
            .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
        table.timestamp('deletedAt');
        table.index(['source', 'externalId']);
    });
}
