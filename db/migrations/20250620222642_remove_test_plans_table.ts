import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
    return dropPlansTable(knex)
}

export async function down(knex: Knex): Promise<void> {
    return knex.schema.createTableIfNotExists('testPlans', (table) => {
        table.increments('uid').primary().unsigned();
        table.integer('projectUid').references('uid').inTable('projects').index();
        table.text('externalId').nullable();
        table.text('source').nullable();
        table.text('name');
        table.text('description');
        table.integer('progress');
        table.string('priority');
        table.string('status');
        table.text('link');
        table.json('customFields');
        table.timestamp('completedAt');
        table.timestamp('externalCreatedAt');
        table.timestamp('externalUpdatedAt');
        table.integer('testMilestoneUid').references('testMilestones.uid');
        table.timestamp('archivedAt');
        table
            .timestamp('createdAt')
            .notNullable()
            .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
        table
            .timestamp('updatedAt')
            .notNullable()
            .defaultTo(knex.raw('CURRENT_TIMESTAMP'));
        table.timestamp('deletedAt');
        table.index(['source', 'externalId']);
    });
}


export async function dropPlansTable(knex: Knex) {
    await knex.schema.alterTable("testPlanConfigurations", (schema) => {
        schema.dropForeign('planUid');
    })
    await knex.schema.dropTable('testPlans');
}
