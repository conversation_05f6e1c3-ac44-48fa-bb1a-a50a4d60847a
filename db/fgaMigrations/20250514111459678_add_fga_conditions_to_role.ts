
  import { Tenant } from "@app/models/tenant"
  import { FgaService } from "@ss-libs/ss-component-auth";
  import { Knex } from 'knex';
  // import { setupDB } from "@app/config/db";
  // import { User } from "@app/models/user";
  // import { ProjectInvite } from "@app/models/projectInvite";
  import { Role } from "@app/models/role";
  export async function tenantMigration(tenant : Tenant, fga: FgaService,tenantDb?: Knex){
    
    const writesToAdd = new Set();
    const writesToDelete = new Set();
    // new writeToAdd strcuture => objectType|objectID|subjectType|subjectID|relation|conditionName|JSON.stringify(context)

    if(tenant.tenantType == 'org'){
      const orgMember = (await fga.query('', `org:${tenant.tenantUid}`, 'member')).map(item => item.key.user);
      const membersUids = orgMember.map((member) => member.split(':')[1]);
      const roles = await Role.query(tenantDb).select('uid', 'slug', 'projectUid', 'description', 'name');
      for(const member of membersUids){
        const userAssignedRoles = await fga.query(`user:${member}`, 'role:', 'assignee')

       // Scenarios to handle:
        // 1. If a user is invited at the project level, create a role at the org level with zero permissions and assign it to the user as a default role. The admin can update this role later.
        // 2. Check if the user already has a role at the org level. If so, move it to have an empty list in the default_role condition, e.g., { condition: 'default_role', context: { excluded: [] } }.
        // 3. If the user has roles at both the org level and the project level, remove the role at the org level and reassign it with an default_role condition. Add the projects from the project-level roles to the excluded list, e.g., { condition: 'default_role', context: { excluded: [projectUids] } }. Then, add those project UIDs with an override_role condition, e.g., { condition: 'override_role', context: { allowed: [projectUids] } }.

        // Check if the user role in project level 
        if(userAssignedRoles.length){
          // if length == 1 .. use this role directly in org level  with empty excluded condition
          if(userAssignedRoles.length == 1){
            const roleUid = userAssignedRoles[0].key.object.split(':')[1];
            const userRole = roles.find((role) => role.uid == roleUid);
            const orgRole = roles.find((role) => role.slug == userRole.slug && !role.projectUid);
            const orgRoleUid = orgRole?.uid;

            if(orgRole){ // Scenario 1: If the role already exists at the org level, assign it to the user
              writesToAdd.add(`role|${orgRoleUid}|user|${member}|assignee|default_role|${JSON.stringify({excluded: []})}`) // add the role in org level
            }else{
              // Scenario 2: If the role does not exist at the org level, create a new role
              const newRole = {
                slug: userRole.slug,
                name: userRole.name,
                description: userRole.description,
                system: false,
              } 
              const insertedRole = await Role.query(tenantDb).insert(newRole).returning('uid');

              // Add permissions to the newly created role
              const permissions = await fga.query(`role:${userRole.uid}#assignee`,`project:${userRole.projectUid}`, '');
              writesToAdd.add(`role|${insertedRole.uid}|org|${tenant.tenantUid}|owner`);
              permissions.forEach((p) => {
                const permission = p.key.relation;
                writesToAdd.add(`org|${tenant.tenantUid}|role|${insertedRole.uid}|${permission}`)
              })
                // Assign the newly created org-level role to the user with an empty default_role condition .. all projects within the org
              writesToAdd.add(`role|${insertedRole.uid}|user|${member}|assignee|default_role|${JSON.stringify({excluded: []})}`) // assign the user role in org level
            }
            writesToDelete.add(`role|${roleUid}|user|${member}|assignee`) // Remove the old role assignment from the user
          }else if(userAssignedRoles.length > 1){
            // If the user has more than one assigned role, remove the roles at the org level and reassign them with an excluded condition.

            // Extract the UIDs of all roles assigned to the user
            const roleUids = userAssignedRoles.map((role) => role.key.object.split(':')[1]);

            // check if user has any role in org level
            const userRoles = roles.filter((role) => roleUids.includes(role.uid) && !role.projectUid);

            // Filter roles that are at the project leve
            const excludedProjects = roles.filter((role) => roleUids.includes(role.uid) && role.projectUid).map((role) => `project:${role.projectUid}`);

            if(userRoles.length){
              // If the user has at least one role at the org level, select the first one
              const userRole = userRoles[0];
              const orgRoleUid = userRole.uid;
              // Add the org-level role to the user with an default_role condition
              writesToAdd.add(`role|${orgRoleUid}|user|${member}|assignee|default_role|${JSON.stringify({excluded: excludedProjects})}`) // add the role in org level and exluded the project with specific role
            }else{
              // Check if there are any roles at the org level
              const orgRole = roles.filter(e => e.system == false && e.projectUid == null);
              if(orgRole.length){
                // randomly select one role
                const orgRoleUid = orgRole[Math.floor(Math.random() * orgRole.length)].uid;
                writesToAdd.add(`role|${orgRoleUid}|user|${member}|assignee|default_role|${JSON.stringify({excluded: excludedProjects})}`) // add the role in org level and exluded the project with specific role
              }else{
              // If no org-level roles exist, create a new org-level role with zero permissions
                const newRole = {
                  slug: `default-role-${Date.now()}`,
                  name: `Default Role ${Date.now()}`,
                  description: 'Default role created for user',
                  system: false,
                } 
                const insertedRole = await Role.query(tenantDb).insert(newRole).returning('uid');
                writesToAdd.add(`role|${insertedRole.uid}|org|${tenant.tenantUid}|owner`); // 0 permissions
                writesToAdd.add(`role|${insertedRole.uid}|user|${member}|assignee|default_role|${JSON.stringify({excluded: excludedProjects})}`) // assign the user role in org level
              }
            }
              // Group project-level roles by their slug and map them to their associated projects
              /**
               * Like: {
               * 'test_role': ['project:1', 'project:2'],
               * 'client_role': ['project:3']
               * }
               */
            const projectRoles = userAssignedRoles.reduce((acc, curr) => {
              const roleUid = curr.key.object.split(':')[1];
              const role = roles.find((role) => role.uid == roleUid && role.projectUid);
              if(role){
                if(!acc[role.slug])
                  acc[role.slug] = [`project:${role.projectUid}`];
                else
                  acc[role.slug].push(`project:${role.projectUid}`)
              }
              return acc
            }, {})

            // Iterate over each slug in the grouped project roles
            for(const slug of Object.keys(projectRoles)){
              const orgRole = roles.find((role) => role.slug == slug && !role.projectUid);

              if(orgRole){
                // If the role already exists at the org level, assign it to the user with an override_role condition
                const orgRoleUid = orgRole.uid;
                writesToAdd.add(`role|${orgRoleUid}|user|${member}|assignee|override_role|${JSON.stringify({allowed: projectRoles[slug]})}`)
              }else{
                // If no org-level role exists with the same slug, create a new org-level role
                const roleDetails = roles.find((role) => role.slug == slug && role.projectUid); // Select same role slug from any project
                const newRole = {
                  slug: roleDetails.slug,
                  name: roleDetails.name,
                  description: roleDetails.description,
                  system: false,
                }
                // Insert the new org-level role into the database
                const permissions = await fga.query(`role:${roleDetails.uid}#assignee`,`project:${roleDetails.projectUid}`, '');
                const insertedRole = await Role.query(tenantDb).insert(newRole).returning('uid');
                writesToAdd.add(`role|${insertedRole.uid}|org|${tenant.tenantUid}|owner`);
                permissions.forEach((p) => {
                  const permission = p.key.relation;
                  writesToAdd.add(`org|${tenant.tenantUid}|role|${insertedRole.uid}|${permission}`) // add permissions to the new role  
                })
                writesToAdd.add(`role|${insertedRole.uid}|user|${member}|assignee|override_role|${JSON.stringify({allowed: projectRoles[slug]})}`)
              }
            }
            // Remove the old assignee from user 
            for(const r of roleUids){
              writesToDelete.add(`role|${r}|user|${member}|assignee`) // remove the old role from user
            }
          }
        }
      }
    }
    return {
      writesToAdd,
      writesToDelete
    }
  
  }
  
  export const authModel = {
    schema_version: "1.1",
    type_definitions: [
      {
        type: "accessToken",
        relations: {},
        metadata: null
      },
      {
        type: "user",
        relations: {
          delete_activity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_custom_field: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_dashboard: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_defect: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_entity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_integration: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_key: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_member: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_project: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_report: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_role: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_step: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_tag: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_template: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          full_billing: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          limited_billing: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "full_billing"
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          member: {
            this: {}
          },
          no_billing: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "limited_billing"
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          owner: {
            this: {}
          },
          read_activity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          read_dashboard: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          read_defect: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          read_entity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          read_integration: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          read_member: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          read_report: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          read_role: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_activity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_custom_field: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_dashboard: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_defect: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_entity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_integration: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_key: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_member: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_project: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_report: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_role: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_setting: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_step: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_tag: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_template: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          }
        },
        metadata: {
          relations: {
            delete_activity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_custom_field: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_dashboard: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_defect: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_integration: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_key: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_member: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_project: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_report: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_role: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_step: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_tag: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_template: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            full_billing: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            limited_billing: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            member: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                }
              ]
            },
            no_billing: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            owner: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_activity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_dashboard: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_defect: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_integration: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_member: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_report: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_role: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_activity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_custom_field: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_dashboard: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_defect: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_integration: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_key: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_member: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_project: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_report: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_role: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_setting: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_step: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_tag: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_template: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            }
          }
        }
      },
      {
        type: "org",
        relations: {
          delete_activity: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_activity"
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_custom_field: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_dashboard: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_dashboard"
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_defect: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_entity: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_entity"
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_integration: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_integration"
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_key: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_member: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_member"
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_project: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_report: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_report"
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_role: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_role"
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_step: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_tag: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          delete_template: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          full_billing: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          limited_billing: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "full_billing"
                  }
                }
              ]
            }
          },
          member: {
            this: {}
          },
          no_billing: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "limited_billing"
                  }
                }
              ]
            }
          },
          owner: {
            this: {}
          },
          read_activity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          read_dashboard: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          read_defect: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          read_entity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          read_integration: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          read_member: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          read_report: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          read_role: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_activity: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_activity"
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_custom_field: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_dashboard: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_dashboard"
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_defect: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_defect"
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_entity: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_entity"
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_integration: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_integration"
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_key: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_member: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_member"
                        }
                      },
                      {
                        computedUserset: {
                          relation: "read_role"
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_project: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_report: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_report"
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_role: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_role"
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_setting: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_step: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_tag: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_template: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          }
        },
        metadata: {
          relations: {
            delete_activity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_custom_field: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_dashboard: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_defect: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_integration: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_key: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_member: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_project: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_report: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_role: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_step: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_tag: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_template: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            full_billing: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            limited_billing: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            member: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                }
              ]
            },
            no_billing: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            owner: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_activity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_dashboard: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_defect: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_integration: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_member: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_report: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_role: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_activity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_custom_field: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_dashboard: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_defect: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_integration: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_key: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_member: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_project: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_report: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_role: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_setting: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_step: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_tag: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_template: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            }
          }
        }
      },
      {
        type: "project",
        relations: {
          delete_activity: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_activity"
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_activity"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_activity"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          delete_custom_field: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "delete_custom_field"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          delete_dashboard: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_dashboard"
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_dashboard"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_dashboard"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          delete_defect: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_defect"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_defect"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          delete_entity: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_entity"
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_entity"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          delete_integration: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_integration"
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_integration"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_integration"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          delete_key: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "delete_key"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          delete_member: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_member"
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_member"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_member"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          delete_project: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "delete_project"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          delete_report: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_report"
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_report"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_report"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          delete_role: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_role"
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_role"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_role"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          delete_step: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "delete_step"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          delete_tag: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "delete_tag"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          delete_template: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "delete_template"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          member: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "member"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          owner: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          parent: {
            this: {}
          },
          read_activity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_activity"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          read_dashboard: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_dashboard"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          read_defect: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_defect"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          read_entity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_entity"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          read_integration: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_integration"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          read_member: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_member"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          read_report: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_report"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          read_role: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_role"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          write_activity: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_activity"
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_activity"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_activity"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          write_custom_field: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "write_custom_field"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          write_dashboard: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_dashboard"
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_dashboard"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_dashboard"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          write_defect: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_defect"
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_defect"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_defect"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          write_entity: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_entity"
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_entity"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          write_integration: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_integration"
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_integration"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_integration"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          write_key: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "write_key"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          write_member: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_member"
                        }
                      },
                      {
                        computedUserset: {
                          relation: "read_role"
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_member"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_role"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          write_project: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "write_project"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          write_report: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_report"
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_report"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_report"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          write_role: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_role"
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_role"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_role"
                          },
                          tupleset: {
                            relation: "parent"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          write_setting: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "write_setting"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          write_step: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "write_step"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          write_tag: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "write_tag"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          },
          write_template: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "write_template"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "owner"
                    },
                    tupleset: {
                      relation: "parent"
                    }
                  }
                }
              ]
            }
          }
        },
        metadata: {
          relations: {
            delete_activity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_custom_field: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_dashboard: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_defect: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_integration: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_key: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_member: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_project: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_report: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_role: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_step: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_tag: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            delete_template: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            member: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                }
              ]
            },
            owner: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "org"
                }
              ]
            },
            parent: {
              directly_related_user_types: [
                {
                  type: "org"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "user"
                }
              ]
            },
            read_activity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_dashboard: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_defect: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_integration: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_member: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_report: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            read_role: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_activity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_custom_field: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_dashboard: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_defect: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_integration: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_key: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_member: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_project: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_report: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_role: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_setting: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_step: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_tag: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_template: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "accessToken"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            }
          }
        }
      },
      {
        type: "role",
        relations: {
          assignee: {
            this: {}
          },
          delete_role: {
            this: {}
          },
          owner: {
            this: {}
          }
        },
        metadata: {
          relations: {
            assignee: {
              directly_related_user_types: [
                {
                  type: "user",
                  condition: "default_role"
                },
                {
                  type: "user",
                  condition: "override_role"
                }
              ]
            },
            delete_role: {
              directly_related_user_types: [
                {
                  type: "user"
                }
              ]
            },
            owner: {
              directly_related_user_types: [
                {
                  type: "org"
                },
                {
                  type: "user"
                },
                {
                  type: "project"
                }
              ]
            }
          }
        }
      },
      {
        type: "plan",
        relations: {
          delete_entity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_entity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_entity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          owner: {
            this: {}
          },
          project: {
            this: {}
          },
          read_entity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_entity"
                    },
                    tupleset: {
                      relation: "owner"
                    }
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_entity"
                    },
                    tupleset: {
                      relation: "project"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_entity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_entity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_entity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          }
        },
        metadata: {
          relations: {
            delete_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            owner: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "org"
                }
              ]
            },
            project: {
              directly_related_user_types: [
                {
                  type: "project"
                }
              ]
            },
            read_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            }
          }
        }
      },
      {
        type: "milestone",
        relations: {
          delete_entity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_entity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_entity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          owner: {
            this: {}
          },
          project: {
            this: {}
          },
          read_entity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_entity"
                    },
                    tupleset: {
                      relation: "owner"
                    }
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_entity"
                    },
                    tupleset: {
                      relation: "project"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_entity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_entity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_entity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          }
        },
        metadata: {
          relations: {
            delete_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            owner: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "org"
                }
              ]
            },
            project: {
              directly_related_user_types: [
                {
                  type: "project"
                }
              ]
            },
            read_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            }
          }
        }
      },
      {
        type: "run",
        relations: {
          delete_entity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_entity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_entity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          owner: {
            this: {}
          },
          project: {
            this: {}
          },
          read_entity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_entity"
                    },
                    tupleset: {
                      relation: "owner"
                    }
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_entity"
                    },
                    tupleset: {
                      relation: "project"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_entity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_entity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_entity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          }
        },
        metadata: {
          relations: {
            delete_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            owner: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "org"
                }
              ]
            },
            project: {
              directly_related_user_types: [
                {
                  type: "project"
                }
              ]
            },
            read_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            }
          }
        }
      },
      {
        type: "step",
        relations: {
          delete_step: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "delete_step"
                    },
                    tupleset: {
                      relation: "owner"
                    }
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "delete_step"
                    },
                    tupleset: {
                      relation: "project"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          owner: {
            this: {}
          },
          project: {
            this: {}
          },
          write_step: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "write_step"
                    },
                    tupleset: {
                      relation: "owner"
                    }
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "write_step"
                    },
                    tupleset: {
                      relation: "project"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          }
        },
        metadata: {
          relations: {
            delete_step: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            owner: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "org"
                }
              ]
            },
            project: {
              directly_related_user_types: [
                {
                  type: "project"
                }
              ]
            },
            write_step: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            }
          }
        }
      },
      {
        type: "template",
        relations: {
          delete_template: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "delete_template"
                    },
                    tupleset: {
                      relation: "owner"
                    }
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "delete_template"
                    },
                    tupleset: {
                      relation: "project"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          owner: {
            this: {}
          },
          project: {
            this: {}
          },
          write_template: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "write_template"
                    },
                    tupleset: {
                      relation: "owner"
                    }
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "write_template"
                    },
                    tupleset: {
                      relation: "project"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          }
        },
        metadata: {
          relations: {
            delete_template: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            owner: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "org"
                }
              ]
            },
            project: {
              directly_related_user_types: [
                {
                  type: "project"
                }
              ]
            },
            write_template: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            }
          }
        }
      },
      {
        type: "report",
        relations: {
          delete_report: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_report"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_report"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_report"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_report"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          owner: {
            this: {}
          },
          project: {
            this: {}
          },
          read_report: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_report"
                    },
                    tupleset: {
                      relation: "owner"
                    }
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_report"
                    },
                    tupleset: {
                      relation: "project"
                    }
                  }
                }
              ]
            }
          },
          write_report: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_report"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_report"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_report"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_report"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          }
        },
        metadata: {
          relations: {
            delete_report: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            owner: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "org"
                }
              ]
            },
            project: {
              directly_related_user_types: [
                {
                  type: "project"
                }
              ]
            },
            read_report: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_report: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            }
          }
        }
      },
      {
        type: "tag",
        relations: {
          delete_tag: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "delete_tag"
                    },
                    tupleset: {
                      relation: "owner"
                    }
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "delete_tag"
                    },
                    tupleset: {
                      relation: "project"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          owner: {
            this: {}
          },
          project: {
            this: {}
          },
          write_tag: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "write_tag"
                    },
                    tupleset: {
                      relation: "owner"
                    }
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "write_tag"
                    },
                    tupleset: {
                      relation: "project"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          }
        },
        metadata: {
          relations: {
            delete_tag: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            owner: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "org"
                }
              ]
            },
            project: {
              directly_related_user_types: [
                {
                  type: "project"
                }
              ]
            },
            write_tag: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            }
          }
        }
      },
      {
        type: "case",
        relations: {
          delete_entity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_entity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_entity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          owner: {
            this: {}
          },
          project: {
            this: {}
          },
          read_entity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_entity"
                    },
                    tupleset: {
                      relation: "owner"
                    }
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_entity"
                    },
                    tupleset: {
                      relation: "project"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_entity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_entity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_entity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          }
        },
        metadata: {
          relations: {
            delete_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            owner: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "org"
                }
              ]
            },
            project: {
              directly_related_user_types: [
                {
                  type: "project"
                }
              ]
            },
            read_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            }
          }
        }
      },
      {
        type: "folder",
        relations: {
          delete_entity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_entity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_entity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          owner: {
            this: {}
          },
          project: {
            this: {}
          },
          read_entity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_entity"
                    },
                    tupleset: {
                      relation: "owner"
                    }
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_entity"
                    },
                    tupleset: {
                      relation: "project"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_entity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_entity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_entity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_entity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          }
        },
        metadata: {
          relations: {
            delete_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            owner: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "org"
                }
              ]
            },
            project: {
              directly_related_user_types: [
                {
                  type: "project"
                }
              ]
            },
            read_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_entity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            }
          }
        }
      },
      {
        type: "custom_field",
        relations: {
          delete_custom_field: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "delete_custom_field"
                    },
                    tupleset: {
                      relation: "owner"
                    }
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "delete_custom_field"
                    },
                    tupleset: {
                      relation: "project"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          owner: {
            this: {}
          },
          project: {
            this: {}
          },
          write_custom_field: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "write_custom_field"
                    },
                    tupleset: {
                      relation: "owner"
                    }
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "write_custom_field"
                    },
                    tupleset: {
                      relation: "project"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          }
        },
        metadata: {
          relations: {
            delete_custom_field: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            owner: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "org"
                }
              ]
            },
            project: {
              directly_related_user_types: [
                {
                  type: "project"
                }
              ]
            },
            write_custom_field: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            }
          }
        }
      },
      {
        type: "execution",
        relations: {
          delete_acitvity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_activity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_activity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_activity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      },
                      {
                        computedUserset: {
                          relation: "read_activity"
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          owner: {
            this: {}
          },
          project: {
            this: {}
          },
          read_activity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_activity"
                    },
                    tupleset: {
                      relation: "owner"
                    }
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_activity"
                    },
                    tupleset: {
                      relation: "project"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_activity: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_activity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_activity"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_activity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_activity"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          }
        },
        metadata: {
          relations: {
            delete_acitvity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            owner: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "org"
                }
              ]
            },
            project: {
              directly_related_user_types: [
                {
                  type: "project"
                }
              ]
            },
            read_activity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_activity: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            }
          }
        }
      },
      {
        type: "defect",
        relations: {
          delete_defect: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_defect"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_defect"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_defect"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_defect"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          owner: {
            this: {}
          },
          project: {
            this: {}
          },
          read_defect: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_defect"
                    },
                    tupleset: {
                      relation: "owner"
                    }
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_defect"
                    },
                    tupleset: {
                      relation: "project"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_defect: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_defect"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_defect"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_defect"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_defect"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          }
        },
        metadata: {
          relations: {
            delete_defect: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            owner: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "org"
                }
              ]
            },
            project: {
              directly_related_user_types: [
                {
                  type: "project"
                }
              ]
            },
            read_defect: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_defect: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            }
          }
        }
      },
      {
        type: "integration",
        relations: {
          delete_integration: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_integration"
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_integration"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_integration"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "delete_integration"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_integration"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          owner: {
            this: {}
          },
          project: {
            this: {}
          },
          read_integration: {
            union: {
              child: [
                {
                  this: {}
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_integration"
                    },
                    tupleset: {
                      relation: "owner"
                    }
                  }
                },
                {
                  tupleToUserset: {
                    computedUserset: {
                      relation: "read_integration"
                    },
                    tupleset: {
                      relation: "project"
                    }
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          },
          write_integration: {
            union: {
              child: [
                {
                  intersection: {
                    child: [
                      {
                        this: {}
                      },
                      {
                        computedUserset: {
                          relation: "read_integration"
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_integration"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_integration"
                          },
                          tupleset: {
                            relation: "owner"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  intersection: {
                    child: [
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "write_integration"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      },
                      {
                        tupleToUserset: {
                          computedUserset: {
                            relation: "read_integration"
                          },
                          tupleset: {
                            relation: "project"
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  computedUserset: {
                    relation: "owner"
                  }
                }
              ]
            }
          }
        },
        metadata: {
          relations: {
            delete_integration: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            owner: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "org"
                }
              ]
            },
            project: {
              directly_related_user_types: [
                {
                  type: "project"
                }
              ]
            },
            read_integration: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            },
            write_integration: {
              directly_related_user_types: [
                {
                  type: "user"
                },
                {
                  type: "role",
                  relation: "assignee"
                }
              ]
            }
          }
        }
      }
    ],
    conditions: {
      override_role: {
        name: "override_role",
        expression: "current_project in allowed",
        parameters: {
          allowed: {
            type_name: "TYPE_NAME_LIST",
            generic_types: [
              {
                type_name: "TYPE_NAME_STRING"
              }
            ]
          },
          current_project: {
            type_name: "TYPE_NAME_STRING"
          }
        }
      },
      default_role: {
        name: "default_role",
        expression: "!(current_project in excluded)",
        parameters: {
          excluded: {
            type_name: "TYPE_NAME_LIST",
            generic_types: [
              {
                type_name: "TYPE_NAME_STRING"
              }
            ]
          },
          current_project: {
            type_name: "TYPE_NAME_STRING"
          }
        }
      }
    }
  }