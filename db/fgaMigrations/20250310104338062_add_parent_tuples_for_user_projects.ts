import { FgaService } from '@ss-libs/ss-component-auth';
import { Knex } from 'knex';
import { Tenant } from '@app/models/tenant';

export async function tenantMigration(
  tenant: Tenant,
  fga: FgaService,
  tenantDb?: Knex,
) {
  const writesToAdd = new Set();
  const writesToDelete = new Set();

  const result = { writesToAdd, writesToDelete };

  if (tenant.tenantType === 'org') return result;

  const projects = await tenantDb('projects').select('*');

  if (projects.length === 0) return result;

  const subject = `${tenant.tenantType}:${tenant.tenantUid}`;

  for (const { uid } of projects) {
    const object = `project:${uid}`;
    // check if parent tuple exists
    const exists = await fga.query(subject, object, 'parent');
    if (exists.length === 0) {
      writesToAdd.add(`${object}|${subject}|parent`);
    }
  }

  return {
    writesToAdd,
    writesToDelete,
  };
}

export const authModel = {
  schema_version: '1.1',
  type_definitions: [
    {
      type: 'accessToken',
      relations: {},
      metadata: null,
    },
    {
      type: 'user',
      relations: {
        delete_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_dashboard: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_integration: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_key: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_member: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_project: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_role: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        full_billing: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        limited_billing: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'full_billing',
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        member: {
          this: {},
        },
        no_billing: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'limited_billing',
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        read_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_dashboard: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_integration: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_member: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_role: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_dashboard: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_integration: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_key: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_member: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_project: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_role: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_setting: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_activity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_defect: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_integration: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_key: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_member: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_project: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_report: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_role: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_step: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_tag: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_template: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          full_billing: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          limited_billing: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          member: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          no_billing: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_activity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_defect: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_integration: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_member: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_report: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_role: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_activity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_defect: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_integration: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_key: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_member: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_project: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_report: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_role: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_setting: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_step: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_tag: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_template: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
        },
        module: '',
        source_info: null,
      },
    },
    {
      type: 'org',
      relations: {
        delete_activity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_activity',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_dashboard: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_dashboard',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_entity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_entity',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_integration: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_integration',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_key: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_member: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_member',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_project: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_report: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_report',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_role: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_role',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        full_billing: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        limited_billing: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'full_billing',
                },
              },
            ],
          },
        },
        member: {
          this: {},
        },
        no_billing: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'limited_billing',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        read_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_dashboard: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_integration: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_member: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_role: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_activity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_activity',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_dashboard: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_dashboard',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_defect: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_defect',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_entity',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_integration: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_integration',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_key: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_member: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_member',
                      },
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_role',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_project: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_report: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_report',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_role: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_role',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_setting: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_activity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_defect: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_integration: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_key: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_member: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_project: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_report: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_role: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_step: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_tag: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_template: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          full_billing: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          limited_billing: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          member: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          no_billing: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_activity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_defect: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_integration: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_member: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_report: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_role: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_activity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_defect: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_integration: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_key: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_member: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_project: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_report: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_role: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_setting: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_step: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_tag: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_template: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
        },
        module: '',
        source_info: null,
      },
    },
    {
      type: 'project',
      relations: {
        delete_activity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_activity',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_activity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_activity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        delete_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'delete_custom_field',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        delete_dashboard: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_dashboard',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_dashboard',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_dashboard',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        delete_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_defect',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_defect',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        delete_entity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_entity',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        delete_integration: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_integration',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_integration',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_integration',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        delete_key: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'delete_key',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        delete_member: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_member',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_member',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_member',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        delete_report: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_report',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_report',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_report',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        delete_role: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_role',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_role',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_role',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        delete_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'delete_step',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        delete_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'delete_tag',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        delete_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'delete_template',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        member: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'member',
                  },
                },
              },
            ],
          },
        },
        owner: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        parent: {
          this: {},
        },
        read_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_activity',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        read_dashboard: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_dashboard',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        read_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_defect',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_entity',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        read_integration: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_integration',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        read_member: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_member',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        read_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_report',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        read_role: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_role',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        write_activity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_activity',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_activity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_activity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        write_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'write_custom_field',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        write_dashboard: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_dashboard',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_dashboard',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_dashboard',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        write_defect: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_defect',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_defect',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_defect',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_entity',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        write_integration: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_integration',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_integration',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_integration',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        write_key: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'write_key',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        write_member: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_member',
                      },
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_role',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_member',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_role',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        write_report: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_report',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_report',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_report',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        write_role: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_role',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_role',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'parent',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_role',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        write_setting: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'write_setting',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        write_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'write_step',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        write_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'write_tag',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        write_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'write_template',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'parent',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_activity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_defect: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_integration: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_key: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_member: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_report: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_role: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_step: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_tag: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_template: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          member: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'org',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          parent: {
            directly_related_user_types: [
              {
                type: 'org',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'user',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_activity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_defect: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_integration: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_member: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_report: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_role: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_activity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_defect: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_integration: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_key: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_member: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_report: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_role: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_setting: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_step: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_tag: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_template: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'accessToken',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
        },
        module: '',
        source_info: null,
      },
    },
    {
      type: 'role',
      relations: {
        assignee: {
          this: {},
        },
        delete_role: {
          this: {},
        },
        owner: {
          this: {},
        },
      },
      metadata: {
        relations: {
          assignee: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          delete_role: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'org',
                condition: '',
              },
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'project',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
        },
        module: '',
        source_info: null,
      },
    },
    {
      type: 'plan',
      relations: {
        delete_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'owner',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_entity',
                  },
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'project',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_entity',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'org',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
        },
        module: '',
        source_info: null,
      },
    },
    {
      type: 'milestone',
      relations: {
        delete_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'owner',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_entity',
                  },
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'project',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_entity',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'org',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
        },
        module: '',
        source_info: null,
      },
    },
    {
      type: 'run',
      relations: {
        delete_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'owner',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_entity',
                  },
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'project',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_entity',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'org',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
        },
        module: '',
        source_info: null,
      },
    },
    {
      type: 'step',
      relations: {
        delete_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'owner',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'delete_step',
                  },
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'project',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'delete_step',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        write_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'owner',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'write_step',
                  },
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'project',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'write_step',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_step: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'org',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_step: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
        },
        module: '',
        source_info: null,
      },
    },
    {
      type: 'template',
      relations: {
        delete_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'owner',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'delete_template',
                  },
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'project',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'delete_template',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        write_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'owner',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'write_template',
                  },
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'project',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'write_template',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_template: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'org',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_template: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
        },
        module: '',
        source_info: null,
      },
    },
    {
      type: 'report',
      relations: {
        delete_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_report',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_report',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_report',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_report',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'owner',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_report',
                  },
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'project',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_report',
                  },
                },
              },
            ],
          },
        },
        write_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_report',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_report',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_report',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_report',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_report: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'org',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_report: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_report: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
        },
        module: '',
        source_info: null,
      },
    },
    {
      type: 'tag',
      relations: {
        delete_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'owner',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'delete_tag',
                  },
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'project',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'delete_tag',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        write_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'owner',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'write_tag',
                  },
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'project',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'write_tag',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_tag: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'org',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_tag: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
        },
        module: '',
        source_info: null,
      },
    },
    {
      type: 'case',
      relations: {
        delete_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'owner',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_entity',
                  },
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'project',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_entity',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'org',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
        },
        module: '',
        source_info: null,
      },
    },
    {
      type: 'folder',
      relations: {
        delete_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'owner',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_entity',
                  },
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'project',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_entity',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_entity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_entity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'org',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
        },
        module: '',
        source_info: null,
      },
    },
    {
      type: 'custom_field',
      relations: {
        delete_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'owner',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'delete_custom_field',
                  },
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'project',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'delete_custom_field',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        write_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'owner',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'write_custom_field',
                  },
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'project',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'write_custom_field',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'org',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
        },
        module: '',
        source_info: null,
      },
    },
    {
      type: 'execution',
      relations: {
        delete_acitvity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_activity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_activity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_activity',
                        },
                      },
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_activity',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'owner',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_activity',
                  },
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'project',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_activity',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_activity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_activity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_activity',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_activity',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_acitvity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'org',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_activity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_activity: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
        },
        module: '',
        source_info: null,
      },
    },
    {
      type: 'defect',
      relations: {
        delete_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_defect',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_defect',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_defect',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_defect',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'owner',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_defect',
                  },
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'project',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_defect',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_defect',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_defect',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_defect',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_defect',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_defect: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'org',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_defect: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_defect: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
        },
        module: '',
        source_info: null,
      },
    },
    {
      type: 'integration',
      relations: {
        delete_integration: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_integration',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_integration',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_integration',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'delete_integration',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_integration',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_integration: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'owner',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_integration',
                  },
                },
              },
              {
                tupleToUserset: {
                  tupleset: {
                    object: '',
                    relation: 'project',
                  },
                  computedUserset: {
                    object: '',
                    relation: 'read_integration',
                  },
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_integration: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        object: '',
                        relation: 'read_integration',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_integration',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'owner',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_integration',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'write_integration',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        tupleset: {
                          object: '',
                          relation: 'project',
                        },
                        computedUserset: {
                          object: '',
                          relation: 'read_integration',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  object: '',
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_integration: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'org',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          read_integration: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
          write_integration: {
            directly_related_user_types: [
              {
                type: 'user',
                condition: '',
              },
              {
                type: 'role',
                relation: 'assignee',
                condition: '',
              },
            ],
            module: '',
            source_info: null,
          },
        },
        module: '',
        source_info: null,
      },
    },
  ],
  conditions: {},
};
