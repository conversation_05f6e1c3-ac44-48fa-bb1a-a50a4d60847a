import { FgaService } from '@ss-libs/ss-component-auth';
import { Knex } from 'knex';
import { Tenant } from '@app/models/tenant';

export async function tenantMigration(
  tenant: Tenant,
  fga: FgaService,
  tenantDb?: Knex,
) {
  const writesToAdd = new Set();
  const writesToDelete = new Set();

  const mapping = {
    // Case
    read_case: ['read_entity'],
    write_case: ['read_entity', 'write_entity'],
    delete_case: ['read_entity', 'delete_entity'],
    // Run
    read_run: ['read_entity'],
    write_run: ['read_entity', 'write_entity'],
    delete_run: ['read_entity', 'delete_entity'],
    // Plan
    read_plan: ['read_entity'],
    write_plan: ['read_entity', 'write_entity'],
    delete_plan: ['read_entity', 'delete_entity'],
    // Milestone
    read_milestone: ['read_entity'],
    write_milestone: ['read_entity', 'write_entity'],
    delete_milestone: ['read_entity', 'delete_entity'],
    // Folder
    read_folder: ['read_entity'],
    write_folder: ['read_entity', 'write_entity'],
    delete_folder: ['read_entity', 'delete_entity'],
    // execution
    read_execution: ['read_activity'],
    write_execution: ['read_activity', 'write_activity'],
    delete_execution: ['read_activity', 'delete_activity'],
    // Comments
    read_comment: ['read_activity'],
    write_comment: ['read_activity', 'write_activity'],
    delete_comment: ['read_activity', 'delete_activity'],
    // Member
    write_member: ['read_member', 'read_role', 'write_member'],
    delete_member: ['read_member', 'delete_member'],
    // Role
    write_role: ['read_role', 'write_role'],
    delete_role: ['read_role', 'delete_role'],
    // Dashboard
    write_dashboard: ['read_dashboard', 'write_dashboard'],
    delete_dashboard: ['read_dashboard', 'delete_dashboard'],
    // Defect
    write_defect: ['read_defect', 'write_defect'],
    delete_defect: ['read_defect', 'delete_defect'],
    // Report
    write_report: ['read_report', 'write_report'],
    delete_report: ['read_report', 'write_report'],
  };

  const remove = [
    'read_step',
    'read_tag',
    'read_custom_field',
    'read_template',
    'read_setting',
    'read_branch',
    'write_branch',
    'write_branch',
    'read_repo',
    'write_repo',
    'delete_repo',
    'read_suite',
    'write_suite',
    'delete_suite',
    'read_project',
  ];

  for (const key of [...Object.keys(mapping), ...remove]) {
    const tuples = await fga.query(
      ``,
      `${tenant.tenantType}:${tenant.tenantUid}`,
      key,
    );
    tuples.map((tuple) => {
      const objectType = tuple.key.object.split(':')[0];
      const objectId = tuple.key.object.split(':')[1];
      const subjectId = tuple.key.user.split(':')[1];
      const subjectType = tuple.key.user.split(':')[0];

      writesToDelete.add(
        `${objectType}|${objectId}|${subjectType}|${subjectId}|${key}`,
      );
      if (Object.keys(mapping).includes(key)) {
        mapping[key].map(async (item: string) => {
          const exists = await fga.check(
            `${subjectType}:${subjectId}`,
            `${objectType}:${objectId}`,
            item,
          );
          if (!exists)
            writesToAdd.add(
              `${objectType}|${objectId}|${subjectType}|${subjectId}|${item}`,
            );
        });
      }
    });
  }

  return {
    writesToAdd,
    writesToDelete,
  };
}

export const authModel = {
  schema_version: '1.1',
  type_definitions: [
    {
      type: 'user',
      relations: {
        delete_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_dashboard: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_integration: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_key: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_member: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_project: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_role: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        full_billing: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        limited_billing: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'full_billing',
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        member: {
          this: {},
        },
        no_billing: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'limited_billing',
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        read_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_dashboard: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_integration: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_member: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_role: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_dashboard: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_integration: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_key: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_member: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_project: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_role: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_setting: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_key: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_project: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_role: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_step: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_tag: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_template: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          full_billing: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          limited_billing: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
            ],
          },
          no_billing: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_role: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_key: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_project: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_role: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_setting: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_step: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_tag: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_template: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'org',
      relations: {
        delete_activity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_activity',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_dashboard: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_dashboard',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_entity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_entity',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_integration: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_integration',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_key: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_member: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_member',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_project: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_report: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_report',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_role: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_role',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        full_billing: {
          this: {},
        },
        limited_billing: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'full_billing',
                },
              },
            ],
          },
        },
        member: {
          this: {},
        },
        no_billing: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'limited_billing',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        read_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_dashboard: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_integration: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_member: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_role: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_activity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_activity',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_dashboard: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_dashboard',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_defect: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_defect',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_entity',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_integration: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_integration',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_key: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_member: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_member',
                      },
                    },
                    {
                      computedUserset: {
                        relation: 'read_role',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_project: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_report: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_report',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_role: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_role',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_setting: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_key: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_project: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_role: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_step: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_tag: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_template: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          full_billing: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          limited_billing: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
            ],
          },
          no_billing: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_role: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_key: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_project: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_role: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_setting: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_step: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_tag: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_template: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'project',
      relations: {
        delete_activity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_activity',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_activity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_activity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_custom_field',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_dashboard: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_dashboard',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_dashboard',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_dashboard',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_defect',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_defect',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_entity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_entity',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_integration: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_integration',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_integration',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_integration',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_key: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_key',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_member: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_member',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_member',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_member',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_report: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_report',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_report',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_report',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_role: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_role',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_role',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_role',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_step',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_tag',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_template',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        member: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'member',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        read_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_activity',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_dashboard: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_dashboard',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_defect',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_integration: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_integration',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_member: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_member',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_report',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_role: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_role',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_activity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_activity',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_activity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_activity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_custom_field',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_dashboard: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_dashboard',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_dashboard',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_dashboard',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_defect: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_defect',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_defect',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_defect',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_entity',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_integration: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_integration',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_integration',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_integration',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_key: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_key',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_member: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_member',
                      },
                    },
                    {
                      computedUserset: {
                        relation: 'read_role',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_member',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_role',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_report: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_report',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_report',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_report',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_role: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_role',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_role',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_role',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_setting: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_setting',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_step',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_tag',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_template',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_key: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_role: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_step: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_tag: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_template: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          read_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_role: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_key: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_role: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_setting: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_step: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_tag: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_template: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'role',
      relations: {
        assignee: {
          this: {},
        },
        delete_role: {
          this: {},
        },
        owner: {
          this: {},
        },
      },
      metadata: {
        relations: {
          assignee: {
            directly_related_user_types: [
              {
                type: 'user',
              },
            ],
          },
          delete_role: {
            directly_related_user_types: [
              {
                type: 'user',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'org',
              },
              {
                type: 'user',
              },
              {
                type: 'project',
              },
            ],
          },
        },
      },
    },
    {
      type: 'plan',
      relations: {
        delete_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'milestone',
      relations: {
        delete_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'run',
      relations: {
        delete_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'step',
      relations: {
        delete_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_step',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_step',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        write_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_step',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_step',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_step: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          write_step: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'template',
      relations: {
        delete_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_template',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_template',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        write_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_template',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_template',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_template: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          write_template: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'report',
      relations: {
        delete_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_report',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_report',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_report',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_report',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_report',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_report',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
            ],
          },
        },
        write_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_report',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_report',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_report',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_report',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          read_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'tag',
      relations: {
        delete_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_tag',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_tag',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        write_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_tag',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_tag',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_tag: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          write_tag: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'case',
      relations: {
        delete_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'folder',
      relations: {
        delete_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'custom_field',
      relations: {
        delete_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_custom_field',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_custom_field',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        write_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_custom_field',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_custom_field',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          write_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'execution',
      relations: {
        delete_acitvity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_activity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_activity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_activity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      computedUserset: {
                        relation: 'read_activity',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_activity',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_activity',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_activity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_activity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_activity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_activity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_acitvity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          read_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'defect',
      relations: {
        delete_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_defect',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_defect',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_defect',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_defect',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_defect',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_defect',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_defect',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_defect',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_defect',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_defect',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          read_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'integration',
      relations: {
        delete_integration: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_integration',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_integration',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_integration',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_integration',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_integration',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_integration: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_integration',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_integration',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_integration: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_integration',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_integration',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_integration',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_integration',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_integration',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          read_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
  ],
};
