import { FgaService } from '@ss-libs/ss-component-auth';
import { Knex } from 'knex';
import { Tenant } from '@app/models/tenant';

export async function tenantMigration(
  tenant: Tenant,
  fga: FgaService,
  tenantDb?: Knex,
) {
  const writesToAdd = new Set();
  const writesToDelete = new Set();

  return {
    writesToAdd,
    writesToDelete,
  };
}

export const authModel = {
  schema_version: '1.1',
  type_definitions: [
    {
      type: 'accessToken',
      relations: {},
      metadata: null,
    },
    {
      type: 'user',
      relations: {
        delete_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_dashboard: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_integration: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_key: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_member: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_project: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_role: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        full_billing: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        limited_billing: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'full_billing',
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        member: {
          this: {},
        },
        no_billing: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'limited_billing',
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        read_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_dashboard: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_integration: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_member: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_role: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_dashboard: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_integration: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_key: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_member: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_project: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_role: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_setting: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_key: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_project: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_role: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_step: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_tag: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_template: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          full_billing: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          limited_billing: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
            ],
          },
          no_billing: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_role: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_key: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_project: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_role: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_setting: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_step: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_tag: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_template: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'org',
      relations: {
        delete_activity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_activity',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_dashboard: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_dashboard',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_entity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_entity',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_integration: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_integration',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_key: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_member: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_member',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_project: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_report: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_report',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_role: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_role',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        delete_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        full_billing: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        limited_billing: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'full_billing',
                },
              },
            ],
          },
        },
        member: {
          this: {},
        },
        no_billing: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'limited_billing',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        read_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_dashboard: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_integration: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_member: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        read_role: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_activity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_activity',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_dashboard: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_dashboard',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_defect: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_defect',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_entity',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_integration: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_integration',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_key: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_member: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_member',
                      },
                    },
                    {
                      computedUserset: {
                        relation: 'read_role',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_project: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_report: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_report',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_role: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_role',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_setting: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_key: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_project: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_role: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_step: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_tag: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_template: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          full_billing: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          limited_billing: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
            ],
          },
          no_billing: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_role: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_key: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_project: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_role: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_setting: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_step: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_tag: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_template: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'project',
      relations: {
        delete_activity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_activity',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_activity',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_activity',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        delete_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_custom_field',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        delete_dashboard: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_dashboard',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_dashboard',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_dashboard',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        delete_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_defect',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_defect',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        delete_entity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_entity',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        delete_integration: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_integration',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_integration',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_integration',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        delete_key: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_key',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        delete_member: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_member',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_member',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_member',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        delete_report: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_report',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_report',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_report',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        delete_role: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_role',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_role',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_role',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        delete_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_step',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        delete_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_tag',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        delete_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_template',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        member: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'member',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        owner: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        parent: {
          this: {},
        },
        read_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_activity',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        read_dashboard: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_dashboard',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        read_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_defect',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        read_integration: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_integration',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        read_member: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_member',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        read_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_report',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        read_role: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_role',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        write_activity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_activity',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_activity',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_activity',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        write_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_custom_field',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        write_dashboard: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_dashboard',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_dashboard',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_dashboard',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        write_defect: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_defect',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_defect',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_defect',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_entity',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        write_integration: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_integration',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_integration',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_integration',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        write_key: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_key',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        write_member: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_member',
                      },
                    },
                    {
                      computedUserset: {
                        relation: 'read_role',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_member',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_role',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        write_report: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_report',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_report',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_report',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        write_role: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_role',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_role',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_role',
                        },
                        tupleset: {
                          relation: 'parent',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        write_setting: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_setting',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        write_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_step',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        write_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_tag',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
        write_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_template',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'owner',
                  },
                  tupleset: {
                    relation: 'parent',
                  },
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_key: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_role: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_step: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_tag: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          delete_template: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'org',
              },
            ],
          },
          parent: {
            directly_related_user_types: [
              {
                type: 'org',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'user',
              },
            ],
          },
          read_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          read_role: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_dashboard: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_key: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_member: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_role: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_setting: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_step: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_tag: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_template: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'accessToken',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'role',
      relations: {
        assignee: {
          this: {},
        },
        delete_role: {
          this: {},
        },
        owner: {
          this: {},
        },
      },
      metadata: {
        relations: {
          assignee: {
            directly_related_user_types: [
              {
                type: 'user',
              },
            ],
          },
          delete_role: {
            directly_related_user_types: [
              {
                type: 'user',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'org',
              },
              {
                type: 'user',
              },
              {
                type: 'project',
              },
            ],
          },
        },
      },
    },
    {
      type: 'plan',
      relations: {
        delete_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'milestone',
      relations: {
        delete_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'run',
      relations: {
        delete_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'step',
      relations: {
        delete_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_step',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_step',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        write_step: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_step',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_step',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_step: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          write_step: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'template',
      relations: {
        delete_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_template',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_template',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        write_template: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_template',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_template',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_template: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          write_template: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'report',
      relations: {
        delete_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_report',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_report',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_report',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_report',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_report',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_report',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
            ],
          },
        },
        write_report: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_report',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_report',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_report',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_report',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          read_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_report: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'tag',
      relations: {
        delete_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_tag',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_tag',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        write_tag: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_tag',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_tag',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_tag: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          write_tag: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'case',
      relations: {
        delete_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'folder',
      relations: {
        delete_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_entity',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_entity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_entity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          read_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_entity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'custom_field',
      relations: {
        delete_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_custom_field',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'delete_custom_field',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        write_custom_field: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_custom_field',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'write_custom_field',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          write_custom_field: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'execution',
      relations: {
        delete_acitvity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_activity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_activity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_activity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      computedUserset: {
                        relation: 'read_activity',
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_activity',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_activity',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_activity: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_activity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_activity',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_activity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_activity',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_acitvity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          read_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_activity: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'defect',
      relations: {
        delete_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_defect',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_defect',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_defect',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_defect',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_defect',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_defect',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_defect: {
          union: {
            child: [
              {
                this: {},
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_defect',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_defect',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_defect',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_defect',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          read_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_defect: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
    {
      type: 'integration',
      relations: {
        delete_integration: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_integration',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_integration',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_integration',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'delete_integration',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_integration',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        owner: {
          this: {},
        },
        project: {
          this: {},
        },
        read_integration: {
          union: {
            child: [
              {
                this: {},
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_integration',
                  },
                  tupleset: {
                    relation: 'owner',
                  },
                },
              },
              {
                tupleToUserset: {
                  computedUserset: {
                    relation: 'read_integration',
                  },
                  tupleset: {
                    relation: 'project',
                  },
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
        write_integration: {
          union: {
            child: [
              {
                intersection: {
                  child: [
                    {
                      this: {},
                    },
                    {
                      computedUserset: {
                        relation: 'read_integration',
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_integration',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_integration',
                        },
                        tupleset: {
                          relation: 'owner',
                        },
                      },
                    },
                  ],
                },
              },
              {
                intersection: {
                  child: [
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'write_integration',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                    {
                      tupleToUserset: {
                        computedUserset: {
                          relation: 'read_integration',
                        },
                        tupleset: {
                          relation: 'project',
                        },
                      },
                    },
                  ],
                },
              },
              {
                computedUserset: {
                  relation: 'owner',
                },
              },
            ],
          },
        },
      },
      metadata: {
        relations: {
          delete_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          owner: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'org',
              },
            ],
          },
          project: {
            directly_related_user_types: [
              {
                type: 'project',
              },
            ],
          },
          read_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
          write_integration: {
            directly_related_user_types: [
              {
                type: 'user',
              },
              {
                type: 'role',
                relation: 'assignee',
              },
            ],
          },
        },
      },
    },
  ],
};
