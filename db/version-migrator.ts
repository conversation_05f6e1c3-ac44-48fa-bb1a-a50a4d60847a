import 'module-alias/register';

import env from '@app/config/env';
import knex, { Knex } from 'knex';
import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
import { Tenant } from '@app/models/tenant';
import { AppVersion, ComponentTypes } from '@app/models/appVersion';

interface MigrationOptions {
  components?: ComponentTypes[];
  appNode?: number;
  tenantIds?: string[];
  appVersion?: number;
  invalidateSessions?: boolean;
}

class VersionMigrator {
  private sharedDB: Knex;

  constructor() {
    this.sharedDB = this.getDBConnection(
      env.DB_HOST,
      env.DB_USERNAME,
      env.DB_PASSWORD,
      env.DB_DATABASE
    );
  }

  private getDBConnection(
    host: string,
    user: string,
    password: string,
    db: string,
    port = 5432
  ): Knex {
    return knex({
      client: 'pg',
      connection: {
        host,
        port,
        user,
        password,
        database: db,
      },
    });
  }

  private async getAppVersion(version: number): Promise<AppVersion> {
    const appVersion = await AppVersion.query(this.sharedDB)
      .where({ version })
      .first();

    if (!appVersion) {
      throw new Error(`App version not found for version: ${version}`);
    }
    return appVersion;
  }

  private async getTenants(options: MigrationOptions): Promise<Tenant[]> {
    return Tenant.query(this.sharedDB)
      .withGraphFetched('dbServer')
      .where((q) => {
        if (options.tenantIds?.length > 0) {
          q.whereIn('tenantUid', options.tenantIds);
        }
        if (options.appNode) {
          q.where('dbServer.appNodeUid', options.appNode);
        }
      });
  }

  private async updateTenantVersion(
    trx: Knex.Transaction,
    tenant: Tenant,
    component: string,
    appVersion?: AppVersion
  ): Promise<void> {
    if (appVersion && tenant.dbServer.appNodeUid !== appVersion.appNodeUid) {
      throw new Error(
        `Tenant ${tenant.tenantUid} dbServer appNodeUid (${tenant.dbServer.appNodeUid}) does not match appVersion appNodeUid (${appVersion.appNodeUid})`
      );
    }
    const versionToApply = appVersion || await AppVersion.query(trx)
      .where({
        component,
        appNodeUid: tenant.dbServer.appNodeUid,
        isDefault: true,
      })
      .first()
      .select('uid');
    
    if (!versionToApply) {
      throw new Error(
        `App version not found for component: ${component} and appNode: ${tenant.dbServer.appNodeUid}`
      );
    }

    await Tenant.query(trx)
      .where('tenantUid', tenant.tenantUid)
      .patch({
        [(component === 'frontend' ? 'frontendVersionUid' : 'backendVersionUid')]: versionToApply.uid,
      });
  }

  private async invalidateTenantSessions(tenantUid: string): Promise<void> {
    try {
      const now = new Date().toISOString();
      
      const memberships = await this.sharedDB('memberships')
        .where('accountUid', tenantUid)
        .select('userUid');
      
      const userUids = memberships.map(membership => membership.userUid);

      if (userUids.length === 0) {
        console.log(`No users found for tenant: ${tenantUid}`);
        return;
      }
      
      console.log(`Found ${userUids.length} users for tenant: ${tenantUid}`);
      
      let expiredCount = 0;
      
      for (const userUid of userUids) {
        const result = await this.sharedDB('sessions')
          .where('expired', '>', now)
          .whereRaw(`sess->'user'->>'uid' = ?`, [userUid])
          .update({ expired: now });
          
        expiredCount += result;
      }
      
      console.log(`Successfully expired ${expiredCount} sessions for ${userUids.length} users of tenant: ${tenantUid}`);
    } catch (error) {
      console.error('Error invalidating tenant sessions:', error);
      throw error;
    }
  }

  public async migrate(options: MigrationOptions): Promise<void> {
    const components = options.components || ['backend', 'frontend'];
    console.log(`Running migration for components: ${components.join(', ')}`);

    let appVersion: AppVersion | undefined;
    if (options.appVersion) {
      appVersion = await this.getAppVersion(options.appVersion);
    }

    const tenants = await this.getTenants(options);
    if (tenants.length === 0) {
      console.log('No tenants found.');
      return;
    }

    console.log(`Found ${tenants.length} tenants to migrate.`);

    try {
      await this.sharedDB.transaction(async (trx) => {
        for (const tenant of tenants) {
          console.log(`Migrating tenant: ${tenant.tenantUid}`);
          if (options.invalidateSessions) {
            await this.invalidateTenantSessions(tenant.tenantUid);
          }
          for (const component of components) {
            await this.updateTenantVersion(trx, tenant, component, appVersion);
          }
        }
      });
      console.log('All tenants migrated successfully.');
    } catch (error) {
      console.error('Migration failed:', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    } finally {
      await this.sharedDB.destroy();
      console.log('Database connection closed.');
    }
  }
}

// Parse command-line arguments
const argv = yargs(hideBin(process.argv))
  .option('components', {
    alias: 'c',
    type: 'array',
    description: 'Components to migrate (frontend and/or backend)',
    choices: ['frontend', 'backend'],
  })
  .option('appNode', {
    alias: 'a',
    type: 'number',
    description: 'App node UID to migrate',
  })
  .option('tenantIds', {
    alias: 't',
    type: 'array',
    description: 'List of tenant UIDs to migrate',
  })
  .option('appVersion', {
    alias: 'v',
    type: 'number',
    description: 'App version UID to migrate',
  })
  .option('invalidateSessions', {
    alias: 'its',
    type: 'boolean',
    description: 'Invalidate sessions only for the tenants users',
    default: false,
  })
  .help()
  .alias('help', 'h')
  .strict()
  .parseSync();

const migrator = new VersionMigrator();
migrator.migrate(argv as MigrationOptions)
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('Migration failed:', error instanceof Error ? error.message : 'Unknown error');
    process.exit(1);
  });
