// IMPORTANT NOTE # Install system packages for PostgreSQL client so that pg_dump and pg_restore commands are available
// RUN apt - get update && apt - get install - y postgresql - client && apt - get clean && rm - rf /var/lib/apt / lists/*

import 'module-alias/register';

import env from '@app/config/env';
import { Tenant } from '@app/models/tenant';
import { exec } from 'child_process';
import * as fs from 'fs/promises';
import knex from 'knex';
import * as path from 'path';
import { promisify } from 'util';

import { DBServer } from '@app/models/dbServer';

const execAsync = promisify(exec);


const command = process.argv.slice(2);
const uids: any[] = process.argv.slice(3);
const commandsList = {
  copy: () => runDbCopy(uids),
};

interface DatabaseConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  database: string;
}

interface CopyOptions {
  cleanDestination: boolean;
  compress: boolean;
  verbose: boolean;
  parallelJobs: number;
  excludeSchemas: string[];
  includeTables: string[];
  tempDir: string;
}

// DB Connection
function getDBConnection(
  host: string,
  user: string,
  password: string,
  db: string,
  port = 5432,
) {
  return knex({
    client: 'pg',
    connection: {
      host: host,
      port: port,
      user: user,
      password: password,
      database: db,
    },
  });
}

// Connect to SharedDB
const sharedDB = getDBConnection(
  env.DB_HOST,
  env.DB_USERNAME,
  env.DB_PASSWORD,
  env.DB_DATABASE,
);



class PostgresDatabaseCopier {
  private source: DatabaseConfig;
  private destination: DatabaseConfig;
  private options: CopyOptions;

  constructor(
    source: DatabaseConfig,
    destination: DatabaseConfig,
    options: Partial<CopyOptions> = {}
  ) {
    this.source = source;
    this.destination = destination;
    this.options = {
      cleanDestination: true,
      compress: true,
      verbose: false,
      parallelJobs: 4,
      excludeSchemas: [],
      includeTables: [],
      tempDir: '/tmp',
      ...options
    };
  }

  public async copyDatabase(): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const dumpFile = path.join(this.options.tempDir, `pg_dump_${timestamp}.dump`);

    try {
      // 1. Create dump from source
      await this.createDump(dumpFile);

      // 2. Restore to destination
      await this.restoreDump(dumpFile);

      console.log('Database copied successfully!');
    } catch (error) {
      console.error('Database copy failed:', error);
      throw error;
    } finally {
      // 3. Clean up
      await this.cleanupDumpFile(dumpFile);
    }
  }

  private async createDump(dumpFile: string): Promise<void> {
    console.log('Creating database dump...');

    const dumpCmd = [
      'pg_dump',
      `--host=${this.source.host}`,
      `--port=${this.source.port}`,
      `--username=${this.source.user}`,
      `--dbname=${this.source.database}`,
      this.options.compress ? '--format=custom' : '--format=plain',
      `--file=${dumpFile}`,
      this.options.verbose ? '--verbose' : '',
      ...this.options.excludeSchemas.flatMap(s => ['--exclude-schema', s]),
      ...this.options.includeTables.flatMap(t => ['--table', t]),
      '--no-password'
    ].filter(Boolean).join(' ');

    try {
      const { stdout, stderr } = await execAsync(dumpCmd, {
        env: { ...process.env, PGPASSWORD: this.source.password }
      });
      if (this.options.verbose) {
        console.log(stdout);
        console.error(stderr);
      }
    } catch (error) {
      console.error('Dump failed:', error);
      throw new Error(`Failed to create dump: ${error.message}`);
    }
  }

  private async restoreDump(dumpFile: string): Promise<void> {
    if (this.options.cleanDestination) {
      await this.cleanDestinationDatabase();
    }

    console.log('Restoring database...');

    const restoreCmd = [
      'pg_restore',
      `--host=${this.destination.host}`,
      `--port=${this.destination.port}`,
      `--username=${this.destination.user}`,
      `--dbname=${this.destination.database}`,

      this.options.verbose ? '--verbose' : '',
      '--no-owner',
      '--no-privileges',
      '--exit-on-error',
      dumpFile,
      '--no-password'
    ].filter(Boolean).join(' ');

    try {
      const { stdout, stderr } = await execAsync(restoreCmd, {
        env: { ...process.env, PGPASSWORD: this.destination.password }
      });
      if (this.options.verbose) {
        console.log(stdout);
        console.error(stderr);
      }
    } catch (error) {
      console.error('Restore failed:', error);
      throw new Error(`Failed to restore dump: ${error.message}`);
    }
  }

  private async cleanDestinationDatabase(): Promise<void> {
    console.log('Cleaning destination database...');
    const cleanCommand = `DROP DATABASE IF EXISTS "${this.destination.database}";`;
    const createCommand = `CREATE DATABASE "${this.destination.database}";`;
    const cleanCmd = [
      'psql',
      `--host=${this.destination.host}`,
      `--port=${this.destination.port}`,
      `--username=${this.destination.user}`,
      '--command',
      `'${cleanCommand}'`,
      'postgres',
      '--no-password'
    ].filter(Boolean).join(' ');


    const createCmd = [
      'psql',
      `--host=${this.destination.host}`,
      `--port=${this.destination.port}`,
      `--username=${this.destination.user}`,
      '--command',
      `'${createCommand}'`,
      'postgres',
      '--no-password'
    ].filter(Boolean).join(' ');

    try {
      await execAsync(cleanCmd, {
        env: { ...process.env, PGPASSWORD: this.destination.password }
      });

      await execAsync(createCmd, {
        env: { ...process.env, PGPASSWORD: this.destination.password }
      });
    } catch (error) {
      console.error('Clean failed:', error);
      throw new Error(`Failed to clean destination: ${error.message}`);
    }
  }

  private async cleanupDumpFile(dumpFile: string): Promise<void> {
    try {
      await fs.unlink(dumpFile);
      console.log('Temporary dump file removed');
    } catch (error) {
      console.warn('Could not remove dump file:', error);
    }
  }
}

const runDbCopy = async (uids: any[]) => {
  const tenants = await Tenant.query(sharedDB).where((q) => {
    uids.length > 0 ? q.whereIn('tenantUid', uids) : q.whereNull('dbServerUid');
  }).withGraphFetched('dbServer');

  const dbServer = await DBServer.query(sharedDB).findOne({
    'dbServers.isDefault': true,
    'appNode.isDefault': true,
  })
    .joinRelated('appNode');

  console.log('dbServerCopy', dbServer)

  for (const tenant of tenants) {

    const sourceConfig: DatabaseConfig = {
      host: env.DB_HOST,
      port: env.DB_PORT,
      user: env.DB_USERNAME,
      password: env.DB_PASSWORD,
      database: tenant.tenantUid
    };

    const destConfig: DatabaseConfig = {
      host: dbServer.host,
      port: dbServer.port,
      user: dbServer.username,
      password: dbServer.password,
      database: tenant.tenantUid
    };

    const options: Partial<CopyOptions> = {
      cleanDestination: true,
      compress: true,
      verbose: true,
      parallelJobs: 4,
      excludeSchemas: [],
      includeTables: [],
      tempDir: '/tmp'
    };

    const copier = new PostgresDatabaseCopier(sourceConfig, destConfig, options);
    await copier.copyDatabase();

    console.log('Updating Tenant');
    await Tenant.query(sharedDB)
      .findById(tenant.uid) // Find the tenant by its UID
      .patch({ dbServerUid: dbServer.uid }); // Update the dbServerUid
    console.log('Tenant Updated', tenant.uid, " with ", dbServer.uid);

  }
  process.exit(0)
};


if (commandsList[command[0]]) {
  commandsList[command[0]]();
} else {
  console.log('Unknown command');
}
