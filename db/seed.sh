#!/bin/bash

export PGPASSWORD=password
echo "
INSERT INTO users (uid, firstName, lastName, email, password_hash, preferences, admin, last_sign_in_at, last_sign_in_ip, password_reset_token, passwordResetAttemptsCount, passwordResetAt, password_reset_created_at, created_at, updated_at, deleted_at) VALUES ('a7dc4349-166a-11ed-8f2b-d8252f7cdfec', 'Test', 'User', '<EMAIL>', '\$2b\$10\$FX58IXqFmSEK3ofKvMddFuGXXuCt.M33T9a7kj6XQD7/3nht3GZty', '{}', 0, '2022-08-24 14:08:19', '::1', NULL, NULL, NULL, NULL, '2022-08-24 14:08:19', '2022-08-24 14:08:19', NULL);

INSERT INTO orgs (uid, name, created_by, created_at, updated_at, deleted_at) 
VALUES ('bf36d1a0-166a-11ed-8f2b-d8252f7cdfec', 'test-org', 'a7dc4349-166a-11ed-8f2b-d8252f7cdfec', '2022-08-24 14:08:58', '2022-08-24 14:08:58', NULL);

INSERT INTO handles(name, ownerType, ownerUid)
VALUES('dacoaster', 'user', 'a7dc4349-166a-11ed-8f2b-d8252f7cdfec');
INSERT INTO handles(name, ownerType, ownerUid)
VALUES('testOrg', 'org', 'bf36d1a0-166a-11ed-8f2b-d8252f7cdfec');

INSERT INTO accessTokens ( uid, accessToken_hash, name, ownerType, ownerUid, expiresAt, created_at, updated_at, deleted_at ) 
VALUES ( 'b89c4349-166a-11ed-8f2b-d8252f7cdfzz', '\$2b\$10\$v/Sj3.yXWLOgMmTDUdrZQe2OsSvM.0xB.5yq3UzB65B8PiM0dh85C', 'testtoken', 'user', 'a7dc4349-166a-11ed-8f2b-d8252f7cdfec', '2022-11-22 08:00:00', '2022-08-24 14:09:10', '2022-08-24 14:09:10', NULL );

INSERT INTO repos ( uid, externalId, source, name ) 
VALUES ( 'b89c4349-166a-11ed-8f2b-d8252f7cdfec', 'org/test-repo', 'github', 'org/test-repo' );

INSERT INTO repo_branches ( uid, externalId, name, repo_uid ) 
VALUES ( '999da349-166a-11ed-8f2b-d8252f7cdfec', 'main', 'main', 'b89c4349-166a-11ed-8f2b-d8252f7cdfec' );
INSERT INTO repo_branches ( uid, externalId, name, repo_uid ) 
VALUES ( '2c2686d2-9f76-4f89-9e18-08532594ebdd', 'feat/feature1', 'feat/feature1', 'b89c4349-166a-11ed-8f2b-d8252f7cdfec' );

INSERT INTO test_cases ( uid, externalId, source, name, customFields, created_at, updated_at, case_ref, repo_uid, version, active) 
VALUES ( '510d43a0-536e-4f9a-8b6b-862e81425f7d', '12342', 'automation', 'Case #1', '{\"os\": \"Windows 11\", \"pages\": 5, \"clicks\": 30, \"status\": \"complete\", \"end_time\": \"1970-01-01 00:10:00\", \"start_time\": \"1970-01-01 00:00:01\"}', '2022-10-03 10:18:13', '2022-10-03 10:18:13', '4489d1a0-166a-11ed-8f2b-d8252f7cdfec', 'b89c4349-166a-11ed-8f2b-d8252f7cdfec', '1', true);
INSERT INTO test_cases ( uid, externalId, source, name, customFields, created_at, updated_at, case_ref, repo_uid, version, active) 
VALUES ( '0b126c8e-16eb-4ed6-ac8b-3e2c0378ad86', '12342', 'automation', 'Case #0', '{\"os\": \"Windows 10\", \"pages\": 5, \"clicks\": 30, \"status\": \"complete\", \"end_time\": \"1970-01-01 00:10:00\", \"start_time\": \"1970-01-01 00:00:01\"}', '2022-10-03 10:18:13', '2022-10-03 10:18:13', '4489d1a0-166a-11ed-8f2b-d8252f7cdfec', 'b89c4349-166a-11ed-8f2b-d8252f7cdfec', '1', true);

INSERT INTO test_runs ( uid, externalId, source, name, customFields, created_at, updated_at ) 
VALUES ( '8918a9a0-166a-11ed-8f2b-d8252f7cdfec', '100', 'automation', 'Run 1', '{\"os\": \"Windows 11\", \"name\": \"My Test Session\", \"pages\": 5, \"clicks\": 30, \"status\": \"complete\", \"end_time\": \"1970-01-01 00:10:00\", \"start_time\": \"1970-01-01 00:00:01\"}', '2022-10-03 10:18:13', '2022-10-03 10:18:13' );
INSERT INTO test_runs ( uid, externalId, source, name, customFields, created_at, updated_at ) 
VALUES ( 'cab6b2bd-fb9b-447d-a155-e2f8fea7ddd3', '101', 'automation', 'Run 2', '{\"os\": \"Windows 11\", \"name\": \"My Test Session\", \"pages\": 5, \"clicks\": 30, \"status\": \"complete\", \"end_time\": \"1970-01-01 00:10:00\", \"start_time\": \"1970-01-01 00:00:01\"}', '2022-10-03 10:18:13', '2022-10-03 10:18:13' );
INSERT INTO test_runs ( uid, externalId, source, name, customFields, created_at, updated_at ) 
VALUES ( 'fce05aa9-fea2-4346-ac78-44a3915f4d44', '102', 'automation', 'Run 3', '{\"os\": \"Windows 11\", \"name\": \"My Test Session\", \"pages\": 5, \"clicks\": 30, \"status\": \"complete\", \"end_time\": \"1970-01-01 00:10:00\", \"start_time\": \"1970-01-01 00:00:01\"}', '2022-10-03 10:18:13', '2022-10-03 10:18:13' );
INSERT INTO test_runs ( uid, externalId, source, name, customFields, created_at, updated_at ) 
VALUES ( 'e34aa3b6-7596-46fd-9551-3162ac013d64', '103', 'automation', 'Run 4', '{\"os\": \"Windows 11\", \"name\": \"My Test Session\", \"pages\": 5, \"clicks\": 30, \"status\": \"complete\", \"end_time\": \"1970-01-01 00:10:00\", \"start_time\": \"1970-01-01 00:00:01\"}', '2022-10-03 10:18:13', '2022-10-03 10:18:13' );
INSERT INTO test_runs ( uid, externalId, source, name, customFields, created_at, updated_at ) 
VALUES ( '4a558904-a199-47aa-a456-43dc8c0cd699', '104', 'automation', 'Run 5', '{\"os\": \"Windows 11\", \"name\": \"My Test Session\", \"pages\": 5, \"clicks\": 30, \"status\": \"complete\", \"end_time\": \"1970-01-01 00:10:00\", \"start_time\": \"1970-01-01 00:00:01\"}', '2022-10-03 10:18:13', '2022-10-03 10:18:13' );

INSERT INTO test_executions (externalId, source, customFields, created_at, updated_at, test_case_uid, testRunUid, repo_branch_uid, status) 
VALUES ('12342', 'automation', '{\"os\": \"Windows 11\", \"name\": \"My Test Session\", \"pages\": 5, \"clicks\": 30, \"status\": \"complete\", \"end_time\": \"1970-01-01 00:10:00\", \"start_time\": \"1970-01-01 00:00:01\"}', '2022-10-03 10:18:13', '2022-10-03 10:18:13', '510d43a0-536e-4f9a-8b6b-862e81425f7d', '8918a9a0-166a-11ed-8f2b-d8252f7cdfec', '999da349-166a-11ed-8f2b-d8252f7cdfec', 'failed');
INSERT INTO test_executions (externalId, source, customFields, created_at, updated_at, test_case_uid, testRunUid, repo_branch_uid, status) 
VALUES ('12342', 'automation', '{\"os\": \"Windows 11\", \"name\": \"My Test Session\", \"pages\": 5, \"clicks\": 30, \"status\": \"error\", \"end_time\": \"1970-01-01 00:10:00\", \"start_time\": \"1970-01-01 00:00:01\"}', '2022-10-03 10:18:13', '2022-10-03 10:18:13', '510d43a0-536e-4f9a-8b6b-862e81425f7d', 'cab6b2bd-fb9b-447d-a155-e2f8fea7ddd3', '999da349-166a-11ed-8f2b-d8252f7cdfec', 'passed');
INSERT INTO test_executions (externalId, source, customFields, created_at, updated_at, test_case_uid, testRunUid, repo_branch_uid, status) 
VALUES ('12342', 'automation', '{\"os\": \"Windows 11\", \"name\": \"My Test Session\", \"pages\": 5, \"clicks\": 30, \"status\": \"complete\", \"end_time\": \"1970-01-01 00:10:00\", \"start_time\": \"1970-01-01 00:00:01\"}', '2022-10-03 10:18:13', '2022-10-03 10:18:13', '510d43a0-536e-4f9a-8b6b-862e81425f7d', 'fce05aa9-fea2-4346-ac78-44a3915f4d44', '999da349-166a-11ed-8f2b-d8252f7cdfec', 'skipped');
INSERT INTO test_executions (externalId, source, customFields, created_at, updated_at, test_case_uid, testRunUid, repo_branch_uid, status) 
VALUES ('12342', 'automation', '{\"os\": \"Windows 11\", \"name\": \"My Test Session\", \"pages\": 5, \"clicks\": 30, \"status\": \"error\", \"end_time\": \"1970-01-01 00:10:00\", \"start_time\": \"1970-01-01 00:00:01\"}', '2022-10-03 10:18:13', '2022-10-03 10:18:13', '510d43a0-536e-4f9a-8b6b-862e81425f7d', 'e34aa3b6-7596-46fd-9551-3162ac013d64', '999da349-166a-11ed-8f2b-d8252f7cdfec', 'passed');
INSERT INTO test_executions (externalId, source, customFields, created_at, updated_at, test_case_uid, testRunUid, repo_branch_uid, status) 
VALUES ('12342', 'automation', '{\"os\": \"Windows 11\", \"name\": \"My Test Session\", \"pages\": 5, \"clicks\": 30, \"status\": \"complete\", \"end_time\": \"1970-01-01 00:10:00\", \"start_time\": \"1970-01-01 00:00:01\"}', '2022-10-03 10:18:13', '2022-10-03 10:18:13', '510d43a0-536e-4f9a-8b6b-862e81425f7d', '4a558904-a199-47aa-a456-43dc8c0cd699', '999da349-166a-11ed-8f2b-d8252f7cdfec', 'unknown');
INSERT INTO test_executions (externalId, source, customFields, created_at, updated_at, test_case_uid, testRunUid, repo_branch_uid, status) 
VALUES ('12342', 'automation', '{\"os\": \"Windows 11\", \"name\": \"My Test Session\", \"pages\": 5, \"clicks\": 30, \"status\": \"complete\", \"end_time\": \"1970-01-01 00:10:00\", \"start_time\": \"1970-01-01 00:00:01\"}', '2022-10-03 10:18:13', '2022-10-03 10:18:13', '0b126c8e-16eb-4ed6-ac8b-3e2c0378ad86', '8918a9a0-166a-11ed-8f2b-d8252f7cdfec', '999da349-166a-11ed-8f2b-d8252f7cdfec', 'passed');
INSERT INTO test_executions (externalId, source, customFields, created_at, updated_at, test_case_uid, testRunUid, repo_branch_uid, status) 
VALUES ('12342', 'automation', '{\"os\": \"Windows 11\", \"name\": \"My Test Session\", \"pages\": 5, \"clicks\": 30, \"status\": \"complete\", \"end_time\": \"1970-01-01 00:10:00\", \"start_time\": \"1970-01-01 00:00:01\"}', '2022-10-03 10:18:13', '2022-10-03 10:18:13', '0b126c8e-16eb-4ed6-ac8b-3e2c0378ad86', 'cab6b2bd-fb9b-447d-a155-e2f8fea7ddd3', '999da349-166a-11ed-8f2b-d8252f7cdfec', 'passed');
INSERT INTO test_executions (externalId, source, customFields, created_at, updated_at, test_case_uid, testRunUid, repo_branch_uid, status) 
VALUES ('12342', 'automation', '{\"os\": \"Windows 11\", \"name\": \"My Test Session\", \"pages\": 5, \"clicks\": 30, \"status\": \"error\", \"end_time\": \"1970-01-01 00:10:00\", \"start_time\": \"1970-01-01 00:00:01\"}', '2022-10-03 10:18:13', '2022-10-03 10:18:13', '0b126c8e-16eb-4ed6-ac8b-3e2c0378ad86', 'fce05aa9-fea2-4346-ac78-44a3915f4d44', '999da349-166a-11ed-8f2b-d8252f7cdfec', 'failed');
INSERT INTO test_executions (externalId, source, customFields, created_at, updated_at, test_case_uid, testRunUid, repo_branch_uid, status) 
VALUES ('12342', 'automation', '{\"os\": \"Windows 11\", \"name\": \"My Test Session\", \"pages\": 5, \"clicks\": 30, \"status\": \"complete\", \"end_time\": \"1970-01-01 00:10:00\", \"start_time\": \"1970-01-01 00:00:01\"}', '2022-10-03 10:18:13', '2022-10-03 10:18:13', '0b126c8e-16eb-4ed6-ac8b-3e2c0378ad86', 'e34aa3b6-7596-46fd-9551-3162ac013d64', '999da349-166a-11ed-8f2b-d8252f7cdfec', 'skipped');
INSERT INTO test_executions (externalId, source, customFields, created_at, updated_at, test_case_uid, testRunUid, repo_branch_uid, status) 
VALUES ('12342', 'automation', '{\"os\": \"Windows 11\", \"name\": \"My Test Session\", \"pages\": 5, \"clicks\": 30, \"status\": \"error\", \"end_time\": \"1970-01-01 00:10:00\", \"start_time\": \"1970-01-01 00:00:01\"}', '2022-10-03 10:18:13', '2022-10-03 10:18:13', '0b126c8e-16eb-4ed6-ac8b-3e2c0378ad86', '4a558904-a199-47aa-a456-43dc8c0cd699', '999da349-166a-11ed-8f2b-d8252f7cdfec', 'passed');
INSERT INTO test_executions (externalId, source, customFields, created_at, updated_at, test_case_uid, testRunUid, repo_branch_uid, status) 
VALUES ('12342', 'automation', '{\"os\": \"Windows 11\", \"name\": \"My Test Session\", \"pages\": 5, \"clicks\": 30, \"status\": \"complete\", \"end_time\": \"1970-01-01 00:10:00\", \"start_time\": \"1970-01-01 00:00:01\"}', '2022-10-03 10:18:13', '2022-10-03 10:18:13', '0b126c8e-16eb-4ed6-ac8b-3e2c0378ad86', NULL, '2c2686d2-9f76-4f89-9e18-08532594ebdd', 'unknown');" | psql -hlocalhost -Utestfiesta tf_development
