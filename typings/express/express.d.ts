import { FgaService } from '@ss-libs/ss-component-auth';
import { Knex } from 'knex';
import { Org, OrgModel } from '@app/models/org';
import { TenantModel } from '@app/models/tenant';
import { AccessToken, AccessTokenModel } from '@app/models/accessToken';
import { AppConfigModel } from '@app/models/appConfig';
import { AttachmentModel } from '@app/models/attachment';
import { CommentModel } from '@app/models/comment';
import { CustomFieldModel } from '@app/models/customField';
import { DashboardModel } from '@app/models/dashboard';
import { DataRelationshipModel } from '@app/models/dataRelationship';
import { ExternalEntityModel } from '@app/models/externalEntity';
import { Handle, HandleModel } from '@app/models/handle';
import { InviteModel } from '@app/models/invite';
import { ProjectInviteModel } from '@app/models/projectInvite';
import { IntegrationTokenModel } from '@app/models/integrationToken';
import { RepoModel } from '@app/models/repo';
import { RepoBranchModel } from '@app/models/repoBranch';
import { RoleModel, RoleTagModel } from '@app/models/role';
import { SubscriptionModel } from '@app/models/subscription';
import { TagModel } from '@app/models/tag';
import { TestTemplateModel } from '@app/models/testTemplate';
import { TestCaseModel } from '@app/models/testCase';
import { TestCaseStepModel } from '@app/models/testCaseStep';
import { TestCaseTagModel } from '@app/models/testCaseTag';
import { TestExecutionModel } from '@app/models/testExecution';
import { TestMilestoneModel } from '@app/models/testMilestone';
import { TestPlanModel } from '@app/models/testPlan';
import { Project, ProjectModel } from '@app/models/project';
import { TestResultModel } from '@app/models/testResult';
import { TestRunModel } from '@app/models/testRun';
import { SharedTestStepModel } from '@app/models/sharedTestStep';
import { UserModel, User as _User } from '@app/models/user';
import { TestExecutionStepModel } from '@app/models/testExecutionStep';
import { FolderModel } from '@app/models/folder';
import { TestMilestoneRunModel } from '@app/models/testMilestoneRun';
import { ConfigurationModel } from '@app/models/configuration';
import { TestPlanConfigurationModel } from '@app/models/testPlanConfiguration';
import { TestMilestonePlanModel } from '@app/models/testMilestonePlan';
import { MembershipModel } from '@app/models/membership';
import { IntegrationModel } from '@app/models/integration';
import { DefectModel } from '@app/models/defect';
import { MemberTagModel } from '@app/models/memberTag';
import { DefectExecutionModel } from '@app/models/defectExecution';
import { AuditLogModel } from '@app/models/auditLogs';
import { TestPlanRunModel } from '@app/models/testPlanRun';
import { SSOConfigModel } from '@app/models/ssoConfig';
import { SSOUserModel } from '@app/models/ssoUser';
import { DBServerModel } from '@app/models/dbServer';
import { AppNodeModel } from '@app/models/appNode';
import { AppVersionModel } from '@app/models/appVersion';

import { TenantConfig } from '@app/types/tenantConfig';
import { ScheduledTaskModel } from '@app/models/scheduledTask';

declare global {
  namespace Express {
    export interface Request {
      locals: {
        user: _User;
        handle: Handle;
        org: Org & { size?: number };
        project: Project;
        accessToken?: AccessToken;
        accessableProjects?: string[];
        [key: string]: any;
      };
      fga: FgaService;
      configs: TenantConfig;
      models: {
        AccessToken: AccessTokenModel;
        AuditLog: AuditLogModel;
        AppConfig: AppConfigModel;
        Attachment: AttachmentModel;
        Comment: CommentModel;
        CustomField: CustomFieldModel;
        Dashboard: DashboardModel;
        Defect: DefectModel;
        DefectExecution: DefectExecutionModel;
        DataRelationship: DataRelationshipModel;
        ExternalEntity: ExternalEntityModel;
        Folder: FolderModel;
        Handle: HandleModel;
        Invite: InviteModel;
        ProjectInvite: ProjectInviteModel;
        Integration: IntegrationModel;
        Membership: MembershipModel;
        IntegrationToken: IntegrationTokenModel;
        Org: OrgModel;
        Project: ProjectModel;
        Repo: RepoModel;
        RepoBranch: RepoBranchModel;
        Role: RoleModel;
        RoleTag: RoleTagModel;
        Subscription: SubscriptionModel;
        ScheduledTask: ScheduledTaskModel;
        Tag: TagModel;
        Tenant: TenantModel;
        TestTemplate: TestTemplateModel;
        TestCase: TestCaseModel;
        TestCaseStep: TestCaseStepModel;
        TestCaseTag: TestCaseTagModel;
        TestExecution: TestExecutionModel;
        TestExecutionStep: TestExecutionStepModel;
        TestMilestone: TestMilestoneModel;
        TestMilestoneRun: TestMilestoneRunModel;
        Configuration: ConfigurationModel;
        TestPlanConfiguration: TestPlanConfigurationModel;
        TestPlan: TestPlanModel;
        TestPlanRun: TestPlanRunModel;
        TestResult: TestResultModel;
        TestRun: TestRunModel;
        SharedTestStep: SharedTestStepModel;
        SSOConfig: SSOConfigModel;
        User: UserModel;
        SSOUser: SSOUserModel;
        TestMilestonePlan: TestMilestonePlanModel;
        MemberTag: MemberTagModel;
        DBServer: DBServerModel;
        AppNode: AppNodeModel;
        AppVersion: AppVersionModel;
      };
      knexDB: Knex; // shared DB
      sharedKnexDB: Knex; // tenant specific DB
    }
  }
}
