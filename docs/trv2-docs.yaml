openapi: 3.0.0
info:
  title: TRV2 API Documentation
  description: |
    The TRV2 API provides endpoints for managing various entities in the system, which includes projects, folders, cases, runs, plans, milestones, executions, results, tags, preferences, templates, configurations, and attachments. This API serves as the backbone for the TRV2 platform, allowing users to interact with and manage critical data regarding projects, tasks, milestones, results, and other associated objects. Each entity can be managed through dedicated routes that facilitate the creation, retrieval, modification, and deletion of relevant data.
    The following documentation outlines all available endpoints, their purpose, required parameters, expected responses, and usage examples.
  version: 1.0.0

servers:
  - url: "http://localhost:5050/trv2/{handle}/"
    description: Local development server
    variables:
      handle:
        default: ""
        description: The handle of your organization

paths:
  # Project endpoints
  index.php?/api/v2/get_projects:
    get:
      tags:
        - Project
      summary: Returns the list of available projects.
      description: |
        Retrieves all projects related to the organization. This endpoint returns a paginated list of projects with details such as project ID, name, completion status, announcement, users, and groups.
      parameters:
        - name: limit
          in: query
          required: false
          description: The number of projects to retrieve per page.
          schema:
            type: integer
            default: 250
        - name: offset
          in: query
          required: false
          description: The offset for pagination.
          schema:
            type: integer
            default: 0
        - name: is_completed
          in: query
          required: false
          description: Filter projects by completion status. 1 to return completed projects only, 0 for active projects only.
          schema:
            type: integer
            default: null
      responses:
        '200':
          description: A list of projects for the specified organization with pagination.
          content:
            application/json:
              schema:
                type: object
                properties:
                  offset:
                    type: integer
                    description: The current offset of the results.
                  limit:
                    type: integer
                    description: The limit of projects per page.
                  size:
                    type: integer
                    description: The size of the results array.
                  _links:
                    type: object
                    properties:
                      next:
                        type: string
                        description: URL for the next page of results, or null if there are no more results.
                        nullable: true
                      prev:
                        type: string
                        description: URL for the previous page of results, or null if there are no previous results.
                        nullable: true
                  results:
                    type: array
                    description: The list of projects for the specified organization.
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          description: The unique identifier of the project projectUid in TF.
                        completed_on:
                          type: integer
                          description: The timestamp when the project was completed (archived in TF), or null if not completed.
                          nullable: true
                        default_role_id:
                          type: integer
                          description: TF does not support this it will always be null.
                          nullable: true
                        default_role:
                          type: string
                          description: TF does not support this it will always be null.
                          nullable: true
                        is_completed:
                          type: boolean
                          description: A flag indicating whether the project is completed (archived in TF).
                        name:
                          type: string
                          description: The name of the project projectName in TF.
                        show_announcement:
                          type: boolean
                          description: A flag indicating whether the project has an announcement description in TF.
                        suite_mode:
                          type: integer
                          description: The suite mode of the project, 1 represents single suite mode. TF does not support this it will always be 1.
                        url:
                          type: string
                          description: The URL to access the Web UI for the project.
                        users:
                          type: array
                          items:
                            type: string
                          description: A list of user UIDs associated with the project.
                        groups:
                          type: array
                          items:
                            type: string
                          description: TF does not support this it will always be empty array.
              examples:
                example-1:
                  summary: Sample response with a single project
                  value:
                    offset: 0
                    limit: 250
                    size: 1
                    _links:
                      next: null
                      prev: null
                    results:
                      - id: 1
                        completed_on: null
                        default_role_id: null
                        default_role: null
                        is_completed: false
                        name: "name"
                        show_announcement: false
                        suite_mode: 1
                        url: "url"
                        users:
                          - "userUid"
                        groups: []
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No projects found for the specified filters or pagination criteria.
        '500':
          description: Internal server error.

  index.php?/api/v2/get_project/{project_id}:
    get:
      tags:
        - Project
      summary: Returns the details of a specific project.
      description: |
        Retrieves the details of a specific project based on its unique identifier.
      parameters:
        - name: project_id
          in: path
          required: true
          description: The unique identifier of the project projectUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The details of the specified project.
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The unique identifier of the project projectUid in TF.
                  name:
                    type: string
                    description: The name of the project projectName in TF.
                  announcement:
                    type: string
                    description: The announcement description of the project in TF.
                  show_announcement:
                    type: boolean
                    description: A flag indicating whether the project has an announcement description in TF.
                  suite_mode:
                    type: integer
                    description: The suite mode of the project, 1 represents single suite mode. TF does not support this it will always be 1.
                  url:
                    type: string
                    description: The URL to access the Web UI for the project.
                  users:
                    type: array
                    description: A list of user UIDs associated with the project.
                  groups:
                    type: array
                    description: TF does not support this it will always be empty array.
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No project found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/add_project:
    post:
      tags:
        - Project
      summary: Creates a new project.
      description: |
        Creates a new project with the provided details. This endpoint accepts a JSON object containing the necessary fields to create a project, such as the project name and an optional announcement.
      requestBody:
        required: true
        description: The details of the project to create.
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: The name of the project.
                  example: "Project Alpha"
                announcement:
                  type: string
                  description: The description or announcement of the project.
                  nullable: true
                  example: "This is a special announcement for Project Alpha."
              required:
                - name
      responses:
        '200':
          description: The project was successfully created.
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The unique identifier of the project projectUid in TF.
                  name:
                    type: string
                    description: The name of the project projectName in TF.
                  announcement:
                    type: string
                    description: The announcement description of the project in TF.
                  show_announcement:
                    type: boolean
                    description: A flag indicating whether the project has an announcement description in TF.
                  suite_mode:
                    type: integer
                    description: The suite mode of the project, 1 represents single suite mode. TF does not support this it will always be 1.
                  url:
                    type: string
                    description: The URL to access the Web UI for the project.
                  users:
                    type: array
                    description: A list of user UIDs associated with the project.
                  groups:
                    type: array
                    description: TF does not support this it will always be empty array.
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No project found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/update_project/{project_id}:
    post:
      tags:
        - Project
      summary: Updates an existing project.
      description: |
        Updates an existing project with the provided details. This endpoint accepts a JSON object containing the necessary fields to update a project, such as the project name and an optional announcement.
      parameters:
        - name: project_id
          in: path
          required: true
          description: The unique identifier of the project projectUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the project to update.
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: The name of the project.
                  required: true
                announcement:
                  type: string
                  description: The description or announcement of the project.
                  required: false
      responses:
        '200':
          description: The project was successfully updated.
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The unique identifier of the project projectUid in TF.
                  name:
                    type: string
                    description: The name of the project projectName in TF.
                  announcement:
                    type: string
                    description: The announcement description of the project in TF.
                  show_announcement:
                    type: boolean
                    description: A flag indicating whether the project has an announcement description in TF.
                  suite_mode:
                    type: integer
                    description: The suite mode of the project, 1 represents single suite mode. TF does not support this it will always be 1.
                  url:
                    type: string
                    description: The URL to access the Web UI for the project.
                  users:
                    type: array
                    description: A list of user UIDs associated with the project.
                  groups:
                    type: array
                    description: TF does not support this it will always be empty array.
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No project found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/delete_project/{project_id}:
    post:
      tags:
        - Project
      summary: Deletes a project.
      description: |
        Deletes a project with the provided details. This endpoint accepts a JSON object containing the necessary fields to delete a project, such as the project name and an optional announcement.
      parameters:
        - name: project_id
          in: path
          required: true
          description: The unique identifier of the project projectUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The project was successfully deleted.
          content:
            application/json:
              schema:
                type: null
                description: The project was successfully deleted.
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No project found for the specified ID.
        '500':
          description: Internal server error.

  # Folder endpoints
  index.php?/api/v2/get_suite/{suite_id}:
    get:
      tags:
        - Suite
      summary: Returns the details of a specific suite.
      description: |
        Retrieves the details of a specific suite based on its unique identifier.
      parameters:
        - name: suite_id
          in: path
          required: true
          description: The unique identifier of the suite folderUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The details of the specified suite.
          content:
            application/json:
              schema:
                type: object
                properties:
                  completed_on:
                    type: integer
                    description: The date/time when the test suite was closed (as UNIX timestamp) (added with TestRail 4.0)
                  description:
                    type: string
                    description: The description of the test suite.
                  id:
                    type: integer
                    description: The unique ID of the test suite.
                  is_baseline:
                    type: boolean
                    description: TF does not support this it will always be false.
                  is_completed:
                    type: boolean
                    description: TF does not support this it will always be false.
                  is_master:
                    type: boolean
                    description: True if the folder requested is the root folder.
                  name:
                    type: string
                    description: The name of the test suite.
                  project_id:
                    type: integer
                    description: The ID of the project this test suite belongs to.
                  url:
                    type: string
                    description: The address/URL of the test suite in the user interface.

        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No suite found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/get_suites/{project_id}:
    get:
      tags:
        - Suite
      summary: Returns the list of suites for a specific project.
      description: |
        Retrieves the list of suites for a specific project based on its unique identifier.
      parameters:
        - name: project_id
          in: path
          required: true
          description: The unique identifier of the project projectUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The list of suites for the specified project.
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                properties:
                  completed_on:
                    type: integer
                    description: The date/time when the test suite was closed (as UNIX timestamp) (added with TestRail 4.0)
                  description:
                    type: string
                    description: The description of the test suite.
                  id:
                    type: integer
                    description: The unique ID of the test suite.
                  is_baseline:
                    type: boolean
                    description: TF does not support this it will always be false.
                  is_completed:
                    type: boolean
                    description: TF does not support this it will always be false.
                  is_master:
                    type: boolean
                    description: True if the folder requested is the root folder.
                  name:
                    type: string
                    description: The name of the test suite.
                  project_id:
                    type: integer
                    description: The ID of the project this test suite belongs to.
                  url:
                    type: string
                    description: The address/URL of the test suite in the user interface.
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No suites found for the specified project.
        '500':
          description: Internal server error.

  index.php?/api/v2/add_suite/{project_id}:
    post:
      tags:
        - Suite
      summary: Creates a new suite.
      description: |
        Creates a new suite with the provided details. This endpoint accepts a JSON object containing the necessary fields to create a suite, such as the suite name and an optional description.
      parameters:
        - name: project_id
          in: path
          required: true
          description: The unique identifier of the project projectUid in TF.
          schema:
            type: integer
      responses:
        '403':
          description: This operation is not permitted because this project only supports a single test suite.

  index.php?/api/v2/update_suite/{suite_id}:
    post:
      tags:
        - Suite
      summary: Updates an existing suite.
      description: |
        Updates an existing suite with the provided details. This endpoint accepts a JSON object containing the necessary fields to update a suite, such as the suite name and an optional description.
      parameters:
        - name: suite_id
          in: path
          required: true
          description: The unique identifier of the suite folderUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the suite to update.
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: The name of the suite.
                  required: true
                description:
                  type: string
                  description: The description of the suite.
                  required: false
      responses:
        '200':
          description: The suite was successfully updated.
          content:
            application/json:
              schema:
                type: object
                properties:
                  completed_on:
                    type: integer
                    description: The date/time when the test suite was closed (as UNIX timestamp) (added with TestRail 4.0)
                  description:
                    type: string
                    description: The description of the test suite.
                  id:
                    type: integer
                    description: The unique ID of the test suite.
                  is_baseline:
                    type: boolean
                    description: TF does not support this it will always be false.
                  is_completed:
                    type: boolean
                    description: TF does not support this it will always be false.
                  is_master:
                    type: boolean
                    description: True if the folder requested is the root folder.
                  name:
                    type: string
                    description: The name of the test suite.
                  project_id:
                    type: integer
                    description: The ID of the project this test suite belongs to.
                  url:
                    type: string
                    description: The address/URL of the test suite in the user interface.
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No suite found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/delete_suite/{suite_id}:
    post:
      tags:
        - Suite
      summary: Deletes a suite.
      description: |
        Deletes a suite with the provided details. This endpoint accepts a JSON object containing the necessary fields to delete a suite, such as the suite name and an optional description.
      parameters:
        - name: suite_id
          in: path
          required: true
          description: The unique identifier of the suite folderUid in TF.
          schema:
            type: integer
      responses:
        '403':
          description: This operation is not permitted because this project only supports a single test suite.

  # Section endpoints
  index.php?/api/v2/get_section/{section_id}:
    get:
      tags:
        - Sections
      summary: Returns the details of a specific section.
      description: |
        Retrieves the details of a specific section based on its unique identifier.
      parameters:
        - name: section_id
          in: path
          required: true
          description: The unique identifier of the section sectionUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The details of the specified section.
          content:
            application/json:
              schema:
                type: object
                properties:
                  depth:
                    type: integer
                    description: TF does not support this will always be null.
                  description:
                    type: string
                    description: The description of the section.
                  display_order:
                    type: integer
                    description: TF does not support this will always be null.
                  id:
                    type: integer
                    description: The unique ID of the section.
                  name:
                    type: string
                    description: The name of the section.
                  parent_id:
                    type: integer
                    description: The ID of the parent section in the test suite.
                  suite_id:
                    type: integer
                    description: The ID of the test suite this section belongs to.
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No section found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/get_sections/{project_id}:
    get:
      tags:
        - Sections
      summary: Returns the list of sections for a specific project.
      description: |
        Retrieves the list of sections for a specific project based on its unique identifier.
      parameters:
        - name: project_id
          in: path
          required: true
          description: The unique identifier of the project projectUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The list of sections for the specified project.
          content:
            application/json:
              schema:
                type: object
                properties:
                  offset:
                    type: integer
                    description: The current offset of the results.
                  limit:
                    type: integer
                    description: The limit of sections per page.
                  size:
                    type: integer
                    description: The size of the results array.
                  _links:
                    type: object
                    properties:
                      next:
                        type: string
                        description: URL for the next page of results, or null if there are no more results.
                        nullable: true
                      prev:
                        type: string
                        description: URL for the previous page of results, or null if there are no previous results.
                        nullable: true
                  sections:
                    type: array
                    description: The list of sections for the specified project.
                    items:
                      type: object
                      properties:
                        depth:
                          type: integer
                          description: TF will return the computed depth of the section.
                        description:
                          type: string
                          description: The description of the section.
                        display_order:
                          type: integer
                          description: TF will return the computed display order of the section.
                        id:
                          type: integer
                          description: The unique ID of the section.
                        name:
                          type: string
                          description: The name of the section.
                        parent_id:
                          type: integer
                          description: The ID of the parent section in the test suite.
                        suite_id:
                          type: integer
                          description: The ID of the test suite this section belongs to.
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No sections found for the specified project.
        '500':
          description: Internal server error.

  index.php?/api/v2/add_section/{project_id}:
    post:
      tags:
        - Sections
      summary: Creates a new section.
      description: |
        Creates a new section with the provided details. This endpoint accepts a JSON object containing the necessary fields to create a section, such as the section name and an optional description.
      parameters:
        - name: project_id
          in: path
          required: true
          description: The unique identifier of the project projectUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the section to create.
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: The name of the section.
                  required: true
                description:
                  type: string
                  description: The description of the section.
                  required: false
                parent_id:
                  type: integer
                  description: The ID of the parent section in the test suite. If not provided, the section will be created under the root folder.
                  required: false
      responses:
        '200':
          description: The section was successfully created.
          content:
            application/json:
              schema:
                type: object
                properties:
                  depth:
                    type: integer
                    description: TF does not support this will always be null.
                  description:
                    type: string
                    description: The description of the section.
                  display_order:
                    type: integer
                    description: TF does not support this will always be null.
                  id:
                    type: integer
                    description: The unique ID of the section.
                  name:
                    type: string
                    description: The name of the section.
                  parent_id:
                    type: integer
                    description: The ID of the parent section in the test suite.
                  suite_id:
                    type: integer
                    description: The ID of the test suite this section belongs to.
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No section found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/move_section/{section_id}:
    post:
      tags:
        - Sections
      summary: Moves a section to a new parent section.
      description: |
        Moves a section to a new parent section. This endpoint accepts a JSON object containing the necessary fields to move a section, such as the new parent section ID.
      parameters:
        - name: section_id
          in: path
          required: true
          description: The unique identifier of the sub folder folderUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the section to move.
        content:
          application/json:
            schema:
              type: object
              properties:
                parent_id:
                  type: integer
                  description: The ID of the parent section (it can be null if it should be moved to the root). Must be in the same project and suite. May not be a direct child of the section being moved.
                  required: false
      responses:
        '200':
          description: The section was successfully moved.
          content:
            application/json:
              schema:
                type: object
                properties:
                  depth:
                    type: integer
                    description: TF does not support this will always be null.
                  description:
                    type: string
                    description: The description of the section.
                  display_order:
                    type: integer
                    description: TF does not support this will always be null.
                  id:
                    type: integer
                    description: The unique ID of the section.
                  name:
                    type: string
                    description: The name of the section.
                  parent_id:
                    type: integer
                    description: The ID of the parent section in the test suite.
                  suite_id:
                    type: integer
                    description: The ID of the test suite this section belongs to.
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No section found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/update_section/{section_id}:
    post:
      tags:
        - Sections
      summary: Updates an existing section.
      description: |
        Updates an existing section with the provided details. This endpoint accepts a JSON object containing the necessary fields to update a section, such as the section name and an optional description.
      parameters:
        - name: section_id
          in: path
          required: true
          description: The unique identifier of the sub folder folderUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the section to update.
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: The name of the section.
                  required: false
                description:
                  type: string
                  description: The description of the section.
                  required: false
      responses:
        '200':
          description: The section was successfully updated.
          content:
            application/json:
              schema:
                type: object
                properties:
                  depth:
                    type: integer
                    description: TF does not support this will always be null.
                  description:
                    type: string
                    description: The description of the section.
                  display_order:
                    type: integer
                    description: TF does not support this will always be null.
                  id:
                    type: integer
                    description: The unique ID of the section.
                  name:
                    type: string
                    description: The name of the section.
                  parent_id:
                    type: integer
                    description: The ID of the parent section in the test suite.
                  suite_id:
                    type: integer
                    description: The ID of the test suite this section belongs to.
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No section found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/delete_section/{section_id}:
    post:
      tags:
        - Sections
      summary: Deletes a section.
      description: |
        Deletes a section with the provided details. This endpoint accepts a JSON object containing the necessary fields to delete a section, such as the section name and an optional description.
      parameters:
        - name: section_id
          in: path
          required: true
          description: The unique identifier of the sub folder folderUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The section was successfully deleted.
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No section found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/get_case/{case_id}:
    get:
      tags:
        - Cases
      summary: Returns the details of a specific case.
      description: |
        Retrieves the details of a specific case based on its unique identifier.
      parameters:
        - name: case_id
          in: path
          required: true
          description: The unique identifier of the case caseUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The details of the specified case. In addition to the properties below, the response will also include the custom fields of the case which can be accessed by forming name as custom_case_<field_name>.
          content:
            application/json:
              schema:
                type: object
                properties:
                  created_by:
                    type: string
                    description: The UUID of the user who created the test case.
                  created_on:
                    type: integer
                    description: The date/time when the test case was created (as UNIX timestamp).
                  id:
                    type: integer
                    description: The unique ID of the test case.
                  priority_id:
                    type: integer
                    description: The ID of the priority that is linked to the test case.
                  section_id:
                    type: integer
                    description: The ID of the folder the test case belongs to.
                  suite_id:
                    type: integer
                    description: The ID of the root folder of project the test case belongs to.
                  is_deleted:
                    type: integer
                    description: Always 0 as the api will not return non deleted cases.
                  type_id:
                    type: integer
                    description: The ID of the first testCase tag that is linked to the test case.
                  template_id:
                    type: integer
                    description: The ID of the template (field layout) the test case uses.
                  title:
                    type: string
                    description: The title of the test case.
                  updated_by:
                    type: string
                    description: TF does not support this it will always be same as created_by.
                  updated_on:
                    type: integer
                    description: The date/time when the test case was last updated (as UNIX timestamp).
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No case found for the specified ID.

  index.php?/api/v2/get_cases/{project_id}:
    get:
      tags:
        - Cases
      summary: Returns the list of cases for a specific project.
      description: |
        Retrieves the list of cases for a specific project based on its unique identifier.
      parameters:
        - name: project_id
          in: path
          required: true
          description: The unique identifier of the project projectUid in TF.
          schema:
            type: integer
        - name: created_after
          in: query
          required: false
          description: Only return test cases created after this date (as UNIX timestamp)
          schema:
            type: integer
        - name: created_before
          in: query
          required: false
          description: Only return test cases created before this date (as UNIX timestamp)
          schema:
            type: integer
        - name: created_by
          in: query
          required: false
          description: A comma-separated list of creators (user UIDs) to filter by
          schema:
            type: string
        - name: filter
          in: query
          required: false
          description: Only return cases with matching filter string in the case title
          schema:
            type: string
        - name: limit
          in: query
          required: false
          description: The number of test cases the response should return
          schema:
            type: integer
        - name: offset
          in: query
          required: false
          description: Where to start counting the tests cases from (the offset)
          schema:
            type: integer
        - name: priority_id
          in: query
          required: false
          description: A comma-separated list of priority UIDs to filter by
          schema:
            type: string
        - name: section_id
          in: query
          required: false
          description: The ID of a test case section
          schema:
            type: integer
        - name: template_id
          in: query
          required: false
          description: A comma-separated list of template UIDs to filter by
          schema:
            type: string
        - name: type_id
          in: query
          required: false
          description: A comma-separated list of testCase tags to filter by
          schema:
            type: string
        - name: updated_after
          in: query
          required: false
          description: Only return test cases updated after this date (as UNIX timestamp)
          schema:
            type: integer
        - name: updated_before
          in: query
          required: false
          description: Only return test cases updated before this date (as UNIX timestamp)
          schema:
            type: integer
      responses:
        '200':
          description: The list of cases for the specified project.
          content:
            application/json:
              schema:
                type: object
                properties:
                  offset:
                    type: integer
                    description: The offset of the results.
                  limit:
                    type: integer
                    description: The limit of cases per page.
                  size:
                    type: integer
                    description: The total number of cases.
                  _links:
                    type: object
                    properties:
                      next:
                        type: string
                        description: The URL to the next page of results.
                      prev:
                        type: string
                        description: The URL to the previous page of results.
                  cases:
                    type: array
                    description: The list of cases for the specified project.  In addition to the properties below, the response will also include the custom fields of the case which can be accessed by forming name as custom_case_<field_name>.
                    items:
                      type: object
                      properties:
                        created_by:
                          type: string
                          description: The UUID of the user who created the test case.
                        created_on:
                          type: integer
                          description: The date/time when the test case was created (as UNIX timestamp).
                        id:
                          type: integer
                          description: The unique ID of the test case.
                        priority_id:
                          type: integer
                          description: The ID of the priority that is linked to the test case.
                        section_id:
                          type: integer
                          description: The ID of the folder the test case belongs to.
                        suite_id:
                          type: integer
                          description: The ID of the root folder of project the test case belongs to.
                        template_id:
                          type: integer
                          description: The ID of the template (field layout) the test case uses.
                        type_id:
                          type: integer
                          description: The ID of the first testCase tag that is linked to the test case.
                        is_deleted:
                          type: integer
                          description: Always 0 as the api will not return non deleted cases.
                        title:
                          type: string
                          description: The title of the test case.
                        updated_by:
                          type: string
                          description: TF does not support this it will always be same as created_by.
                        updated_on:
                          type: integer
                          description: The date/time when the test case was last updated (as UNIX timestamp).
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No case found for the specified ID.

  index.php?/api/v2/get_history_for_case/{case_id}:
    get:
      tags:
        - Cases
      summary: TestFiesta does not support this endpoint.
      description: |
        TestFiesta does not support this endpoint.
      responses:
        '403':
          description: This operation is not permitted because TestFiesta does not support this endpoint.

  index.php?/api/v2/add_case/{section_id}:
    post:
      tags:
        - Cases
      summary: Creates a new case.
      description: |
        Creates a new case with the provided details. This endpoint accepts a JSON object containing the necessary fields to create a case, such as the case name and an optional description. In
      parameters:
        - name: section_id
          in: path
          required: true
          description: The unique identifier of the section sectionUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the case to create. In addition to the properties below, the request body can also include the custom fields of the case which can be accessed by forming name as custom_case_<field_name>.
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                  description: The title / name of the test case
                  required: true
                template_id:
                  type: integer
                  description: The ID of the template (field layout) if not provided, the default template will be used.
                  required: false
                type_id:
                  type: integer
                  description: The ID of the testCase tag that is required to be linked to the test case
                  required: false
                priority_id:
                  type: integer
                  description: The ID of the priority that is linked to the test case
                  required: false
      responses:
        '200':
          description: The case was successfully created.
          content:
            application/json:
              schema:
                type: object
                properties:
                  created_by:
                    type: string
                    description: The UUID of the user who created the test case.
                  created_on:
                    type: integer
                    description: The date/time when the test case was created (as UNIX timestamp).
                  id:
                    type: integer
                    description: The unique ID of the test case.
                  priority_id:
                    type: integer
                    description: The ID of the priority that is linked to the test case.
                  section_id:
                    type: integer
                    description: The ID of the folder the test case belongs to.
                  suite_id:
                    type: integer
                    description: The ID of the root folder of project the test case belongs to.
                  template_id:
                    type: integer
                    description: The ID of the template (field layout) the test case uses.
                  type_id:
                    type: integer
                    description: The ID of the first testCase tag that is linked to the test case.
                  is_deleted:
                    type: integer
                    description: Always 0 as the api will not return non deleted cases.
                  title:
                    type: string
                    description: The title of the test case.
                  updated_by:
                    type: string
                    description: TF does not support this it will always be same as created_by.
                  updated_on:
                    type: integer
                    description: The date/time when the test case was last updated (as UNIX timestamp).
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No case found for the specified ID.

  index.php?/api/v2/copy_cases_to_section/{section_id}:
    post:
      tags:
        - Cases
      summary: Copies cases to a section.
      description: |
        Copies cases to a section. This endpoint accepts a JSON object containing the necessary fields to copy cases to a section, such as the section ID and the cases to copy.
      parameters:
        - name: section_id
          in: path
          required: true
          description: The unique identifier of the section sectionUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the cases to copy.
        content:
          application/json:
            schema:
              type: object
              properties:
                case_ids:
                  type: array
                  description: A comma-separated list of case IDs to copy.
      responses:
        '200':
          description: The cases were successfully copied.
          content:
            application/json:
              schema:
                type: null
                description: The cases were successfully copied.
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No section found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/update_case/{case_id}:
    post:
      tags:
        - Cases
      summary: Updates an existing case.
      description: |
        Updates an existing case with the provided details. This endpoint accepts a JSON object containing the necessary fields to update a case, such as the case name and an optional description.
      parameters:
        - name: case_id
          in: path
          required: true
          description: The unique identifier of the case caseUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the case to update. In addition to the properties below, the request body can also include the custom fields of the case which can be accessed by forming name as custom_case_<field_name>.
        content:
          application/json:
            schema:
              type: object
              properties:
                section_id:
                  type: integer
                  description: The ID of the section the test case should be moved to
                  required: false
                title:
                  type: string
                  description: The title / name of the test case
                  required: false
                template_id:
                  type: integer
                  description: The ID of the template (field layout),
                  required: false
                type_id:
                  type: integer
                  description: The ID of the testCase tag. Previous tags will be preserve and new tags will be added.
                  required: false
                priority_id:
                  type: integer
                  description: The ID of the priority that is linked to the test case
                  required: false
      responses:
        '200':
          description: The case was successfully updated. In addition to the properties below, the response will also include the custom fields of the case which can be accessed by forming name as custom_case_<field_name>.
          content:
            application/json:
              schema:
                type: object
                properties:
                  created_by:
                    type: string
                    description: The UUID of the user who created the test case.
                  created_on:
                    type: integer
                    description: The date/time when the test case was created (as UNIX timestamp).
                  id:
                    type: integer
                    description: The unique ID of the test case.
                  priority_id:
                    type: integer
                    description: The ID of the priority that is linked to the test case.
                  section_id:
                    type: integer
                    description: The ID of the section the test case belongs to.
                  suite_id:
                    type: integer
                    description: The ID of the root folder of project the test case belongs to.
                  template_id:
                    type: integer
                    description: The ID of the template (field layout) the test case uses.
                  type_id:
                    type: integer
                    description: The ID of the first testCase tag that is linked to the test case.
                  is_deleted:
                    type: integer
                    description: Always 0 as the api will not return non deleted cases.
                  title:
                    type: string
                    description: The title of the test case.
                  updated_by:
                    type: string
                    description: The UUID of the user who updated the test case.
                  updated_on:
                    type: integer
                    description: The date/time when the test case was last updated (as UNIX timestamp).
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No case found for the specified ID.

  index.php?/api/v2/update_cases/{suite_id}:
    post:
      tags:
        - Cases
      summary: Updates multiple cases.
      description: |
        Updates multiple cases with the provided details. This endpoint accepts a JSON object containing the necessary fields to update multiple cases, such as the case name and an optional description.
      parameters:
        - name: suite_id
          in: path
          required: true
          description: The unique identifier of the suite suiteUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the cases to update. In addition to the properties below, the request body can also include the custom fields of the case which can be accessed by forming name as custom_case_<field_name>.
        content:
          application/json:
            schema:
              type: object
              properties:
                case_ids:
                  type: array
                  description: A comma-separated list of case IDs that should be updated.
                  required: true
                section_id:
                  type: integer
                  description: The ID of the section the test case should be moved to.
                  required: false
                title:
                  type: string
                  description: The title / name of the test case.
                  required: false
                template_id:
                  type: integer
                  description: The ID of the template (field layout),
                  required: false
                type_id:
                  type: integer
                  description: The ID of the testCase tag. Previous tags will be preserve and new tags will be added.
                  required: false
                priority_id:
                  type: integer
                  description: The ID of the priority that is linked to the test case
                  required: false
      responses:
        '200':
          description: The cases were successfully updated. In addition to the properties below, the response will also include the custom fields of the case which can be accessed by forming name as custom_case_<field_name>.
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    created_by:
                      type: string
                    created_on:
                      type: integer
                    id:
                      type: integer
                    priority_id:
                      type: integer
                    section_id:
                      type: integer
                    suite_id:
                      type: integer
                    template_id:
                      type: integer
                    type_id:
                      type: integer
                    is_deleted:
                      type: integer
                    title:
                      type: string
                    updated_by:
                      type: string
                    updated_on:
                      type: integer
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No cases found for the specified IDs.
        '500':
          description: Internal server error.

  index.php?/api/v2/move_cases_to_section/{section_id}:
    post:
      tags:
        - Cases
      summary: Moves cases to a section.
      description: |
        Moves cases to a section. This endpoint accepts a JSON object containing the necessary fields to move cases to a section, such as the section ID and the cases to move.
      parameters:
        - name: section_id
          in: path
          required: true
          description: The unique identifier of the folder folderUid in TF in which the cases should be moved.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the cases to move.
        content:
          application/json:
            schema:
              type: object
              properties:
                case_ids:
                  type: array
                  description: A comma-separated list of case IDs to move.
                  required: true
      responses:
        '200':
          description: The cases were successfully moved.
          content:
            application/json:
              schema:
                type: null
                description: The cases were successfully moved.
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No section found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/delete_case/{case_id}:
    post:
      tags:
        - Cases
      summary: Deletes a case.
      description: |
        Deletes a case. This endpoint accepts a JSON object containing the necessary fields to delete a case, such as the case ID.
      parameters:
        - name: case_id
          in: path
          required: true
          description: The unique identifier of the case caseUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the case to delete.
        content:
          application/json:
            schema:
              type: object
              properties:
                soft:
                  type: integer
                  description: soft=1 will return information about the data which will be deleted but will not proceed with the deletion
                  required: false
      responses:
        '200':
          description: The cases were successfully deleted.
          content:
            application/json:
              schema:
                type: null
                description: The cases were successfully deleted.
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No case found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/delete_cases/{suite_id}:
    post:
      tags:
        - Cases
      summary: Deletes multiple cases.
      description: |
        Deletes multiple cases with the provided details. This endpoint accepts a JSON object containing the necessary fields to delete multiple cases, such as the case IDs.
      parameters:
        - name: suite_id
          in: path
          required: true
          description: The unique identifier of the suite suiteUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the cases to delete.
        content:
          application/json:
            schema:
              type: object
              properties:
                case_ids:
                  type: array
                  description: A comma-separated list of case IDs to delete.
                  required: true
                soft:
                  type: integer
                  description: soft=1 will return information about the data which will be deleted but will not proceed with the deletion
                  required: false
      responses:
        '200':
          description: The cases were successfully deleted.
          content:
            application/json:
              schema:
                type: null
                description: The cases were successfully deleted.
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No cases found for the specified IDs.
        '500':
          description: Internal server error.

  index.php?/api/v2/get_run/{run_id}:
    get:
      tags:
        - Runs
      summary: Retrieves a run.
      description: |
        Retrieves a run with the provided details. This endpoint accepts a JSON object containing the necessary fields to retrieve a run, such as the run ID.
      parameters:
        - name: run_id
          in: path
          required: true
          description: The unique identifier of the run runUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The run was successfully retrieved. In addition to the properties below, the response will also include the count of statuses in the run which can be accessed by forming name as custom_<status_name>_count.
          content:
            application/json:
              schema:
                type: object
                properties:
                  assignedto_id:
                    type: integer
                    description: TF does not support this field will be null
                  completed_on:
                    type: integer
                    description: The date/time when the test run was closed (as UNIX timestamp) / archivedAt
                  config:
                    type: string
                    description: The configuration of the test run as a string (if part of a test plan)
                  config_ids:
                    type: array
                    description: The array of IDs of the configurations of the test run (if part of a test plan)
                  created_by:
                    type: string
                    description: The UUID of the user who created the test run
                  created_on:
                    type: integer
                    description: The date/time when the test run was created (as UNIX timestamp)
                  description:
                    type: string
                    description: The description of the test run
                  id:
                    type: integer
                    description: The unique ID of the test run
                  include_all:
                    type: boolean
                    description: True if the test run includes all test cases and false otherwise
                  is_completed:
                    type: boolean
                    description: True if the test run was closed / archived and false otherwise
                  milestone_id:
                    type: integer
                    description: The first ID from array of the milestones linked to the test run
                  plan_id:
                    type: integer
                    description: The first ID from array of the test plans linked to the test run
                  name:
                    type: string
                    description: The name of the test run
                  project_id:
                    type: integer
                    description: The ID of the project this test run belongs to
                  suite_id:
                    type: integer
                    description: The ID of the test suite in TF will walway point to root project folder
                  updated_on:
                    type: integer
                    description: The date/time when the test run was last updated (as UNIX timestamp)
                  url:
                    type: string
                    description: The address/URL of the test run in the user interface
                  refs:
                    type: string
                    description: Always null as TF does not support this field
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No run found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/get_runs/{project_id}:
    get:
      tags:
        - Runs
      summary: Retrieves all runs for a project.
      description: |
        Retrieves all runs for a project. This endpoint accepts a JSON object containing the necessary fields to retrieve all runs for a project, such as the project ID.
      parameters:
        - name: project_id
          in: path
          required: true
          description: The unique identifier of the project projectUid in TF.
          schema:
            type: integer
        - name: created_after
          in: query
          required: false
          description: Only return test runs created after this date (as UNIX timestamp)
          schema:
            type: integer
        - name: created_before
          in: query
          required: false
          description: Only return test runs created before this date (as UNIX timestamp)
          schema:
            type: integer
        - name: created_by
          in: query
          required: false
          description: A comma-separated list of creators (user IDs) to filter by
          schema:
            type: string
        - name: is_completed
          in: query
          required: false
          description: 1 to return completed / archived test runs only. 0 to return active test runs only
          schema:
            type: integer
        - name: limit
          in: query
          required: false
          description: Limit the result to limit test runs. Use offset to skip records
          schema:
            type: integer
        - name: offset
          in: query
          required: false
          description: Use offset to skip records
          schema:
            type: integer
        - name: milestone_id
          in: query
          required: false
          description: A comma-separated list of milestone IDs to filter by
          schema:
            type: string
      responses:
        '200':
          description: The runs were successfully retrieved. In addition to the properties below, the response will also include the count of statuses in the run which can be accessed by forming name as custom_<status_name>_count.
          content:
            application/json:
              schema:
                type: object
                properties:
                  offset:
                    type: integer
                    description: The offset of the runs
                  limit:
                    type: integer
                    description: The limit of the runs
                  size:
                    type: integer
                    description: The size of the runs
                  _links:
                    type: object
                    properties:
                      next:
                        type: string
                        description: The URL of the next page of runs
                      prev:
                        type: string
                        description: The URL of the previous page of runs
                  runs:
                    type: array
                    items:
                      type: object
                      properties:
                        assignedto_id:
                          type: integer
                          description: TF does not support this field will be null
                        completed_on:
                          type: integer
                          description: The date/time when the test run was closed (as UNIX timestamp) / archivedAt
                        config:
                          type: string
                          description: The configuration of the test run as a string (if part of a test plan)
                        config_ids:
                          type: array
                          description: The array of IDs of the configurations of the test run (if part of a test plan)
                        created_by:
                          type: string
                          description: The UUID of the user who created the test run
                        created_on:
                          type: integer
                          description: The date/time when the test run was created (as UNIX timestamp)
                        description:
                          type: string
                          description: The description of the test run
                        id:
                          type: integer
                          description: The unique ID of the test run
                        include_all:
                          type: boolean
                          description: True if the test run includes all test cases and false otherwise
                        is_completed:
                          type: boolean
                          description: True if the test run was closed / archived and false otherwise
                        milestone_id:
                          type: integer
                          description: The first ID from array of the milestones linked to the test run
                        plan_id:
                          type: integer
                          description: The first ID from array of the test plans linked to the test run
                        name:
                          type: string
                          description: The name of the test run
                        project_id:
                          type: integer
                          description: The ID of the project this test run belongs to
                        suite_id:
                          type: integer
                          description: The ID of the test suite in TF will walway point to root project folder
                        updated_on:
                          type: integer
                          description: The date/time when the test run was last updated (as UNIX timestamp)
                        url:
                          type: string
                          description: The address/URL of the test run in the user interface
                        refs:
                          type: string
                          description: Always null as TF does not support this field
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No run found for the specified ID.
        '500':
          description: Internal server error.
  
  index.php?/api/v2/add_run/{project_id}:
    post:
      tags:
        - Runs
      summary: Creates a new run.
      description: |
        Creates a new run with the provided details. This endpoint accepts a JSON object containing the necessary fields to create a run, such as the project ID.
      parameters:
        - name: project_id
          in: path
          required: true
          description: The unique identifier of the project projectUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the run to create.
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: The name of the test run
                  required: true
                description:
                  type: string
                  description: The description of the test run
                  required: false
                include_all:
                  type: boolean
                  description: True if the test run includes all test cases and false otherwise true by default
                  required: false
                case_ids:
                  type: array
                  description: A comma-separated list of case IDs to add to the test run. Required if include_all is false.
                  required: false
                milestone_id:
                  type: integer
                  description: The ID of the milestone to link to the test run
                  required: false
      responses:
        '200':
          description: The run was successfully created. In addition to the properties below, the response will also include the count of statuses in the run which can be accessed by forming name as custom_<status_name>_count.
          content:
            application/json:
              schema:
                type: object
                properties:
                  assignedto_id:
                    type: integer
                    description: TF does not support this field will be null
                  completed_on:
                    type: integer
                    description: The date/time when the test run was closed (as UNIX timestamp) / archivedAt
                  config:
                    type: string
                    description: The configuration of the test run as a string (if part of a test plan)
                  config_ids:
                    type: array
                    description: The array of IDs of the configurations of the test run (if part of a test plan)
                  created_by:
                    type: string
                    description: The UUID of the user who created the test run
                  created_on:
                    type: integer
                    description: The date/time when the test run was created (as UNIX timestamp)
                  description:
                    type: string
                    description: The description of the test run
                  id:
                    type: integer
                    description: The unique ID of the test run
                  include_all:
                    type: boolean
                    description: True if the test run includes all test cases and false otherwise
                  is_completed:
                    type: boolean
                    description: True if the test run was closed / archived and false otherwise
                  milestone_id:
                    type: integer
                    description: The first ID from array of the milestones linked to the test run
                  plan_id:
                    type: integer
                    description: The first ID from array of the test plans linked to the test run
                  name:
                    type: string
                    description: The name of the test run
                  project_id:
                    type: integer
                    description: The ID of the project this test run belongs to
                  suite_id:
                    type: integer
                    description: The ID of the test suite in TF will walway point to root project folder
                  updated_on:
                    type: integer
                    description: The date/time when the test run was last updated (as UNIX timestamp)
                  url:
                    type: string
                    description: The address/URL of the test run in the user interface
                  refs:
                    type: string
                    description: Always null as TF does not support this field
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No run found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/update_run/{run_id}:
    post:
      tags:
        - Runs
      summary: Updates an existing run.
      description: |
        Updates an existing run with the provided details. This endpoint accepts a JSON object containing the necessary fields to update a run, such as the run ID.
      parameters:
        - name: run_id
          in: path
          required: true
          description: The unique identifier of the run runUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the run to update.
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: The name of the test run
                  required: false
                description:
                  type: string
                  description: The description of the test run
                  required: false
                include_all:
                  type: boolean
                  description: True if the test run includes all test cases and false otherwise true by default
                  required: false
                case_ids:
                  type: array
                  description: A comma-separated list of case IDs to add to the test run. Required if include_all is false.
                  required: false
                milestone_id:
                  type: integer
                  description: This will add the milestone to the test run and will not remove any existing milestones.
                  required: false

      responses:
        '200':
          description: The run was successfully created. In addition to the properties below, the response will also include the count of statuses in the run which can be accessed by forming name as custom_<status_name>_count.
          content:
            application/json:
              schema:
                type: object
                properties:
                  assignedto_id:
                    type: integer
                    description: TF does not support this field will be null
                  completed_on:
                    type: integer
                    description: The date/time when the test run was closed (as UNIX timestamp) / archivedAt
                  config:
                    type: string
                    description: The configuration of the test run as a string (if part of a test plan)
                  config_ids:
                    type: array
                    description: The array of IDs of the configurations of the test run (if part of a test plan)
                  created_by:
                    type: string
                    description: The UUID of the user who created the test run
                  created_on:
                    type: integer
                    description: The date/time when the test run was created (as UNIX timestamp)
                  description:
                    type: string
                    description: The description of the test run
                  id:
                    type: integer
                    description: The unique ID of the test run
                  include_all:
                    type: boolean
                    description: True if the test run includes all test cases and false otherwise
                  is_completed:
                    type: boolean
                    description: True if the test run was closed / archived and false otherwise
                  milestone_id:
                    type: integer
                    description: The first ID from array of the milestones linked to the test run
                  plan_id:
                    type: integer
                    description: The first ID from array of the test plans linked to the test run
                  name:
                    type: string
                    description: The name of the test run
                  project_id:
                    type: integer
                    description: The ID of the project this test run belongs to
                  suite_id:
                    type: integer
                    description: The ID of the test suite in TF will walway point to root project folder
                  updated_on:
                    type: integer
                    description: The date/time when the test run was last updated (as UNIX timestamp)
                  url:
                    type: string
                    description: The address/URL of the test run in the user interface
                  refs:
                    type: string
                    description: Always null as TF does not support this field
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No run found for the specified ID.
        '500':
          description: Internal server error.
  
  index.php?/api/v2/close_run/{run_id}:
    post:
      tags:
        - Runs
      summary: Closes an existing run.
      description: |
        Closes an existing run with the provided details. This endpoint accepts a JSON object containing the necessary fields to close a run, such as the run ID.
      parameters:
        - name: run_id
          in: path
          required: true
          description: The unique identifier of the run runUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The run was successfully closed. In addition to the properties below, the response will also include the count of statuses in the run which can be accessed by forming name as custom_<status_name>_count.
          content:
            application/json:
              schema:
                type: object
                properties:
                  assignedto_id:
                    type: integer
                    description: TF does not support this field will be null
                  completed_on:
                    type: integer
                    description: The date/time when the test run was closed (as UNIX timestamp) / archivedAt
                  config:
                    type: string
                    description: The configuration of the test run as a string (if part of a test plan)
                  config_ids:
                    type: array
                    description: The array of IDs of the configurations of the test run (if part of a test plan)
                  created_by:
                    type: string
                    description: The UUID of the user who created the test run
                  created_on:
                    type: integer
                    description: The date/time when the test run was created (as UNIX timestamp)
                  description:
                    type: string
                    description: The description of the test run
                  id:
                    type: integer
                    description: The unique ID of the test run
                  include_all:
                    type: boolean
                    description: True if the test run includes all test cases and false otherwise
                  is_completed:
                    type: boolean
                    description: True if the test run was closed / archived and false otherwise
                  milestone_id:
                    type: integer
                    description: The first ID from array of the milestones linked to the test run
                  plan_id:
                    type: integer
                    description: The first ID from array of the test plans linked to the test run
                  name:
                    type: string
                    description: The name of the test run
                  project_id:
                    type: integer
                    description: The ID of the project this test run belongs to
                  suite_id:
                    type: integer
                    description: The ID of the test suite in TF will walway point to root project folder
                  updated_on:
                    type: integer
                    description: The date/time when the test run was last updated (as UNIX timestamp)
                  url:
                    type: string
                    description: The address/URL of the test run in the user interface
                  refs:
                    type: string
                    description: Always null as TF does not support this field
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No run found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/delete_run/{run_id}:
    post:
      tags:
        - Runs
      summary: Deletes an existing run.
      description: |
        Deletes an existing run with the provided details. This endpoint accepts a JSON object containing the necessary fields to delete a run, such as the run ID.
      parameters:
        - name: run_id
          in: path
          required: true
          description: The unique identifier of the run runUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the run to delete.
        content:
          application/json:
            schema:
              type: object
              properties:
                soft:
                  type: integer
                  description: If soft=1, this will return data on the number of affected tests. Including soft=1 will not actually delete the entity.
                  required: false
      responses:
        '200':
          description: The run was successfully deleted.
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No run found for the specified ID.
        '500':
          description: Internal server error.

        

  index.php?/api/v2/get_plan/{plan_id}:
    get:
      tags:
        - Plans
      summary: Retrieves a plan.
      description: |
        Retrieves a plan with the provided details. This endpoint accepts a JSON object containing the necessary fields to retrieve a plan, such as the plan ID.
      parameters:
        - name: plan_id
          in: path
          required: true
          description: The unique identifier of the plan planUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The plan was successfully retrieved. In addition to the properties below, the response will also include the count of statuses in the plan which can be accessed by forming name as custom_<status_name>_count.
          content:
            application/json:
              schema:
                type: object
                properties:
                  assignedto_id:
                    type: integer
                    description: TF does not support this field will be null
                  completed_on:
                    type: integer
                    description: The date/time when the test plan was closed (as UNIX timestamp) / archivedAt
                  created_by:
                    type: string
                    description: The UUID of the user who created the test plan
                  created_on:
                    type: integer
                    description: The date/time when the test plan was created (as UNIX timestamp)
                  description:
                    type: string
                    description: The description of the test plan
                  entries:
                    type: array
                    description: An array of 'entries', i.e. group of test runs
                  id:
                    type: integer
                    description: The unique ID of the test plan
                  is_completed:
                    type: boolean
                    description: True if the test plan was closed / archived and false otherwise
                  milestone_id:
                    type: integer
                    description: The first ID from array of the milestones linked to the test plan
                  name:
                    type: string
                    description: The name of the test plan
                  project_id:
                    type: integer
                    description: The ID of the project this test plan belongs to
                  url:
                    type: string
                    description: The address/URL of the test plan in the user interface
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No plan found for the specified ID.
        '500':
          description: Internal server error.


  index.php?/api/v2/get_plans/{project_id}:
    get:
      tags:
        - Plans
      summary: Retrieves all plans.
      description: |
        Retrieves all plans with the provided details. This endpoint accepts a JSON object containing the necessary fields to retrieve all plans, such as the project ID.
      parameters:
        - name: project_id
          in: path
          required: true
          description: The unique identifier of the project projectUid in TF.
          schema:
            type: integer
        - name: created_after
          in: query
          required: false
          description: Only return test plans created after this date (as UNIX timestamp)
          schema:
            type: integer
        - name: created_before
          in: query
          required: false
          description: Only return test plans created before this date (as UNIX timestamp)
          schema:
            type: integer
        - name: created_by
          in: query
          required: false
          description: A comma-separated list of creators (user IDs) to filter by
          schema:
            type: string
        - name: is_completed
          in: query
          required: false
          description: 1 to return completed test plans only. 0 to return active test plans only
          schema:
            type: integer
        - name: limit
          in: query
          required: false
          description: Limit the result to limit test plans. Use offset to skip records
          schema:
            type: integer
        - name: offset
          in: query
          required: false
          description: Use offset to skip records
          schema:
            type: integer
        - name: milestone_id
          in: query
          required: false
          description: A comma-separated list of milestone IDs to filter by
          schema:
            type: string
      responses:
        '200':
          description: The plans were successfully retrieved. In addition to the properties below, the response will also include the count of statuses in the plan which can be accessed by forming name as custom_<status_name>_count.
          content:
            application/json:
              schema:
                type: object
                properties:
                  offset:
                    type: integer
                    description: The offset of the plans
                  limit:
                    type: integer
                    description: The limit of the plans
                  size:
                    type: integer
                    description: The size of the plans
                  _links:
                    type: object
                    properties:
                      next:
                        type: string
                        description: The URL of the next page of plans
                      prev:
                        type: string
                        description: The URL of the previous page of plans
                  plans:
                    type: array
                    items:
                      type: object
                      properties:
                        assignedto_id:
                          type: integer
                          description: TF does not support this field will be null
                        completed_on:
                          type: integer
                          description: The date/time when the test plan was closed (as UNIX timestamp) / archivedAt
                        created_by:
                          type: string
                          description: The UUID of the user who created the test plan
                        created_on:
                          type: integer
                          description: The date/time when the test plan was created (as UNIX timestamp)
                        description:
                          type: string
                          description: The description of the test plan
                        id:
                          type: integer
                          description: The unique ID of the test plan
                        is_completed:
                          type: boolean
                          description: True if the test plan was closed / archived and false otherwise
                        milestone_id:
                          type: integer
                          description: The first ID from array of the milestones linked to the test plan
                        name:
                          type: string
                          description: The name of the test plan
                        project_id:
                          type: integer
                          description: The ID of the project this test plan belongs to
                        refs:
                          type: string
                          description: Always null as TF does not support this field
                        url:
                          type: string
                          description: The address/URL of the test plan in the user interface
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No plans found for the specified project ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/add_plan/{project_id}:
    post:
      tags:
        - Plans
      summary: Adds a plan.
      description: |
        Adds a plan with the provided details. This endpoint accepts a JSON object containing the necessary fields to add a plan, such as the project ID.
      parameters:
        - name: project_id
          in: path
          required: true
          description: The unique identifier of the project projectUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the plan to add.
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: The name of the test plan
                  required: true
                description:
                  type: string
                  description: The description of the test plan
                  required: false
                milestone_id:
                  type: integer
                  description: The ID of the milestone to link to the test plan
                  required: false
                entries:
                  type: array
                  description: An array of objects describing the test runs of the plan,
                  required: false
                  items:
                    type: object
                    properties:
                      include_all:
                        type: boolean
                        description: True if the test run includes all test cases and false otherwise
                      config_ids:
                        type: array
                        description: The array of IDs of the configurations of the test run
                      runs:
                        type: array
                        description: An array of objects describing the test runs of the plan,
                        required: false
                        items:
                          type: object
                          properties:
                            include_all:
                              type: boolean
                              description: True if the test run includes all test cases and false otherwise
                            case_ids:
                              type: array
                              description: The array of IDs of the test cases of the test run
                            config_ids:
                              type: array
                              description: The array of IDs of the configurations of the test run
        responses:
          '200':
            description: The plan was successfully added. In addition to the properties below, the response will also include the count of statuses in the plan which can be accessed by forming name as custom_<status_name>_count.
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    assignedto_id:
                      type: integer
                      description: TF does not support this field will be null
                    completed_on:
                      type: integer
                      description: The date/time when the test plan was closed (as UNIX timestamp) / archivedAt
                    created_by:
                      type: string
                      description: The UUID of the user who created the test plan
                    created_on:
                      type: integer
                      description: The date/time when the test plan was created (as UNIX timestamp)
                    description:
                      type: string
                      description: The description of the test plan
                    id:
                      type: integer
                      description: The unique ID of the test plan
                    is_completed:
                      type: boolean
                      description: True if the test plan was closed / archived and false otherwise
                    milestone_id:
                      type: integer
                      description: The first ID from array of the milestones linked to the test plan
                    name:
                      type: string
                      description: The name of the test plan
                    project_id:
                      type: integer
                      description: The ID of the project this test plan belongs to
                    url:
                      type: string
                      description: The address/URL of the test plan in the user interface
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No plan found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/update_plan/{plan_id}:
    post:
      tags:
        - Plans
      summary: Updates a plan.
      description: |
        Updates a plan with the provided details. This endpoint accepts a JSON object containing the necessary fields to update a plan, such as the plan ID.
      parameters:
        - name: plan_id
          in: path
          required: true
          description: The unique identifier of the plan planUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the plan to update.
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: The name of the test plan
                  required: false
                description:
                  type: string
                  description: The description of the test plan
                  required: false
                milestone_id:
                  type: integer
                  description: The ID of the milestone to link to the test plan
                  required: false
        responses:
          '200':
            description: The plan was successfully updated. In addition to the properties below, the response will also include the count of statuses in the plan which can be accessed by forming name as custom_<status_name>_count.
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    assignedto_id:
                      type: integer
                      description: TF does not support this field will be null
                    completed_on:
                      type: integer
                      description: The date/time when the test plan was closed (as UNIX timestamp) / archivedAt
                    created_by:
                      type: string
                      description: The UUID of the user who created the test plan
                    created_on:
                      type: integer
                      description: The date/time when the test plan was created (as UNIX timestamp)
                    description:
                      type: string
                      description: The description of the test plan
                    id:
                      type: integer
                      description: The unique ID of the test plan
                    is_completed:
                      type: boolean
                      description: True if the test plan was closed / archived and false otherwise
                    milestone_id:
                      type: integer
                      description: The first ID from array of the milestones linked to the test plan
                    name:
                      type: string
                      description: The name of the test plan
                    project_id:
                      type: integer
                      description: The ID of the project this test plan belongs to
                    url:
                      type: string
                      description: The address/URL of the test plan in the user interface
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No plan found for the specified ID.
        '500':
          description: Internal server error.


  index.php?/api/v2/add_plan_entry/{plan_id}:
    post:
      tags:
        - Plans
      summary: Adds a plan entry.
      description: |
        Testfairy does not support plan entries. This endpoint will add runs to the plan and runs will be grouped into 2 entries. 1 entry of the runs with configs and 1 entry of the runs without configs. In addtion to it there is no support for additional enteries the endpoint will add runs accordingly.
      parameters:
        - name: plan_id
          in: path
          required: true
          description: The unique identifier of the plan planUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the plan to add.
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: The name of the test run(s)
                  required: false
                description:
                  type: string
                  description: The description of the test plan
                  required: false
                case_ids:
                  type: array
                  description: An array of case IDs for the custom case selection (Required if include_all is false)
                  required: false
                config_ids:
                  type: array
                  description: An array of configuration IDs used for the test run of the test plan entry
                  required: false
                runs:
                  type: array
                  description: An array of objects describing the test runs of the plan,
                  required: false
                  items:
                    type: object
                    properties:
                      include_all:
                        type: boolean
                        description: True if the test run includes all test cases and false otherwise
                      case_ids:
                        type: array
                        description: The array of IDs of the test cases of the test run
                      config_ids:
                        type: array
                        description: The array of IDs of the configurations of the test run
        responses:
          '200':
            description: The plan entry was successfully added.
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    id:
                      type: string
                      description: The unique ID of the test plan entry
                    name:
                      type: string
                      description: The name of the test run(s)
                    description:
                      type: string
                      description: The description of the test plan
                    include_all:
                      type: boolean
                      description: True if the test run includes all test cases and false otherwise
                    refs:
                      type: string
                      description: Always null as TF does not support this field
                    suite_id:
                      type: integer
                      description: The ID of the suite this test plan entry belongs to
                    runs:
                      type: array
                      description: An array of objects describing the test runs of the plan,
                      required: false
                      items:
                        type: object
                        properties:
                          id:
                            type: integer
                            description: The unique ID of the test run
                          name:
                            type: string
                            description: The name of the test run
                          description:
                            type: string
                            description: The description of the test run
                          include_all:
                            type: boolean
                            description: True if the test run includes all test cases and false otherwise
                          suite_id:
                            type: integer
                            description: The ID of the suite this test run belongs to
                          url:
                            type: string
                            description: The address/URL of the test run in the user interface
                          assignedto_id:
                            type: integer
                            description: The ID of the user the entire test run is assigned to
                          config_ids:
                            type: array
                            description: The array of IDs of the configurations of the test run
                          milestone_id:
                            type: integer
                            description: The ID of the milestone this test run belongs to
                          project_id:
                            type: integer
                            description: The ID of the project this test run belongs to
                          plan_id:
                            type: integer
                            description: The ID of the plan this test run belongs to
                          entry_id:
                            type: integer
                            description: The ID of the plan entry this test run belongs to
                          config:
                            type: string
                            description: The name of the configuration this test run belongs to
                          created_on:
                            type: integer
                            description: The date/time when the test run was created (as UNIX timestamp)
                          created_by:
                            type: string
                            description: The UUID of the user who created the test run
                          refs:
                            type: string
                            description: Always null as TF does not support this field
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No plan found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/add_run_to_plan_entry/{plan_id}/{entry_id}:
    post:
      tags:
        - Plans
      summary: Adds a run to a plan entry.
      description: |
        Adds a run to a plan entry.
      parameters:
        - name: plan_id
          in: path
          required: true
          description: The unique identifier of the plan planUid in TF.
          schema:
            type: integer
        - name: entry_id
          in: path
          required: true
          description: The unique identifier of the plan entry entryUid in TF.
          schema:
            type: string
      requestBody:
        required: true
        description: The details of the run to add.
        content:
          application/json:
            schema:
              type: object
              properties:
                config_ids:
                  type: array
                  description: An array of configuration IDs used for the test run of the test plan entry
                  required: true
                description:
                  type: string
                  description: The description of the test run
                  required: false
                include_all:
                  type: boolean
                  description: True for including all test cases of the test suite and false for a custom case selection
                  required: false
                case_ids:
                  type: array
                  description: An array of case IDs for the custom case selection
                  required: false
        responses:
          '200':
            description: The run was successfully added to the plan entry.
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    assignedto_id:
                      type: integer
                      description: TF does not support this field will be null
                    completed_on:
                      type: integer
                      description: The date/time when the test plan was closed (as UNIX timestamp) / archivedAt
                    created_by:
                      type: string
                      description: The UUID of the user who created the test plan
                    created_on:
                      type: integer
                      description: The date/time when the test plan was created (as UNIX timestamp)
                    description:
                      type: string
                      description: The description of the test plan
                    entries:
                      type: array
                      description: An array of 'entries', i.e. group of test runs
                    id:
                      type: integer
                      description: The unique ID of the test plan
                    is_completed:
                      type: boolean
                      description: True if the test plan was closed / archived and false otherwise
                    milestone_id:
                      type: integer
                      description: The first ID from array of the milestones linked to the test plan
                    name:
                      type: string
                      description: The name of the test plan
                    project_id:
                      type: integer
                      description: The ID of the project this test plan belongs to
                    url:
                      type: string
                      description: The address/URL of the test plan in the user interface
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No plan found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/update_plan_entry/{plan_id}/{entry_id}:
    post:
      tags:
        - Plans
      summary: Updates a plan entry.
      description: |
        Updates a plan entry.
      parameters:
        - name: plan_id
          in: path
          required: true
          description: The unique identifier of the plan planUid in TF.
          schema:
            type: integer
        - name: entry_id
          in: path
          required: true
          description: The unique identifier of the plan entry entryUid in TF.
          schema:
            type: string
      requestBody:
        required: true
        description: The details of the plan entry to update.
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: The name of the test run(s)
                  required: false
                description:
                  type: string
                  description: The description of the test plan
                  required: false
                case_ids:
                  type: array
                  description: An array of case IDs for the custom case selection
                  required: false
                include_all:
                  type: boolean
                  description: True for including all test cases of the test suite and false for a custom case selection
                config_ids:
                  type: array
                  description: An array of configuration IDs used for the test run of the test plan entry
                  required: false
        responses:
          '200':
            description: The plan entry was successfully updated.
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    id:
                      type: string
                      description: The unique ID of the test plan entry
                    name:
                      type: string
                      description: The name of the test run(s)
                    description:
                      type: string
                      description: The description of the test plan
                    include_all:
                      type: boolean
                      description: True if the test run includes all test cases and false otherwise
                    refs:
                      type: string
                      description: Always null as TF does not support this field
                    suite_id:
                      type: integer
                      description: The ID of the suite this test plan entry belongs to
                    runs:
                      type: array
                      description: An array of objects describing the test runs of the plan,
                      required: false
                      items:
                        type: object
                        properties:
                          id:
                            type: integer
                            description: The unique ID of the test run
                          name:
                            type: string
                            description: The name of the test run
                          description:
                            type: string
                            description: The description of the test run
                          include_all:
                            type: boolean
                            description: True if the test run includes all test cases and false otherwise
                          suite_id:
                            type: integer
                            description: The ID of the suite this test run belongs to
                          url:
                            type: string
                            description: The address/URL of the test run in the user interface
                          assignedto_id:
                            type: integer
                            description: The ID of the user the entire test run is assigned to
                          config_ids:
                            type: array
                            description: The array of IDs of the configurations of the test run
                          milestone_id:
                            type: integer
                            description: The ID of the milestone this test run belongs to
                          project_id:
                            type: integer
                            description: The ID of the project this test run belongs to
                          plan_id:
                            type: integer
                            description: The ID of the plan this test run belongs to
                          entry_id:
                            type: integer
                            description: The ID of the plan entry this test run belongs to
                          config:
                            type: string
                            description: The name of the configuration this test run belongs to
                          created_on:
                            type: integer
                            description: The date/time when the test run was created (as UNIX timestamp)
                          created_by:
                            type: string
                            description: The UUID of the user who created the test run
                          refs:
                            type: string
                            description: Always null as TF does not support this field
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No plan found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/update_run_in_plan_entry/{run_id}:
    post:
      tags:
        - Plans
      summary: Updates a run in a plan entry.
      description: |
        Updates a run in a plan entry.
      parameters:
        - name: run_id
          in: path
          required: true
          description: The unique identifier of the run runUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the run to update.
        content:
          application/json:
            schema:
              type: object
              properties:
                case_ids:
                  type: array
                  description: An array of case IDs for the custom case selection
                  required: false
                description:
                  type: string
                  description: The description of the test run
                  required: false
                include_all:
                  type: boolean
                  description: True for including all test cases of the test suite and false for a custom case selection
                  required: false
        responses:
          '200':
            description: The run was successfully updated.
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    assignedto_id:
                      type: integer
                      description: TF does not support this field will be null
                    completed_on:
                      type: integer
                      description: The date/time when the test plan was closed (as UNIX timestamp) / archivedAt
                    created_by:
                      type: string
                      description: The UUID of the user who created the test plan
                    created_on:
                      type: integer
                      description: The date/time when the test plan was created (as UNIX timestamp)
                    description:
                      type: string
                      description: The description of the test plan
                    entries:
                      type: array
                      description: An array of 'entries', i.e. group of test runs
                    id:
                      type: integer
                      description: The unique ID of the test plan
                    is_completed:
                      type: boolean
                      description: True if the test plan was closed / archived and false otherwise
                    milestone_id:
                      type: integer
                      description: The first ID from array of the milestones linked to the test plan
                    name:
                      type: string
                      description: The name of the test plan
                    project_id:
                      type: integer
                      description: The ID of the project this test plan belongs to
                    url:
                      type: string
                      description: The address/URL of the test plan in the user interface
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No run found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/close_plan/{plan_id}:
    post:
      tags:
        - Plans
      summary: Closes a plan.
      description: |
        Closes a plan.
      parameters:
        - name: plan_id
          in: path
          required: true
          description: The unique identifier of the plan planUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The plan was successfully closed / archived.
          content:
            application/json:
              schema:
                type: object
                properties:
                  assignedto_id:
                    type: integer
                    description: TF does not support this field will be null
                  completed_on:
                    type: integer
                    description: The date/time when the test plan was closed (as UNIX timestamp) / archivedAt
                  created_by:
                    type: string
                    description: The UUID of the user who created the test plan
                  created_on:
                    type: integer
                    description: The date/time when the test plan was created (as UNIX timestamp)
                  description:
                    type: string
                    description: The description of the test plan
                  entries:
                    type: array
                    description: An array of 'entries', i.e. group of test runs
                  id:
                    type: integer
                    description: The unique ID of the test plan
                  is_completed:
                    type: boolean
                    description: True if the test plan was closed / archived and false otherwise
                  milestone_id:
                    type: integer
                    description: The first ID from array of the milestones linked to the test plan
                  name:
                    type: string
                    description: The name of the test plan
                  project_id:
                    type: integer
                    description: The ID of the project this test plan belongs to
                  url:
                    type: string
                    description: The address/URL of the test plan in the user interface
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No plan found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/delete_plan/{plan_id}:
    post:
      tags:
        - Plans
      summary: Deletes a plan.
      description: |
        Deletes a plan.
      parameters:
        - name: plan_id
          in: path
          required: true
          description: The unique identifier of the plan planUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The plan was successfully deleted.
          content:
            application/json:
              schema:
                type: null
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No plan found for the specified ID.
        '500':
          description: Internal server error.


  index.php?/api/v2/delete_plan_entry/{plan_id}/{entry_id}:
    post:
      tags:
        - Plans
      summary: Deletes a plan entry.
      description: |
        Deletes a plan entry.
      parameters:
        - name: plan_id
          in: path
          required: true
          description: The unique identifier of the plan planUid in TF.
          schema:
            type: integer
        - name: entry_id
          in: path
          required: true
          description: Id of entry in plan
          schema:
            type: string
      responses:
        '200':
          description: The plan entry was successfully deleted.
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No plan found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/delete_run_from_plan_entry/{run_id}:
    post:
      tags:
        - Plans
      summary: Deletes a run from a plan entry.
      description: |
        Deletes a run from a plan entry.
      parameters:
        - name: run_id
          in: path
          required: true
          description: The unique identifier of the run runUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The run was successfully deleted from the plan entry.
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No run found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/get_milestone/{milestone_id}:
    get:
      tags:
        - Milestones
      summary: Gets a milestone.
      description: |
        Gets a milestone.
      parameters:
        - name: milestone_id
          in: path
          required: true
          description: The unique identifier of the milestone milestoneUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The milestone was successfully retrieved.
          content:
            application/json:
              schema:
                type: object
                properties:
                  completed_on:
                    type: integer
                    description: The date/time when the milestone was marked as completed (as UNIX timestamp)
                  description:
                    type: string
                    description: The description of the milestone
                  due_on:
                    type: integer
                    description: The due date/time of the milestone (as UNIX timestamp)
                  id:
                    type: integer
                    description: The unique ID of the milestone
                  is_completed:
                    type: boolean
                    description: True if the milestone is marked as started and false otherwise
                  is_started:
                    type: boolean
                    description: True if the milestone is marked as started and false otherwise
                  milestones:
                    type: array
                    description: The sub milestones that belong to the milestone (if any); only available with get_milestone — requires TestRail 5.3 or later
                  name:
                    type: string
                    description: The name of the milestone
                  parent_id:
                    type: integer
                    description: The ID of the parent milestone the milestone belongs to (if any)
                  project_id:
                    type: integer
                    description: The ID of the project the milestone belongs to
                  refs:
                    type: string
                    description: TF does not support this field will be null
                  start_on:
                    type: integer
                    description: The scheduled start date/time of the milestone (as UNIX timestamp)
                  started_on:
                    type: integer
                    description: it will be same as start_on
                  url:
                    type: string
                    description: The address/URL of the milestone in the user interface
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No milestone found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/get_milestones/{project_id}:
    get:
      tags:
        - Milestones
      summary: Gets milestones.
      description: |
        Gets milestones.
      parameters:
        - name: project_id
          in: path
          required: true
          description: The unique identifier of the project projectUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The milestones were successfully retrieved.
          content:
            application/json:
              schema:
                type: object
                properties:
                  limit:
                    type: integer
                    description: The number of milestones to return
                  offset:
                    type: integer
                    description: The offset of the milestones to return
                  size:
                    type: integer
                    description: The total number of milestones
                  _links:
                    type: object
                    properties:
                      next:
                        type: string
                        description: The address/URL of the next page of milestones
                      prev:
                        type: string
                        description: The address/URL of the previous page of milestones
                  milestones:
                    type: array
                    description: An array of 'milestones', i.e. group of milestones
                    items:
                      type: object
                      properties:
                        completed_on:
                          type: integer
                          description: The date/time when the milestone was marked as completed (as UNIX timestamp)
                        description:
                          type: string
                          description: The description of the milestone
                        due_on:
                          type: integer
                          description: The due date/time of the milestone (as UNIX timestamp)
                        id:
                          type: integer
                          description: The unique ID of the milestone
                        is_completed:
                          type: boolean
                          description: True if the milestone is marked as started and false otherwise
                        is_started:
                          type: boolean
                          description: True if the milestone is marked as started and false otherwise
                        milestones:
                          type: array
                          description: The sub milestones that belong to the milestone (if any); only available with get_milestone — requires TestRail 5.3 or later
                        name:
                          type: string
                          description: The name of the milestone
                        parent_id:
                          type: integer
                          description: The ID of the parent milestone the milestone belongs to (if any)
                        project_id:
                          type: integer
                          description: The ID of the project the milestone belongs to
                        refs:
                          type: string
                          description: TF does not support this field will be null
                        start_on:
                          type: integer
                          description: The scheduled start date/time of the milestone (as UNIX timestamp)
                        started_on:
                          type: integer
                          description: it will be same as start_on
                        url:
                          type: string
                          description: The address/URL of the milestone in the user interface
  
  index.php?/api/v2/add_milestone/{project_id}:
    post:
      tags:
        - Milestones
      summary: Adds a milestone.
      description: |
        Adds a milestone.
      parameters:
        - name: project_id
          in: path
          required: true
          description: The unique identifier of the project projectUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the milestone to add.
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: The name of the milestone
                description:
                  type: string
                  description: The description of the milestone
                due_on:
                  type: integer
                  description: The due date of the milestone (as UNIX timestamp)
                parent_id:
                  type: integer
                  description: The ID of the parent milestone, if any (for sub-milestones)
                start_on:
                  type: integer
                  description: The scheduled start date of the milestone (as UNIX timestamp)
      responses:
        '200':
          description: The milestone was successfully added.
          content:
            application/json:
              schema:
                type: object
                properties:
                  completed_on:
                    type: integer
                    description: The date/time when the milestone was marked as completed (as UNIX timestamp)
                  description:
                    type: string
                    description: The description of the milestone
                  due_on:
                    type: integer
                    description: The due date/time of the milestone (as UNIX timestamp)
                  id:
                    type: integer
                    description: The unique ID of the milestone
                  is_completed:
                    type: boolean
                    description: True if the milestone is marked as started and false otherwise
                  is_started:
                    type: boolean
                    description: True if the milestone is marked as started and false otherwise
                  milestones:
                    type: array
                    description: The sub milestones that belong to the milestone (if any); only available with get_milestone — requires TestRail 5.3 or later
                  name:
                    type: string
                    description: The name of the milestone
                  parent_id:
                    type: integer
                    description: The ID of the parent milestone the milestone belongs to (if any)
                  project_id:
                    type: integer
                    description: The ID of the project the milestone belongs to
                  refs:
                    type: string
                    description: TF does not support this field will be null
                  start_on:
                    type: integer
                    description: The scheduled start date/time of the milestone (as UNIX timestamp)
                  started_on:
                    type: integer
                    description: it will be same as start_on
                  url:
                    type: string
                    description: The address/URL of the milestone in the user interface
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No milestone found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/update_milestone/{milestone_id}:
    post:
      tags:
        - Milestones
      summary: Updates a milestone.
      description: |
        Updates a milestone.
      parameters:
        - name: milestone_id
          in: path
          required: true
          description: The unique identifier of the milestone milestoneUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the milestone to update.
        content:
          application/json:
            schema:
              type: object
              properties:
                is_completed:
                  type: boolean
                  description: True if a milestone is considered completed and false otherwise
                is_started:
                  type: boolean
                  description: True if a milestone is considered started and false otherwise
                parent_id:
                  type: integer
                  description: The ID of the parent milestone, if any (for sub-milestones)
                start_on:
                  type: integer
                  description: The scheduled start date of the milestone (as UNIX timestamp)
      responses:
        '200':
          description: The milestone was successfully updated.
          content:
            application/json:
              schema:
                type: object
                properties:
                  completed_on:
                    type: integer
                    description: The date/time when the milestone was marked as completed (as UNIX timestamp)
                  description:
                    type: string
                    description: The description of the milestone
                  due_on:
                    type: integer
                    description: The due date/time of the milestone (as UNIX timestamp)
                  id:
                    type: integer
                    description: The unique ID of the milestone
                  is_completed:
                    type: boolean
                    description: True if the milestone is marked as started and false otherwise
                  is_started:
                    type: boolean
                    description: True if the milestone is marked as started and false otherwise
                  milestones:
                    type: array
                    description: The sub milestones that belong to the milestone (if any); only available with get_milestone — requires TestRail 5.3 or later
                  name:
                    type: string
                    description: The name of the milestone
                  parent_id:
                    type: integer
                    description: The ID of the parent milestone the milestone belongs to (if any)
                  project_id:
                    type: integer
                    description: The ID of the project the milestone belongs to
                  refs:
                    type: string
                    description: TF does not support this field will be null
                  start_on:
                    type: integer
                    description: The scheduled start date/time of the milestone (as UNIX timestamp)
                  started_on:
                    type: integer
                    description: it will be same as start_on
                  url:
                    type: string
                    description: The address/URL of the milestone in the user interface
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No milestone found for the specified ID.
        '500':
          description: Internal server error.
  index.php?/api/v2/delete_milestone/{milestone_id}:
    post:
      tags:
        - Milestones
      summary: Deletes a milestone.
      description: |
        Deletes a milestone.
      parameters:
        - name: milestone_id
          in: path
          required: true
          description: The unique identifier of the milestone milestoneUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The milestone was successfully deleted.
          content:
            application/json:
              schema:
                type: null
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No milestone found for the specified ID.
        '500':
          description: Internal server error.


  index.php?/api/v2/get_test/{test_id}:
    get:
      tags:
        - Tests
      summary: Gets a test.
      description: |
        Gets a test.
      parameters:
        - name: test_id
          in: path
          required: true
          description: The unique identifier of the test testUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The test was successfully retrieved.
          content:
            application/json:
              schema:
                type: object
                properties:
                  assigned_to_id:
                    type: integer
                    description: TF does not support this field will be null
                  case_id:
                    type: integer
                    description: TF does not support this field will be null
                  id:
                    type: integer
                    description: The unique ID of the test
                  milestone_id:
                    type: integer
                    description: The ID of the milestone that is linked to the test case
                  priority_id:
                    type: integer
                    description: The ID of the priority that is linked to the test case
                  refs:
                    type: string
                    description: A comma-separated list of references/requirements that are linked to the test case
                  run_id:
                    type: integer
                    description: The ID of the test run the test belongs to
                  status_id:
                    type: integer
                    description: The ID of the current status of the test, also see get_statuses
                  title:
                    type: string
                    description: The title of the related test case
                  type_id:
                    type: integer
                    description: The ID of the first test case tag that is linked to the test case

        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No test found for the specified ID.
        '500':
          description: Internal server error.
          

  index.php?/api/v2/get_tests/{run_id}:
    get:
      tags:
        - Tests
      summary: Gets tests.
      description: |
        Gets tests.
      parameters:
        - name: run_id
          in: path
          required: true
          description: The unique identifier of the run runUid in TF.
          schema:
            type: integer

        - name: status_id
          in: query
          required: false
          description: A comma-separated list of status IDs to filter by
          schema:
            type: string
        - name: limit
          in: query
          required: false
          description: The number that sets the limit of tests to be shown on the response (Optional parameter. The response size limit is 250 by default) – requires TestRail 6.7 or later
          schema:
            type: integer
        - name: offset
          in: query
          required: false
          description: The number that sets the position where the response should start from (Optional parameter) – requires TestRail 6.7 or later
          schema:schema:
            type: integer
      responses:
        '200':
          description: The tests were successfully retrieved.
          content:
            application/json:
              schema:
                type: object
                properties:
                  limit:
                    type: integer
                    description: The number of tests to return
                  offset:
                    type: integer
                    description: The offset of the tests to return
                  size:
                    type: integer
                    description: The total number of tests
                  _links:
                    type: object
                    properties:
                      next:
                        type: string
                        description: The address/URL of the next page of tests
                      prev:
                        type: string
                        description: The address/URL of the previous page of tests
                  tests:
                    type: array
                    description: An array of 'tests', i.e. group of tests
                    items:
                      type: object
                      properties:
                        assigned_to_id:
                          type: integer
                          description: TF does not support this field will be null
                        case_id:
                          type: integer
                          description: TF does not support this field will be null
                        id:
                          type: integer
                          description: The unique ID of the test
                        milestone_id:
                          type: integer
                          description: The ID of the milestone that is linked to the test case
                        priority_id:
                          type: integer
                          description: The ID of the priority that is linked to the test case
                        refs:
                          type: string
                          description: A comma-separated list of references/requirements that are linked to the test case
                        run_id:
                          type: integer
                          description: The ID of the test run the test belongs to
                        status_id:
                          type: integer
                          description: The ID of the current status of the test, also see get_statuses
                        title:
                          type: string
                          description: The title of the related test case
                        type_id:
                          type: integer
                          description: The ID of the first test case tag that is linked to the test case

        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No test found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/get_results/{test_id}:
    get:
      tags:
        - Results
      summary: Gets results.
      description: |
        Gets results.
      parameters:
        - name: test_id
          in: path
          required: true
          description: The unique identifier of the test testUid in TF.
          schema:
            type: integer
        - name: limit
          in: query
          required: false
          description: The number that sets the limit of results to be shown on the response (Optional parameter. The response size limit is 250 by default) – requires TestRail 6.7 or later
          schema:
            type: integer
        - name: offset
          in: query
          required: false
          description: The number that sets the position where the response should start from (Optional parameter) – requires TestRail 6.7 or later
          schema:
            type: integer
      responses:
        '200':
          description: The results were successfully retrieved.
          content:
            application/json:
              schema:
                type: object
                properties:
                  limit:
                    type: integer
                    description: The number of results to return
                  offset:
                    type: integer
                    description: The offset of the results to return
                  size:
                    type: integer
                    description: The total number of results
                  _links:
                    type: object
                    properties:
                      next:
                        type: string
                        description: The address/URL of the next page of results
                      prev:
                        type: string
                        description: The address/URL of the previous page of results
                  results:
                    type: array
                    description: An array of 'results', i.e. group of results
                    items:
                      type: object
                      properties:
                        assignedto_id:
                          type: integer
                          description: TF does not support this field will be null
                        comment:
                          type: string
                          description: The comment or error message of the test result
                        created_by:
                          type: string
                          description: The ID of the user who created the test result
                        created_on:
                          type: integer
                          description: The date/time when the test result was created (as UNIX timestamp)
                        defects:
                          type: string
                          description: Null right now
                        id:
                          type: integer
                          description: The unique ID of the test result
                        status_id:
                          type: integer
                          description: The status of the test result, e.g. passed or failed, also see get_statuses
                        test_id:
                          type: integer
                          description: The ID of the test this test result belongs to
          '400':
            description: Invalid input, missing parameters, or incorrect format.
          '404':
            description: No results found for the specified ID.
          '500':
            description: Internal server error.

  index.php?/api/v2/get_results_for_case/{run_id}/{case_id}:
    get:
      tags:
        - Results
      summary: Gets results for a case.
      description: |
        Gets results for a case.
      parameters:
        - name: run_id
          in: path
          required: true
          description: The unique identifier of the run runUid in TF.
          schema:
            type: integer
        - name: case_id
          in: path
          required: true
          description: The unique identifier of the case caseUid in TF.
          schema:
            type: integer
        - name: limit
          in: query
          required: false
          description: The number that sets the limit of results to be shown on the response (Optional parameter. The response size limit is 250 by default) – requires TestRail 6.7 or later
          schema:
            type: integer
        - name: offset
          in: query
          required: false
          description: The number that sets the position where the response should start from (Optional parameter) – requires TestRail 6.7 or later
          schema:
            type: integer
      responses:
        '200':
          description: The results were successfully retrieved.
        content:
          application/json:
            schema:
              type: object
              properties:
                limit:
                  type: integer
                  description: The number of results to return
                offset:
                  type: integer
                  description: The offset of the results to return
                size:
                  type: integer
                  description: The total number of results
                _links:
                  type: object
                  properties:
                    next:
                      type: string
                      description: The address/URL of the next page of results
                    prev:
                      type: string
                      description: The address/URL of the previous page of results
                results:
                  type: array
                  description: An array of 'results', i.e. group of results
                  items:
                    type: object
                    properties:
                      assignedto_id:
                        type: integer
                        description: TF does not support this field will be null
                      comment:
                        type: string
                        description: The comment or error message of the test result
                      created_by:
                        type: string
                        description: The ID of the user who created the test result
                      created_on:
                        type: integer
                        description: The date/time when the test result was created (as UNIX timestamp)
                      defects:
                        type: string
                        description: Null right now
                      id:
                        type: integer
                        description: The unique ID of the test result
                      status_id:
                        type: integer
                        description: The status of the test result, e.g. passed or failed, also see get_statuses
                      test_id:
                        type: integer
                        description: The ID of the test this test result belongs to
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No results found for the specified ID.
        '500':
          description: Internal server error.


  index.php?/api/v2/get_results_for_run/{run_id}:
    get:
      tags:
        - Results
      summary: Gets results for a run.
      description: |
        Gets results for a run.
      parameters:
        - name: run_id
          in: path
          required: true
          description: The unique identifier of the run runUid in TF.
          schema:
            type: integer
        - name: limit
          in: query
          required: false
          description: The number that sets the limit of results to be shown on the response (Optional parameter. The response size limit is 250 by default) – requires TestRail 6.7 or later
          schema:
            type: integer
        - name: offset
          in: query
          required: false
          description: The number that sets the position where the response should start from (Optional parameter) – requires TestRail 6.7 or later
          schema:
            type: integer
        - name: created_after
          in: query
          required: false
          description: Only return test results created after this date (as UNIX timestamp)
          schema:
            type: integer
        - name: created_before
          in: query
          required: false
          description: Only return test results created before this date (as UNIX timestamp)
          schema:
            type: integer
        - name: created_by
          in: query
          required: false
          description: A comma-separated list of creators (user IDs) to filter by
          schema:
            type: string
        - name: status_id
          in: query
          required: false
          description: A comma-separated list of status IDs to filter by
          schema:
            type: string
      responses:
        '200':
          description: The results were successfully retrieved.
          content:
            application/json:
              schema:
                type: object
                properties:
                  limit:
                    type: integer
                    description: The number of results to return
                  offset:
                    type: integer
                    description: The offset of the results to return
                  size:
                    type: integer
                    description: The total number of results
                  _links:
                    type: object
                    properties:
                      next:
                        type: string
                        description: The address/URL of the next page of results
                      prev:
                        type: string
                        description: The address/URL of the previous page of results
                  results:
                    type: array
                    description: An array of 'results', i.e. group of results
                    items:
                      type: object
                      properties:
                        assignedto_id:
                          type: integer
                          description: TF does not support this field will be null
                        comment:
                          type: string
                          description: The comment or error message of the test result
                        created_by:
                          type: string
                          description: The ID of the user who created the test result
                        created_on:
                          type: integer
                          description: The date/time when the test result was created (as UNIX timestamp)
                        defects:
                          type: string
                          description: Null right now
                        id:
                          type: integer
                          description: The unique ID of the test result
                        status_id:
                          type: integer
                          description: The status of the test result, e.g. passed or failed, also see get_statuses
                      test_id:
                        type: integer
                        description: The ID of the test this test result belongs to
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No results found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/add_result/{test_id}:
    post:
      tags:
        - Results
      summary: Adds a result.
      description: |
        Adds a result.
      parameters:
        - name: test_id
          in: path
          required: true
          description: The unique identifier of the test testUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the result to add.
        content:
          application/json:
            schema:
              type: object
              properties:
                status_id:
                  type: integer
                  description: The ID of the test status.
                comment:
                  type: string
                  description: The comment/description for the test result
      responses:
        '200':
          description: The result was successfully added.
          content:
            application/json:
              schema:
                type: object
                properties:
                  assignedto_id:
                    type: integer
                    description: TF does not support this field will be null
                  comment:
                    type: string
                    description: The comment or error message of the test result
                  created_by:
                    type: string
                    description: The ID of the user who created the test result
                  created_on:
                    type: integer
                    description: The date/time when the test result was created (as UNIX timestamp)
                  defects:
                    type: string
                    description: Null right now
                  id:
                    type: integer
                    description: The unique ID of the test result
                  status_id:
                    type: integer
                    description: The status of the test result, e.g. passed or failed, also see get_statuses
                  test_id:
                    type: integer
                    description: The ID of the test this test result belongs to
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No test found for the specified ID.
        '500':
          description: Internal server error.


  index.php?/api/v2/add_result_for_case/{run_id}/{case_id}:
    post:
      tags:
        - Results
      summary: Adds a result for a case.
      description: |
        Adds a result for a case.
      parameters:
        - name: run_id
          in: path
          required: true
          description: The unique identifier of the run runUid in TF.
          schema:
            type: integer
        - name: case_id
          in: path
          required: true
          description: The unique identifier of the case caseUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the result to add.
        content:
          application/json:
            schema:
              type: object
              properties:
                status_id:
                  type: integer
                  description: The ID of the test status.
                comment:
                  type: string
                  description: The comment/description for the test result
      responses:
        '200':
          description: The result was successfully added.
          content:
            application/json:
              schema:
                type: object
                properties:
                  assignedto_id:
                    type: integer
                    description: TF does not support this field will be null
                  comment:
                    type: string
                    description: The comment or error message of the test result
                  created_by:
                    type: string
                    description: The ID of the user who created the test result
                  created_on:
                    type: integer
                    description: The date/time when the test result was created (as UNIX timestamp)
                  defects:
                    type: string
                    description: Null right now
                  id:
                    type: integer
                    description: The unique ID of the test result
                  status_id:
                    type: integer
                    description: The status of the test result, e.g. passed or failed, also see get_statuses
                  test_id:
                    type: integer
                    description: The ID of the test this test result belongs to
        '400':
          description: Invalid input, missing parameters, or incorrect format.
        '404':
          description: No test found for the specified ID.
        '500':
          description: Internal server error.

  index.php?/api/v2/add_results/{run_id}:
    post:
      tags:
        - Results
      summary: Adds results.
      description: |
        Adds results.
      parameters:
        - name: run_id
          in: path
          required: true
          description: The unique identifier of the run runUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the results to add.
        content:
          application/json:
            schema:
              type: object
              properties:
                results:
                  type: array
                  description: An array of 'results', i.e. group of results
                  items:
                    type: object
                    properties:
                      test_id:
                        type: integer
                        description: The ID of the test this test result belongs to
                        required: true
                      status_id:
                        type: integer
                        description: The ID of the test status.
                      comment:
                        type: string
                        description: The comment/description for the test result
      responses:
        '200':
          description: The results were successfully added.
          content:
            application/json:
              schema:
                type: object
                properties:
                  limit:
                    type: integer
                    description: The number of results to return
                  offset:
                    type: integer
                    description: The offset of the results to return
                  size:
                    type: integer
                    description: The total number of results
                  _links:
                    type: object
                    properties:
                      next:
                        type: string
                        description: The address/URL of the next page of results
                      prev:
                        type: string
                        description: The address/URL of the previous page of results
                  results:
                    type: array
                    description: An array of 'results', i.e. group of results
                    items:
                      type: object
                  properties:
                    assignedto_id:
                      type: integer
                      description: TF does not support this field will be null
                    comment:
                      type: string
                      description: The comment or error message of the test result
                    created_by:
                      type: string
                      description: The ID of the user who created the test result
                    created_on:
                      type: integer
                      description: The date/time when the test result was created (as UNIX timestamp)
                    defects:
                      type: string
                      description: Null right now
                    id:
                      type: integer
                      description: The unique ID of the test result
                    status_id:
                      type: integer
                      description: The status of the test result, e.g. passed or failed, also see get_statuses
                    test_id:
                      type: integer
                      description: The ID of the test this test result belongs to
          '400':
            description: Invalid input, missing parameters, or incorrect format.
          '404':
            description: No test found for the specified ID.
          '500':
            description: Internal server error.


  index.php?/api/v2/add_results_for_cases/{run_id}:
    post:
      tags:
        - Results
      summary: Adds results for cases.
      description: |
        Adds results for cases.
      parameters:
        - name: run_id
          in: path
          required: true
          description: The unique identifier of the run runUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the results to add.
        content:
          application/json:
            schema:
              type: object
              properties:
                results:
                  type: array
                  description: An array of 'results', i.e. group of results
                  items:
                    type: object
                    properties:
                      case_id:
                        type: integer
                        description: The ID of the case this test result belongs to
                        required: true
                      status_id:
                        type: integer
                        description: The ID of the test status.
                      comment:
                        type: string
                        description: The comment/description for the test result

      responses:
        '200':
          description: The results were successfully added.
          content:
            application/json:
              schema:
                type: object
                properties:
                  limit:
                    type: integer
                    description: The number of results to return
                  offset:
                    type: integer
                    description: The offset of the results to return
                  size:
                    type: integer
                    description: The total number of results
                  _links:
                    type: object
                    properties:
                      next:
                        type: string
                        description: The address/URL of the next page of results
                      prev:
                        type: string
                        description: The address/URL of the previous page of results
                  results:
                    type: array
                    description: An array of 'results', i.e. group of results
                    items:
                      type: object
                  properties:
                    assignedto_id:
                      type: integer
                      description: TF does not support this field will be null
                    comment:
                      type: string
                      description: The comment or error message of the test result
                    created_by:
                      type: string
                      description: The ID of the user who created the test result
                    created_on:
                      type: integer
                      description: The date/time when the test result was created (as UNIX timestamp)
                    defects:
                      type: string
                      description: Null right now
                    id:
                      type: integer
                      description: The unique ID of the test result
                    status_id:
                      type: integer
                      description: The status of the test result, e.g. passed or failed, also see get_statuses
                    test_id:
                      type: integer
                      description: The ID of the test this test result belongs to
          '400':
            description: Invalid input, missing parameters, or incorrect format.
          '404':
            description: No test found for the specified ID.
          '500':
            description: Internal server error.

  index.php?/api/v2/get_case_types:
    get:
      tags:
        - Case Types
      summary: Gets case types.
      description: |
        Gets case types.
      responses:
        '200':
          description: The case types were successfully retrieved.
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: integer
                      description: The unique ID of the case type
                    name:
                      type: string
                      description: The name of the case type
                    is_default:
                      type: boolean
                      description: always false
          '400':
            description: Invalid input, missing parameters, or incorrect format.
          '404':
            description: No case types found.
          '500':
            description: Internal server error.

  index.php?/api/v2/get_priorities:
    get:
      tags:
        - Priorities
      summary: Gets priorities.
      description: |
        Gets priorities.
      responses:
        '200':
          description: The priorities were successfully retrieved.
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: integer
                      description: The unique ID of the priority
                    name:
                      type: string
                      description: The name of the priority
                    is_default:
                      type: boolean
                      description: always false
                    priority:
                      type: integer
                      description: The priority level same as id
                    short_name:
                      type: string
                      description: The short name of the priority
          '400':
            description: Invalid input, missing parameters, or incorrect format.
          '404':
            description: No priorities found.
          '500':
            description: Internal server error.


  index.php?/api/v2/get_statuses:
    get:
      tags:
        - Statuses
      summary: Gets statuses.
      description: |
        Gets statuses.
      responses:
        '200':
          description: The statuses were successfully retrieved.
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    color_bright:
                      type: integer
                      description: The bright color of the status same as medium color
                    color_dark:
                      type: integer
                      description: The dark color of the status same as medium color
                    color_medium:
                      type: integer
                      description: The medium color of the status
                    id:
                      type: integer
                      description: The unique ID of the status
                    is_final:
                      type: boolean
                      description: Whether the status is final
                    is_system:
                      type: boolean
                      description: always false
                    is_untested:
                      type: boolean
                      description: Whether the status is untested
                    label:
                      type: string
                      description: The label of the status
                    name:
                      type: string

  index.php?/api/v2/get_templates/{project_id}:
    get:
      tags:
        - Templates
      summary: Gets templates.
      description: |
        Gets templates.
      responses:
        '200':
          description: The templates were successfully retrieved.
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: integer
                      description: The unique ID of the template
                    name:
                      type: string
                      description: The name of the template
                    is_default:
                      type: boolean
                      description: Whether the template is the default template
                    case_fields:
                      type: array
                      description: The fields of the template
                      items:
                        type: object
                        properties:
                          system_name:
                            type: string
                            description: The system name of the field
                          name:
                            type: string
                            description: The name of the field
                          type:
                            type: string
                            description: The type of the field
          '400':
            description: Invalid input, missing parameters, or incorrect format.
          '404':
            description: No templates found for the specified ID.
          '500':
            description: Internal server error.

  index.php?/api/v2/get_configs/{project_id}:
    get:
      tags:
        - Configurations
      summary: Gets configurations.
      description: |
        Gets configurations.
      responses:
        '200':
          description: The configurations were successfully retrieved.
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: integer
                      description: The unique ID of the configuration group
                    name:
                      type: string
                      description: The name of the configuration group
                    project_id:
                      type: integer
                      description: The ID of the project this configuration group belongs to
                    configs:
                      type: array
                      description: The configurations of the configuration group
                      items:
                        type: object
                        properties:
                          id:
                            type: integer
                            description: The unique ID of the configuration
                          name:
                            type: string
                            description: The name of the configuration
                          group_id:
                            type: integer
                            description: The ID of the configuration group this configuration belongs to
          '400':
            description: Invalid input, missing parameters, or incorrect format.
          '404':
            description: No configurations found for the specified ID.
          '500':
            description: Internal server error.

  index.php?/api/v2/add_config_group/{project_id}:
    post:
      tags:
        - Configurations
      summary: Adds a configuration group.
      description: |
        Adds a configuration group.
      parameters:
        - name: project_id
          in: path
          required: true
          description: The unique identifier of the project projectUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the configuration group to add.
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: The name of the configuration group
      responses:
        '200':
          description: The configuration group was successfully added.
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The unique ID of the configuration group
                  name:
                    type: string
                    description: The name of the configuration group
                  project_id:
                    type: integer
                    description: The ID of the project this configuration group belongs to
                  configs:
                    type: array
                    description: The configurations of the configuration group
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          description: The unique ID of the configuration
                          required: true
                        name:
                          type: string
                          description: The name of the configuration
                          required: true
                        group_id:
                          type: integer
                          description: The ID of the configuration group this configuration belongs to
          '400':
            description: Invalid input, missing parameters, or incorrect format.
          '404':
            description: No project found for the specified ID.
          '500':
            description: Internal server error.

  index.php?/api/v2/add_config/{config_group_id}:
    post:
      tags:
        - Configurations
      summary: Adds a configuration.
      description: |
        Adds a configuration.
      parameters:
        - name: config_group_id
          in: path
          required: true
          description: The unique identifier of the configuration group configGroupUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the configuration to add.
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: The name of the configuration

      responses:
        '200':
          description: The configuration was successfully added.
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The unique ID of the configuration
                  name:
                    type: string
                    description: The name of the configuration
                  group_id:
                    type: integer
                    description: The ID of the configuration group this configuration belongs to
          '400':
            description: Invalid input, missing parameters, or incorrect format.
          '404':
            description: No project found for the specified ID.
          '500':
            description: Internal server error.

  index.php?/api/v2/update_config_group/{config_group_id}:
    post:
      tags:
        - Configurations
      summary: Updates a configuration group.
      description: |
        Updates a configuration group.
      parameters:
        - name: config_group_id
          in: path
          required: true
          description: The unique identifier of the configuration group configGroupUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the configuration group to update.
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: The name of the configuration group
      responses:
        '200':
          description: The configuration group was successfully updated.
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The unique ID of the configuration group
                  name:
                    type: string
                    description: The name of the configuration group
                  project_id:
                    type: integer
                    description: The ID of the project this configuration group belongs to
                  configs:
                    type: array
                    description: The configurations of the configuration group
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          description: The unique ID of the configuration
                          required: true
                        name:
                          type: string
                          description: The name of the configuration
                          required: true
                        group_id:
                          type: integer
                          description: The ID of the configuration group this configuration belongs to
                          required: true
          '400':
            description: Invalid input, missing parameters, or incorrect format.
          '404':
            description: No configuration group found for the specified ID.
          '500':

  index.php?/api/v2/update_config/{config_id}:
    post:
      tags:
        - Configurations
      summary: Updates a configuration.
      description: |
        Updates a configurations.
      parameters:
        - name: config_id
          in: path
          required: true
          description: The unique identifier of the configuration configUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the configuration to update.
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Name of the configuration to update
      responses:
        '200':
          description: The configuration was successfully updated.
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The unique ID of the configuration group
                  name:
                    type: string
                    description: The name of the configuration group
                  project_id:
                    type: integer
                    description: The ID of the project this configuration group belongs to
                  configs:
                    type: array
                    description: The configurations of the configuration group
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          description: The unique ID of the configuration
                          required: true
                        name:
                          type: string
                          description: The name of the configuration
                          required: true
                        group_id:
                          type: integer
                          description: The ID of the configuration group this configuration belongs to
                          required: true
          '400':
            description: Invalid input, missing parameters, or incorrect format.
          '404':
            description: No configuration group found for the specified ID.
          '500':

  index.php?/api/v2/delete_config_group/{config_group_id}:
    post:
      tags:
        - Configurations
      summary: Deletes a configuration group.
      description: |
        Deletes a configuration group.
      parameters:
        - name: config_group_id
          in: path
          required: true
          description: The unique identifier of the configuration group configGroupUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The configuration group was successfully deleted.
          content:
            application/json:
              schema:
                type: null
          '400':
            description: Invalid input, missing parameters, or incorrect format.
          '404':
            description: No configuration group found for the specified ID.
          '500':
            description: Internal server error.

  index.php?/api/v2/delete_config/{config_id}:
    post:
      tags:
        - Configurations
      summary: Deletes a configuration.
      description: |
        Deletes a configuration.
      parameters:
        - name: config_id
          in: path
          required: true
          description: The unique identifier of the configuration configUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The configuration was successfully deleted.
          content:
            application/json:
              schema:
                type: null
          '400':
            description: Invalid input, missing parameters, or incorrect format.
          '404':
            description: No configuration found for the specified ID.
          '500':
            description: Internal server error.

  index.php?/api/v2/add_attachment_to_case/{case_id}:
    post:
      tags:
        - Attachments
      summary: Adds an attachment to a case.
      description: |
        Adds an attachment to a case.
      parameters:
        - name: case_id
          in: path
          required: true
          description: The unique identifier of the case caseUid in TF.
          schema:
            type: integer
      requestBody:
        required: true
        description: The details of the attachment to add.
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                attachment:
                  type: string
                  description: The attachment to add
      responses:
        '200':
          description: The attachment was successfully added.
          content:
            application/json:
              schema:
                type: object
                properties:
                  attachment_id:
                    type: string
                    description: The unique ID of the attachment
          '400':
            description: Invalid input, missing parameters, or incorrect format.
          '404':
            description: No case found for the specified ID.
          '500':
            description: Internal server error.


  index.php?/api/v2/get_attachments_for_case/{case_id}:
    get:
      tags:
        - Attachments
      summary: Gets attachments for a case.
      description: |
        Gets attachments for a case.
      parameters:
        - name: case_id
          in: path
          required: true
          description: The unique identifier of the case caseUid in TF.
          schema:
            type: integer
      responses:
        '200':
          description: The attachments were successfully retrieved.
          content:
            application/json:
              schema:
                type: object
                properties:
                  limit:
                    type: integer
                    description: The number of attachments to return
                  offset:
                    type: integer
                    description: The offset of the attachments to return
                  size:
                    type: integer
                    description: The total number of attachments
                  _links:
                    type: object
                    properties:
                      next:
                        type: string
                        description: The address/URL of the next page of attachments
                      prev:
                        type: string
                        description: The address/URL of the previous page of attachments
                  attachments:
                    type: array
                    description: The attachments of the case
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          description: The unique ID of the attachment
                        name:
                          type: string
                          description: The name of the attachment
                        size:
                          type: integer
                          description: The size of the attachment
                        created_on:
                          type: integer
                          description: The date/time when the attachment was created (as UNIX timestamp)
                        project_id:
                          type: integer
                          description: The ID of the project this attachment belongs to
                        case_id:
                          type: integer
                          description: The ID of the case this attachment belongs to
                        user_id:
                          type: integer
                          description: The ID of the user who added the attachment
          '400':
            description: Invalid input, missing parameters, or incorrect format.
          '404':
            description: No case found for the specified ID.
          '500':
            description: Internal server error.
            
            

