export default {
  delete: {
    tags: ['Suites'],
    summary: 'Delete a test suites by suiteId',
    description: 'Delete a test suites by suiteId',
    operationId: 'deleteSuite',
    parameters: [
      {
        name: 'suiteId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        description: 'Test suite has been deleted successfully',
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
