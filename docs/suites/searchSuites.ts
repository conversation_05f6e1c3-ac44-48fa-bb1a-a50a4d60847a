import errors from '@app/constants/errors';

export default {
  get: {
    tags: ['Suites'],
    summary: 'Get a list of test suites by text query',
    description: 'Get a list of test suites by text query',
    operationId: 'searchSuites',
    parameters: [
      {
        name: 'orgId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
      {
        name: 'query',
        in: 'query',
        require: true,
        schema: {
          type: 'string',
        },
      },
    ],
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                suites: {
                  type: 'array',
                  items: {
                    $ref: '#/components/schemas/TestSuites',
                  },
                },
              },
            },
          },
        },
      },
      403: {
        description: errors.PROBLEM_FETCHING_TEST_SUITES,
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
