export default {
  get: {
    tags: ['Suites'],
    summary: 'Get a test suites by suiteId',
    description: 'Get a test runs by suiteId',
    operationId: 'getSuite',
    parameters: [
      {
        name: 'suiteId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/TestSuites',
            },
          },
        },
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
