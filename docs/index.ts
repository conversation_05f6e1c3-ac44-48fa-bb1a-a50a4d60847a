import basicInfo from './basicInfo';
import servers from './servers';
import components from './components';
import tags from './tags';
import runs from './runs';
import plans from './plans';
import projects from './projects';
import suites from './suites';
import data from './data';
import milestones from './milestones';
import cases from './cases';
import folders from './folders';
import executions from './executions';
import step from './step';
import configurations from './configurations';

export default {
  ...basicInfo,
  ...servers,
  ...components,
  ...tags,
  paths: {
    ...data,
    ...runs,
    ...plans,
    ...projects,
    ...suites,
    ...milestones,
    ...cases,
    ...folders,
    ...executions,
    ...step,
    ...configurations,
  },
};
