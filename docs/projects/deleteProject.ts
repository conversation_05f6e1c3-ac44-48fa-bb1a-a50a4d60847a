export default {
  delete: {
    tags: ['Projects'],
    summary: 'Delete a test projects by projectId',
    description: 'Delete a test projects by projectId',
    operationId: 'deleteProject',
    parameters: [
      {
        name: 'projectId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        description: 'Test project has been deleted successfully',
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
