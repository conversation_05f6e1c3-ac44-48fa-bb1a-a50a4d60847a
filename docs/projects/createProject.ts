export default {
  post: {
    tags: ['Projects'],
    summary: 'Create a test projects',
    description: 'Create a test projects',
    operationId: 'createProject',
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            required: ['org', 'externalId', 'source'],
            type: 'object',
            properties: {
              org: {
                type: 'string',
                format: 'uuid',
              },
              externalId: {
                type: 'string',
              },
              source: {
                type: 'string',
              },
              name: {
                type: 'string',
              },
              customFields: {
                type: 'object',
              },
            },
          },
        },
      },
    },
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                ownerUid: {
                  type: 'string',
                  format: 'uuid',
                },
                externalId: {
                  type: 'string',
                },
                source: {
                  type: 'string',
                },
                name: {
                  type: 'string',
                },
                customFields: {
                  type: 'object',
                },
              },
            },
          },
        },
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
