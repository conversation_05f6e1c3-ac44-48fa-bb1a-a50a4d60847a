import errors from '@app/constants/errors';

export default {
  get: {
    tags: ['Projects'],
    summary: 'Get a list of test projects by text query',
    description: 'Get a list of test projects by text query',
    operationId: 'searchProjects',
    parameters: [
      {
        name: 'orgId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
      {
        name: 'query',
        in: 'query',
        require: true,
        schema: {
          type: 'string',
        },
      },
    ],
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                projects: {
                  type: 'array',
                  items: {
                    $ref: '#/components/schemas/TestProjects',
                  },
                },
              },
            },
          },
        },
      },
      403: {
        description: errors.PROBLEM_FETCHING_TEST_PROJECTS,
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
