export default {
  delete: {
    tags: ['Plans'],
    summary: 'Delete a test plans by planId',
    description: 'Delete a test plans by planId',
    operationId: 'deletePlan',
    parameters: [
      {
        name: 'planId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        description: 'Test plan has been deleted successfully',
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
