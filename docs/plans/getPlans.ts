export default {
  get: {
    tags: ['Plans'],
    summary: 'Get a list of test plans',
    description: 'Get a list of test plans',
    operationId: 'getPlans',
    parameters: [
      {
        name: 'orgId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
      {
        name: 'per_page',
        in: 'query',
        description: 'Limit data per page',
        schema: {
          type: 'integer',
        },
      },
      {
        name: 'current_page',
        in: 'query',
        description: 'Current page',
        schema: {
          type: 'integer',
        },
      },
    ],
    responses: {
      200: {
        description: 'A list of test plans',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                total: {
                  type: 'integer',
                },
                per_page: {
                  type: 'integer',
                },
                offset: {
                  type: 'integer',
                },
                to: {
                  type: 'integer',
                },
                last_page: {
                  type: 'integer',
                },
                current_page: {
                  type: 'integer',
                },
                from: {
                  type: 'integer',
                },
                plans: {
                  type: 'array',
                  items: {
                    allOf: [
                      {
                        $ref: '#/components/schemas/TestPlans',
                      },
                      {
                        type: 'object',
                        properties: {
                          attachments: {
                            type: 'array',
                            items: {
                              type: 'object',
                            },
                          },
                          reports: {
                            type: 'array',
                            items: {
                              type: 'object',
                            },
                          },
                        },
                      },
                    ],
                  },
                },
              },
            },
          },
        },
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
