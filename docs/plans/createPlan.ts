export default {
  post: {
    tags: ['Plans'],
    summary: 'Create a test plans',
    description: 'Create a test plans',
    operationId: 'createPlan',
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            required: ['name', 'milestoneId', 'status', 'priority'],
            type: 'object',
            properties: {
              milestoneId: {
                type: 'string',
                format: 'uuid',
              },
              name: {
                type: 'string',
              },
              description: {
                type: 'string',
              },
              status: {
                type: 'string',
              },
              priority: {
                type: 'string',
              },
              customFields: {
                type: 'object',
              },
              runs: {
                type: 'array',
              },
            },
          },
        },
      },
    },
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                ownerUid: {
                  type: 'string',
                  format: 'uuid',
                },
                externalId: {
                  type: 'string',
                },
                source: {
                  type: 'string',
                },
                name: {
                  type: 'string',
                },
                description: {
                  type: 'string',
                },
                milestoneId: {
                  type: 'string',
                },
                status: {
                  type: 'string',
                },
                priority: {
                  type: 'string',
                },
                customFields: {
                  type: 'object',
                },
              },
            },
          },
        },
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
