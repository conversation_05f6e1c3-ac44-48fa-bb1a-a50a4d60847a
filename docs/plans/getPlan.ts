export default {
  get: {
    tags: ['Plans'],
    summary: 'Get a test plans by planId',
    description: 'Get a test plans by planId',
    operationId: 'getPlan',
    parameters: [
      {
        name: 'planId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/TestPlans',
            },
          },
        },
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
