import errors from '@app/constants/errors';

export default {
  get: {
    tags: ['Plans'],
    summary: 'Get a list of test plans by text query',
    description: 'Get a list of test plans by text query',
    operationId: 'searchPlans',
    parameters: [
      {
        name: 'orgId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
      {
        name: 'query',
        in: 'query',
        require: true,
        schema: {
          type: 'string',
        },
      },
    ],
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                plans: {
                  type: 'array',
                  items: {
                    $ref: '#/components/schemas/TestPlans',
                  },
                },
              },
            },
          },
        },
      },
      403: {
        description: errors.PROBLEM_FETCHING_TEST_PLANS,
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
