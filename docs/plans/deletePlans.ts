export default {
	post: {
		tags: ['Plans'],
		summary: 'Delete test plans',
		description: 'Delete test plans',
		operationId: 'deletePlans',
		requestBody: {
			required: true,
			content: {
				'application/json': {
					schema: {
						required: ['planIds'],
						type: 'object',
						properties: {
							org: {
								planIds: 'array',
							},
						},
					},
				},
			},
		},
		responses: {
			200: {
				description: 'Test plans have been deleted successfully',
			},
			403: {
				$ref: '#/components/responses/UnauthorizedError',
			},
			500: {
				description: 'Server error',
			},
		},
		security: [
			{
				bearerAuth: [],
			},
		],
	},
};
