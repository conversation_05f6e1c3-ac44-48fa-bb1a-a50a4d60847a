export default {
	get: {
		tags: ['Plans'],
		summary: 'Update a test plan by planId',
		description: 'Update a test plans by planId',
		operationId: 'updatePlan',
		parameters: [
			{
				name: 'planId',
				in: 'path',
				required: true,
				schema: {
					type: 'string',
					format: 'uuid',
				},
			},
		],
		requestBody: {
			required: true,
			content: {
				'application/json': {
					schema: {
						required: ['planIds'],
						type: 'object',
						properties: {
							org: {
								planIds: 'array',
							},
						},
					},
				},
			},
		},
		responses: {
			200: {
				description: 'Test plans updated successfully',
			},
			403: {
				$ref: '#/components/responses/UnauthorizedError',
			},
			500: {
				description: 'Server error',
			},
		},
		security: [
			{
				bearerAuth: [],
			},
		],
	},
};
