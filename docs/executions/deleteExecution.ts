export default {
  delete: {
    tags: ['Executions'],
    summary: 'Delete a test suites by execId',
    description: 'Delete a test suites by execId',
    operationId: 'deleteExecution',
    parameters: [
      {
        name: 'execId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        description: 'Test execution has been deleted successfully',
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
