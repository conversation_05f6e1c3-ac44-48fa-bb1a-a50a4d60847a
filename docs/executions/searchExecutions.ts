import errors from '@app/constants/errors';

export default {
  get: {
    tags: ['Executions'],
    summary: 'Get a list of test executions by text query',
    description: 'Get a list of test executions by text query',
    operationId: 'searchExecutions',
    parameters: [
      {
        name: 'orgId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
      {
        name: 'query',
        in: 'query',
        require: true,
        schema: {
          type: 'string',
        },
      },
    ],
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                executions: {
                  type: 'array',
                  items: {
                    $ref: '#/components/schemas/TestExecutions',
                  },
                },
              },
            },
          },
        },
      },
      403: {
        description: errors.PROBLEM_FETCHING_TEST_EXECUTIONS,
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
