export default {
  post: {
    tags: ['Executions'],
    summary: 'Create a test executions',
    description: 'Create a test executions',
    operationId: 'createExecution',
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            required: ['org', 'externalId', 'source'],
            type: 'object',
            properties: {
              org: {
                type: 'string',
                format: 'uuid',
              },
              externalId: {
                type: 'string',
              },
              source: {
                type: 'string',
              },
              customFields: {
                type: 'object',
              },
              steps: {
                type: 'array',
                items: {
                  type: 'object',
                },
              },
            },
          },
        },
      },
    },
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                ownerUid: {
                  type: 'string',
                  format: 'uuid',
                },
                externalId: {
                  type: 'string',
                },
                source: {
                  type: 'string',
                },
                customFields: {
                  type: 'object',
                },
              },
            },
          },
        },
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
