export default {
  get: {
    tags: ['Executions'],
    summary: 'Get a test executions by execId',
    description: 'Get a test executions by execId',
    operationId: 'getExecution',
    parameters: [
      {
        name: 'execId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/TestExecutions',
            },
          },
        },
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
