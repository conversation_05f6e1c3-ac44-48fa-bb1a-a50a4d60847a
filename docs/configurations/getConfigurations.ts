export default {
  get: {
    tags: ['Configurations'],
    summary: 'Get a list of test configurations',
    description: 'Get a list of test configurations',
    operationId: 'getConfigurations',
    responses: {
      200: {
        description: 'A list of test configurations',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                total: {
                  type: 'integer',
                },
                per_page: {
                  type: 'integer',
                },
                offset: {
                  type: 'integer',
                },
                to: {
                  type: 'integer',
                },
                last_page: {
                  type: 'integer',
                },
                current_page: {
                  type: 'integer',
                },
                from: {
                  type: 'integer',
                },
                configurations: {
                  type: 'array',
                  items: {
                    allOf: [
                      {
                        $ref: '#/components/schemas/Configuration',
                      },
                    ],
                  },
                },
              },
            },
          },
        },
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
