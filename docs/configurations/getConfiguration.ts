export default {
  get: {
    tags: ['Configurations'],
    summary: 'Get a test configuration by configurationId',
    description: 'Get a test configuration by configurationId',
    operationId: 'getConfiguration',
    parameters: [
      {
        name: 'configurationId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Configuration',
            },
          },
        },
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
