export default {
  delete: {
    tags: ['Configurations'],
    summary: 'Delete a test configuration by configurationId',
    description: 'Delete a test configuration by configurationId',
    operationId: 'deleteConfiguration',
    parameters: [
      {
        name: 'configurationId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        description: 'Test configuration has been deleted successfully',
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
