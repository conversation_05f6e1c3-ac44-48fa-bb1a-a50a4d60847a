export default {
  post: {
    tags: ['Configurations'],
    summary: 'Create a test configuration',
    description: 'Create a test configuration',
    operationId: 'createConfiguration',
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            required: ['name', 'options'],
            type: 'object',
            properties: {
              name: {
                type: 'string',
              },
              options: {
                type: 'array',
                items: {
                  type: 'string',
                },
              },
              description: {
                type: 'string',
              },
            },
          },
        },
      },
    },
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Configuration',
            },
          },
        },
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
