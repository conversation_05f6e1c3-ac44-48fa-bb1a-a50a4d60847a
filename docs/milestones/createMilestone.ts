export default {
  post: {
    tags: ['Milestones'],
    summary: 'Create a test milestones',
    description: 'Create a test milestones',
    operationId: 'createMilestone',
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            required: ['org', 'externalId', 'source'],
            type: 'object',
            properties: {
              org: {
                type: 'string',
                format: 'uuid',
              },
              externalId: {
                type: 'string',
              },
              source: {
                type: 'string',
              },
              name: {
                type: 'string',
              },
              customFields: {
                type: 'object',
              },
              runIds: {
                type: 'array',
              },
            },
          },
        },
      },
    },
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                ownerUid: {
                  type: 'string',
                  format: 'uuid',
                },
                externalId: {
                  type: 'string',
                },
                source: {
                  type: 'string',
                },
                name: {
                  type: 'string',
                },
                customFields: {
                  type: 'object',
                },
              },
            },
          },
        },
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
