import createMilestone from './createMilestone';
import deleteMilestone from './deleteMilestone';
import updateMilestone from './updateMilestone';
import getMilestone from './getMilestone';
import getMilestones from './getMilestones';
import searchMilestones from './searchMilestones';
import attachRuns from './attachRuns';
import detachRuns from './detachRuns';

export default {
  '/milestones': {
    ...createMilestone,
  },
  '/orgs/{orgId}/milestones': {
    ...getMilestones,
  },
  '/orgs/{orgId}/milestones/search': {
    ...searchMilestones,
  },
  '/milestones/{milestoneId}': {
    ...getMilestone,
    ...deleteMilestone,
    ...updateMilestone,
  },
  '/{orgId}/milestones/{milestoneId}/runs': {
    ...attachRuns,
    ...detachRuns,
  },
};
