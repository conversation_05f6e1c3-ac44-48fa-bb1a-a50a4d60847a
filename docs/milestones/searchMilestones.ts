import errors from '@app/constants/errors';

export default {
  get: {
    tags: ['Milestones'],
    summary: 'Get a list of test milestones by text query',
    description: 'Get a list of test milestones by text query',
    operationId: 'searchMilestones',
    parameters: [
      {
        name: 'orgId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
      {
        name: 'query',
        in: 'query',
        require: true,
        schema: {
          type: 'string',
        },
      },
    ],
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                milestones: {
                  type: 'array',
                  items: {
                    $ref: '#/components/schemas/TestMilestones',
                  },
                },
              },
            },
          },
        },
      },
      403: {
        description: errors.PROBLEM_FETCHING_TEST_MILESTONES,
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
