export default {
  delete: {
    tags: ['Milestones'],
    summary: 'Delete a test milestones by milestoneId',
    description: 'Delete a test milestones by milestoneId',
    operationId: 'deleteMilestone',
    parameters: [
      {
        name: 'milestoneId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        description: 'Test milestone has been deleted successfully',
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
