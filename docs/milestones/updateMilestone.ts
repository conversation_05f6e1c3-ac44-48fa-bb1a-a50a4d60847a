export default {
  delete: {
    tags: ['Milestones'],
    summary: 'Update a test milestone by milestoneId',
    description: 'Update a test milestone by milestoneId',
    operationId: 'updateMilestone',
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              name: {
                type: 'string',
              },
              description: {
                type: 'string',
              },
              due_date: {
                type: 'string',
              },
              runIds: {
                type: 'array',
              },
            },
          },
        },
      },
    },
    parameters: [
      {
        name: 'milestoneId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                name: {
                  type: 'string',
                },
                description: {
                  type: 'string',
                },
                dueAt: {
                  type: 'string',
                },
              },
            },
          },
        },
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
