export default {
  post: {
    tags: ['Milestones'],
    summary: 'Detach tests to a test milestones',
    description: 'Detach tests to a test milestones',
    operationId: 'detachRuns',
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            required: ['runIds'],
            type: 'object',
            properties: {
              runs: {
                type: 'array',
              },
            },
          },
        },
      },
    },
    parameters: [
      {
        name: 'milestoneId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        description: 'test runs have been removed from milestone successfully',
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
