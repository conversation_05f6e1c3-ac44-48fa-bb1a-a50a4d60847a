export default {
  post: {
    tags: ['Milestones'],
    summary: 'Attach tests to a test milestones',
    description: 'Attach tests to a test milestones',
    operationId: 'attachRuns',
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            required: ['runIds'],
            type: 'object',
            properties: {
              runs: {
                type: 'array',
              },
            },
          },
        },
      },
    },
    parameters: [
      {
        name: 'milestoneId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        description: 'test runs has been attached to milestone successfully',
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
