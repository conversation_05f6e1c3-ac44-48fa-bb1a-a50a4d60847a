export default {
  get: {
    tags: ['Milestones'],
    summary: 'Get a test milestones by milestoneId',
    description: 'Get a test milestones by milestoneId',
    operationId: 'getMilestone',
    parameters: [
      {
        name: 'milestoneId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/TestMilestones',
            },
          },
        },
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
