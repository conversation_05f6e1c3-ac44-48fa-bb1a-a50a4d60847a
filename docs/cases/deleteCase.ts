export default {
  delete: {
    tags: ['Cases'],
    summary: 'Delete a test cases by caseId',
    description: 'Delete a test cases by caseId',
    operationId: 'deleteCase',
    parameters: [
      {
        name: 'caseId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        description: 'Test case has been deleted successfully',
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
