import errors from '@app/constants/errors';

export default {
  get: {
    tags: ['Cases'],
    summary: 'Get a list of test cases by text query',
    description: 'Get a list of test cases by text query',
    operationId: 'searchCases',
    parameters: [
      {
        name: 'orgId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
      {
        name: 'query',
        in: 'query',
        require: true,
        schema: {
          type: 'string',
        },
      },
    ],
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                cases: {
                  type: 'array',
                  items: {
                    $ref: '#/components/schemas/TestCases',
                  },
                },
              },
            },
          },
        },
      },
      403: {
        description: errors.PROBLEM_FETCHING_TEST_CASES,
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
