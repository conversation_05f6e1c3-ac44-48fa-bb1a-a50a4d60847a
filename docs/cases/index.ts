import createCase from "./createCase";
import deleteCase from "./deleteCase";
import getCase from "./getCase";
import getCases from "./getCases";
import searchCases from "./searchCases";

export default {
  "/cases": {
    ...createCase,
  },
  "/orgs/{orgId}/cases": {
    ...getCases,
  },
  "/orgs/{orgId}/cases/search": {
    ...searchCases,
  },
  "/cases/{caseId}": {
    ...getCase,
    ...deleteCase,
  },
};
