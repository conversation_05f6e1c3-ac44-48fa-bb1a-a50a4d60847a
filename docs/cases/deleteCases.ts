export default {
	delete: {
		tags: ['Cases'],
		summary: 'Delete test cases by their caseIds',
		description: 'Delete test cases by their caseIds',
		operationId: 'deleteCases',
		requestBody: {
			required: true,
			content: {
				'application/json': {
					schema: {
						required: ['ids'],
						type: 'object',
						properties: {
							ids: {
								type: 'array',
							},
						},
					},
				},
			},
		},
		responses: {
			200: {
				description: 'Test cases have been deleted successfully',
			},
			403: {
				$ref: '#/components/responses/UnauthorizedError',
			},
			500: {
				description: 'Server error',
			},
		},
		security: [
			{
				bearerAuth: [],
			},
		],
	},
};
