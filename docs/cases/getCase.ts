export default {
  get: {
    tags: ['Cases'],
    summary: 'Get a test cases by caseId',
    description: 'Get a test cases by caseId',
    operationId: 'getCase',
    parameters: [
      {
        name: 'caseId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/TestCases',
            },
          },
        },
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
