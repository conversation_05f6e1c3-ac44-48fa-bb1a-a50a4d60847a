export default {
  delete: {
    tags: ['Folders'],
    summary: 'Delete a test folders by folderId',
    description: 'Delete a test folders by folderId',
    operationId: 'deleteFolder',
    parameters: [
      {
        name: 'folderId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        description: 'Test folder has been deleted successfully',
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
