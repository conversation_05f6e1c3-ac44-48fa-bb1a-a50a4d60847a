export default {
  get: {
    tags: ['Folders'],
    summary: 'Get a test folders by folderId',
    description: 'Get a test runs by folderId',
    operationId: 'getFolder',
    parameters: [
      {
        name: 'folderId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/TestFolders',
            },
          },
        },
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
