import errors from '@app/constants/errors';

export default {
  get: {
    tags: ['Folders'],
    summary: 'Get a list of test folders by text query',
    description: 'Get a list of test folders by text query',
    operationId: 'searchFolders',
    parameters: [
      {
        name: 'orgId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
      {
        name: 'query',
        in: 'query',
        require: true,
        schema: {
          type: 'string',
        },
      },
    ],
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                folders: {
                  type: 'array',
                  items: {
                    $ref: '#/components/schemas/TestFolders',
                  },
                },
              },
            },
          },
        },
      },
      403: {
        description: errors.PROBLEM_FETCHING_TEST_FOLDERS,
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
