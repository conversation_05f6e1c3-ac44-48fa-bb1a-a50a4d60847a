export default {
  required: ['name', 'created_by'],
  type: 'object',
  properties: {
    uid: {
      type: 'string',
      format: 'uuid',
    },
    name: {
      type: 'string',
      maxLength: 255,
    },
    avatarUrl: {
      type: 'string',
      format: 'uri',
      maxLength: 255,
    },
    created_by: {
      type: 'string',
      format: 'uuid',
    },
    createdAt: {
      type: 'string',
      format: 'date-time',
    },
    updatedAt: {
      type: 'string',
      format: 'date-time',
    },
    deletedAt: {
      type: 'string',
      format: 'date-time',
    },
  },
};
