export default {
  required: [
    'primary_table',
    'primary_key',
    'secondary_table',
    'secondary_key',
    'source',
    'direction',
  ],
  type: 'object',
  properties: {
    uid: {
      type: 'string',
      format: 'uuid',
    },
    primary_table: {
      type: 'string',
    },
    primary_key: {
      type: 'string',
    },
    secondary_table: {
      type: 'string',
    },
    secondary_key: {
      type: 'string',
    },
    source: {
      type: 'string',
    },
    direction: {
      type: 'string',
    },
    userUid: {
      type: 'string',
      format: 'uuid',
    },
    createdAt: {
      type: 'string',
      format: 'date-time',
    },
    updatedAt: {
      type: 'string',
      format: 'date-time',
    },
    deletedAt: {
      type: 'string',
      format: 'date-time',
    },
  },
};
