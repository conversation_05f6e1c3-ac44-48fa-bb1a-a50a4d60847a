export default {
  required: ['org_uid', 'name', 'body', 'created_by'],
  type: 'object',
  properties: {
    uid: {
      type: 'string',
      format: 'uuid',
    },
    org_uid: {
      type: 'string',
      format: 'uuid',
    },
    name: {
      type: 'string',
      maxLength: 255,
    },
    body: {
      type: 'object',
    },
    created_by: {
      type: 'string',
      format: 'uuid',
    },
    createdAt: {
      type: 'string',
      format: 'date-time',
    },
    updatedAt: {
      type: 'string',
      format: 'date-time',
    },
    deletedAt: {
      type: 'string',
      format: 'date-time',
    },
  },
};
