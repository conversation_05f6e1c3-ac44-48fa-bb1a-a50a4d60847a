export default {
  required: ['size', 'key', 'file_name', 'type', 'host_uid'],
  type: 'object',
  properties: {
    uid: {
      type: 'string',
      format: 'uuid',
    },
    name: {
      type: 'string',
      nullable: true,
      maxLength: 255,
    },
    size: {
      type: 'string',
      maxLength: 255,
    },
    key: {
      type: 'string',
      maxLength: 255,
    },
    file_name: {
      type: 'string',
      maxLength: 255,
    },
    metadata: {
      type: 'object',
    },
    type: {
      type: 'string',
      maxLength: 25,
    },
    checksum: {
      type: 'string',
      nullable: true,
      maxLength: 255,
    },
    host_type: {
      type: 'string',
      nullable: true,
      maxLength: 25,
    },
    host_uid: {
      type: 'string',
      format: 'uuid',
    },
    timestamps: {
      type: 'string',
      format: 'date-time',
    },
  },
};
