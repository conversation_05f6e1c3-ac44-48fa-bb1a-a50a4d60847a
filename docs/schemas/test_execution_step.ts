export default {
  required: ['ownerUid', 'test_step_uid', 'test_execution_uid'],
  type: 'object',
  properties: {
    uid: {
      type: 'string',
      format: 'uuid',
    },
    ownerUid: {
      type: 'string',
      format: 'uuid',
      description: 'ID of the owner',
    },
    externalId: {
      type: 'string',
    },
    source: {
      type: 'string',
    },
    link: {
      type: 'string',
      maxLength: 255,
    },
    customFields: {
      type: 'object',
    },
    external_createdAt: {
      type: 'string',
      format: 'date-time',
    },
    external_updatedAt: {
      type: 'string',
      format: 'date-time',
    },
    createdAt: {
      type: 'string',
      format: 'date-time',
    },
    updatedAt: {
      type: 'string',
      format: 'date-time',
    },
    deletedAt: {
      type: 'string',
      format: 'date-time',
    },
    description: {
      type: 'string',
    },
    test_case_uid: {
      type: 'string',
      format: 'uuid',
    },
    test_step_uid: {
      type: 'string',
      format: 'uuid',
    },
    test_execution_uid: {
      type: 'string',
      format: 'uuid',
    },
  },
};
