export default {
  required: [
    'org_uid',
    'inviter_uid',
    'role_uid',
    'email',
    'token',
    'expiresAt',
  ],
  type: 'object',
  properties: {
    uid: {
      type: 'string',
      format: 'uuid',
    },
    org_uid: {
      type: 'string',
      format: 'uuid',
    },
    inviter_uid: {
      type: 'string',
      format: 'uuid',
    },
    role_uid: {
      type: 'string',
      format: 'uuid',
    },
    status: {
      type: 'string',
      enum: ['processed', 'dropped', 'delivered', 'deferred', 'bounce'],
    },
    email: {
      type: 'string',
      format: 'email',
    },
    token: {
      type: 'string',
    },
    expiresAt: {
      type: 'string',
      format: 'date-time',
    },
    accepted_at: {
      type: 'boolean',
    },
    createdAt: {
      type: 'string',
      format: 'date-time',
    },
    updatedAt: {
      type: 'string',
      format: 'date-time',
    },
    deletedAt: {
      type: 'string',
      format: 'date-time',
    },
  },
};
