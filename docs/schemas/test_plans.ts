export default {
  required: ['externalId', 'source', 'name'],
  type: 'object',
  properties: {
    uid: {
      type: 'string',
      format: 'uuid',
    },
    ownerUid: {
      type: 'string',
      format: 'uuid',
      description: 'ID of organization',
    },
    externalId: {
      type: 'string',
    },
    source: {
      type: 'string',
    },
    name: {
      type: 'string',
      maxLength: 255,
    },
    link: {
      type: 'string',
      maxLength: 255,
    },
    customFields: {
      type: 'object',
    },
    completed_at: {
      type: 'string',
      format: 'date-time',
    },
    external_createdAt: {
      type: 'string',
      format: 'date-time',
    },
    external_updatedAt: {
      type: 'string',
      format: 'date-time',
    },
    createdAt: {
      type: 'string',
      format: 'date-time',
    },
    updatedAt: {
      type: 'string',
      format: 'date-time',
    },
    deletedAt: {
      type: 'string',
      format: 'date-time',
    },
  },
};
