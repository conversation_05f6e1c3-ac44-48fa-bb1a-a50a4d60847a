export default {
  required: ['name', 'ownerUid'],
  type: 'object',
  properties: {
    uid: {
      type: 'string',
      format: 'uuid',
    },
    name: {
      type: 'string',
      maxLength: 255,
    },
    ownerUid: {
      type: 'string',
      maxLength: 255,
    },
    ownerType: {
      type: 'string',
      enum: ['org', 'user'],
    },
    current: {
      type: 'boolean',
    },
    createdAt: {
      type: 'string',
      format: 'date-time',
    },
    updatedAt: {
      type: 'string',
      format: 'date-time',
    },
  },
};
