export default {
  required: ['accessToken_hash', 'name', 'expiresAt'],
  type: 'object',
  properties: {
    uid: {
      type: 'string',
      format: 'uuid',
    },
    accessToken_hash: {
      type: 'string',
      maxLength: 255,
    },
    name: {
      type: 'string',
      maxLength: 255,
    },
    expiresAt: {
      type: 'string',
    },
    ownerUid: {
      type: 'string',
      format: 'uuid',
    },
    ownerType: {
      type: 'string',
      enum: ['user', 'org'],
    },
    createdAt: {
      type: 'string',
      format: 'date-time',
    },
    updatedAt: {
      type: 'string',
      format: 'date-time',
    },
    deletedAt: {
      type: 'string',
      format: 'date-time',
    },
  },
};
