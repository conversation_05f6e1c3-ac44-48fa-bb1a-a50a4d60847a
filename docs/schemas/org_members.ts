export default {
  required: ['userUid', 'org_uid', 'role_uid'],
  type: 'object',
  properties: {
    userUid: {
      type: 'string',
      format: 'uuid',
    },
    org_uid: {
      type: 'string',
      format: 'uuid',
      maxLength: 255,
    },
    role_uid: {
      type: 'string',
      format: 'uuid',
    },
    createdAt: {
      type: 'string',
      format: 'date-time',
    },
    updatedAt: {
      type: 'string',
      format: 'date-time',
    },
    deletedAt: {
      type: 'string',
      format: 'date-time',
    },
  },
};
