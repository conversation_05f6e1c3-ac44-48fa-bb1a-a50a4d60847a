export default {
  post: {
    tags: ['Runs'],
    summary: 'Update test runs',
    description: 'Update test runs',
    operationId: 'updateRuns',
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            required: ['runs'],
            type: 'object',
            properties: {
              runs: {
                type: 'array',
              },
            },
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Test runs updated successfully',
      },
      207: {
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                },
                failedUpdates: {
                  type: 'array',
                },
              },
            },
          },
        },
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
