export default {
  delete: {
    tags: ['Runs'],
    summary: 'Delete a test runs by runId',
    description: 'Delete a test runs by runId',
    operationId: 'deleteRun',
    parameters: [
      {
        name: 'runId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        description: 'Test run has been deleted successfully',
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
