import errors from '@app/constants/errors';

export default {
  get: {
    tags: ['Runs'],
    summary: 'Get a list of test runs by text query',
    description: 'Get a list of test runs by text query',
    operationId: 'searchRuns',
    parameters: [
      {
        name: 'orgId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
      {
        name: 'query',
        in: 'query',
        require: true,
        schema: {
          type: 'string',
        },
      },
    ],
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                runs: {
                  type: 'array',
                  items: {
                    $ref: '#/components/schemas/TestRuns',
                  },
                },
              },
            },
          },
        },
      },
      403: {
        description: errors.PROBLEM_FETCHING_TEST_RUNS,
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
