export default {
  get: {
    tags: ['Runs'],
    summary: 'Get a test runs by runId',
    description: 'Get a test runs by runId',
    operationId: 'getRun',
    parameters: [
      {
        name: 'runId',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/TestRuns',
            },
          },
        },
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
