export default {
  post: {
    tags: ['Runs'],
    summary: 'Delete test runs',
    description: 'Delete test runs',
    operationId: 'deleteRuns',
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            required: ['runIds'],
            type: 'object',
            properties: {
              runIds: {
                type: 'array',
                format: 'uuid',
              },
            },
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Test runs have been deleted successfully',
      },
      403: {
        $ref: '#/components/responses/UnauthorizedError',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
