export default {
  get: {
    tags: ['Test Execution Step'],
    summary: 'Get a specific test execution step by UID',
    operationId: 'getTestExecutionStepByUID',
    parameters: [
      {
        name: 'STEP_UID',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        description: 'Successful retrieval',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                ownerUid: {
                  type: 'string',
                  format: 'uuid',
                },
                externalId: {
                  type: 'string',
                },
                source: {
                  type: 'string',
                },
                link: {
                  type: 'string',
                },
                customFields: {
                  type: 'object',
                },
                external_createdAt: {
                  type: 'string',
                  format: 'date-time',
                },
                external_updatedAt: {
                  type: 'string',
                  format: 'date-time',
                },
                description: {
                  type: 'string',
                },
                test_case_uid: {
                  type: 'string',
                  format: 'uuid',
                },
                test_step_uid: {
                  type: 'string',
                  format: 'uuid',
                },
                test_execution_uid: {
                  type: 'string',
                  format: 'uuid',
                },
              },
            },
          },
        },
      },
      404: {
        description: 'Test execution step not found',
      },
    },
  },
};
