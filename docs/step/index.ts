import createTestCaseStep from "./createTestCaseStep";
import getTestCaseSteps from "./getTestCaseSteps";
import getTestCaseStep from "./getTestCaseStep";
import updateTestCaseStep from "./updateTestCaseStep";
import deleteTestCaseStep from "./deleteTestCaseStep";
import createTestExecutionStep from "./createTestExecutionStep";
import deleteTestExecutionStep from "./deleteTestExecutionStep";
import getTestExecutionSteps from "./getTestExecutionSteps";
import getTestExecutionStep from "./getTestExecutionStep";
import updateTestExecutionStep from "./updateTestExecutionStep";

export default {
  "/cases/{CASE_UID}/steps/": {
    ...createTestCaseStep,
    ...getTestCaseSteps,
  },
  "/cases/steps/{STEP_UID}": {
    ...getTestCaseStep,
    ...updateTestCaseStep,
    ...deleteTestCaseStep,
  },
  "/executions/{EXEC_UID}/steps/": {
    ...createTestExecutionStep,
    ...getTestExecutionSteps,
  },
  "/executions/steps/{STEP_UID}": {
    ...updateTestExecutionStep,
    ...getTestExecutionStep,
    ...deleteTestExecutionStep,
  },
};
