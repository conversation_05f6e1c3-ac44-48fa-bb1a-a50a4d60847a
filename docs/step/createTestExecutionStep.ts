export default {
  post: {
    tags: ['Test Execution Step'],
    summary: 'Create a new test execution step for a given test execution',
    operationId: 'createTestExecutionStep',
    parameters: [
      {
        name: 'EXEC_UID',
        in: 'path',
        required: true,
        description: 'Unique ID of the test execution',
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              ownerUid: { type: 'string', format: 'uuid' },
              externalId: { type: 'string' },
              source: { type: 'string' },
              link: { type: 'string' },
              customFields: { type: 'object' },
              external_createdAt: { type: 'string', format: 'date-time' },
              external_updatedAt: { type: 'string', format: 'date-time' },
              description: { type: 'string' },
              test_case_uid: { type: 'string', format: 'uuid' },
              test_step_uid: { type: 'string', format: 'uuid' },
              test_execution_uid: { type: 'string', format: 'uuid' },
            },
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Successful creation of test_execution_step',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/TestExecutionStep',
            },
          },
        },
      },
      400: {
        description: 'Bad request',
      },
      500: {
        description: 'Server error',
      },
    },
  },
};
