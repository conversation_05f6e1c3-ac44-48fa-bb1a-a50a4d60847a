export default {
  delete: {
    tags: ['Test Case Step'],
    summary: 'Delete a specific test case step given its UID',
    operationId: 'deleteTestCaseStep',
    parameters: [
      {
        name: 'STEP_UID',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        description: 'Successfully deleted',
      },
      404: {
        description: 'Test case step not found',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
