export default {
  get: {
    tags: ['Test Case Steps'],
    summary: 'Retrieve all test case steps for a given test case',
    operationId: 'getTestCaseSteps',
    parameters: [
      {
        name: 'CASE_UID',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        description: 'List of test_case_steps',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/TestCaseStep',
              },
            },
          },
        },
      },
      404: {
        description: 'Test case not found',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
