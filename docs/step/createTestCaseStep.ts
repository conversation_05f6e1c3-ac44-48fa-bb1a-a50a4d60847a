export default {
  post: {
    tags: ['Test Case Steps'],
    summary: 'Create a new test case step on test case',
    description: 'Create a new test case step on test case',
    operationId: 'createTestCaseStep',
    parameters: [
      {
        name: 'CASE_UID',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              ownerUid: { type: 'string', format: 'uuid' },
              externalId: { type: 'string' },
              source: { type: 'string' },
              link: { type: 'string' },
              customFields: { type: 'object' },
              external_createdAt: { type: 'string', format: 'date-time' },
              external_updatedAt: { type: 'string', format: 'date-time' },
              createdAt: { type: 'string', format: 'date-time' },
              updatedAt: { type: 'string', format: 'date-time' },
              deletedAt: { type: 'string', format: 'date-time' },
              description: { type: 'string' },
              version: { type: 'integer' },
              active: { type: 'boolean' },
              test_case_uid: { type: 'string', format: 'uuid' },
              test_step_uid: { type: 'string', format: 'uuid' },
            },
          },
        },
      },
    },
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/TestCaseStep',
            },
          },
        },
      },
      400: {
        description: 'Bad request',
      },
      500: {
        description: 'Server error',
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
};
