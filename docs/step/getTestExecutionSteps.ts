export default {
  get: {
    tags: ['Test Execution Step'],
    summary: 'Display non-deleted test execution step for test execution',
    operationId: 'getAllActiveTestExecutionSteps',
    parameters: [
      {
        name: 'EXEC_UID',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    responses: {
      200: {
        description: 'Successful retrieval',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/TestExecutionStep',
              },
            },
          },
        },
      },
      404: {
        description: 'Test execution not found',
      },
    },
  },
};
