export default {
  patch: {
    tags: ['Test Execution Step'],
    summary: 'Update a test execution step by UID',
    operationId: 'updateTestExecutionStepByUID',
    parameters: [
      {
        name: 'STEP_UID',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          format: 'uuid',
        },
      },
    ],
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              ownerUid: {
                type: 'string',
                format: 'uuid',
              },
              externalId: {
                type: 'string',
              },
              source: {
                type: 'string',
              },
              link: {
                type: 'string',
              },
              customFields: {
                type: 'object',
              },
              external_createdAt: {
                type: 'string',
                format: 'date-time',
              },
              external_updatedAt: {
                type: 'string',
                format: 'date-time',
              },
              description: {
                type: 'string',
              },
              test_case_uid: {
                type: 'string',
                format: 'uuid',
              },
              test_step_uid: {
                type: 'string',
                format: 'uuid',
              },
              test_execution_uid: {
                type: 'string',
                format: 'uuid',
              },
            },
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Successfully updated',
      },
      400: {
        description: 'Invalid input',
      },
    },
  },
};
