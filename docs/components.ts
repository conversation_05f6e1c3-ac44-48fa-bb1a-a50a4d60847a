import schemaAttachments from './schemas/attachments';
import schemaCases from './schemas/test_cases';
import schemaConfiguration from './schemas/configuration';
import schemaExecutions from './schemas/test_executions';
import schemaFolders from './schemas/test_folders';
// const schemaAcessTokens = require('./schemas/accessTokens');
// const schemaDataRelationships = require('./schemas/data_relationships');
// const schemaHandles = require('./schemas/handles');
// const schemaInvites = require('./schemas/invites');
// const schemaOrgDashboards = require('./schemas/org_dashboards');
// const schemaOrgs = require('./schemas/orgs');
// const schemaRoles = require('./schemas/roles');
import schemaMilestones from './schemas/test_milestones';
import schemaPlans from './schemas/test_plans';
import schemaProjects from './schemas/test_projects';
import schemaRuns from './schemas/test_runs';
import schemaSuites from './schemas/test_suites';
import schemaTestCaseStep from './schemas/test_case_step';
import schemaTestExecutionStep from './schemas/test_execution_step';
// const schemaApps = require('./schemas/apps');
// const schemaRepos = require('./schemas/repos');

export default {
  components: {
    schemas: {
      Attachments: {
        ...schemaAttachments,
      },
      // AccessTokens: {
      //  ...schemaAcessTokens,
      // },
      // DataRelationships: {
      //  ...schemaDataRelationships,
      // },
      // Handles: {
      //  ...schemaHandles,
      // },
      // Invites: {
      //  ...schemaInvites,
      // },
      // OrgDashboards: {
      //  ...schemaOrgDashboards,
      // },
      // Orgs: {
      //  ...schemaOrgs,
      // },
      // Roles: {
      //  ...schemaRoles,
      // },
      TestRuns: {
        ...schemaRuns,
      },
      TestPlans: {
        ...schemaPlans,
      },
      TestProjects: {
        ...schemaProjects,
      },
      TestSuites: {
        ...schemaSuites,
      },
      TestMilestones: {
        ...schemaMilestones,
      },
      TestCases: {
        ...schemaCases,
      },
      TestExecutions: {
        ...schemaExecutions,
      },
      TestFolders: {
        ...schemaFolders,
      },
      TestCaseStep: {
        ...schemaTestCaseStep,
      },
      TestExecutionStep: {
        ...schemaTestExecutionStep,
      },
      Configuration: {
        ...schemaConfiguration,
      },
      // Apps: {
      //  ...schemaApps,
      // },
      // Repos: {
      //  ...schemaRepos,
      // },
    },
    responses: {
      UnauthorizedError: {
        description: 'Access token is missing or invalid',
      },
    },
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
      },
    },
  },
};
