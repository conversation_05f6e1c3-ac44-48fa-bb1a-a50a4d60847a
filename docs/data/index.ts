import dataMapping from '@app/constants/dataMapping';

export default {
  '/data': {
    post: {
      tags: ['Data'],
      summary: 'Add new data',
      description: 'Add new data',
      operationId: 'addNewData',
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                source: {
                  type: 'string',
                  enum: Object.keys(dataMapping.EXTERNAL_RELATIONSHIP_MAPPING),
                },
                type: {
                  type: 'string',
                  enum: Object.keys(dataMapping.EXTERNAL_TYPE_MAPPING),
                },
                entries: {
                  type: 'array',
                  items: {
                    type: 'object',
                  },
                },
              },
            },
          },
        },
      },
      responses: {
        202: {
          description: 'Pushed data to worker',
        },
        400: {
          description: 'Validation error',
        },
        403: {
          $ref: '#/components/responses/UnauthorizedError',
        },
        404: {
          description: 'Validation error',
        },
      },
      security: [
        {
          bearerAuth: [],
        },
      ],
    },
  },
};
