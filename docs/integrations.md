## 🔌 Integrations Documentation

Our system currently supports **three integrations**:

* GitHub (GH)
* Jira Cloud
* TestRail

These integrations allow users to map external service projects to **TestFiesta** projects. We support **many-to-many mapping**, meaning:

* A single TestFiesta project can be linked to **multiple GitHub repositories**.
* A single GitHub repository can be linked to **multiple TestFiesta projects**.

---

## 🔐 Integration Setup & Environment Configuration

Before working with integrations, it’s important to set up the required encryption and environment variables correctly.

### 🔑 Encryption Key Setup

We use encryption when creating and working with integrations. To enable this, you must generate and store encryption keys.

Run the following command:

```bash
npm run seed:encryptionKeys
```

Then, **store the generated private key** in your `.env` file.

---

### 🧾 OAuth Credentials Setup

Both GitHub and Jira integrations require **Client ID** and **Client Secret** to function correctly. Here's how to configure them:

---

### ☁️ JIRA Setup

1. Create a developer account at the [Atlassian Developer Console](https://developer.atlassian.com/).
2. Go to [My Apps](https://developer.atlassian.com/console/myapps/) and create a **new app**.
3. Set up **OAuth 2.0 credentials** in the Authorization section:

   * Specify the Redirect URI: e.g., `http://localhost:5050/core/app/oauth`
   * This should match the value set in your `JIRA_REDIRECT_URI` environment variable.
4. Configure required **scopes** in the Permissions section (match `JIRA_SCOPES`).
5. Store the following values in your `.env` file:

   ```env
   OAUTH_JIRA_CLIENT_ID=your_client_id
   OAUTH_JIRA_CLIENT_SECRET=your_client_secret
   ```

---

### 🐙 GitHub Setup

1. Go to [GitHub Developer Settings](https://github.com/settings/apps).
2. Create a new **OAuth App**.
3. Set the Redirect URI: e.g., `http://localhost:5050/core/app/oauth`

   * This should match `GITHUB_REDIRECT_URI` in your environment variables.
4. Store the credentials in your `.env` file:

   ```env
   OAUTH_GITHUB_CLIENT_ID=your_client_id
   OAUTH_GITHUB_CLIENT_SECRET=your_client_secret
   ```

---

## 🔧 Configuration Structure

The mappings are stored under the `projectConfigurations` object inside the `integration.configuration` object.

A user can:

* Create **multiple configurations** within a single integration.
* Or, create **multiple integrations**, each with its own mapping.

This flexibility allows users to structure their integrations however they prefer.

---

## 🔐 Authentication Methods

* **Jira Integration**: We Currently supports **Jira Cloud via OAuth**. Requires a `resourceId` for the connected account, which is stored in the projectConfiguration with the key `resourceId`.
* **Jira Integration**: We Currently supports **GH via OAuth**. Requires a `RepoName and username` for the connected account, which is stored in `projectName` for every TF and GH mapping.
* **TestRail Integration**: Uses **Basic Auth**. The configuration stores the `url` and `username` needed for authentication.

---

## 🔁 Sync Services

We support **sync services** for all integrations. The system uses the **last synced time**, stored in the `syncedAt` column of the integration, to fetch only the data that has been **created or updated** after that time.

### ⏱ Default Sync Intervals:

* **GitHub**: Every 10 minutes
* **Jira**: Every 10 minutes
* **TestRail**: Every 1 hour

These syncs are managed using **Temporal workflows** with cron schedules.

### 📛 Cron Job Naming Convention:

```
<orgHandle>:integration:sync:<service>:<integrationUid>
```

A default cron job is created when the integration is created.

### ⚙️ Change Sync Schedule Manually

You can change the sync schedule for any integration manually using the following command:

```bash
npm run integration:syncDuration -- <orgHandleName> <cronSchedule> <integrationUid>
```

* `orgHandleName`: The organization handle (required)
* `cronSchedule`: The new cron expression (required)
* `integrationUid`: (Optional) If provided, updates that specific integration only; otherwise, updates all integrations under the organization

> ### ⏰ Cron Format
> Cron syntax:
> 
> ```
> * * * * *
> │ │ │ │ │
> │ │ │ │ └─ Day of the week (0 - 7) (Sunday is both 0 and 7)
> │ │ │ └─── Month (1 - 12)
> │ │ └───── Day of the month (1 - 31)
> │ └─────── Hour (0 - 23)
> └───────── Minute (0 - 59)
> ```

### ⏰ Cron Examples:

* Every 10 minutes: `*/10 * * * *`
* Every 30 minutes: `*/30 * * * *`
* Every hour: `0 * * * *`

Use this flexibility to delay or speed up syncs as needed.

---

## 📅 TestRail SyncDuration Handling

Due to **TestRail's lack of support for filtering data updated after a specific timestamp**, we have a `syncDuration` limit to define how much older data to check.

This is stored in the `configuration` object with the key `syncDuration`, using this format:

```
<number><unit>
```

Where:

* `d` = days
* `m` = months
* `y` = years

### 🔍 Example:

* `10d` = look back 10 days
* `3m` = look back 3 months
* `1y` = look back 1 year

Any entity created **before** the given `syncDuration` will be **ignored** during the sync. For example, if `syncDuration = 3m`, changes to entities created **more than 3 months ago** will not be synced.

