name: Tag release to kick off build
on:
  release:
    types: [released]
jobs:
  tag_release:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [16.x]
    steps:
      - uses: actions/checkout@v3
      - name: Use node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      - name: Auth with gcloud
        uses: 'google-github-actions/auth@v2'
        with:
          credentials_json: "${{ secrets.GC_BUILD_CREDENTIALS }}"

      - name: Set up gcloud
        uses: 'google-github-actions/setup-gcloud@v2'

      - name: Kick off build
        run: |
          gcloud builds triggers run production-backend-deploy --tag=${{ github.event.release.tag_name }}

      - name: Extract all commit messages and build m
        uses: sergeysova/jq-action@v2
        id: message
        with:
          cmd: |
            echo -n "BACKEND: ${{ github.event.release.name }}:${{ github.event.release.tag_name }} is being deployed to PRODUCTION. More info at ${{ github.event.release.html_url }}"

      - name: Discord notification
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK_URL }}
        uses: Ilshidur/action-discord@master
        with: 
          args: "${{ steps.message.outputs.value }}"
