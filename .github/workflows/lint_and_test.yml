name: Run Unit Tests and Lint Files
on:
  push:
    branches:
      - '**'
jobs:
  unit_test_lint:
    if: ${{ ! github.event.pull_request.merged }}
    runs-on: ubuntu-latest-m
    strategy:
      matrix:
        node-version: [18.x]

    env:
      PORT: 5000
      FRONTEND_URL: 'http://localhost:8080'
      BACKEND_URL: 'http://localhost:5000'
      MAIL_USER: '<EMAIL>'
      JWT_EXPIRATION_TIME: '10m'
      JWT_SIGNING_SECRET: 'kalsdjlk4A$a4oi3a9o3o9a303#35'
      API_VERSION_1_ROUTE: '/v1'
      API_INTERNAL_ROUTE: '/core'
      JWT_SECRET: 'rocksteady_secret_key'
      DB_HOST: 'localhost'
      DB_DATABASE: 'tf_test'
      DB_USERNAME: 'testfiesta'
      DB_PASSWORD: 'password'
      DB_PORT: '5432'
      REDIS_HOST: 'localhost'
      REDIS_PORT: 6379
      REDIS_PASSWORD: 'password'
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      OPENFGA_DATASTORE_HOST: 'localhost:8080'
      OPENFGA_DATASTORE_API_SCHEME: 'http'
      OPENFGA_DATASTORE_NAME: testfiesta
      NODE_ENV: 'test'
      STRIPE_SECRET_KEY: stripe-secret-key
      STRIPE_SIGNING_SECRET: signing-secret-from-stripe
      STRIPE_DEFAULT_USER_PRICE_ID: stripe_free_user_price
      STRIPE_DEFAULT_ORG_PRICE_ID: stripe_free_org_price
      GITHUB_NPM_TOKEN: ${{ secrets.GH_CI_TOKEN }}
      GCP_SERVICE_ACCOUNT_FILE_PATH: file-path
      GCP_STORAGE_BUCKET: testfiesta
      GC_SERVICE_KEY_FILE: file-path
      GCS_BUCKET_NAME: testfiesta
      OAUTH_JIRA_CLIENT_ID: 'jira-client-id'
      OAUTH_JIRA_CLIENT_SECRET: 'jira-client-secret'
      OAUTH_GITHUB_CLIENT_ID: 'github-client-id'
      OAUTH_GITHUB_CLIENT_SECRET: 'github-client-secret'
      SENDGRID_KEY: 'sendgrid-key'
      SENDGRID_SIGNING_SECRET: 'sendgrid-signing-secret'
      CORS_ORIGINS: "http://localhost:8080,http://localhost:5000"
      OAUTH_GOOGLE_CLIENT_ID: 'google-client-id'
      OAUTH_GOOGLE_CLIENT_SECRET: 'google-client-secret'
      OAUTH_GOOGLE_CALLBACK_URL: 'http://localhost:5000/core/auth/google/callback'
      OAUTH_GOOGLE_REDIRECT_SIGN_IN_CALLBACK_URL: 'http://localhost:8080/login'
      OAUTH_GOOGLE_REDIRECT_SIGN_UP_CALLBACK_URL: 'http://localhost:8080/signup'
      OIDC_REDIRECT_CALLBACK_URL: 'http://localhost:8080'
      OIDC_CALLBACK_URL: 'http://localhost:5000/core/auth/oidc/callback'
      TEMPORAL_HOST: temporal
      TEMPORAL_PORT: 7233

    services:
      postgres:
        image: postgres
        ports:
          - 5432:5432
        env:
          POSTGRES_DB: tf_test
          POSTGRES_USER: testfiesta
          POSTGRES_PASSWORD: password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      openfga:
        image: emmanuerl/openfga
        ports:
          - 3000:3000
          - 8080:8080
          - 8081:8081
      redis:
        image: redis
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      temporal:
        image: temporalio/auto-setup:1.24.1
        env:
          DB: postgres12
          DB_PORT: 5432
          POSTGRES_USER: testfiesta
          POSTGRES_PWD: password
          POSTGRES_SEEDS: postgres
        ports:
          - '7233:7233'

    steps:
      - uses: actions/checkout@v3
      # setup node
      - name: Use node.js 16
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      - run: npm ci

      - run: npm run build

      - run: npm run migrate:latest

      - name: "Test internal APIs"
        run: npm test
        env:
          NODE_OPTIONS: --max-old-space-size=5120

      - name: "Test external APIs"
        run: npm run api:test  
      
      - name: "Test AI enabled APIs"
        run: npm run test:assist
      
      - run: npm run lint

      - run: npm run knip
