name: Create PR to update Open API Spec

on:
  release:
    types: [released]

jobs:
  update-openapi-docs:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout Repo
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: npm ci
      env:
        GITHUB_NPM_TOKEN: ${{ secrets.GH_CI_TOKEN }}
      
    - name: Generate OpenApi spec
      run: npm run api:make-docs:prod
      
    - name: Checkout docs repo
      uses: actions/checkout@v4
      with:
        repository: testfiesta/testfiesta-docs
        path: testfiesta-docs
        token: ${{ secrets.GH_PUSHER_TOKEN }}
        
    - name: Copy OpenAPI spec to docs repo
      run: |
        cp ./dist/swagger.json testfiesta-docs/.gitbook/assets/swagger.json
        
    - name: Create PR for docs update
      uses: peter-evans/create-pull-request@v5
      with:
        token: ${{ secrets.GH_PUSHER_TOKEN }}
        path: testfiesta-docs
        commit-message: "Update OpenAPI specification"
        branch: update-openapi-spec
        delete-branch: true
        title: "Update OpenAPI Specification"
        body: |
          This PR updates the OpenAPI specification with the latest changes from the backend.
          Auto-generated by GitHub Actions.
        base: main
