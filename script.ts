import { faker } from '@faker-js/faker';
import { setupDB } from '@app/config/db';
import { Knex } from 'knex';
import { setTimeout } from 'node:timers/promises';

export function loader(msg: string) {
  let done = false;

  process.stdout.write(`${msg}`);

  const promise = new Promise(async (res) => {
    while (!done) {
      process.stdout.clearLine(0);
      process.stdout.write(`\r ${msg}`);
      await setTimeout(500);
      process.stdout.write('.');
      await setTimeout(500);
      process.stdout.write('.');
      await setTimeout(500);
      process.stdout.write('.');
      await setTimeout(500);
    }

    process.stdout.write('\n');
    res(done);
  });

  return {
    done: () => {
      done = true;
    },
    wait: () => promise,
  };
}

export async function bulkInsertCases(db: Knex<any, any[]>, n = 10) {
  let asyncOps: Promise<any>[] = [];

  const { done, wait } = loader(`Inserting ${n} Test Cases`);

  for (let i = 0; i < n; i++) {
    asyncOps.push(
      db('testCases')
        .insert({
          name: faker.lorem.words(),
          description: faker.lorem.lines(1),
          customFields: {
            priority: faker.number.int(100),
            status: faker.number.int(100),
          },
          projectUid: 1,
        })
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

export async function bulkInsertTestExecutions(db: Knex<any, any[]>, n = 10) {
  let asyncOps: Promise<any>[] = [];

  const { done, wait } = loader(`Inserting ${n} Test Executions`);

  for (let i = 0; i < n; i++) {
    asyncOps.push(
      db('testExecutions')
        .insert({
          status: faker.number.int(100),
          testCaseRef: faker.number.int(100),
          testRunUid: faker.string.uuid(),
          priority: faker.number.int(100),
          testCaseUid: faker.number.int(100),
          projectUid: 1,
        })
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

export async function bulkInsertMilestones(db: Knex<any, any[]>, n = 10) {
  let asyncOps: Promise<any>[] = [];

  const { done, wait } = loader(`Inserting ${n} Milestones`);

  for (let i = 0; i < n; i++) {
    asyncOps.push(
      db('tags')
        .insert({
          name: faker.lorem.words(),
          description: faker.lorem.lines(1),
          systemType: 'milestone',
          customFields: {
            dueAt: faker.date.future(),
            status: faker.number.int(100),
          },
          entityTypes: [],
          projectUid: 1,
        })
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

export async function bulkInsertRuns(db: Knex<any, any[]>, n = 10) {
  let asyncOps: Promise<any>[] = [];

  const { done, wait } = loader(`Inserting ${n} Runs`);

  for (let i = 0; i < n; i++) {
    asyncOps.push(
      db('tags')
        .insert({
          name: faker.lorem.words(),
          projectUid: 1,
          customFields: {
            priority: faker.number.int(100),
            status: faker.number.int(100),
          },
          systemType: 'run',
          entityTypes: [],
        })
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

export async function bulkInsertPlans(db: Knex<any, any[]>, n = 10) {
  let asyncOps: Promise<any>[] = [];

  const { done, wait } = loader(`Inserting ${n} Plans`);

  for (let i = 0; i < n; i++) {
    asyncOps.push(
      db('tags')
        .insert({
          name: faker.lorem.words(),
          slug: faker.string.uuid(),
          description: faker.lorem.lines(1),
          systemType: 'plan',
          customFields: {
            status: faker.number.int(100),
            priority: faker.number.int(100),
          },
          entityTypes: [],
          projectUid: 1,
        })
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

export async function main() {
  let db: Knex<any, any[]>;
  try {
    db = setupDB('a5baa206-cd9c-421b-9bb4-36e7c86ef337');

    const dbName = await db
      .raw(`select current_database() as db_name;`)
      .then(({ rows }) => rows[0]?.db_name);

    console.log('Connected to DB: ', dbName);

    await bulkInsertPlans(db, 1000);
    await bulkInsertMilestones(db, 1000);
    await bulkInsertRuns(db, 1000);
    await bulkInsertCases(db, 1000);
    await bulkInsertTestExecutions(db, 1000);
  } catch (e) {
    console.error(e);
  } finally {
    await db?.destroy();
  }
}

main();
