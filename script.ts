import { faker } from '@faker-js/faker';
import { setupDB } from '@app/config/db';
import { Knex } from 'knex';
import { setTimeout } from 'node:timers/promises';
import { getNextId } from '@app/lib/model';
import { TestCase } from './src/models/testCase';

export function createProject(db: Knex<any, any[]>) {
  return db('projects')
    .insert({
      name: faker.lorem.words(),
      key: faker.string.uuid(),
      customFields: {
        priority: faker.number.int(100),
        status: faker.number.int(100),
      },
    })
    .returning('*')
    .then(([val]) => val);
}

export function loader(msg: string) {
  let done = false;

  process.stdout.write(`${msg}`);

  const promise = new Promise(async (res) => {
    while (!done) {
      process.stdout.clearLine(0);
      process.stdout.write(`\r ${msg}`);
      await setTimeout(500);
      process.stdout.write('.');
      await setTimeout(500);
      process.stdout.write('.');
      await setTimeout(500);
      process.stdout.write('.');
      await setTimeout(500);
    }

    process.stdout.write('\n');
    res(done);
  });

  return {
    done: () => {
      done = true;
    },
    wait: () => promise,
  };
}

export async function bulkInsertCases(
  db: Knex<any, any[]>,
  projectUid: number,
  n = 10,
) {
  let asyncOps: Promise<any>[] = [];

  const { done, wait } = loader(`Inserting ${n} Test Cases`);

  for (let i = 0; i < n; i++) {
    const uid = await getNextId(db, TestCase);

    asyncOps.push(
      db('testCases')
        .insert({
          uid,
          testCaseRef: uid,
          version: 1,
          active: true,
          name: faker.lorem.words(),
          externalId: faker.string.alphanumeric(10),
          source: faker.company.name(),
          customFields: {
            priority: faker.number.int(100),
            status: faker.number.int(100),
          },
          priority: faker.number.int(100),
          status: faker.number.int(100),
          projectUid,
          steps: [],
        })
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

export async function bulkInsertTestExecutions(
  db: Knex<any, any[]>,
  projectUid,
  n = 10,
) {
  let asyncOps: Promise<any>[] = [];

  const cases = await bulkInsertCases(db, projectUid, 100).then((res) =>
    res.filter((r) => r.status === 'fulfilled').map((r) => r.value),
  );

  const runs = await bulkInsertRuns(db, projectUid, 100).then((res) =>
    res.filter((r) => r.status === 'fulfilled').map((r) => r.value),
  );

  const { done, wait } = loader(`Inserting ${n} Test Executions`);

  for (let i = 0; i < n; i++) {
    const randomCase = cases[i % cases.length];
    const randomRun = runs[i % runs.length];

    asyncOps.push(
      db('testExecutions')
        .insert({
          externalId: faker.string.alphanumeric(10),
          source: faker.company.name(),
          name: faker.lorem.words(),
          status: faker.number.int(100),
          testCaseRef: randomCase.testCaseRef,
          testRunUid: randomRun.uid,
          priority: faker.number.int(100),
          testCaseUid: randomCase.uid,
          projectUid,
          customFields: {
            tags: [{ uid: 1, name: 'Test Tag' }],
          },
          steps: [],
        })
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

export async function bulkInsertMilestones(
  db: Knex<any, any[]>,
  projectUid,
  n = 10,
) {
  let asyncOps: Promise<any>[] = [];

  const { done, wait } = loader(`Inserting ${n} Milestones`);

  for (let i = 0; i < n; i++) {
    asyncOps.push(
      db('tags')
        .insert({
          name: faker.lorem.words(),
          description: faker.lorem.lines(1),
          systemType: 'milestone',
          customFields: {
            dueAt: faker.date.future(),
            status: faker.number.int(100),
          },
          entitiyTypes: [], // Note: this is the actual column name in the migration (with typo)
          projectUid,
        })
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

export async function bulkInsertRuns(
  db: Knex<any, any[]>,
  projectUid: number,
  n = 10,
) {
  let asyncOps: Promise<any>[] = [];

  const { done, wait } = loader(`Inserting ${n} Runs`);

  for (let i = 0; i < n; i++) {
    asyncOps.push(
      db('tags')
        .insert({
          name: faker.lorem.words(),
          projectUid,
          customFields: {
            priority: faker.number.int(100),
            status: faker.number.int(100),
          },
          systemType: 'run',
          entityTypes: [],
        })
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

export async function bulkInsertPlans(
  db: Knex<any, any[]>,
  projectUid: number |string,
  n = 10,
) {
  let asyncOps: Promise<any>[] = [];

  const { done, wait } = loader(`Inserting ${n} Plans`);

  for (let i = 0; i < n; i++) {
    asyncOps.push(
      db('tags')
        .insert({
          name: faker.lorem.words(),
          slug: faker.string.uuid(),
          description: faker.lorem.lines(1),
          systemType: 'plan',
          customFields: {
            status: faker.number.int(100),
            priority: faker.number.int(100),
          },
          entityTypes: [],
          projectUid,
        })
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

// Create regular tags for tagging entities
export async function bulkInsertTags(
  db: Knex<any, any[]>,
  projectUid: number,
  n = 10,
) {
  let asyncOps: Promise<any>[] = [];

  const { done, wait } = loader(`Inserting ${n} Tags`);

  for (let i = 0; i < n; i++) {
    asyncOps.push(
      db('tags')
        .insert({
          name: faker.lorem.word(),
          description: faker.lorem.sentence(),
          systemType: 'tag',
          entitiyTypes: [], // Note: this is the actual column name in the migration (with typo)
          projectUid,
        })
        .returning('*')
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

// Create attachments
export async function bulkInsertAttachments(
  db: Knex<any, any[]>,
  n = 10,
) {
  let asyncOps: Promise<any>[] = [];

  const { done, wait } = loader(`Inserting ${n} Attachments`);

  // Valid mediaType values based on the enum constraint
  const validMediaTypes = ['attachment', 'profile-picture'];

  // Valid file types that are commonly supported
  const validFileTypes = [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'application/pdf', 'text/plain', 'text/csv',
    'application/json', 'application/xml',
    'application/zip', 'application/x-zip-compressed'
  ];

  for (let i = 0; i < n; i++) {
    const fileType = faker.helpers.arrayElement(validFileTypes);
    const mediaType = faker.helpers.arrayElement(validMediaTypes);

    asyncOps.push(
      db('attachments')
        .insert({
          uid: faker.string.uuid(),
          name: faker.system.fileName(),
          size: faker.number.int({ min: 1000, max: 1000000 }),
          type: fileType, // This is the 'type' field, not 'fileType'
          mediaType: mediaType, // Must be 'attachment' or 'profile-picture'
          source: faker.company.name(),
          externalId: faker.string.alphanumeric(10),
          metadata: {},
        })
        .returning('*')
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

// Create defects
export async function bulkInsertDefects(
  db: Knex<any, any[]>,
  projectUid: number,
  n = 10,
) {
  let asyncOps: Promise<any>[] = [];

  const { done, wait } = loader(`Inserting ${n} Defects`);

  for (let i = 0; i < n; i++) {
    asyncOps.push(
      db('defects')
        .insert({
          name: faker.lorem.words(),
          priority: faker.number.int(100).toString(), // Convert to string as per schema
          status: faker.number.int(100).toString(), // Convert to string as per schema
          externalId: faker.string.alphanumeric(10),
          assignedTo: faker.string.uuid(),
          creator: faker.string.uuid(),
          projectUids: [projectUid], // Array of project UIDs as per schema
          customFields: {
            severity: faker.number.int(5),
            type: faker.lorem.word(),
            description: faker.lorem.paragraph(),
          },
        })
        .returning('*')
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

// Junction table functions to create relationships

// Link milestones to plans
export async function linkMilestonesToPlans(
  db: Knex<any, any[]>,
  milestones: any[],
  plans: any[],
  n = 50,
) {
  let asyncOps: Promise<any>[] = [];
  const { done, wait } = loader(`Linking ${n} Milestone-Plan relationships`);

  for (let i = 0; i < n; i++) {
    const milestone = milestones[i % milestones.length];
    const plan = plans[i % plans.length];

    asyncOps.push(
      db('testMilestonePlans')
        .insert({
          milestoneUid: milestone.uid,
          planUid: plan.uid,
        })
        .onConflict(['milestoneUid', 'planUid'])
        .ignore()
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

// Link milestones to runs
export async function linkMilestonesToRuns(
  db: Knex<any, any[]>,
  milestones: any[],
  runs: any[],
  n = 50,
) {
  let asyncOps: Promise<any>[] = [];
  const { done, wait } = loader(`Linking ${n} Milestone-Run relationships`);

  for (let i = 0; i < n; i++) {
    const milestone = milestones[i % milestones.length];
    const run = runs[i % runs.length];

    asyncOps.push(
      db('testMilestoneRuns')
        .insert({
          milestoneUid: milestone.uid,
          runUid: run.uid,
        })
        .onConflict(['milestoneUid', 'runUid'])
        .ignore()
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

// Link plans to runs
export async function linkPlansToRuns(
  db: Knex<any, any[]>,
  plans: any[],
  runs: any[],
  n = 50,
) {
  let asyncOps: Promise<any>[] = [];
  const { done, wait } = loader(`Linking ${n} Plan-Run relationships`);

  for (let i = 0; i < n; i++) {
    const plan = plans[i % plans.length];
    const run = runs[i % runs.length];

    asyncOps.push(
      db('testPlanRuns')
        .insert({
          planUid: plan.uid,
          runUid: run.uid,
        })
        .onConflict(['planUid', 'runUid'])
        .ignore()
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

// Link runs to tags
export async function linkRunsToTags(
  db: Knex<any, any[]>,
  runs: any[],
  tags: any[],
  n = 50,
) {
  let asyncOps: Promise<any>[] = [];
  const { done, wait } = loader(`Linking ${n} Run-Tag relationships`);

  for (let i = 0; i < n; i++) {
    const run = runs[i % runs.length];
    const tag = tags[i % tags.length];

    asyncOps.push(
      db('testRunTags')
        .insert({
          runUid: run.uid,
          tagUid: tag.uid,
        })
        .onConflict(['runUid', 'tagUid'])
        .ignore()
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

// Link cases to tags
export async function linkCasesToTags(
  db: Knex<any, any[]>,
  cases: any[],
  tags: any[],
  n = 50,
) {
  let asyncOps: Promise<any>[] = [];
  const { done, wait } = loader(`Linking ${n} Case-Tag relationships`);

  for (let i = 0; i < n; i++) {
    const testCase = cases[i % cases.length];
    const tag = tags[i % tags.length];

    asyncOps.push(
      db('testCaseTags')
        .insert({
          testCaseRef: testCase.testCaseRef,
          tagUid: tag.uid,
        })
        .onConflict(['testCaseRef', 'tagUid'])
        .ignore()
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

// Link executions to defects
export async function linkExecutionsToDefects(
  db: Knex<any, any[]>,
  executions: any[],
  defects: any[],
  n = 30,
) {
  let asyncOps: Promise<any>[] = [];
  const { done, wait } = loader(`Linking ${n} Execution-Defect relationships`);

  for (let i = 0; i < n; i++) {
    const execution = executions[i % executions.length];
    const defect = defects[i % defects.length];

    asyncOps.push(
      db('defectExecutions')
        .insert({
          executionUid: execution.uid,
          defectUid: defect.uid,
          executionUrl: faker.internet.url(),
        })
        .onConflict(['executionUid', 'defectUid'])
        .ignore()
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

// Link entities to attachments
export async function linkEntitiesToAttachments(
  db: Knex<any, any[]>,
  runs: any[],
  plans: any[],
  cases: any[],
  executions: any[],
  attachments: any[],
  n = 40,
) {
  let asyncOps: Promise<any>[] = [];
  const { done, wait } = loader(`Linking ${n} Entity-Attachment relationships`);

  // Link runs to attachments
  for (let i = 0; i < Math.floor(n / 4); i++) {
    const run = runs[i % runs.length];
    const attachment = attachments[i % attachments.length];

    asyncOps.push(
      db('runAttachments')
        .insert({
          runUid: run.uid,
          attachmentUid: attachment.uid,
        })
        .onConflict(['runUid', 'attachmentUid'])
        .ignore()
        .catch((e) => console.error(e)),
    );
  }

  // Link plans to attachments
  for (let i = 0; i < Math.floor(n / 4); i++) {
    const plan = plans[i % plans.length];
    const attachment = attachments[i % attachments.length];

    asyncOps.push(
      db('planAttachments')
        .insert({
          planUid: plan.uid,
          attachmentUid: attachment.uid,
        })
        .onConflict(['planUid', 'attachmentUid'])
        .ignore()
        .catch((e) => console.error(e)),
    );
  }

  // Link cases to attachments
  for (let i = 0; i < Math.floor(n / 4); i++) {
    const testCase = cases[i % cases.length];
    const attachment = attachments[i % attachments.length];

    asyncOps.push(
      db('caseAttachments')
        .insert({
          caseRef: testCase.testCaseRef,
          attachmentUid: attachment.uid,
        })
        .onConflict(['caseRef', 'attachmentUid'])
        .ignore()
        .catch((e) => console.error(e)),
    );
  }

  // Link executions to attachments
  for (let i = 0; i < Math.floor(n / 4); i++) {
    const execution = executions[i % executions.length];
    const attachment = attachments[i % attachments.length];

    asyncOps.push(
      db('executionAttachments')
        .insert({
          executionUid: execution.uid,
          attachmentUid: attachment.uid,
        })
        .onConflict(['executionUid', 'attachmentUid'])
        .ignore()
        .catch((e) => console.error(e)),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

export async function main(tenantId: string) {
  let db: Knex<any, any[]>;
  try {
    db = setupDB(tenantId);

    const dbName = await db
      .raw(`select current_database() as db_name;`)
      .then(({ rows }) => rows[0]?.db_name);

    console.log('Connected to DB: ', dbName);

    const project = { uid: 1 };
    console.log('Using project:', project);

    // Step 1: Create base entities
    console.log('\n=== Creating Base Entities ===');
    const [plans, milestones, runs, cases, tags, attachments, defects] = await Promise.all([
      bulkInsertPlans(db, project.uid, 2000).then((res) =>
        res.filter((r) => r.status === 'fulfilled').map((r) => r.value)
      ),
      bulkInsertMilestones(db, project.uid, 1000).then((res) =>
        res.filter((r) => r.status === 'fulfilled').map((r) => r.value)
      ),
      bulkInsertRuns(db, project.uid, 3000).then((res) =>
        res.filter((r) => r.status === 'fulfilled').map((r) => r.value)
      ),
      bulkInsertCases(db, project.uid, 5000).then((res) =>
        res.filter((r) => r.status === 'fulfilled').map((r) => r.value)
      ),
      bulkInsertTags(db, project.uid, 1000).then((res) =>
        res.filter((r) => r.status === 'fulfilled').map((r) => r.value)
      ),
      bulkInsertAttachments(db, 2000).then((res) =>
        res.filter((r) => r.status === 'fulfilled').map((r) => r.value)
      ),
      bulkInsertDefects(db, project.uid, 1500).then((res) =>
        res.filter((r) => r.status === 'fulfilled').map((r) => r.value)
      ),
    ]);

    console.log(`\nCreated: ${plans.length} plans, ${milestones.length} milestones, ${runs.length} runs, ${cases.length} cases, ${tags.length} tags, ${attachments.length} attachments, ${defects.length} defects`);

    // Step 2: Create test executions (depends on cases and runs)
    console.log('\n=== Creating Test Executions ===');
    const executions = await bulkInsertTestExecutions(db, project.uid, 10000).then((res) =>
      res.filter((r) => r.status === 'fulfilled').map((r) => r.value)
    );
    console.log(`Created: ${executions.length} executions`);

    // Step 3: Create all relationships
    console.log('\n=== Creating Relationships ===');
    await Promise.all([
      // Core test management relationships
      linkMilestonesToPlans(db, milestones, plans, 2500),
      linkMilestonesToRuns(db, milestones, runs, 4000),
      linkPlansToRuns(db, plans, runs, 5000),

      // Tagging relationships
      linkRunsToTags(db, runs, tags, 6000),
      linkCasesToTags(db, cases, tags, 8000),

      // Defect relationships
      linkExecutionsToDefects(db, executions, defects, 3000),

      // Attachment relationships
      linkEntitiesToAttachments(db, runs, plans, cases, executions, attachments, 4000),
    ]);

    console.log('\n=== Data Generation Complete! ===');
    console.log('\nSummary:');
    console.log(`- ${plans.length} Test Plans`);
    console.log(`- ${milestones.length} Test Milestones`);
    console.log(`- ${runs.length} Test Runs`);
    console.log(`- ${cases.length} Test Cases`);
    console.log(`- ${executions.length} Test Executions`);
    console.log(`- ${tags.length} Tags`);
    console.log(`- ${attachments.length} Attachments`);
    console.log(`- ${defects.length} Defects`);
    console.log('\nAll relationships have been created across:');
    console.log('- Milestone ↔ Plan relationships');
    console.log('- Milestone ↔ Run relationships');
    console.log('- Plan ↔ Run relationships');
    console.log('- Run ↔ Tag relationships');
    console.log('- Case ↔ Tag relationships');
    console.log('- Execution ↔ Defect relationships');
    console.log('- Entity ↔ Attachment relationships');

  } catch (e) {
    console.error('Error during data generation:', e);
  } finally {
    await db?.destroy();
  }
}

const tenantId = process.argv.slice(2).at(0);

if (!tenantId) {
  console.error('Usage: npx ts-node path/to/script.ts <tenant_id>');
  console.error('tenant_id is required');
  process.exit(1);
}

main(tenantId);
