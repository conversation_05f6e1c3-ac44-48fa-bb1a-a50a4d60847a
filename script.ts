import { faker } from '@faker-js/faker';
import { setupDB } from '@app/config/db';
import { Knex } from 'knex';
import { setTimeout } from 'node:timers/promises';
import { getNextId } from '@app/lib/model';
import { TestCase } from './src/models/testCase';

export function createProject(db: Knex<any, any[]>) {
  return db('projects')
    .insert({
      name: faker.lorem.words(),
      key: faker.string.uuid(),
      customFields: {
        priority: faker.number.int(100),
        status: faker.number.int(100),
      },
    })
    .returning('*')
    .then(([val]) => val);
}

export function loader(msg: string) {
  let done = false;

  process.stdout.write(`${msg}`);

  const promise = new Promise(async (res) => {
    while (!done) {
      process.stdout.clearLine(0);
      process.stdout.write(`\r ${msg}`);
      await setTimeout(500);
      process.stdout.write('.');
      await setTimeout(500);
      process.stdout.write('.');
      await setTimeout(500);
      process.stdout.write('.');
      await setTimeout(500);
    }

    process.stdout.write('\n');
    res(done);
  });

  return {
    done: () => {
      done = true;
    },
    wait: () => promise,
  };
}

export async function bulkInsertCases(
  db: Knex<any, any[]>,
  projectUid: string,
  n = 10,
) {
  let asyncOps: Promise<any>[] = [];

  const { done, wait } = loader(`Inserting ${n} Test Cases`);

  for (let i = 0; i < n; i++) {
    const uid = await getNextId(db, TestCase);

    asyncOps.push(
      db('testCases')
        .insert({
          uid,
          testCaseRef: uid,
          version: 1,
          active: true,
          name: faker.lorem.words(),
          externalId: faker.string.alphanumeric(10),
          source: faker.company.name(),
          customFields: {
            priority: faker.number.int(100),
            status: faker.number.int(100),
          },
          priority: faker.number.int(100),
          status: faker.number.int(100),
          projectUid,
          steps: [],
        })
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

export async function bulkInsertTestExecutions(
  db: Knex<any, any[]>,
  projectUid,
  n = 10,
) {
  let asyncOps: Promise<any>[] = [];

  const cases = await bulkInsertCases(db, projectUid, 100).then((res) =>
    res.filter((r) => r.status === 'fulfilled').map((r) => r.value),
  );

  const runs = await bulkInsertRuns(db, projectUid, 100).then((res) =>
    res.filter((r) => r.status === 'fulfilled').map((r) => r.value),
  );

  const { done, wait } = loader(`Inserting ${n} Test Executions`);

  for (let i = 0; i < n; i++) {
    const randomCase = cases[i % cases.length];
    const randomRun = runs[i % runs.length];

    asyncOps.push(
      db('testExecutions')
        .insert({
          externalId: faker.string.alphanumeric(10),
          source: faker.company.name(),
          name: faker.lorem.words(),
          status: faker.number.int(100),
          testCaseRef: randomCase.testCaseRef,
          testRunUid: randomRun.uid,
          priority: faker.number.int(100),
          testCaseUid: randomCase.uid,
          projectUid,
          customFields: {
            tags: [{ uid: 1, name: 'Test Tag' }],
          },
          steps: [],
        })
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

export async function bulkInsertMilestones(
  db: Knex<any, any[]>,
  projectUid,
  n = 10,
) {
  let asyncOps: Promise<any>[] = [];

  const { done, wait } = loader(`Inserting ${n} Milestones`);

  for (let i = 0; i < n; i++) {
    asyncOps.push(
      db('tags')
        .insert({
          name: faker.lorem.words(),
          description: faker.lorem.lines(1),
          systemType: 'milestone',
          customFields: {
            dueAt: faker.date.future(),
            status: faker.number.int(100),
          },
          entityTypes: [],
          projectUid,
        })
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

export async function bulkInsertRuns(
  db: Knex<any, any[]>,
  projectUid: string,
  n = 10,
) {
  let asyncOps: Promise<any>[] = [];

  const { done, wait } = loader(`Inserting ${n} Runs`);

  for (let i = 0; i < n; i++) {
    asyncOps.push(
      db('tags')
        .insert({
          name: faker.lorem.words(),
          projectUid,
          customFields: {
            priority: faker.number.int(100),
            status: faker.number.int(100),
          },
          systemType: 'run',
          entityTypes: [],
        })
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

export async function bulkInsertPlans(
  db: Knex<any, any[]>,
  projectUid: string,
  n = 10,
) {
  let asyncOps: Promise<any>[] = [];

  const { done, wait } = loader(`Inserting ${n} Plans`);

  for (let i = 0; i < n; i++) {
    asyncOps.push(
      db('tags')
        .insert({
          name: faker.lorem.words(),
          slug: faker.string.uuid(),
          description: faker.lorem.lines(1),
          systemType: 'plan',
          customFields: {
            status: faker.number.int(100),
            priority: faker.number.int(100),
          },
          entityTypes: [],
          projectUid,
        })
        .catch((e) => {
          console.error(e);
          throw e;
        }),
    );
  }

  const result = Promise.allSettled(asyncOps).then((val) => {
    done();
    return val;
  });

  return Promise.all([wait(), result]).then(([_, result]) => result);
}

export async function main(tenantId: string) {
  let db: Knex<any, any[]>;
  try {
    db = setupDB(tenantId);

    const dbName = await db
      .raw(`select current_database() as db_name;`)
      .then(({ rows }) => rows[0]?.db_name);

    console.log('Connected to DB: ', dbName);

    const project = await createProject(db);

    await bulkInsertPlans(db, project.uid, 1000);
    await bulkInsertMilestones(db, project.uid, 1000);
    await bulkInsertRuns(db, project.uid, 1000);
    await bulkInsertCases(db, project.uid, 1000);
    await bulkInsertTestExecutions(db, project.uid, 1000);
  } catch (e) {
    console.error(e);
  } finally {
    await db?.destroy();
  }
}

const tenantId = process.argv.slice(2).at(0);

if (!tenantId) {
  console.error('Usage: npx ts-node path/to/script.ts <tenant_id>');
  console.error('tenant_id is required');
  process.exit(1);
}

main(tenantId);
