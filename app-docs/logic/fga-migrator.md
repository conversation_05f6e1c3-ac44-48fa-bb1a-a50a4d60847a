# FGA Migrator Script

## Overview
The FGA Migrator is a utility designed to apply schema migrations to the FGA (Fine-Grained Authorization) service. It ensures that all tenant schemas remain up-to-date and consistent with system requirements.


## How It Works
1. **Pulling the Latest Schema**:
- The script processes migration files in sequential order.
- Each migration file contains specific changes to the FGA schema, such as adding new roles, permissions, or conditions.

2. **Applying Migrations**:
   - The script processes migration files in sequential order.
   - Each migration file contains specific changes to the FGA schema, such as adding new roles, permissions, or conditions.
3. **No Rollback Support**:
   - The script does not support rolling back migrations. Once a migration is applied, it cannot be reverted automatically.
   - Ensure that migrations are tested thoroughly in a staging environment before applying them to production.


## Usage
1. **Run the Migrator**:
   - Execute the script using the command:
     ```bash
     npm run fga migrate
     ```
   - This will update all tenants to the latest FGA schema and apply any pending changes.

2. **Create a New Migration**:
   - To create a new migration file, use the command:
     ```bash
     npm run fga make:migrate
     ```
   - This will generate a new migration file where you can define the required schema changes.

