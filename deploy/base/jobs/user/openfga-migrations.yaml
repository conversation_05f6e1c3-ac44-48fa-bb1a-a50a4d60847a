apiVersion: batch/v1
kind: Job
metadata:
  name: openfga-migrate
  namespace: production
  labels:
    app.kubernetes.io/instance: openfga-migrate
    app.kubernetes.io/name: openfga-migrate
spec:
  template:
    metadata:
      labels:
        app.kubernetes.io/instance: openfga-migrate
        app.kubernetes.io/name: openfga-migrate
    spec:
      containers:
        - name: migrate-database
          image: openfga/openfga:v0.55.7
          imagePullPolicy: Always
          args: ["migrate"]
          env:
            - name: OPENFGA_MAX_CONCURRENT_READS_FOR_LIST_OBJECTS
              value: "4294967295"
            - name: OPENFGA_GRPC_ADDR
              value: 0.0.0.0:8081
            - name: OPENFGA_HTTP_ENABLED
              value: "true"
            - name: OPENFGA_HTTP_ADDR
              value: 0.0.0.0:8080
            - name: OPENFGA_HTTP_CORS_ALLOWED_ORIGINS
              value: '*'
            - name: OPENFGA_HTTP_CORS_ALLOWED_HEADERS
              value: '*'
            - name: OPENFGA_PLAYGROUND_ENABLED
              value: "false"
            - name: OPENFGA_DATASTORE_ENGINE
              value: postgres
            - name: OPENFGA_DATASTORE_URI
              valueFrom:
                secretKeyRef:
                  key: uri
                  name: openfga-secrets
        - name: cloud-sql-proxy
          args:
            - --private-ip
            - --structured-logs
            - --port=5432
            - testfiesta-production-3119:us-central1:testfiesta-production-db
          env:
            - name: DB_NAME
              valueFrom:
                secretKeyRef:
                  key: OPENFGA_DB_DATABASE
                  name: openfga-secrets
            - name: DB_PASS
              valueFrom:
                secretKeyRef:
                  key: OPENFGA_DB_PASSWORD
                  name: openfga-secrets
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  key: OPENFGA_DB_USERNAME
                  name: openfga-secrets
          image: gcr.io/cloud-sql-connectors/cloud-sql-proxy:2.8.0
          imagePullPolicy: IfNotPresent
      restartPolicy: Never
      serviceAccount: testfiesta-openfga-sa
      serviceAccountName: testfiesta-openfga-sa
  backoffLimit: 1
