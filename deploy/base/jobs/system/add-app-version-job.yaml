apiVersion: batch/v1
kind: Job
metadata:
  name: app-version-insert
  labels:
    app: backend-job
    tier: api
spec:
  template:
    metadata:
      labels:
        app: backend-job
        tier: api
    spec:
      restartPolicy: Never
      containers:
      - name: app-version-insert
        image: backend-image
        command: 
          - "npm"
          - "run"
          - "version:insert"
          - "--"
          - "--component='backend'"
          - "--appVersion='v0.62.8'"
        imagePullPolicy: Always
        env:
            - name: DB_DATABASE
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: DB_DATABASE
            - name: DB_HOST
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: DB_HOST
            - name: DB_PORT
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: DB_PORT
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: DB_PASSWORD
            - name: DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: DB_USERNAME
            - name: REDIS_HOST
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: REDIS_HOST
            - name: REDIS_CA
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: REDIS_CA
            - name: REDIS_PORT
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: REDIS_PORT
            - name: OPENFGA_DATASTORE_HOST
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: OPENFGA_DATASTORE_HOST
            - name: OPENFGA_DATASTORE_API_SCHEME
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: OPENFGA_DATASTORE_API_SCHEME
            - name: OPENFGA_DATASTORE_NAME
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: OPENFGA_DATASTORE_NAME
            - name: OPENFGA_DATASTORE_STORE_ID
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: OPENFGA_DATASTORE_STORE_ID
            - name: OPENFGA_DATASTORE_AUTH_MODEL
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: OPENFGA_DATASTORE_AUTH_MODEL
            - name: JWT_EXPIRATION_TIME 
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: JWT_EXPIRATION_TIME
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: JWT_SECRET
            - name: JWT_SIGNING_SECRET
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: JWT_SIGNING_SECRET
            - name: OAUTH_JIRA_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: OAUTH_JIRA_CLIENT_ID
            - name: OAUTH_JIRA_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: OAUTH_JIRA_CLIENT_SECRET
            - name: OAUTH_GITHUB_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: OAUTH_GITHUB_CLIENT_ID
            - name: OAUTH_GITHUB_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: OAUTH_GITHUB_CLIENT_SECRET
            - name: OAUTH_GOOGLE_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: OAUTH_GOOGLE_CLIENT_ID
            - name: OAUTH_GOOGLE_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: OAUTH_GOOGLE_CLIENT_SECRET
            - name: SENDGRID_KEY
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: SENDGRID_KEY
            - name: SENDGRID_SIGNING_SECRET
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: SENDGRID_SIGNING_SECRET
            - name: OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: OPENAI_API_KEY
            - name: GC_SERVICE_KEY_FILE
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: GC_SERVICE_KEY_FILE
            - name: GCS_BUCKET_NAME
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: GCS_BUCKET_NAME
            - name: STRIPE_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: STRIPE_SECRET_KEY
            - name: STRIPE_SIGNING_SECRET
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: STRIPE_SIGNING_SECRET
            - name: INTEGRATION_1_KEY
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: INTEGRATION_1_KEY
