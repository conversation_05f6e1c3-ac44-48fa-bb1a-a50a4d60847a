steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'us-central1-docker.pkg.dev/cicd-0927/testfiesta-backend/testfiesta-backend:$TAG_NAME', '--build-arg', 'GITHUB_NPM_TOKEN', '.']
    secretEnv: ['GITHUB_NPM_TOKEN']

  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'us-central1-docker.pkg.dev/cicd-0927/testfiesta-backend/testfiesta-backend:$TAG_NAME']

  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
     - -c
     - |
       EXTERNAL_IP="$(curl icanhazip.com)/32"
       echo "$$EXTERNAL_IP" | tee /workspace/IP.txt
       EXTERNAL_CIDRS="$(gcloud container clusters describe staging-cluster --location us-central1 --format 'value(masterAuthorizedNetworksConfig.cidrBlocks[].cidrBlock)' | sed 's/;/,/g'),$$EXTERNAL_IP"
       gcloud container clusters update staging-cluster --enable-master-authorized-networks --master-authorized-networks "$$EXTERNAL_CIDRS"
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-staging-d872'

  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'container'
      - 'clusters'
      - 'get-credentials'
      - 'staging-cluster'
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-staging-d872'

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'kubectl'
    args:
      - 'apply'
      - '-k'
      - 'deploy/overlays/staging/deployment'
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CONTAINER_CLUSTER=staging-cluster'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-staging-d872'

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'kubectl'
    args:
      - 'rollout'
      - 'status'
      - '-n'
      - 'staging'
      - 'deployment/backend-v0-62-8'
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CONTAINER_CLUSTER=staging-cluster'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-staging-d872'

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'kubectl'
    args:
      - 'apply'
      - '-k'
      - 'deploy/overlays/staging/jobs/system'
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CONTAINER_CLUSTER=staging-cluster'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-staging-d872'

  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
     - -c
     - |
       kubectl wait --timeout=2m --for=condition=complete -n staging job/app-version-insert-v0-62-8 || STATUS=$?
       if [ $$STATUS -ne 0 ]; then curl -vvv -XPOST -H 'Content-Type:application/json' --data "{\"content\":\"Version adding failed for Staging Backend [v0-62-8]\"}" https://discord.com/api/webhooks/1381490175586074684/3YRKtiRiKXZqSFEKXkwDV4SBSGwu2TmZLCuTFH_SUejleX3X5zv1n4PO41vZMRUAvHDI; fi
       echo "Version create done"
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CONTAINER_CLUSTER=staging-cluster'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-staging-d872'

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'kubectl'
    args:
      - 'apply'
      - '-k'
      - 'deploy/overlays/staging/jobs/user'
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CONTAINER_CLUSTER=staging-cluster'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-staging-d872'

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'kubectl'
    args:
      - 'apply'
      - '-k'
      - 'deploy/overlays/staging/deployment/default'
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CONTAINER_CLUSTER=staging-cluster'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-staging-d872'

  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
     - -c
     - |
       EXTERNAL_IP=$(cat /workspace/IP.txt)
       EXTERNAL_CIDRS=$(gcloud container clusters describe staging-cluster --location us-central1 --format 'value(masterAuthorizedNetworksConfig.cidrBlocks[].cidrBlock)' | sed 's/;/,/g' | sed "s~,$$EXTERNAL_IP~~g")
       gcloud container clusters update staging-cluster --enable-master-authorized-networks --master-authorized-networks "$$EXTERNAL_CIDRS"
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-staging-d872'

images:
  - 'us-central1-docker.pkg.dev/cicd-0927/testfiesta-backend/testfiesta-backend:$TAG_NAME'
availableSecrets:
  secretManager:
  - versionName: projects/testfiesta-staging-d872/secrets/GITHUB_NPM_TOKEN/versions/latest
    env: 'GITHUB_NPM_TOKEN'
options:
  logging: CLOUD_LOGGING_ONLY
