steps:
  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
     - -c
     - |
       EXTERNAL_IP="$(curl icanhazip.com)/32"
       echo "$$EXTERNAL_IP" | tee /workspace/IP.txt
       EXTERNAL_CIDRS="$(gcloud container clusters describe production-cluster --location us-central1 --format 'value(masterAuthorizedNetworksConfig.cidrBlocks[].cidrBlock)' | sed 's/;/,/g'),$$EXTERNAL_IP"
       gcloud container clusters update production-cluster --enable-master-authorized-networks --master-authorized-networks "$$EXTERNAL_CIDRS"
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-production-3119'

  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'container'
      - 'clusters'
      - 'get-credentials'
      - 'production-cluster'
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-production-3119'

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'kubectl'
    args:
      - 'apply'
      - '-k'
      - 'deploy/overlays/production/deployment'
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CONTAINER_CLUSTER=production-cluster'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-production-3119'

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'kubectl'
    args:
      - 'rollout'
      - 'status'
      - '-n'
      - 'production'
      - 'deployment/backend-v0-62-8'
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CONTAINER_CLUSTER=production-cluster'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-production-3119'

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'kubectl'
    args:
      - 'apply'
      - '-k'
      - 'deploy/overlays/production/jobs/system'
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CONTAINER_CLUSTER=production-cluster'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-production-3119'

  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
     - -c
     - |
     - kubectl wait --timeout=2m --for=condition=complete -n production job/app-version-insert-v0-62-8 || STATUS=$?
       if [ $$STATUS -ne 0 ]; then curl -vvv -XPOST -H 'Content-Type:application/json' --data "{\"content\":\"Version adding failed for **PRODUCTION** Backend [v0-62-8]\"}" https://discord.com/api/webhooks/1381490175586074684/3YRKtiRiKXZqSFEKXkwDV4SBSGwu2TmZLCuTFH_SUejleX3X5zv1n4PO41vZMRUAvHDI; fi
     - echo "Add version done."
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-production-3119'

  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
     - -c
     - |
       EXTERNAL_IP=$(cat /workspace/IP.txt)
       EXTERNAL_CIDRS=$(gcloud container clusters describe production-cluster --location us-central1 --format 'value(masterAuthorizedNetworksConfig.cidrBlocks[].cidrBlock)' | sed 's/;/,/g' | sed "s~,$$EXTERNAL_IP~~g")
       gcloud container clusters update production-cluster --enable-master-authorized-networks --master-authorized-networks "$$EXTERNAL_CIDRS"
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=us-central1'
      - 'CLOUDSDK_CORE_PROJECT=testfiesta-production-3119'

options:
  logging: CLOUD_LOGGING_ONLY
