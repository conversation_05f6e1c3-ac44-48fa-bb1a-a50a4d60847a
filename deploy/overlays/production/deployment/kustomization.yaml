apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../base/deployment

namespace: production

images:
- name: backend-image
  newName: us-central1-docker.pkg.dev/cicd-0927/testfiesta-backend/testfiesta-backend
  newTag: v0.61.6

nameSuffix: -v0-61-6

commonLabels:
  version: v0.61.6

patches:
- path: app-patches.yaml
  target:
    kind: Deployment
    labelSelector: app=backend
- path: route-patches.yaml
  target:
    kind: HTTPRoute
    labelSelector: app=backend
- path: hc-patches.yaml
  target:
    kind: HealthCheckPolicy
    labelSelector: app=backend
