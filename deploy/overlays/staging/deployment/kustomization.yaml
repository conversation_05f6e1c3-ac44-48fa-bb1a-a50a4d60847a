apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../base/deployment

namespace: staging

images:
- name: backend-image
  newName: us-central1-docker.pkg.dev/cicd-0927/testfiesta-backend/testfiesta-backend
  newTag: v0.62.8

nameSuffix: -v0-62-8

commonLabels:
  version: v0.62.8

patches:
- path: app-patches.yaml
  target:
    kind: Deployment
    labelSelector: app=backend
- path: route-patches.yaml
  target:
    kind: HTTPRoute
    labelSelector: app=backend
- path: hc-patches.yaml
  target:
    kind: HealthCheckPolicy
    labelSelector: app=backend
