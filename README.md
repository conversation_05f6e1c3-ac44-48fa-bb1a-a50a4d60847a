## Warning

This repository consists of some private github packages that can only be installed by github personal access token auth;

1. Generate a github personal access token with the `read:package` permissions.
2. set the `GITHUB_NPM_TOKEN` variable in your terminal env


## Deployments
Deployments and version upgrades are automated in staging.

Version creation is automated in production.  **No actual upgrades/migrations ar
e automated in production.**

In production one must:
- Update the individual account versions to the latest
- Run migrations (regular and FGA) 


## Usage

for developers who simply want to use this service (frontend, QA, e.t.c) and are not actively making changes to the repo

#### 1. Setup env variables

Simply create you `.env` based off the `.env.example` template, and adjust to your taste

For the sake of private in house components; `@ss-libs/*` packages, you need to have your [Github access token](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens) and set it as the value of `GITHUB_NPM_TOKEN` in the `.env` file. This token must have the `read:packages` permission attached to it.

```sh
# .env file
GITHUB_NPM_TOKEN=<your-github-token>
```

#### 2. Start processes

```sh
# to run all services
docker compose up --build

# to run in the background
docker compose up --build -d

# to have a fresh install
docker compose down
docker compose up --builld # or -d if you want to run in background
```

## Development

#### 1. Setup env variables

Simply create you `.env.local` based off the `.env.example` template, and adjust to your taste

```sh
cp .example.env .env.local
```

For the sake of in house components; `@ss-libs/*` packages, you need to have your [Github access token](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens) and set it as the value of `GITHUB_NPM_TOKEN` in the `.env` file. This token must have the `read:packages` permission attached to it.

```sh
# .env file
GITHUB_NPM_TOKEN=<your-github-token>
```

#### 2. Install dependencies:

You can now run the following command **in the same terminal session as previous step** to install your dependencies

```sh
npm install
```

#### 3. Setup software dependencies

To make development easier, the development dependencies (postgres, redis and openfga) can be setup easily using `docker compose`. The docker compose file does the following

1. Sets up Postgres
2. Sets up Redis
3. Sets up OpenFGA and [integrates it with the existing postgres](https://openfga.dev/docs/getting-started/setup-openfga/docker)

```sh
# make sure you're in the project root
docker compose up --build redis openfga postgres migrate-openfga temporal 

# if you want to use temporal-ui for debugging
docker compose up --build redis openfga postgres migrate-openfga temporal temporal-ui

# make sure you load .env.local for docker-compose.yml
docker compose --env-file ./.env.local up


# to stop the servers
docker compose stop

# to delete the servers
docker compose down
```

#### 4. Migrate the database

To migrate the database to it's latest structure:

```sh
npm run migrate:latest
```

`Note: Tenants in maintenance mode are skipped during the migration process.`

#### 5. Setup Openfga

This stage is now automated, provided you have set the `OPENFGA_STORE_NAME` in your `.env` file, you shuold be good to go

#### 6. Starting the application

starting the application is as easy as running the following commands

1. In one terminal, start the build process in watch mode

```sh
npm run watch
```

2. In a second terminal, start the server in dev mode (hot reload enabled)

```sh
npm run start:dev
```

3. You might also need the workers to be running for some functionalities (such as signup and org creation)

```sh
npm run start:worker:dev
```

## Testing

we use [mocha](https://mochajs.org/) for automated testing, to test the application: ensure you've installed dependencies, updated your env, as well as setup all softwares required.

```sh
npm test
```

## Copy Database to other host

This script allows you to copy tenant databases from one host to another. It ensures that each tenant is associated with the correct `dbServer` and updates the `dbServerUid` in the `tenants` table accordingly.

### Prerequisites

1. Ensure that the source database configurations are properly set in the `.env` file.
2. The `dbServers` table must have a valid `isDefault` server for the region, and the `appNodes` table must have a corresponding `isDefault` node.

### How It Works

1. The script fetches all tenants that either match the provided `uids` or have a `NULL` value for `dbServerUid`.
2. It identifies the default `dbServer` and `appNode` for the region.
3. Updates the `dbServerUid` in the `tenants` table for each tenant.
4. Copies the tenant's database from the source host to the destination host.

### Running the Script

To run the database copy script, use the following command:

```bash
npm run tenant:copy -- <tenant-uids>
```
tenant-uids are optional

## Authorization

Authorization is powered by openfga and implemented in [our auth component](https://github.com/ss-libs/ss-component-auth). Members of an org get assigned a set of predefined roles and these roles determine if an org member can perform the desired operation on the specified resource. As of this writing, the possible roles available in an org are:

- owner
- admin
- member
  With owner having all privileges and member having the least prvileges. accessing a particular resource (case, project, step, e.t.c) also depends on the the `handle` param, the resource type, as well as the request's HTTP method. While handles are just user friendly unique identifiers (like github usernames), the resource type is a special value that must be set in the `req.locals.resource` value, a helper middleware `setResource` has been added to the codebase for this reason. for the request's method, if a handle belongs to an `ownerType` of user, we simply check if the handle `ownerUid` is the user in session's `uid`. for cases where the handle belongs to an org:
- for GET requests, the user must have the `can_view` rights to the resource
- for POST requests, the user must have the `can_create_resource` permission in the org
- for PATCH/PUT requests, the user must have the `can_edit` rights to the resource
- for DELETE, the user must have the `can_delete` rights to the resource
  all of the `can_*` are mostly inherited from the role of a member of an org, to understand this better, using the local playground to visualise the authorization model is highly recommended, this will help clarify most black boxes.

## Test Cases / Executions / Steps / Results

There are a number of ways that this could have been designed, so I'm documentening the current (01/19/24) thinking on initial implementation for future contemplation.

A `test_case` is a generic test. This is versioned, so there is a version counter and each new version has its own uid but references back to a shared `test_case_uid` that is the same between all versions of a case. For exploratory tests, these equate to "test charters".
_customFields_ on this type of entity include things that are generic to the case or charter (e.g. estimate, charter, preconditions, etc).

A `test_execution` is a specific instance of a case that is attached to a test run. It references a specific version of a specific test case (via the `uid` field on the `test_case` table). For exploratory tests, these equate to a single Pinata "session" or timeline (even if it's worked on over multiple work sessions).
_customFields_ on this type of entity include things that are specific to an execution (e.g. assignee, duration, etc).

A `test_step` is an individual step in a test case. These are versioned just like test cases. For static manual tests, these are always the same. For exploratory tests, you won't have steps. Instead, it just stores data on the `test_execution_steps` bridge table.
_customFields_ on this type of entity include things that are generic to the step (e.g. estimate, description, etc).

A `test_execution_step` is a bridge table that represents the test step as it was when the test execution was run. Normally, there is very little data here as a bridge table, but in the case of exploratory tests, we store the adhoc data here since it is always different. The alternative would be to create an endless number of `test_steps` for the case/charter that are different every time. This would create needless joins and not fit well in the data model at scale. Instead, we store all customFields data on the bridge table with no step linked.
\_customFields\* on this type of entity include things that are specific to an execution step (e.g. assignee, duration, exploratory specific step fields, etc).

A `test_result` is an individual result (with a status and comment) from running either an execution or a test_execution_step. There was some back and forth on whether this data should just be included in the text execution and test execution step tables, but it was broken into a separate entity largely because that's how many other tools do it. In my mind, you'd only do a test run once and thus each execution would truly be a single time of running a test. In actuality, test runs get reused a lot as a project is tested multiple times. So people expect to be able to just add new "results" onto each execution or execution step. These do not exist for exploratory tests.

## Versioning

Versioning entities within tables should all work the same way. Using `test_cases` as an example: The two "id" fields on each `test_case` are `test_case.uid` and `test_case.version_ref`. The `uid` field is universally unique for a specific `test_case` version. The `version_ref` field represents the abstract idea of a specific case and this field is the same for all versions of the same case. The `test_case.version` field is an incrementing integer that keeps track of the order of the versions for a case. The `test_case.active` field is a boolean field that indicates which version of the case is the current "active" version.

So here's an example case:
| uid | version_ref | version | active |

---

| AAA | XXXX | 1 | false |
| BBB | XXXX | 2 | false |
| CCC | XXXX | 3 | true |

This shows a single test case that has been updated twice. So there are three versions and the latest version is the current active version displayed in the front end.

This gets really interesting for `test_case_steps` because we also want to track when a step is removed from a case. In the event that a step is removed from a case, we should create a new case version like we do for other changes to the case, but then update the `test_case_steps.test_case_removed_version` field to point at the last version number where that case and step were connected. Likewise, adding a new step to a case requires recording the step version where it was added.

Overall, this layout means its important when looking at intertable relationships to note whether the relationship references a case (and whatever it's latest version is) via the `test_case_version_ref` field or if it references a specific version of a case that was active at the time the relationship was made via the `test_case_uid` field. e.g. `test_steps` are linked to a `test_case` - no matter which version of that case is the latest, so they are linked via `test_case_version_ref` and `test_step_version_ref`. `test_executions` are linked to a specific version of a `test_case` that was active when the execution was run, so they are linked via `test_case_uid` and `test_execution_uid`.

## Generating Open Api Spec
First we need to run the following command to generate open api spec file. For production build, this is happen at the build time whereas for the development if we need it, we have to manually generate it.

```bash
npm run api:make-docs
```
For `v1` Api spec route will be hosted at: `/v1/spec`

## Encryption

For the encryption setup, the `.env` file requires a versioned private key based on the service. For example, you should define your key as `INTEGRATION_1_KEY`, where `1` represents the version number of the key and `ÌNTEGRATION` is the service name.
#### Generate RSA key pair
1. Generate RSA key pair for every service, insert public key into  `publicKey` table  and get private key
```typescript
npm run seed:encryptionKeys
```
2. Store generated private key in `.env` file.


```typescript
//So here's an example private key will be generated on your terminal:
INTEGRATION_1_KEY="-----BEGIN PRIVATE KEY-----\nMIIJQgIBADANBgkqhkiG9w0BAQEFAASCCSwwggk...\n-----END PRIVATE KEY-----\n"
```


We utilize a hybrid approach for encryption, combining both symmetric and asymmetric encryption methods to ensure robust security for sensitive data.

For more detailed information regarding our encryption practices, please refer to the [encryption component documentation](https://github.com/ss-libs/ss-component-encryption).

## 🔧 Maintenance Mode
This script allows you to toggle maintenance mode for specific tenants or all tenants.

```bash
# Enable maintenance mode for all tenants
npm run maintenance:on all

# Disable maintenance mode for all tenants
npm run maintenance:off all

# Enable maintenance mode for specific tenants (comma-separated uids)
npm run maintenance:on tenant1,tenant2

# Disable maintenance mode for specific tenants
npm run maintenance:off tenant1,tenant2

```

## Account Upgrades (Temporary Process)
Until the admin portal is available, account upgrades must be performed manually using the user version update job in production. Follow these steps:

1. Prepare the tenant information:
   - Collect the tenant UIDs or handles that need to be upgraded
   - Ensure you have kubectl access to the production cluster

2. Update the user version update job:
   ```sh
   # Add tenant identifiers (UIDs or handles) as comma-separated arguments
   # Example: docker cmd: ["npm", "run", "migrate:version"]
   kubectl edit -k deploy/overlays/production/jobs/user
   ```

3. Apply the updated job configuration:
   ```sh
   kubectl apply -k deploy/overlays/production/jobs/user
   ```

4. Monitor the job completion in the production cluster


## System Architecture Components

### App Versions, Nodes, and Database Servers

The system uses a coordinated architecture of app versions, nodes, and database servers to manage multi-tenant deployments:

#### App Versions
- Tracks different versions of frontend and backend components
- Each version is recorded with metadata about the release
- Used to ensure compatibility between components
- Managed through two key scripts:

  1. **migrate:version**: Performs complete version migration
  ```bash
  # Run complete version migration (defaults to both component)
  npm run migrate:version

  # Run migration for specific component
  VERSION_ARGS="--component frontend backend" npm run migrate:version  # Migrate frontend version

  # Run migration for specific app node
  VERSION_ARGS="--appNode 1" npm run migrate:version # Migrate tenants on app node 1

  # Run migration for specific tenants
  VERSION_ARGS="--tenantIds tenant1 tenant2" npm run migrate:version

  # Run migration and invalidate session for tenants
  VERSION_ARGS="--tenantIds tenant1 tenant2 --invalidateSession" npm run migrate:version # Migrate tenants and invalidate session

  # Run migration for specific app version
  VERSION_ARGS="--appVersion 2" npm run migrate:version # Migrate to version 2
  ```
  This command executes three sequential operations:
  - Updates tenant version mappings (version-migrator.ts)
  - Runs database migrations (migrator.js)
  - Updates Fine-Grained Access control (fga-migrator.ts)

  **Available Options:**
  - `--component, -c`: Component to migrate (frontend or backend)
  - `--appNode, -a`: App node UID to migrate
  - `--tenantIds, -t`: List of tenant UIDs to migrate
  - `--appVersion, -v`: App version UID to migrate
  - `--help, -h`: Show help information

 2. **version:insert**: Manages version information
    ```bash
    npm run version:insert
    # or with specific component
    npx ts-node ./db/utils/addAppVersion.ts --component <frontend|backend>
    ```

  **Version Update Process:**
  1. First run `version:insert` to register the new version
  2. Then execute `migrate:version` to apply all migrations

#### App Nodes
- Represents physical or virtual servers running the application
- Each region has a default node marked with `isDefault`
- Nodes are associated with specific database servers
- Used for routing tenant requests to appropriate servers

#### Database Servers
- Manages the physical database instances
- Each region has a default server marked with `isDefault`
- Linked to app nodes for coordinated tenant management
- Used for tenant database allocation and management

#### How They Work Together
1. When a new tenant is created:
   - System identifies the default database server for the region
   - Associates tenant with appropriate app node
   - Sets up tenant's database on the assigned server

2. For tenant operations:
   - Requests are routed to correct app node
   - App version compatibility is verified
   - Database operations use the assigned server

3. For maintenance and scaling:
   - New app versions can be rolled out gradually
   - Tenants can be migrated between database servers
   - App nodes can be added or removed as needed

## Database Utility Scripts

The following utility scripts are available in the `db/utils` directory to help manage various database operations:

### App Version Management
**addAppVersion.ts**: Manages application version information for components (frontend/backend).
  ```bash
  npx ts-node db/utils/addAppVersion.ts --component <frontend|backend> --version <version>
  ```
### Integration Deactivation Script
 The **deactivateIntegrations.ts** script is a utility for bulk deactivation of integrations across all tenants in the system.
  ```bash
  npx ts-node db/utils/deactivateIntegrations.ts
  ```
### Encryption Keys Generation Script
 The **generateEncryptionKeys.ts** script is a utility for generating and managing RSA key pairs for various services in the system..
  ```bash
  npx ts-node db/utils/generateEncryptionKeys.ts
  ```

### Integration Sync Setup Script
 The **integrationSync.ts** script is a one-time utility for setting up integration synchronization workflows for existing integrations across all organizations.
 ```bash
  npx ts-node db/utils/integrationSync.ts
 ```
  #### Process Flow
  1. Connects to shared database
  2. Retrieves all active organization handles
  3. For each organization:
    - Connects to tenant's database
    - Retrieves active integrations (non-deleted, non-archived)
    - Sets up temporal workflows for each integration
    - Configures cron schedules based on service configurations 

### Integration Sync Duration Update Script
  The **integrationSyncDuration.ts** script is a utility for updating the sync duration configuration for all active integrations across organizations.
  ```bash
  npx ts-node db/utils/integrationSyncDuration.ts
  ```
#### Process Flow
1. Connects to shared database
2. Retrieves all active organization handles
3. For each organization:
   - Connects to tenant's database
   - Retrieves active integrations (non-deleted, non-archived)
   - Updates integration configuration with '3m' sync duration
   - Logs progress and results

### Integration Refresh Script
The **refreshIntegrations.ts** script is a utility for queuing update workflows for all active integrations across organizations.
```bash
npx ts-node db/utils/refreshIntegrations.ts
```
  #### Process Flow
  1. Connects to shared database
  2. Retrieves all active organization handles
  3. For each organization:
    - Fetches tenant database configuration
    - Connects to tenant's database
    - Retrieves active integrations (non-deleted, non-archived)
    - Queues update workflow for each integration

### Stripe Plan Synchronization Script
 The **stripe-plan-sync.ts** script is a utility for synchronizing Stripe pricing plans with the application's subscription plans.
 ```bash
 npx ts-node db/utils/stripe-plan-sync.ts
 ```
 #### Purpose
  1. Connects to database and Stripe API
  2. Marks all existing subscription plans as inactive
  3. Fetches Stripe prices for specified version
  4. For each price:
    - Parses metadata details
    - Maps Stripe price to subscription plan
    - Sets billing and entity types
    - Configures features and limits
  5. Inserts or updates plans in database

### Tenant Maintenance Script
 The **tenantMaintenance.ts** script is a utility for managing maintenance mode status for tenants in the system.
 ```bash
 npx ts-node db/utils/tenantMaintenance.ts <true|false> <tenant_ids|all>
 ```
 #### Arguments
1. Maintenance Flag (Required):
   - true : Enable maintenance mode
   - false , f , or 0 : Disable maintenance mode
2. Tenant Selection (Required):
   - Comma-separated tenant UIDs
   - all for system-wide update

### Tenant Config Middleware
This middleware allows each tenant to have custom settings stored in the `tenants.config` JSON. These settings override the default values if provided. The final config is injected into req.configs so it can be used easily in any endpoint.

Example:
To prevent users from overloading the system by fetching too much dashboard data, we set a maximum date range they can fetch. This config value is injected into the dashboard endpoints, with a default of **90** days.


Tenants can override this default by setting a custom value in their config JSON. For example:
```json
{ "dashboardMaxDateRange": 120 }
```

In this case, the maximum allowed range becomes 120 days instead of the default 90.


#### How to add config values:

Config values are defined in `src/constants/tenant.ts`. You need to specify the HTTP method, an array of endpoint paths, and the config keys required for those endpoints.
