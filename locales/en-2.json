{"testCaseNotFound": "Test case not found", "testExecutionDeleted": "Test execution was deleted successfully", "executionNotFound": "Execution not found", "testCaseDeleted": "Test case was deleted successfully", "tfpDataPushedSuccessfully": "TFPinata data was pushed successfully", "stepDeleted": "Step was deleted successfully", "resultDeleted": "Result was deleted successfully", "testExecutionNotFound": "Test execution not found", "pinataDataPushedSuccessfully": "PINATA data pushed successfully", "testProjectUpdated": "Test project has been updated", "testCasesDeleted": "testCasesDeleted", "sharedStepDeleted": "Shared Step deleted", "sharedStepDeletedInBulk": "Shared Step deleted in bulk", "attachPlansToMilestoneSuccessfully": "Test plans have been attached to milestone successfully", "sharedStepArchived": "Shared Step archived", "toggleSharedStepArchivedInBulk": "Shared Steps archived status toggled in bulk", "sharedStepsNotFound": "Shared Steps not found", "sharedStepUpdated": "Shared Step updated successfully", "projectUnArchived": "Project unarchived successfully", "templateDeleted": "Template deleted successfully", "customFieldDeleted": "Custom Field deleted successfully", "folderDeleted": "Folder deleted successfully", "milestoneDeleted": "Milestone deleted successfully", "runDeleted": "Test run deleted successfully", "tagDeleted": "Tag deleted successfully", "errors": {"organization": {"nameExist": "The name of this organization is already taken.", "invalidOrgUid": "Invalid organization uid", "noOrg": "Organization does not exist", "orgNotFound": "Organization with given ID doesn't exist", "orgFieldRequired": "\"org\" field is required", "invalidOrgNameLength": "The org name must be longer than 2 and shorter than 64 characters", "invalidOrgNameCharacters": "The org name must only contain alpha numeric characters, spaces, hyphens, and underscores.", "orgOwnerCannotBeReassigned": "The owner of the organization cannot be reassigned", "userNotMemberOfOrg": "User not member of organization", "noAccessToOrg": "You don't have access to this organization", "unableToReassignAllOwners": "At least one owner must remain in the organization", "cannotRemoveOwner": "Owner cannot be removed", "userIsNotOwner": "You're not an owner of this account", "unableToRemoveAllOwners": "You cannot remove all owners"}, "authentication": {"invalidService": "Integration service not found", "invalidInviteLink": "This invite link is not invalid", "invitationNotExist": "This invitation is not exist.", "invitationLinkExpired": "This invitation link is invalid or expired", "invitationLinkUsed": "This invitation link was used before", "invitationEmailNoMatch": "This invitation was sent to a different email than the logged in user", "tooManyAttempts": "Too many attempts. Try again after 1 day", "userEmailNotExist": "User with given email doesn't exist", "resetLinkInvalid": "This password reset link is invalid", "resetLinkExpired": "This password reset link is expired", "noAuthenticatedUser": "No authenticated user provided", "emailInUse": "Email already in use", "requireCorrectLogin": "This email is invalid", "userAlreadyInvited": "User has already been invited", "currentPasswordIncorrect": "Your current password is not correct", "requireSignUp": "You should signup first", "invalidCredentials": "Invalid user credentials", "authTokenNotFound": "Service is not authenticated", "refreshTokenExpired": "Refresh token expired, please authenticate again", "accessTokenNotFound": "Access token not found"}, "users": {"invalidUserUid": "Invalid user uid", "userNotFound": "User with given ID doesn't exist", "userNotSpecified": "User not specified", "existingMember": "User already a member of the organization", "emailOrUidRequired": "User email or ID must be provided", "problemFetchingUsers": "We are having problem fetching users", "useruidsMustBeString": "User ids must be strings", "useruidsMustBeArray": "User ids must be an array", "notAdmin": "You are not an admin user", "missingFields": "User ID or organization ID is missing.", "firstNameFieldRequired": "\"firstName\" field is required", "lastNameFieldRequired": "\"lastName\" field is required", "avatarMustBeUrl": "\"avatarUrl\" field must be a URL", "passwordFieldRequired": "\"password\" field is required"}, "validation": {"nameFieldRequired": "\"name\" field is required", "prioritiesMustBeNumbers": "Priorities must be numbers", "prioritiesMustBeArray": "Priorities must be an array", "statusMustBeNumbers": "Status must be numbers", "statusMustBeArray": "Status must be an array", "tagsMustBeNumbers": "Tags must be numbers", "tagsMustBeArray": "Tags must be an array", "queryMustBeString": "Query must be a string", "searchQueryMustBeNumbers": "Search query must be a string", "provideDataToUpdate": "Provide data to update", "statusFieldRequired": "\"status\" field is required", "descriptionFieldRequired": "\"description\" field is required", "priorityFieldRequired": "\"priority\" field is required", "invalidType": "Invalid type", "tokenRequired": "Token is required", "missingPermission": "You don't have permission to perform this action", "typeFieldRequired": "\"type\" field is required", "sizeFieldRequired": "\"size\" field is required", "sourceFieldRequired": "\"source\" field is required", "sourceKeyRequired": "\"source\" key is required", "typeKeyRequired": "\"type\" key is required", "stepUidRequired": "\"step_uid\" key is required", "typesKeyRequired": "\"types\" key is required", "endMustBeDate": "\"endRange\" must be a valid date", "startMustBeDate": "\"startRange\" must be a valid date", "idMustBeNumber": "\"ID\" must be a number", "entriesKeyRequired": "\"entries\" key is required", "invalidSourceKey": "Invalid \"source\" key", "invalidTypeKey": "Invalid \"type\" key", "invalidRequest": "Invalid request", "invalidView": "Invalid view", "projectsNotArray": "\"projects\" query must be an array", "entriesKeyNotArray": "\"entries\" key provided is not an object", "entityTypesFieldNotArray": "\"entityTypes\" key provided is not an array", "runsKeyNotArray": "\"runs\" key provided is not an array", "optionsKeyNotArray": "\"options\" key provided is not an array", "configurationsKeyNotArray": "\"configurations\" key provided is not an array", "tagsKeyNotArray": "\"tags\" key provided is not an array", "limitMustBeNumber": "Limit must be a number", "offsetMustBeNumber": "Offset must be a number", "customFieldsMustBeArray": "\"customFields\" must be an array"}, "plans": {"selectedPlansMustBeNumbers": "Selected plans must be numbers", "planidsFieldRequired": "\"planIds\" field is required", "plansFieldRequired": "\"plans\" field is required", "plansIdFieldRequired": "Each \"planId\" must be integer", "invalidArchivedType": "Each \"archived\" must be (true/false)", "planidsFieldNotArray": "\"planIds\" field is not an array", "plansFieldNotArray": "\"planIds\" field is not an array", "testPlanUidRequired": "\"uid\" field is required for test plan", "eachPlanIdMustBeString": "Each \"planId\" must be an string", "plansAreRequired": "\"plans\" are required and must be array", "planNotFound": "Plan not found", "planuidsMustBeArray": "Plan ids must be an array", "planuidsMustBeNumbers": "Plan ids must be numbers", "problemFetchingTestPlans": "We are having problems fetching test-plans"}, "milestones": {"selectedMilestonesMustBeNumbers": "Selected milestones must be numbers", "invalidTestMilestoneNameLength": "The test milestone name must be longer than 2 and shorter than 64 characters", "milestoneIdIsRequired": "\"milestoneId\" is required", "milestonesAreRequired": "\"milestones\" are required and must be array", "eachMilestoneIdMustBeInteger": "Each \"milestoneId\" must be integer", "invalidDueDate": "invalid due date", "milestoneNotFound": "milestone not found", "milestoneuidsMustBeArray": "Milestone ids must be an array", "milestoneuidsMustBeNumbers": "Milestone ids must be numbers", "problemFetchingTestMilestones": "We are having problems fetching test-milestones"}, "runs": {"selectedRunsMustBeNumbers": "Selected runs must be numbers", "assignDateStartMustBeDate": "Assign date start must be a date", "assignDateEndMustBeDate": "Assign date end must be a date", "dueDateStartMustBeDate": "Due date start must be a date", "dueDateEndMustBeDate": "Due date end must be a date", "runsFieldRequired": "\"runs\" field is required", "runIdsAreRequired": "\"runIds\" are required and must be array", "runIdsMustBeArray": "\"runIds\" must be an array", "eachRunIdMustBeString": "Each \"runId\" must be an string", "testRunUidRequired": "\"uid\" field is required for test run", "invalidTestrunUid": "The test run ID provided is not valid. Please check and try again.", "runNotFound": "Run not found", "runuidsMustBeArray": "Run ids must be an array", "runuidsMustBeNumbers": "Run ids must be numbers", "problemFetchingTestRuns": "We are having problems fetching test-runs"}, "cases": {"problemUpdatingTestCase": "We are having problem updating test-case", "casesIdsAreRequired": "\"ids\" are required and must be array", "testCaseUidIsRequired": "test case id is required", "invalidCaseUid": "Invalid case uid", "caseNotFound": "Case not found", "casesFieldRequired": "Cases must contain at least one item", "caseImprovementCustomFieldRequired": "Please set at least one custom field to generate accurate improvement.", "caseImprovementStepsRequired": "Please set at least one step to generate accurate improvement.", "problemFetchingTestCases": "We are having problems fetching test-cases"}, "executions": {"executionUidIsRequired": "test execution id is required", "testExecutionNotFound": "Test execution not found", "problemFetchingTestExecutions": "We are having problems fetching test-excutions"}, "steps": {"stepNotFound": "Step not found", "stepRelationNotFound": "Step it not related to the execution", "nonSharedStepsRequiredFileds": "Non shared steps must have all required fields", "activeStepNotFound": "Active step not found", "invalidStepUid": "Invalid step uid", "stepsMustBeArray": "\"steps\" must be an array", "sharedStepIdsMustBeAnArray": "'sharedStepIds' must be an array", "sharedStepIdsIsRequired": "'sharedStepIds' is required", "sharedStepsMustBeAnArray": "'sharedSteps' must be an array", "sharedStepsIsRequired": "'sharedSteps' is required", "invalidSharedStepUid": "<PERSON><PERSON>id shared step uid"}, "projects": {"projectNotFound": "Project not found", "projectIdsNotFound": "The following project IDs were not found: ", "noPermissionForProject": "You don't have permission for one of the provided projects.", "testProjectUidRequired": "\"projectId\" field is required for test plan", "projectUidIsRequired": "Each \"projectUid\" is required", "archivedProjectCannotBeUpdated": "The archived project cannot be updated", "projectAlreadyArchived": "The project has already been archived", "projectKeyRequired": "\"key\" field is required for a project", "projectKeyLength": "\"key\" must be between 2 and 10 characters long", "projectKeyPattern": "\"key\" can only contain letters, numbers, hyphens, and underscores", "projectKeyExists": "A project with this key already exists", "projectuidsMustBeArray": "Project ids must be an array", "projectuidsMustBeNumbers": "Project ids must be numbers", "problemFetchingTestProjects": "We are having problems fetching test-projects"}, "tags": {"tagUidRequired": "\"tagId\" field is required", "invalidTagUid": "Invalid tag uid(s)", "tagIdsMustBeArray": "\"tagIds\" must be an array", "eachEntityTypeMustBeString": "Each \"entityType\" must be an string"}, "roles": {"invalidRole": "Invalid role", "permissionsRequired": "Permissions list is required", "invalidPermission": "Invalid permission provided", "duplicateRole": "Role already exists in organisation", "roleNotFound": "Role not found", "roleUidRequired": "Role id is required", "canNotUpdateRole": "Unable to update role", "problemFetchingRoles": "We are having problem fetching roles", "roleIdsIsRequired": "'roleIds' is required", "roleIdsMustBeAnArray": "'roleIds' must be an array", "membersIsRequired": "\"members\" is required", "membersMustBeAnArray": "\"members\" must be an array", "cannotUpdateSystemRole": "You cannot update a system role"}, "attachments": {"attachmentNotFound": "\"attachment\" not found", "ownerIdRequired": "Owner is required", "ownerTypeRequired": "Owner type is required", "fileNameFieldRequired": "\"file_name\" field is required", "invalidMimeType": "invalid \"type\" provided", "mediaTypeFieldRequired": "\"media_type\" field is required", "imageUploadOverSizeLimit": "Image size cannot be larger than 2MB!", "invalidAttachmentUid": "Invalid attachment uid"}, "handles": {"handleDuplicated": "Handle is already in use, please choose another", "handleUnavailable": "Handle not available", "handleNotFound": "Handle not found, please use a registered handle", "handleFound": "Hand<PERSON> found, please select another", "handleLength": "Username must be between 2 and 30 characters", "handleMatch": "Handle can only contain numbers, letters, dashes, and underscores.", "problemFetchingHandle": "We are having problem fetching usernames", "handleIsNotOrg": "<PERSON><PERSON> doesn't belong to an Org"}, "keys": {"keyUnavailable": "Project key not available", "keyFound": "Project key found, please select another"}, "dashboards": {"missingXValue": "Chart missing x value", "missingYValue": "Chart missing y value", "missingWValue": "Chart missing w value", "missingHValue": "Chart missing h value", "editableBoolean": "Editable should be a boolean value", "defaultBoolean": "Default should be a boolean value", "defaultDashboardRestrict": "You should have at least one default dashboard", "dashboardNotFount": "Dashboard not found", "dashboardNotEditable": "Dashboard is not editable.", "dashboardNameRestrict": "Dashboard name must be an string", "dashboardNotFound": "Dashboard not found", "dashboardDateRangeExceeded": "Dashboard date range exceeded"}, "tokens": {"tokenNotFound": "Token with given ID doesn't exist"}, "folders": {"cannotDeleteRootFolder": "Cannot delete project root folder", "folderNotFound": "Folder not found", "invalidFolderUid": "Invalid folder uid", "folderExists": "Folder already exists", "parentFolderNotExists": "Parent folder does not exists", "multipleFoldersForCase": "Case must belong to one folder only", "invalidFolderQueryEntityType": "entityType must be either 'case' or 'execution'", "testRunIdRequiredFolderQuery": "testRunId is required when entityType is execution", "problemFetchingTestFolders": "We are having problems fetching test-folders"}, "repositories": {"repoFieldRequired": "\"repoUID\" field is required", "repoNotFound": "repo not found", "problemFetchingRepoBranches": "We are having problem fetching branches", "problemFetchingTestRepositories": "We are having problems fetching repositories", "problemFetchingTestBranches": "We are having problems fetching branches"}, "customFields": {"customFieldsAreRequired": "\"customFields\" are required and must be object", "customFieldInvalidCustomFieldId": "Invalid custom field id", "customFieldNameRequired": "The name field is required.", "customFieldExists": "Custom field name is already taken", "customFieldTypeRequired": "The type field is required.", "invalidCustomFieldType": "The type field is required.", "customFieldInvalidOptionsArray": "Options must be an array.", "customFieldInvalidSource": "Invalid source value."}, "templates": {"invalidTemplateUid": "Invalid template uid"}, "configs": {"configNotFound": "Config not found", "duplicateConfig": "Config name already in use"}, "suites": {"problemFetchingTestSuites": "We are having problems fetching test-suites"}, "integrations": {"integrationNotFound": "You dont have this service integrated", "integrationConfiguredForProjects": "Integration is configured for projects", "unsupportedIntegration": "Unsupported integration service", "unableToRemoveIntegration": "Unable to remove integration", "integrationUserNotFound": "Integration user not found", "externalApiError": "Error occured during api request", "integrationAuthenticationRequired": "Integration authentication is required", "integrationDeleted": "This integration is removed"}, "defects": {"defectNotFound": "Defect not found"}, "assist": {"messageContentRequired": "Message content is required and must be a valid string.", "fieldValueRequired": "Text assist field value is required.", "promptContentRequired": "Prompt content is required and must be a valid string.", "textFieldRequired": "At least one text field is required.", "fieldNameRequired": "Text assist field name is required.", "noResponseFound": "No response found."}, "sso": {"ssoConfigNotAllowed": "User cannot create or delete SSO config. Please make sure you provide handle of an organization", "unsupportedSsoProvider": "Unsupported SSO provider"}, "scheduledTasks": {"scheduledTaskNotFound": "Scheduled task not found", "scheduledTaskAlreadyRunning": "Scheduled task already running or completed"}, "charts": {"invalidChartType": "Invalid chart type", "invalidChartId": "Invalid chart ID"}, "entities": {"invalidEntityType": "Invalid entity type", "eachLinkMustBeString": "Each \"link\" must be an string"}, "external": {"externalidFieldRequired": "\"externalId\" field is required"}, "pinata": {"failedToProcessPinataData": "Failed to process PINATA data"}, "general": {"problemProcessingRequest": "We're having a problem processing that request", "resourceNotFound": "Resource not found", "internalServerError": "Internal server error", "resultNotFound": "Result not found", "forbidden": "You are not allowed to access this resource"}}}