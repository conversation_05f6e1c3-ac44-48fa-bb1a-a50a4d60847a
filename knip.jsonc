{
  "$schema": "https://unpkg.com/knip@5/schema-jsonc.json",
  "entry": [
    "src/**/*.ts"
  ],
  "ignore": [
    "db/migrations/**/*.ts",
    "db/utils/*.ts",
    "db/create-openfga-store.js",
    "db/fgaMigrations/**/*.ts",
    "docs/**/*.ts",
    "test/data/*.ts",
    "test/utils/*.ts",
    "knexfile.ts",
  ],
  "ignoreDependencies": [
    "perl",
    "otplib",
    "eslint-config-prettier",
    "eslint-plugin-prettier",
    "tsconfig-paths",
    "@temporalio/activity"
  ],
  "ignoreBinaries": [
    "nyc",
    "prettier",
    "lint-staged",
    "perl"
  ],
  "prettier": {
    "config": [
      ".prettierrc",
      ".prettierrc.{json,js,cjs,mjs,yml,yaml,toml,json5}",
      "prettier.config.{js,cjs,mjs}",
      "package.{json,yaml}",
    ],
  },
  "eslint": {
    "config": [
      ".eslintrc",
      ".eslintrc.{js,json,cjs}",
      ".eslintrc.{yml,yaml}",
      "package.json",
    ],
  },
}