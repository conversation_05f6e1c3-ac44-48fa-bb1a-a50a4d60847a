<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <title>Piñata</title>
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/@mdi/font@latest/css/materialdesignicons.min.css"
    />
    <link rel="icon" href="/favicon.ico" />
    <style>
      body {
        margin: 0;
        background-color: #fafafa;
        border-color: #fafafa;
      }

      .wrapper {
        width: 100%;
        min-height: 100vh;
      }

      .container {
        padding: 12px;
        padding-top: 80px;
        margin-left: auto;
        margin-right: auto;
      }

      .container .logo {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 1rem;
      }

      .container .notification {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .container .notification span {
        background-color: #fff;
        border-color: #fff;
        border-radius: 4px;
        color: rgba(0, 0, 0, 0.87);
        box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2),
          0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12) !important;
        display: block;
        font-size: 20px;
        margin-bottom: 16px;
        padding: 25px;
        position: relative;
        transition: 0.3s cubic-bezier(0.25, 0.8, 0.5, 1);
        border-left: 4px solid #6200ea;
      }
    </style>
  </head>

  <body>
    <div class="wrapper">
      <div class="container">
        <div class="logo">
          <img src="/images/logo.png" alt="logo" width="80" />
        </div>
        <div class="notification">
          <span
            >You are now connected to GitHub! You can close this window and go
            back to the Piñata app to get started testing.</span
          >
        </div>
      </div>
    </div>
  </body>
</html>
