{
  "compilerOptions": {
    "target": "ES2017",
    "module": "commonjs",
    "lib": ["es2017", "esnext.asynciterable", "dom", "ES2021.String"],
    "sourceMap": true,
    "inlineSources": true,
    "outDir": "./dist",
    "rootDir": "./",
    "strict": false,
    "noImplicitAny": false,
    "noUnusedLocals": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "baseUrl": "./src",
    "typeRoots": ["./node_modules/@types", "typings"],
    "types": ["node", "jest"],
    "esModuleInterop": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "declaration": true,
    "removeComments": false,
    "allowJs": true,
    // Set `sourceRoot` to  "/" to strip the build path prefix
    // from generated source code references.
    // This improves issue grouping in Sentry.
    "sourceRoot": "/",
    "paths": {
      "@app/config/*": ["config/*"],
      "@app/constants/*": ["constants/*"],
      "@app/controllers/*": ["controllers/*"],
      "@app/middlewares/*": ["middlewares/*"],
      "@app/routes/*": ["routes/*"],
      "@app/workers/*": ["workers/*"],
      "@app/types/*": ["types/*"],
      "@app/utils/*": ["utils/*"],
      "@app/validators/*": ["validators/*"],
      "@app/lib/*": ["lib/*"],
      "@tests/*": ["test/*"],
      "@docs": ["../docs/"],
      "@app/models/*": ["models/*"],
      "@app/services": ["services"],
      "@app/modules/*":["modules/*"],
      "@app/temporal/*":["temporal/*"],
      "@app/integrations/*":["integrations/*"]
    }
  },
  "ts-node": {
    "files": true,
    "require": ["tsconfig-paths/register"]
  },
  "exclude": ["src/**/*.spec.ts"],
  "include": ["src/**/*", "typings/**/*", "docs", "db"]
}
