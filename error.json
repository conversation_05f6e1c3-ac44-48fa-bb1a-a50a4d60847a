{"organizationNameExist": "The name of this organization is already taken.", "invalidService": "Integration service not found", "invalidInviteLink": "This invite link is not invalid", "invitationNotExist": "This invitation is not exist.", "invitationLinkExpired": "This invitation link is invalid or expired", "invitationLinkUsed": "This invitation link was used before", "invitationEmailNoMatch": "This invitation was sent to a different email than the logged in user", "tooManyAttempts": "Too many attempts. Try again after 1 day", "userEmailNotExist": "User with given email doesn't exist", "resetLinkInvalid": "This password reset link is invalid", "resetLinkExpired": "This password reset link is expired", "noAuthenticatedUser": "No authenticated user provided", "searchQueryMustBeNumbers": "Search query must be a string", "emailInUse": "Email already in use", "nameFieldRequired": "\"name\" field is required", "useruidsMustBeString": "User ids must be strings", "useruidsMustBeArray": "User ids must be an array", "prioritiesMustBeNumbers": "Priorities must be numbers", "prioritiesMustBeArray": "Priorities must be an array", "statusMustBeNumbers": "Status must be numbers", "statusMustBeArray": "Status must be an array", "tagsMustBeNumbers": "Tags must be numbers", "tagsMustBeArray": "Tags must be an array", "queryMustBeString": "Query must be a string", "selectedPlansMustBeNumbers": "Selected plans must be numbers", "selectedMilestonesMustBeNumbers": "Selected milestones must be numbers", "selectedRunsMustBeNumbers": "Selected runs must be numbers", "assignDateStartMustBeDate": "Assign date start must be a date", "assignDateEndMustBeDate": "Assign date end must be a date", "dueDateStartMustBeDate": "Due date start must be a date", "dueDateEndMustBeDate": "Due date end must be a date", "provideDataToUpdate": "Provide data to update", "statusFieldRequired": "\"status\" field is required", "descriptionFieldRequired": "\"description\" field is required", "priorityFieldRequired": "\"priority\" field is required", "problemUpdatingTestCase": "We are having problem updating test-case", "requireSignUp": "You should signup first", "invalidType": "Invalid type", "invalidUserUid": "Invalid user uid", "invalidOrgUid": "Invalid organization uid", "tokenRequired": "Token is required", "requireCorrectLogin": "This email is invalid", "userAlreadyInvited": "User has already been invited", "currentPasswordIncorrect": "Your current password is not correct", "notAdmin": "You are not an admin user", "noAccessToOrg": "You don't have access to this organization", "problemFetchingUsers": "We are having problem fetching users", "problemFetchingRoles": "We are having problem fetching roles", "problemFetchingRepoBranches": "We are having problem fetching branches", "problemFetchingHandle": "We are having problem fetching usernames", "noOrg": "Organization does not exist", "canNotUpdateRole": "Unable to update role", "missingFields": "User ID or organization ID is missing.", "missingPermission": "You don't have permission to perform this action", "unableToReassignAllOwners": "At least one owner must remain in the organization", "userNotFound": "User with given ID doesn't exist", "orgNotFound": "Organization with given ID doesn't exist", "missingXValue": "Chart missing x value", "missingYValue": "Chart missing y value", "missingWValue": "Chart missing w value", "missingHValue": "Chart missing h value", "editableBoolean": "Editable should be a boolean value", "defaultBoolean": "Default should be a boolean value", "defaultDashboardRestrict": "You should have at least one default dashboard", "dashboardNotFount": "Dashboard not found", "dashboardNotEditable": "Dashboard is not editable.", "dashboardNameRestrict": "Dashboard name must be an string", "tokenNotFound": "Token with given ID doesn't exist", "sourceKeyRequired": "\"source\" key is required", "typeKeyRequired": "\"type\" key is required", "handleDuplicated": "Handle is already in use, please choose another", "handleUnavailable": "Handle not available", "keyUnavailable": "Project key not available", "handleNotFound": "Handle not found, please use a registered handle", "handleFound": "Hand<PERSON> found, please select another", "keyFound": "Project key found, please select another", "projectNotFound": "Project not found", "projectIdsNotFound": "The following project IDs were not found: ", "stepUidRequired": "\"step_uid\" key is required", "typesKeyRequired": "\"types\" key is required", "runsFieldRequired": "\"runs\" field is required", "endMustBeDate": "\"endRange\" must be a valid date", "startMustBeDate": "\"startRange\" must be a valid date", "idMustBeNumber": "\"ID\" must be a number", "entriesKeyRequired": "\"entries\" key is required", "invalidSourceKey": "Invalid \"source\" key", "invalidTypeKey": "Invalid \"type\" key", "invalidRequest": "Invalid request", "invalidView": "Invalid view", "projectsNotArray": "\"projects\" query must be an array", "noPermissionForProject": "You don't have permission for one of the provided projects.", "entriesKeyNotArray": "\"entries\" key provided is not an object", "entityTypesFieldNotArray": "\"entityTypes\" key provided is not an array", "runsKeyNotArray": "\"runs\" key provided is not an array", "optionsKeyNotArray": "\"options\" key provided is not an array", "configurationsKeyNotArray": "\"configurations\" key provided is not an array", "tagsKeyNotArray": "\"tags\" key provided is not an array", "userNotMemberOfOrg": "User not member of organization", "problemProcessingRequest": "We're having a problem processing that request", "attachmentNotFound": "\"attachment\" not found", "orgOwnerCannotBeReassigned": "The owner of the organization cannot be reassigned", "typeFieldRequired": "\"type\" field is required", "sizeFieldRequired": "\"size\" field is required", "ownerIdRequired": "Owner is required", "ownerTypeRequired": "Owner type is required", "fileNameFieldRequired": "\"file_name\" field is required", "invalidMimeType": "invalid \"type\" provided", "mediaTypeFieldRequired": "\"media_type\" field is required", "handleLength": "Username must be between 2 and 30 characters", "handleMatch": "Handle can only contain numbers, letters, dashes, and underscores.", "problemFetchingTestExecutions": "We are having problems fetching test-excutions", "problemFetchingTestRuns": "We are having problems fetching test-runs", "problemFetchingTestPlans": "We are having problems fetching test-plans", "problemFetchingTestFolders": "We are having problems fetching test-folders", "problemFetchingTestCases": "We are having problems fetching test-cases", "problemFetchingTestSuites": "We are having problems fetching test-suites", "problemFetchingTestMilestones": "We are having problems fetching test-milestones", "problemFetchingTestProjects": "We are having problems fetching test-projects", "problemFetchingTestRepositories": "We are having problems fetching repositories", "problemFetchingTestBranches": "We are having problems fetching branches", "imageUploadOverSizeLimit": "Image size cannot be larger than 2MB!", "firstNameFieldRequired": "\"firstName\" field is required", "lastNameFieldRequired": "\"lastName\" field is required", "avatarMustBeUrl": "\"avatarUrl\" field must be a URL", "orgFieldRequired": "\"org\" field is required", "externalidFieldRequired": "\"externalId\" field is required", "planidsFieldRequired": "\"planIds\" field is required", "plansFieldRequired": "\"plans\" field is required", "plansIdFieldRequired": "Each \"planId\" must be integer", "invalidArchivedType": "Each \"archived\" must be (true/false)", "planidsFieldNotArray": "\"planIds\" field is not an array", "plansFieldNotArray": "\"planIds\" field is not an array", "sourceFieldRequired": "\"source\" field is required", "repoFieldRequired": "\"repoUID\" field is required", "testRunUidRequired": "\"uid\" field is required for test run", "testPlanUidRequired": "\"uid\" field is required for test plan", "testProjectUidRequired": "\"projectId\" field is required for test plan", "tagUidRequired": "\"tagId\" field is required", "stepNotFound": "Step not found", "stepRelationNotFound": "Step it not related to the execution", "nonSharedStepsRequiredFileds": "Non shared steps must have all required fields", "executionUidIsRequired": "test execution id is required", "testCaseUidIsRequired": "test case id is required", "activeStepNotFound": "Active step not found", "failedToProcessPinataData": "Failed to process PINATA data", "invalidRole": "Invalid role", "userNotSpecified": "User not specified", "existingMember": "User already a member of the organization", "emailOrUidRequired": "User email or ID must be provided", "permissionsRequired": "Permissions list is required", "invalidPermission": "Invalid permission provided", "duplicateRole": "Role already exists in organisation", "roleNotFound": "Role not found", "roleUidRequired": "Role id is required", "invalidAttachmentUid": "Invalid attachment uid", "invalidCaseUid": "Invalid case uid", "invalidStepUid": "Invalid step uid", "invalidSharedStepUid": "<PERSON><PERSON>id shared step uid", "invalidTemplateUid": "Invalid template uid", "invalidOrgNameLength": "The org name must be longer than 2 and shorter than 64 characters", "invalidCredentials": "Invalid user credentials", "configNotFound": "Config not found", "duplicateConfig": "Config name already in use", "dashboardNotFound": "Dashboard not found", "dashboardDateRangeExceeded": "Dashboard date range exceeded", "cannotDeleteRootFolder": "Cannot delete project root folder", "folderNotFound": "Folder not found", "resourceNotFound": "Resource not found", "internalServerError": "Internal server error", "invalidTestMilestoneNameLength": "The test milestone name must be longer than 2 and shorter than 64 characters", "invalidOrgNameCharacters": "The org name must only contain alpha numeric characters, spaces, hyphens, and underscores.", "milestoneIdIsRequired": "\"milestoneId\" is required", "runIdsAreRequired": "\"runIds\" are required and must be array", "runIdsMustBeArray": "\"runIds\" must be an array", "plansAreRequired": "\"plans\" are required and must be array", "milestonesAreRequired": "\"milestones\" are required and must be array", "casesIdsAreRequired": "\"ids\" are required and must be array", "customFieldsAreRequired": "\"customFields\" are required and must be object", "eachRunIdMustBeString": "Each \"runId\" must be an string", "eachEntityTypeMustBeString": "Each \"entityType\" must be an string", "projectUidIsRequired": "Each \"projectUid\" is required", "resultNotFound": "Result not found", "eachPlanIdMustBeString": "Each \"planId\" must be an string", "eachMilestoneIdMustBeInteger": "Each \"milestoneId\" must be integer", "eachLinkMustBeString": "Each \"link\" must be an string", "forbidden": "You are not allowed to access this resource", "handleIsNotOrg": "<PERSON><PERSON> doesn't belong to an Org", "stepsMustBeArray": "\"steps\" must be an array", "invalidFolderUid": "Invalid folder uid", "sharedStepIdsMustBeAnArray": "'sharedStepIds' must be an array", "sharedStepIdsIsRequired": "'sharedStepIds' is required", "sharedStepsMustBeAnArray": "'sharedSteps' must be an array", "sharedStepsIsRequired": "'sharedSteps' is required", "archivedProjectCannotBeUpdated": "The archived project cannot be updated", "projectAlreadyArchived": "The project has already been archived", "projectKeyRequired": "\"key\" field is required for a project", "projectKeyLength": "\"key\" must be between 2 and 10 characters long", "projectKeyPattern": "\"key\" can only contain letters, numbers, hyphens, and underscores", "repoNotFound": "repo not found", "invalidDueDate": "invalid due date", "milestoneNotFound": "milestone not found", "projectKeyExists": "A project with this key already exists", "casesFieldRequired": "Cases must contain at least one item", "invalidTestrunUid": "The test run ID provided is not valid. Please check and try again.", "roleIdsIsRequired": "'roleIds' is required", "roleIdsMustBeAnArray": "'roleIds' must be an array", "membersIsRequired": "\"members\" is required", "membersMustBeAnArray": "\"members\" must be an array", "tagIdsMustBeArray": "\"tagIds\" must be an array", "passwordFieldRequired": "\"password\" field is required", "customFieldInvalidCustomFieldId": "Invalid custom field id", "customFieldNameRequired": "The name field is required.", "customFieldExists": "Custom field name is already taken", "customFieldTypeRequired": "The type field is required.", "invalidCustomFieldType": "The type field is required.", "customFieldInvalidOptionsArray": "Options must be an array.", "customFieldInvalidSource": "Invalid source value.", "folderExists": "Folder already exists", "parentFolderNotExists": "Parent folder does not exists", "multipleFoldersForCase": "Case must belong to one folder only", "caseNotFound": "Case not found", "authTokenNotFound": "Service is not authenticated", "refreshTokenExpired": "Refresh token expired, please authenticate again", "invalidEntityType": "Invalid entity type", "invalidChartType": "Invalid chart type", "invalidChartId": "Invalid chart ID", "cannotRemoveOwner": "Owner cannot be removed", "userIsNotOwner": "You're not an owner of this account", "unableToRemoveAllOwners": "You cannot remove all owners", "cannotUpdateSystemRole": "You cannot update a system role", "integrationNotFound": "You dont have this service integrated", "integrationConfiguredForProjects": "Integration is configured for projects", "unsupportedIntegration": "Unsupported integration service", "unableToRemoveIntegration": "Unable to remove integration", "defectNotFound": "Defect not found", "messageContentRequired": "Message content is required and must be a valid string.", "fieldValueRequired": "Text assist field value is required.", "promptContentRequired": "Prompt content is required and must be a valid string.", "textFieldRequired": "At least one text field is required.", "fieldNameRequired": "Text assist field name is required.", "noResponseFound": "No response found.", "invalidTagUid": "Invalid tag uid(s)", "planNotFound": "Plan not found", "testExecutionNotFound": "Test execution not found", "integrationUserNotFound": "Integration user not found", "runNotFound": "Run not found", "customFieldsMustBeArray": "\"customFields\" must be an array", "caseImprovementCustomFieldRequired": "Please set at least one custom field to generate accurate improvement.", "caseImprovementStepsRequired": "Please set at least one step to generate accurate improvement.", "externalApiError": "Error occured during api request", "integrationAuthenticationRequired": "Integration authentication is required", "integrationDeleted": "This integration is removed", "accessTokenNotFound": "Access token not found", "milestoneuidsMustBeArray": "Milestone ids must be an array", "runuidsMustBeArray": "Run ids must be an array", "planuidsMustBeArray": "Plan ids must be an array", "projectuidsMustBeArray": "Project ids must be an array", "milestoneuidsMustBeNumbers": "Milestone ids must be numbers", "runuidsMustBeNumbers": "Run ids must be numbers", "planuidsMustBeNumbers": "Plan ids must be numbers", "projectuidsMustBeNumbers": "Project ids must be numbers", "limitMustBeNumber": "Limit must be a number", "offsetMustBeNumber": "Offset must be a number", "ssoConfigNotAllowed": "User cannot create or delete SSO config. Please make sure you provide handle of an organization", "unsupportedSsoProvider": "Unsupported SSO provider", "invalidFolderQueryEntityType": "entityType must be either 'case' or 'execution'", "testRunIdRequiredFolderQuery": "testRunId is required when entityType is execution", "scheduledTaskNotFound": "Scheduled task not found", "scheduledTaskAlreadyRunning": "Scheduled task already running or completed"}