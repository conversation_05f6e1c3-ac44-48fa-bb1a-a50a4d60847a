Statistical profiling result from isolate-0x128008000-82623-v8.log, (568 ticks, 4 unaccounted, 0 excluded).

 [Shared libraries]:
   ticks  total  nonlib   name
      9    1.6%          /usr/lib/system/libsystem_c.dylib
      2    0.4%          /usr/lib/libc++.1.dylib
      1    0.2%          /usr/lib/system/libsystem_kernel.dylib
      1    0.2%          /usr/lib/libobjc.A.dylib

 [JavaScript]:
   ticks  total  nonlib   name
      2    0.4%    0.4%  LazyCompile: *resolve node:path:1091:10
      2    0.4%    0.4%  LazyCompile: *Module._nodeModulePaths node:internal/modules/cjs/loader:783:37
      1    0.2%    0.2%  LazyCompile: *readPackageScope node:internal/modules/package_json_reader:148:26
      1    0.2%    0.2%  Function: ^require node:internal/modules/helpers:175:31
      1    0.2%    0.2%  Function: ^readSync node:fs:701:18
      1    0.2%    0.2%  Function: ^get pathname node:internal/url:923:15
      1    0.2%    0.2%  Function: ^createUnsafeBuffer node:internal/buffer:1061:28
      1    0.2%    0.2%  Function: ^Module._compile node:internal/modules/cjs/loader:1310:37
      1    0.2%    0.2%  Function: ^<anonymous> node:internal/util:769:18

 [C++]:
   ticks  total  nonlib   name
    490   86.3%   88.3%  T node::builtins::BuiltinLoader::GetCacheUsage(v8::FunctionCallbackInfo<v8::Value> const&)
      9    1.6%    1.6%  T node::binding::DLOpen(v8::FunctionCallbackInfo<v8::Value> const&)
      8    1.4%    1.4%  T _write
      7    1.2%    1.3%  T _mig_get_reply_port
      3    0.5%    0.5%  t std::__1::ostreambuf_iterator<char, std::__1::char_traits<char>> std::__1::__pad_and_output<char, std::__1::char_traits<char>>(std::__1::ostreambuf_iterator<char, std::__1::char_traits<char>>, char const*, char const*, char const*, std::__1::ios_base&, char)
      2    0.4%    0.4%  t std::__1::basic_ostream<char, std::__1::char_traits<char>>& std::__1::__put_character_sequence<char, std::__1::char_traits<char>>(std::__1::basic_ostream<char, std::__1::char_traits<char>>&, char const*, unsigned long)
      2    0.4%    0.4%  t __pthread_mutex_global_init
      2    0.4%    0.4%  t __pthread_main_thread_init
      2    0.4%    0.4%  T node::binding::GetInternalBinding(v8::FunctionCallbackInfo<v8::Value> const&)
      2    0.4%    0.4%  T _pthread_key_init_np
      2    0.4%    0.4%  T _mach_port_get_attributes
      2    0.4%    0.4%  T ___pthread_init
      1    0.2%    0.2%  t void node::Buffer::(anonymous namespace)::StringSlice<(node::encoding)1>(v8::FunctionCallbackInfo<v8::Value> const&)
      1    0.2%    0.2%  T node::loader::ModuleWrap::New(v8::FunctionCallbackInfo<v8::Value> const&)
      1    0.2%    0.2%  T node::BaseObject::pointer_data()
      1    0.2%    0.2%  T _semaphore_destroy
      1    0.2%    0.2%  T _pthread_key_create
      1    0.2%    0.2%  T _mach_vm_map
      1    0.2%    0.2%  T _mach_port_get_set_status
      1    0.2%    0.2%  T __os_semaphore_dispose
      1    0.2%    0.2%  T __os_semaphore_create

 [Summary]:
   ticks  total  nonlib   name
     11    1.9%    2.0%  JavaScript
    540   95.1%   97.3%  C++
     11    1.9%    2.0%  GC
     13    2.3%          Shared libraries
      4    0.7%          Unaccounted

 [C++ entry points]:
   ticks    cpp   total   name
    474   92.2%   83.5%  T node::builtins::BuiltinLoader::GetCacheUsage(v8::FunctionCallbackInfo<v8::Value> const&)
      9    1.8%    1.6%  T node::binding::DLOpen(v8::FunctionCallbackInfo<v8::Value> const&)
      6    1.2%    1.1%  T _mig_get_reply_port
      5    1.0%    0.9%  T _write
      3    0.6%    0.5%  t std::__1::ostreambuf_iterator<char, std::__1::char_traits<char>> std::__1::__pad_and_output<char, std::__1::char_traits<char>>(std::__1::ostreambuf_iterator<char, std::__1::char_traits<char>>, char const*, char const*, char const*, std::__1::ios_base&, char)
      2    0.4%    0.4%  t std::__1::basic_ostream<char, std::__1::char_traits<char>>& std::__1::__put_character_sequence<char, std::__1::char_traits<char>>(std::__1::basic_ostream<char, std::__1::char_traits<char>>&, char const*, unsigned long)
      2    0.4%    0.4%  t __pthread_mutex_global_init
      2    0.4%    0.4%  t __pthread_main_thread_init
      2    0.4%    0.4%  T node::binding::GetInternalBinding(v8::FunctionCallbackInfo<v8::Value> const&)
      2    0.4%    0.4%  T ___pthread_init
      1    0.2%    0.2%  t void node::Buffer::(anonymous namespace)::StringSlice<(node::encoding)1>(v8::FunctionCallbackInfo<v8::Value> const&)
      1    0.2%    0.2%  T node::loader::ModuleWrap::New(v8::FunctionCallbackInfo<v8::Value> const&)
      1    0.2%    0.2%  T node::BaseObject::pointer_data()
      1    0.2%    0.2%  T _pthread_key_init_np
      1    0.2%    0.2%  T _pthread_key_create
      1    0.2%    0.2%  T _mach_vm_map
      1    0.2%    0.2%  T _mach_port_get_set_status

 [Bottom up (heavy) profile]:
  Note: percentage shows a share of a particular caller in the total
  amount of its parent calls.
  Callers occupying less than 1.0% are not shown.

   ticks parent  name
    490   86.3%  T node::builtins::BuiltinLoader::GetCacheUsage(v8::FunctionCallbackInfo<v8::Value> const&)
    178   36.3%    Function: ^internalCompileFunction node:internal/vm:31:33
    173   97.2%      Function: ^wrapSafe node:internal/modules/cjs/loader:1247:18
    171   98.8%        Function: ^Module._compile node:internal/modules/cjs/loader:1310:37
    163   95.3%          Function: ^Module._extensions..js node:internal/modules/cjs/loader:1369:37
    163  100.0%            Function: ^Module.load node:internal/modules/cjs/loader:1184:33
      8    4.7%          LazyCompile: ~Module._extensions..js node:internal/modules/cjs/loader:1369:37
      8  100.0%            Function: ^Module.load node:internal/modules/cjs/loader:1184:33
      2    1.2%        LazyCompile: ~Module._compile node:internal/modules/cjs/loader:1310:37
      2  100.0%          LazyCompile: ~Module._extensions..js node:internal/modules/cjs/loader:1369:37
      2  100.0%            Function: ^Module.load node:internal/modules/cjs/loader:1184:33
      5    2.8%      LazyCompile: ~wrapSafe node:internal/modules/cjs/loader:1247:18
      5  100.0%        LazyCompile: ~Module._compile node:internal/modules/cjs/loader:1310:37
      5  100.0%          LazyCompile: ~Module._extensions..js node:internal/modules/cjs/loader:1369:37
      4   80.0%            LazyCompile: ~Module.load node:internal/modules/cjs/loader:1184:33
      1   20.0%            Function: ^Module.load node:internal/modules/cjs/loader:1184:33
    102   20.8%    Function: ^readSync node:fs:701:18
    102  100.0%      Function: ^tryReadSync node:fs:440:21
    102  100.0%        Function: ^readFileSync node:fs:461:22
     91   89.2%          Function: ^Module._extensions..js node:internal/modules/cjs/loader:1369:37
     91  100.0%            Function: ^Module.load node:internal/modules/cjs/loader:1184:33
     11   10.8%          LazyCompile: ~Module._extensions..js node:internal/modules/cjs/loader:1369:37
     10   90.9%            Function: ^Module.load node:internal/modules/cjs/loader:1184:33
      1    9.1%            LazyCompile: ~Module.load node:internal/modules/cjs/loader:1184:33
     61   12.4%    Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
     55   90.2%      Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      4    7.3%        Function: ~<anonymous> node:tls:1:1
      4  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      4  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      4    7.3%        Function: ~<anonymous> node:internal/fs/promises:1:1
      4  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      4  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      4    7.3%        Function: ~<anonymous> node:crypto:1:1
      4  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      4  100.0%            Function: ^compileForPublicLoader node:internal/bootstrap/realm:320:25
      3    5.5%        LazyCompile: ~get node:internal/util:595:17
      3  100.0%          T node::builtins::BuiltinLoader::GetCacheUsage(v8::FunctionCallbackInfo<v8::Value> const&)
      3  100.0%            Function: ^getOwn node:internal/bootstrap/realm:200:16
      3    5.5%        Function: ~<anonymous> node:internal/streams/duplex:1:1
      3  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      3  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      3    5.5%        Function: ~<anonymous> node:http:1:1
      3  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      3  100.0%            LazyCompile: ~compileForPublicLoader node:internal/bootstrap/realm:320:25
      3    5.5%        Function: ~<anonymous> node:_http_client:1:1
      3  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      3  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      3    5.5%        Function: ~<anonymous> node:_http_agent:1:1
      3  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      3  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      2    3.6%        LazyCompile: ~resolveExports node:internal/modules/cjs/loader:574:24
      2  100.0%          LazyCompile: ~Module._findPath node:internal/modules/cjs/loader:603:28
      2  100.0%            LazyCompile: ~Module._resolveFilename node:internal/modules/cjs/loader:1048:35
      2    3.6%        Function: ~<anonymous> node:stream:1:1
      2  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      2  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      2    3.6%        Function: ~<anonymous> node:perf_hooks:1:1
      2  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      2  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      2    3.6%        Function: ~<anonymous> node:net:1:1
      2  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      2  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      2    3.6%        Function: ~<anonymous> node:internal/streams/operators:1:1
      2  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      2  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      2    3.6%        Function: ~<anonymous> node:child_process:1:1
      2  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      2  100.0%            Function: ^compileForPublicLoader node:internal/bootstrap/realm:320:25
      1    1.8%        LazyCompile: ~lazyWebCrypto node:crypto:125:23
      1  100.0%          LazyCompile: ~get node:crypto:369:8
      1  100.0%            T node::builtins::BuiltinLoader::GetCacheUsage(v8::FunctionCallbackInfo<v8::Value> const&)
      1    1.8%        LazyCompile: ~lazyLoadStreams node:fs:3023:25
      1  100.0%          LazyCompile: ~get ReadStream node:fs:3165:17
      1  100.0%            T node::builtins::BuiltinLoader::GetCacheUsage(v8::FunctionCallbackInfo<v8::Value> const&)
      1    1.8%        Function: ~<anonymous> node:worker_threads:1:1
      1  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      1  100.0%            Function: ^compileForPublicLoader node:internal/bootstrap/realm:320:25
      1    1.8%        Function: ~<anonymous> node:tty:1:1
      1  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      1  100.0%            LazyCompile: ~compileForPublicLoader node:internal/bootstrap/realm:320:25
      1    1.8%        Function: ~<anonymous> node:internal/streams/readable:1:1
      1  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      1  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      1    1.8%        Function: ~<anonymous> node:internal/streams/pipeline:1:1
      1  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      1  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      1    1.8%        Function: ~<anonymous> node:internal/readline/interface:1:1
      1  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      1  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      1    1.8%        Function: ~<anonymous> node:internal/perf/usertiming:1:1
      1  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      1  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      1    1.8%        Function: ~<anonymous> node:internal/perf/timerify:1:1
      1  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      1  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      1    1.8%        Function: ~<anonymous> node:internal/fs/streams:1:1
      1  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      1  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      1    1.8%        Function: ~<anonymous> node:internal/crypto/util:1:1
      1  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      1  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      1    1.8%        Function: ~<anonymous> node:internal/crypto/hkdf:1:1
      1  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      1  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      1    1.8%        Function: ~<anonymous> node:internal/child_process:1:1
      1  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      1  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      1    1.8%        Function: ~<anonymous> node:dgram:1:1
      1  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      1  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      1    1.8%        Function: ~<anonymous> node:_tls_wrap:1:1
      1  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      1  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      1    1.8%        Function: ~<anonymous> node:_http_common:1:1
      1  100.0%          Function: ^compileForInternalLoader node:internal/bootstrap/realm:375:27
      1  100.0%            Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      5    8.2%      Function: ^compileForPublicLoader node:internal/bootstrap/realm:320:25
      5  100.0%        Function: ^loadBuiltinModule node:internal/modules/helpers:94:27
      5  100.0%          Function: ^Module._load node:internal/modules/cjs/loader:940:24
      5  100.0%            Function: ^Module.require node:internal/modules/cjs/loader:1217:36
      1    1.6%      LazyCompile: ~compileForPublicLoader node:internal/bootstrap/realm:320:25
      1  100.0%        LazyCompile: ~loadBuiltinModule node:internal/modules/helpers:94:27
      1  100.0%          LazyCompile: ~Module._load node:internal/modules/cjs/loader:940:24
      1  100.0%            LazyCompile: ~Module.require node:internal/modules/cjs/loader:1217:36
     15    3.1%    Function: ^read node:internal/modules/package_json_reader:47:14
     15  100.0%      Function: ^readPackage node:internal/modules/package_json_reader:139:21
     15  100.0%        Function: ^resolveExports node:internal/modules/cjs/loader:574:24
     15  100.0%          Function: ^Module._findPath node:internal/modules/cjs/loader:603:28
     15  100.0%            Function: ^Module._resolveFilename node:internal/modules/cjs/loader:1048:35
     13    2.7%    T node::builtins::BuiltinLoader::GetCacheUsage(v8::FunctionCallbackInfo<v8::Value> const&)
      4   30.8%      T node::builtins::BuiltinLoader::GetCacheUsage(v8::FunctionCallbackInfo<v8::Value> const&)
      2   50.0%        Function: ^read node:internal/modules/package_json_reader:47:14
      1   50.0%          LazyCompile: *readPackageScope node:internal/modules/package_json_reader:148:26
      1  100.0%            Function: ^trySelf node:internal/modules/cjs/loader:532:17
      1   50.0%          Function: ^readPackage node:internal/modules/package_json_reader:139:21
      1  100.0%            Function: ^readPackageScope node:internal/modules/package_json_reader:148:26
      1   25.0%        Function: ^realpathSync node:fs:2577:22
      1  100.0%          Function: ^toRealPath node:internal/modules/helpers:55:20
      1  100.0%            Function: ^Module._findPath node:internal/modules/cjs/loader:603:28
      1   25.0%        Function: ^<anonymous> node:internal/fs/utils:364:35
      1  100.0%          Function: ^<anonymous> node:internal/fs/utils:695:38
      1  100.0%            Function: ^<anonymous> node:internal/fs/utils:707:42
      1    7.7%      LazyCompile: ~resolvePackageTargetString node:internal/modules/esm/resolve:429:36
      1  100.0%        LazyCompile: ~resolvePackageTarget node:internal/modules/esm/resolve:533:30
      1  100.0%          LazyCompile: ~resolvePackageTarget node:internal/modules/esm/resolve:533:30
      1  100.0%            LazyCompile: ~resolvePackageTarget node:internal/modules/esm/resolve:533:30
      1    7.7%      LazyCompile: ~resolveExports node:internal/modules/cjs/loader:574:24
      1  100.0%        LazyCompile: ~Module._findPath node:internal/modules/cjs/loader:603:28
      1  100.0%          LazyCompile: ~Module._resolveFilename node:internal/modules/cjs/loader:1048:35
      1  100.0%            LazyCompile: ~Module._load node:internal/modules/cjs/loader:940:24
      1    7.7%      LazyCompile: ~finalizeEsmResolution node:internal/modules/cjs/loader:1148:31
      1  100.0%        Function: ^resolveExports node:internal/modules/cjs/loader:574:24
      1  100.0%          Function: ^Module._findPath node:internal/modules/cjs/loader:603:28
      1  100.0%            Function: ^Module._resolveFilename node:internal/modules/cjs/loader:1048:35
      1    7.7%      Function: ~<anonymous> node:internal/main/run_main_module:1:1
      1    7.7%      Function: ^stat node:internal/modules/cjs/loader:178:14
      1  100.0%        Function: ^Module._findPath node:internal/modules/cjs/loader:603:28
      1  100.0%          Function: ^Module._resolveFilename node:internal/modules/cjs/loader:1048:35
      1  100.0%            Function: ^Module._resolveFilename /Users/<USER>/Developer/rocksteady/testfiesta/testfiesta-backend/node_modules/module-alias/index.js:30:36
      1    7.7%      Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      1  100.0%        Function: ^internalCompileFunction node:internal/vm:31:33
      1  100.0%          Function: ^wrapSafe node:internal/modules/cjs/loader:1247:18
      1  100.0%            Function: ^Module._compile node:internal/modules/cjs/loader:1310:37
      1    7.7%      Function: ^read node:internal/modules/package_json_reader:47:14
      1  100.0%        Function: ^readPackage node:internal/modules/package_json_reader:139:21
      1  100.0%          Function: ^readPackageScope node:internal/modules/package_json_reader:148:26
      1  100.0%            Function: ^trySelf node:internal/modules/cjs/loader:532:17
      1    7.7%      Function: ^Module._load node:internal/modules/cjs/loader:940:24
      1  100.0%        Function: ^Module.require node:internal/modules/cjs/loader:1217:36
      1  100.0%          Function: ^require node:internal/modules/helpers:175:31
      1  100.0%            Function: ~<anonymous> /Users/<USER>/Developer/rocksteady/testfiesta/testfiesta-backend/node_modules/semver/functions/compare.js:1:1
      1    7.7%      Function: ^Module._findPath node:internal/modules/cjs/loader:603:28
      1  100.0%        Function: ^Module._resolveFilename node:internal/modules/cjs/loader:1048:35
      1  100.0%          Function: ^Module._resolveFilename /Users/<USER>/Developer/rocksteady/testfiesta/testfiesta-backend/node_modules/module-alias/index.js:30:36
      1  100.0%            Function: ^Module._load node:internal/modules/cjs/loader:940:24
     10    2.0%    Function: ^stat node:internal/modules/cjs/loader:178:14
      5   50.0%      Function: ^tryFile node:internal/modules/cjs/loader:465:17
      3   60.0%        Function: ^tryExtensions node:internal/modules/cjs/loader:480:23
      3  100.0%          Function: ^Module._findPath node:internal/modules/cjs/loader:603:28
      3  100.0%            Function: ^Module._resolveFilename node:internal/modules/cjs/loader:1048:35
      1   20.0%        LazyCompile: ~tryPackage node:internal/modules/cjs/loader:422:20
      1  100.0%          Function: ^Module._findPath node:internal/modules/cjs/loader:603:28
      1  100.0%            Function: ^Module._resolveFilename node:internal/modules/cjs/loader:1048:35
      1   20.0%        Function: ^finalizeEsmResolution node:internal/modules/cjs/loader:1148:31
      1  100.0%          Function: ^resolveExports node:internal/modules/cjs/loader:574:24
      1  100.0%            Function: ^Module._findPath node:internal/modules/cjs/loader:603:28
      5   50.0%      Function: ^Module._findPath node:internal/modules/cjs/loader:603:28
      5  100.0%        Function: ^Module._resolveFilename node:internal/modules/cjs/loader:1048:35
      5  100.0%          Function: ^Module._resolveFilename /Users/<USER>/Developer/rocksteady/testfiesta/testfiesta-backend/node_modules/module-alias/index.js:30:36
      5  100.0%            Function: ^Module._load node:internal/modules/cjs/loader:940:24
     10    2.0%    Function: ^openSync node:fs:587:18
     10  100.0%      Function: ^readFileSync node:fs:461:22
     10  100.0%        Function: ^Module._extensions..js node:internal/modules/cjs/loader:1369:37
     10  100.0%          Function: ^Module.load node:internal/modules/cjs/loader:1184:33
     10  100.0%            Function: ^Module._load node:internal/modules/cjs/loader:940:24
      5    1.0%    LazyCompile: ~internalCompileFunction node:internal/vm:31:33
      5  100.0%      LazyCompile: ~wrapSafe node:internal/modules/cjs/loader:1247:18
      5  100.0%        LazyCompile: ~Module._compile node:internal/modules/cjs/loader:1310:37
      5  100.0%          LazyCompile: ~Module._extensions..js node:internal/modules/cjs/loader:1369:37
      5  100.0%            LazyCompile: ~Module.load node:internal/modules/cjs/loader:1184:33

      9    1.6%  T node::binding::DLOpen(v8::FunctionCallbackInfo<v8::Value> const&)
      9  100.0%    LazyCompile: ~Module._extensions..node node:internal/modules/cjs/loader:1444:39
      9  100.0%      Function: ^Module.load node:internal/modules/cjs/loader:1184:33
      9  100.0%        Function: ^Module._load node:internal/modules/cjs/loader:940:24
      9  100.0%          Function: ^Module.require node:internal/modules/cjs/loader:1217:36
      9  100.0%            Function: ^require node:internal/modules/helpers:175:31

      9    1.6%  /usr/lib/system/libsystem_c.dylib
      1   11.1%    LazyCompile: ~realpathSync node:fs:2577:22
      1  100.0%      LazyCompile: ~toRealPath node:internal/modules/helpers:55:20
      1  100.0%        LazyCompile: ~tryFile node:internal/modules/cjs/loader:465:17
      1  100.0%          LazyCompile: ~tryExtensions node:internal/modules/cjs/loader:480:23
      1  100.0%            LazyCompile: ~tryPackage node:internal/modules/cjs/loader:422:20
      1   11.1%    LazyCompile: ~prepareExecution node:internal/process/pre_execution:55:26
      1  100.0%      LazyCompile: ~prepareMainThreadExecution node:internal/process/pre_execution:39:36
      1  100.0%        Function: ~<anonymous> node:internal/main/run_main_module:1:1
      1   11.1%    LazyCompile: ~pathToFileURL node:internal/url:1517:23
      1  100.0%      Function: ^resolveExports node:internal/modules/cjs/loader:574:24
      1  100.0%        Function: ^Module._findPath node:internal/modules/cjs/loader:603:28
      1  100.0%          Function: ^Module._resolveFilename node:internal/modules/cjs/loader:1048:35
      1  100.0%            Function: ^Module._resolveFilename /Users/<USER>/Developer/rocksteady/testfiesta/testfiesta-backend/node_modules/module-alias/index.js:30:36
      1   11.1%    LazyCompile: ~initializeCJS node:internal/modules/cjs/loader:376:23
      1  100.0%      LazyCompile: ~initializeCJSLoader node:internal/process/pre_execution:582:29
      1  100.0%        LazyCompile: ~setupUserModules node:internal/process/pre_execution:145:26
      1  100.0%          LazyCompile: ~prepareExecution node:internal/process/pre_execution:55:26
      1  100.0%            LazyCompile: ~prepareMainThreadExecution node:internal/process/pre_execution:39:36
      1   11.1%    LazyCompile: ~Module._findPath node:internal/modules/cjs/loader:603:28
      1  100.0%      LazyCompile: ~resolveMainPath node:internal/modules/run_main:14:25
      1  100.0%        LazyCompile: ~executeUserEntryPoint node:internal/modules/run_main:120:31
      1  100.0%          Function: ~<anonymous> node:internal/main/run_main_module:1:1
      1   11.1%    Function: ~<anonymous> node:internal/main/run_main_module:1:1
      1   11.1%    Function: ~<anonymous> /Users/<USER>/Developer/rocksteady/testfiesta/testfiesta-backend/node_modules/debug/src/node.js:1:1
      1  100.0%      Function: ^Module._compile node:internal/modules/cjs/loader:1310:37
      1  100.0%        Function: ^Module._extensions..js node:internal/modules/cjs/loader:1369:37
      1  100.0%          Function: ^Module.load node:internal/modules/cjs/loader:1184:33
      1  100.0%            Function: ^Module._load node:internal/modules/cjs/loader:940:24
      1   11.1%    Function: ~<anonymous> /Users/<USER>/Developer/rocksteady/testfiesta/testfiesta-backend/node_modules/@opentelemetry/api/build/src/trace/context-utils.js:1:1
      1  100.0%      Function: ^Module._compile node:internal/modules/cjs/loader:1310:37
      1  100.0%        Function: ^Module._extensions..js node:internal/modules/cjs/loader:1369:37
      1  100.0%          Function: ^Module.load node:internal/modules/cjs/loader:1184:33
      1  100.0%            Function: ^Module._load node:internal/modules/cjs/loader:940:24

      8    1.4%  T _write
      1   12.5%    T node::builtins::BuiltinLoader::GetCacheUsage(v8::FunctionCallbackInfo<v8::Value> const&)
      1  100.0%      LazyCompile: ~createStackParser /Users/<USER>/Developer/rocksteady/testfiesta/testfiesta-backend/node_modules/@sentry/core/build/cjs/utils-hoist/stacktrace.js:16:27
      1  100.0%        Function: ~<anonymous> /Users/<USER>/Developer/rocksteady/testfiesta/testfiesta-backend/node_modules/@sentry/node/build/cjs/sdk/api.js:1:1
      1  100.0%          Function: ^Module._compile node:internal/modules/cjs/loader:1310:37
      1  100.0%            Function: ^Module._extensions..js node:internal/modules/cjs/loader:1369:37
      1   12.5%    LazyCompile: ~initializeCJS node:internal/modules/cjs/loader:376:23
      1  100.0%      LazyCompile: ~initializeCJSLoader node:internal/process/pre_execution:582:29
      1  100.0%        LazyCompile: ~setupUserModules node:internal/process/pre_execution:145:26
      1  100.0%          LazyCompile: ~prepareExecution node:internal/process/pre_execution:55:26
      1  100.0%            LazyCompile: ~prepareMainThreadExecution node:internal/process/pre_execution:39:36
      1   12.5%    LazyCompile: ~Module._resolveFilename node:internal/modules/cjs/loader:1048:35
      1  100.0%      LazyCompile: ~Module._load node:internal/modules/cjs/loader:940:24
      1  100.0%        LazyCompile: ~executeUserEntryPoint node:internal/modules/run_main:120:31
      1  100.0%          Function: ~<anonymous> node:internal/main/run_main_module:1:1
      1   12.5%    Function: ~<anonymous> /Users/<USER>/Developer/rocksteady/testfiesta/testfiesta-backend/node_modules/@opentelemetry/semantic-conventions/build/src/resource/index.js:1:1
      1  100.0%      Function: ^Module._compile node:internal/modules/cjs/loader:1310:37
      1  100.0%        Function: ^Module._extensions..js node:internal/modules/cjs/loader:1369:37
      1  100.0%          Function: ^Module.load node:internal/modules/cjs/loader:1184:33
      1  100.0%            Function: ^Module._load node:internal/modules/cjs/loader:940:24
      1   12.5%    Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      1  100.0%      Function: ~<anonymous> node:internal/modules/esm/module_map:1:1
      1  100.0%        Function: ^requireBuiltin node:internal/bootstrap/realm:412:24
      1  100.0%          LazyCompile: ~newResolveCache node:internal/modules/esm/loader:34:25
      1  100.0%            Function: ~<instance_members_initializer> node:internal/modules/esm/loader:84:1

      7    1.2%  T _mig_get_reply_port
      2   28.6%    Function: ^handleErrorFromBinding node:internal/fs/utils:347:32
      2  100.0%      Function: ^readSync node:fs:701:18
      2  100.0%        Function: ^tryReadSync node:fs:440:21
      2  100.0%          Function: ^readFileSync node:fs:461:22
      2  100.0%            Function: ^Module._extensions..js node:internal/modules/cjs/loader:1369:37
      1   14.3%    LazyCompile: ~prepareExecution node:internal/process/pre_execution:55:26
      1  100.0%      LazyCompile: ~prepareMainThreadExecution node:internal/process/pre_execution:39:36
      1  100.0%        Function: ~<anonymous> node:internal/main/run_main_module:1:1
      1   14.3%    LazyCompile: ~__toCommonJS /Users/<USER>/Developer/rocksteady/testfiesta/testfiesta-backend/node_modules/@prisma/instrumentation/dist/chunk-PVBRMQBZ.js:18:20
      1  100.0%      Function: ~<anonymous> /Users/<USER>/Developer/rocksteady/testfiesta/testfiesta-backend/node_modules/@prisma/instrumentation/dist/chunk-PVBRMQBZ.js:1:1
      1  100.0%        Function: ^Module._compile node:internal/modules/cjs/loader:1310:37
      1  100.0%          Function: ^Module._extensions..js node:internal/modules/cjs/loader:1369:37
      1  100.0%            Function: ^Module.load node:internal/modules/cjs/loader:1184:33
      1   14.3%    LazyCompile: ~Module._load node:internal/modules/cjs/loader:940:24
      1  100.0%      LazyCompile: ~executeUserEntryPoint node:internal/modules/run_main:120:31
      1  100.0%        Function: ~<anonymous> node:internal/main/run_main_module:1:1
      1   14.3%    LazyCompile: *read node:internal/modules/package_json_reader:47:14
      1  100.0%      LazyCompile: *Module._findPath node:internal/modules/cjs/loader:603:28
      1  100.0%        Function: ^Module._resolveFilename node:internal/modules/cjs/loader:1048:35
      1  100.0%          Function: ^Module._resolveFilename /Users/<USER>/Developer/rocksteady/testfiesta/testfiesta-backend/node_modules/module-alias/index.js:30:36
      1  100.0%            Function: ^Module._load node:internal/modules/cjs/loader:940:24

