NODE_ENV=development
PORT=5050
SENDGRID_KEY=sendgrid-key
SENDGRID_SIGNING_SECRET= sendgrid-signing-secret
FRONTEND_URL=http://localhost:8082
BACKEND_URL=http://localhost:5050
MAIL_USER=<EMAIL>

JWT_EXPIRATION_TIME=10m
JWT_SIGNING_SECRET=kalsdjlk4A000a4oi3a9o3o9a303#35
API_VERSION_1_ROUTE=/v1
JWT_SECRET=secret-key

DB_HOST=postgres
DB_DATABASE=testfiesta
DB_USERNAME=postgres
DB_PASSWORD=password
DB_PORT=5432

CORS_ORIGINS=http://localhost:8082

OPENFGA_DATASTORE_HOST=openfga:8080
OPENFGA_DATASTORE_API_SCHEME=http
OPENFGA_DATASTORE_NAME=testfiesta

REDIS_HOST=redis
REDIS_PORT=6379

STRIPE_DEFAULT_USER_PRICE_ID=price_1OEgERIKSiQciQyYjH2Of9Bn
STRIPE_DEFAULT_ORG_PRICE_ID=price_1OXObyIKSiQciQyY6yMIbzjL

STRIPE_SECRET_KEY=sk_test_51NpB4mIKSiQciQyYgDUpdoFq9seJOgEptD8qmyXHJ0iCr4EueqW8O81TfJ7HGlHaqv8cgkd0X1SdCZhLPBGxBfVz00V0DcrqvM
STRIPE_SIGNING_SECRET=whsec_wfL02h3Zzy11AqOVlV575e6eoT72qLPX
STRIPE_DEFAULT_USER_PRICE_ID=price_1OEgERIKSiQciQyYjH2Of9Bn
STRIPE_DEFAULT_ORG_PRICE_ID=price_1OXObyIKSiQciQyY6yMIbzjL

GC_SERVICE_KEY_FILE=path-to-service-account-file
GCS_BUCKET_NAME=sandbox-attachments

OAUTH_GITHUB_CLIENT_ID=github-client-id
OAUTH_GITHUB_CLIENT_SECRET=github-client-secret

OAUTH_JIRA_CLIENT_ID=jira-client-id
OAUTH_JIRA_CLIENT_SECRET=jira-client-secret

GITHUB_NPM_TOKEN=your-github-npm-token

OPENAI_API_KEY=***********************************************************************************************************************************************************

INTEGRATION_1_KEY=integration-service-private-key

TEMPORAL_HOST=temporal
TEMPORAL_PORT=7233

OAUTH_GOOGLE_CLIENT_ID=google-client-id
OAUTH_GOOGLE_CLIENT_SECRET=google-client-secret
OAUTH_GOOGLE_CALLBACK_URL=http://localhost:5050/core/signin/google/callback
OAUTH_GOOGLE_REDIRECT_SIGN_IN_CALLBACK_URL=http://localhost:8082/login
OAUTH_GOOGLE_REDIRECT_SIGN_UP_CALLBACK_URL=http://localhost:8082/signup

OIDC_REDIRECT_CALLBACK_URL=http://localhost:8082
OIDC_CALLBACK_URL=http://localhost:5050/core/signin/oidc/callback
