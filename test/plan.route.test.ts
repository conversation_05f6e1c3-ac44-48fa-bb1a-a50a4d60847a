import * as data from './data/index';
import * as dbHelpers from './utils/dbHelpers';

import { Application } from 'express';
import { Knex } from 'knex';
import { buildApp } from '../src/app';
import { faker } from '@faker-js/faker';
import milestoneRoute from '../src/routes/internal/milestone';
import orgRoutes from '../src/routes/internal/org';
import planRoutes from '../src/routes/internal/plan';
import projectRoutes from '../src/routes/internal/project';
import request from 'supertest';
import runRoutes from '../src/routes/internal/run';
import { setupDB } from '../src/config/db';
import { setupUserTenant } from './utils/tenant';
import { getNextId } from '../src/lib/model';
import { closeQueues } from '../src/lib/queue';
import { tenantManager } from '../src/lib/tenants';

let app: Application, basePath: string, handle: string, session, project;
const db = setupDB();
let tenantDb: Knex;

beforeAll(async () => {
  app = buildApp(db, [
    orgRoutes,
    planRoutes,
    milestoneRoute,
    runRoutes,
    projectRoutes,
  ]);
  let tenant, user, password;

  tenant = await setupUserTenant(db, app);
  user = tenant.user;
  password = tenant.password;

  handle = tenant.handle;

  tenantDb = setupDB(tenant.user.uid, tenant.dbServer);

  const signin = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signin`)
    .send({ email: user.email, password });
  session = signin.headers['set-cookie'];

  const createProject = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
    .set('Accept', 'application/json')
    .set('Cookie', session)
    .send(data.newProject());
  project = createProject.body;

  basePath = `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${project.key}/plans`;
});

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

describe('Create Plan', () => {
  async function testCreate(dto) {
    const res = await request(app)
      .post(basePath)
      .send(dto)
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    expect(res.body.systemType).toBe('plan');

    const plan = await tenantDb('tags').where('uid', res.body.uid).first('*');
    expect(plan).toBeDefined();
    expect(plan.systemType).toBe('plan');
    return { returned: res.body, saved: plan };
  }
  it('creates a basic test plan', async () => {
    const dto = data.newPlan();
    const { saved } = await testCreate(dto);

    expect(saved.customFields.status).toBe(dto.status);
    expect(saved.customFields.priority).toBe(dto.priority);
  });

  it('creates a plan with milestones', async () => {
    const m = data.newMilestone();
    const [milestone] = await tenantDb('tags')
      .insert({
        name: m.name,
        description: m.description,
        customFields: { startDate: m.startDate, dueAt: m.dueAt },
        systemType: 'milestone',
      })
      .returning('*');

    const dto = data.newPlan();
    dto.milestoneUids = [milestone.uid];

    const { saved } = await testCreate(dto);

    const attachedMilestones = await tenantDb('testMilestonePlans').where(
      'milestoneUid',
      milestone.uid,
    );
    expect(attachedMilestones.length).toBe(1);
    expect(attachedMilestones[0].planUid).toBe(saved.uid);
  });

  it("creates a plan's runs using simple general configuration", async () => {
    let run;
    await tenantDb.transaction(async (trx) => {
      run = await dbHelpers.createRun(trx, project.uid);
    });

    const dto = data.newPlan();

    dto.testRuns = [{ uid: run.uid }];

    dto.configuration = {
      type: 'simple',
      sets: [
        ['browser::firefox', 'browser::safari'],
        ['os::linux', 'os::darwin'],
      ],
    };
    const { saved } = await testCreate(dto);

    // expected result should be 4 runs with configurations,
    // ["browser::firefox","os::linux"], ["browser::firefox","os::darwin"], ["browser::safari","os::linux"], ["browser::safari", "os::darwin"]
    const expected = [
      ['browser::firefox', 'os::linux'].sort().join(),
      ['browser::firefox', 'os::darwin'].sort().join(),
      ['browser::safari', 'os::linux'].sort().join(),
      ['browser::safari', 'os::darwin'].sort().join(),
    ];

    const planRuns = await tenantDb('testPlanRuns').where({
      planUid: saved.uid,
      deletedAt: null,
    });
    const runs = await tenantDb('tags').whereIn(
      'uid',
      planRuns.map((p) => p.runUid),
    );

    expect(runs.length).toBe(4);
    for (const run of runs) {
      expect(run.customFields).toHaveProperty('configs');
      const exists = expected.includes(run.customFields.configs.sort().join());
      expect(exists).toBe(true);
    }
  });

  it("creates a plan's runs using matrix general configuration", async () => {
    let run;
    await tenantDb.transaction(async (trx) => {
      run = await dbHelpers.createRun(trx, project.uid);
    });

    const dto = data.newPlan();
    dto.testRuns = [{ uid: run.uid }];
    dto.configuration = {
      type: 'matrix',
      sets: [
        ['browser::safari', 'os::windows'],
        ['browser::edge', 'os::darwin'],
      ],
    };
    const { saved } = await testCreate(dto);

    // expected result should be 2 runs with configurations,
    //  ['browser::safari', 'os::windows'], ['browser::edge', 'os::darwin']
    const expected = [
      ['browser::safari', 'os::windows'].sort().join(),
      ['browser::edge', 'os::darwin'].sort().join(),
    ];

    const planRuns = await tenantDb('testPlanRuns').where({
      planUid: saved.uid,
      deletedAt: null,
    });
    const runs = await tenantDb('tags').whereIn(
      'uid',
      planRuns.map((p) => p.runUid),
    );

    expect(runs.length).toBe(2);
    for (const run of runs) {
      expect(run.customFields).toHaveProperty('configs');
      const exists = expected.includes(run.customFields.configs.sort().join());
      expect(exists).toBe(true);
    }
  });

  it("creates a plan's runs using simple and matrix configurations", async () => {
    let run1, run2;
    await tenantDb.transaction(async (trx) => {
      run1 = await dbHelpers.createRun(trx, project.uid);
      run2 = await dbHelpers.createRun(trx, project.uid);
    });

    const dto = data.newPlan();
    dto.testRuns = [
      { uid: run1.uid },
      {
        uid: run2.uid,
        configuration: {
          type: 'simple',
          sets: [['browser::chrome'], ['os::linux', 'os::darwin']],
        },
      },
    ];
    dto.configuration = {
      type: 'matrix',
      sets: [['browser::safari', 'os::windows']],
    };

    // since this is a mixed case,
    // first => ['browser::safari', 'os::windows']
    // second => ['browser::chrome', 'os::linux'], ['browser::chrome', 'os::darwin'], ['browser::safari', 'os::windows']
    const { saved } = await testCreate(dto);

    const planRuns = await tenantDb('testPlanRuns').where({
      planUid: saved.uid,
      deletedAt: null,
    });
    const runs = await tenantDb('tags').whereIn(
      'uid',
      planRuns.map((p) => p.runUid),
    );

    expect(runs.length).toBe(4);
    const expected = [
      ['browser::safari', 'os::windows'].sort().join(),
      ['browser::chrome', 'os::linux'].sort().join(),
      ['browser::chrome', 'os::darwin'].sort().join(),
      ['browser::safari', 'os::windows'].sort().join(),
    ];

    for (const run of runs) {
      expect(run.customFields).toHaveProperty('configs');
      const exists = expected.includes(run.customFields.configs.sort().join());
      expect(exists).toBe(true);
    }
  });
});

describe('Duplicate Plan', () => {
  it("it dupliates a plan along with it's runs and executions", async () => {
    let plan, run, testCase;
    await tenantDb.transaction(async (trx) => {
      plan = await dbHelpers.createPlan(trx, project.uid);
      [run, testCase] = await Promise.all([
        dbHelpers.createRun(trx, project.uid, plan.uid),
        dbHelpers.createCase(trx, project.uid),
      ]);
      await dbHelpers.createExec(trx, project.uid, testCase.uid, run.uid);
    });

    const dto = { plans: [{ uid: plan.uid, runUids: [run.uid] }] };
    const res = await request(app)
      .post(`${basePath}/duplicate`)
      .send(dto)
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);

    // check if the runs were created
    const planRuns = await tenantDb('testPlanRuns').where({
      planUid: res.body[0].uid,
      deletedAt: null,
    });
    const runs = await tenantDb('tags').whereIn(
      'uid',
      planRuns.map((p) => p.runUid),
    );

    expect(runs.length).toBe(1);

    for (const run of runs) {
      const execs = await tenantDb('testExecutions').where({
        testRunUid: run.uid,
      });
      expect(execs.length).toBe(1);
      expect(execs[0].testCaseRef).toBe(testCase.testCaseRef);
    }
  });

  it('it duplicates a plan along with the selected runs', async () => {
    let plan, run1;
    await tenantDb.transaction(async (trx) => {
      plan = await dbHelpers.createPlan(trx, project.uid);
      [run1] = await Promise.all([
        dbHelpers.createRun(trx, project.uid, plan.uid),
        dbHelpers.createRun(trx, project.uid, plan.uid), // create another run
      ]);
    });

    const dto = { plans: [{ uid: plan.uid, runUids: [run1.uid] }] };
    const res = await request(app)
      .post(`${basePath}/duplicate`)
      .send(dto)
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);

    // check if the runs were created
    const planRuns = await tenantDb('testPlanRuns').where({
      planUid: res.body[0].uid,
      deletedAt: null,
    });
    const runs = await tenantDb('tags').whereIn(
      'uid',
      planRuns.map((p) => p.runUid),
    );
    expect(runs.length).toBe(1);
  });

  it('it duplicates a plan along without any runs', async () => {
    let plan;
    await tenantDb.transaction(async (trx) => {
      plan = await dbHelpers.createPlan(trx, project.uid);
      await Promise.all([
        dbHelpers.createRun(trx, project.uid, plan.uid),
        dbHelpers.createRun(trx, project.uid, plan.uid), // create another run
      ]);
    });

    const dto = { plans: [{ uid: plan.uid, runUids: [] }] };
    const res = await request(app)
      .post(`${basePath}/duplicate`)
      .send(dto)
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);

    // check if the runs were created
    const runs = await tenantDb('testPlanRuns').where({
      planUid: res.body[0].uid,
    });
    expect(runs.length).toBe(0);
  });
});

describe('List Plans', () => {
  it('returns a list of plans', async () => {
    await tenantDb.transaction(async (trx) => {
      for (let i = 0; i < 10; i++) {
        await dbHelpers.createPlan(trx, project.uid);
      }
    });
    const limit = 5;
    const res = await request(app)
      .get(basePath)
      .query({ limit })
      .set('Cookie', session);
    expect(res.statusCode).toBe(200);

    const savedPlans = await tenantDb('tags')
      .where('systemType', 'plan')
      .select('uid');
    const page = res.body;

    expect(page.count).toBe(savedPlans.length);
    expect(page.items.length).toEqual(limit);

    for (const plan of page.items) expect(plan.systemType).toBe('plan');
  });
});

describe('Delete Plans', () => {
  it('deletes a list of plan uids', async () => {
    const uids: number[] = [];
    let runCount = 0;
    await tenantDb.transaction(async (trx) => {
      for (let i = 0; i < 10; i++) {
        const plan = await dbHelpers.createPlan(trx, project.uid);
        for (let i = 0; i < 5; i++) {
          await dbHelpers.createRun(trx, project.uid, plan.uid);
          runCount++;
        }
        uids.push(plan.uid);
      }
    });

    const res = await request(app)
      .patch(basePath)
      .send({ uids, action: 'delete', cascade: false })
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    expect(res.body.affectedRows).toBe(uids.length);

    const deletedPlans = await tenantDb('tags').whereIn('uid', uids);
    for (const plan of deletedPlans) {
      expect(plan).toHaveProperty('deletedAt');
      expect(plan.deletedAt).not.toBeNull();

      // runs should not be affected
      const planRuns = await tenantDb('testPlanRuns').where({
        planUid: plan.uid,
        deletedAt: null,
      });
      const runs = await tenantDb('tags').whereIn(
        'uid',
        planRuns.map((p) => p.runUid),
      );
      expect(runs.length).toBe(5);
    }
  });

  it('cascades deletes down to runs if specified', async () => {
    const uids: number[] = [];
    await tenantDb.transaction(async (trx) => {
      for (let i = 0; i < 10; i++) {
        const plan = await dbHelpers.createPlan(trx, project.uid);
        for (let i = 0; i < 5; i++) {
          await dbHelpers.createRun(trx, project.uid, plan.uid);
        }
        uids.push(plan.uid);
      }
    });

    const res = await request(app)
      .patch(basePath)
      .send({ uids, action: 'delete', cascade: true })
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    expect(res.body.affectedRows).toBe(uids.length);

    const deletedPlans = await tenantDb('tags').whereIn('uid', uids);
    for (const plan of deletedPlans) {
      expect(plan).toHaveProperty('deletedAt');
      expect(plan.deletedAt).not.toBeNull();
    }

    // runs should not be affected
    const runs = await tenantDb('tags')
      .whereIn('uid', uids)
      .where('deletedAt', null);
    expect(runs.length).toBe(0);
  });
});

describe('Archive Plans', () => {
  it('archives a list of plan uids and their associated runs', async () => {
    const uids: number[] = [];

    await tenantDb.transaction(async (trx) => {
      for (let i = 0; i < 10; i++) {
        const plan = await dbHelpers.createPlan(trx, project.uid);
        for (let i = 0; i < 5; i++) {
          await dbHelpers.createRun(trx, project.uid, plan.uid);
        }
        uids.push(plan.uid);
      }
    });

    const res = await request(app)
      .patch(basePath)
      .send({ uids, action: 'archive' })
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    expect(res.body.affectedRows).toBe(uids.length);

    const plans = await tenantDb('tags').whereIn('uid', uids);
    for (const plan of plans) {
      expect(plan).toHaveProperty('archivedAt');
      expect(plan.archivedAt).not.toBeNull();
    }

    // runs should not be affected
    const runs = await tenantDb('tags')
      .whereIn('uid', uids)
      .where('archivedAt', null);
    expect(runs.length).toBe(0);
  });
});

describe('Unarchive Plans', () => {
  it('unarchives a list of archived plans', async () => {
    const uids: number[] = [];

    await tenantDb.transaction(async (trx) => {
      for (let i = 0; i < 10; i++) {
        const plan = await dbHelpers.createPlan(trx, project.uid, {
          archivedAt: faker.date.recent(),
        });
        uids.push(plan.uid);
      }
    });

    const res = await request(app)
      .patch(basePath)
      .send({ uids, action: 'unarchive' })
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    expect(res.body.affectedRows).toBe(uids.length);
    const plans = await tenantDb('tags').whereIn('uid', uids);
    for (const plan of plans) {
      expect(plan).toHaveProperty('archivedAt');
      expect(plan.archivedAt).toBeNull();
    }
  });
});

describe('Add Milestones to Plans', () => {
  it('fails on providing an invalid plan uid', async () => {
    const milestoneUids: number[] = [];

    let uid;
    await tenantDb.transaction(async (trx) => {
      for (let i = 0; i < 5; i++) {
        const milestone = await dbHelpers.createMilestone(trx, project.uid);
        milestoneUids.push(milestone.uid);
      }
      uid = await getNextId(trx, <any>{ tableName: 'tags' });
    });

    const res = await request(app)
      .patch(basePath)
      .send({ uids: [uid], action: 'addMilestones', milestoneUids })
      .set('Cookie', session);

    expect(res.statusCode).toBe(409);
  });

  it('fails on providing an invalid milestone uid', async () => {
    const uids: number[] = [];
    let milestoneUid;
    await tenantDb.transaction(async (trx) => {
      for (let i = 0; i < 5; i++) {
        const plan = await dbHelpers.createPlan(trx, project.uid);
        uids.push(plan.uid);
      }
      milestoneUid = await getNextId(trx, <any>{ tableName: 'tags' });
    });

    const res = await request(app)
      .patch(basePath)
      .send({ uids, action: 'addMilestones', milestoneUids: [milestoneUid] })
      .set('Cookie', session);

    expect(res.statusCode).toBe(409);
  });

  it('successfully attaches plans to milestones', async () => {
    const planUids: number[] = [];
    const milestoneUids: number[] = [];
    const counts = { plans: 0, milestones: 0 };
    await tenantDb.transaction(async (trx) => {
      for (let i = 0; i < 5; i++) {
        const plan = await dbHelpers.createPlan(trx, project.uid);
        planUids.push(plan.uid);
        counts.plans++;
      }

      for (let i = 0; i < 5; i++) {
        const mst = await dbHelpers.createMilestone(trx, project.uid);
        milestoneUids.push(mst.uid);
        counts.milestones++;
      }
    });

    const res = await request(app)
      .patch(basePath)
      .send({ uids: planUids, action: 'addMilestones', milestoneUids })
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    const plans = await tenantDb('tags').whereIn('uid', planUids);
    expect(plans.length).toBe(counts.plans);

    for (const plan of plans) {
      const attachedMilestones = await tenantDb('testMilestonePlans').where(
        'planUid',
        plan.uid,
      );
      expect(attachedMilestones.length).toBe(counts.milestones);
    }
  });
});

describe('Remove Milestones from Plans', () => {
  it('successfully attaches plans to milestones', async () => {
    const planUids: number[] = [];
    const milestoneUids: number[] = [];
    await tenantDb.transaction(async (trx) => {
      const plan = await dbHelpers.createPlan(trx, project.uid);
      const milestone = await dbHelpers.createMilestone(trx, project.uid);
      await trx('testMilestonePlans').insert({
        planUid: plan.uid,
        milestoneUid: milestone.uid,
        deletedAt: null,
      });
      planUids.push(plan.uid);
      milestoneUids.push(milestone.uid);
    });

    const res = await request(app)
      .patch(basePath)
      .send({ uids: planUids, action: 'removeMilestones', milestoneUids })
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    const attachments = await tenantDb('testMilestonePlans')
      .whereIn('planUid', planUids)
      .whereIn('milestoneUid', milestoneUids)
      .returning('*');
    for (const a of attachments) expect(a.deletedAt).not.toBe(null);
  });
});

describe('Add Runs to Plans', () => {
  it('successfully attaches existing runs to plans', async () => {
    const trx = await tenantDb.transaction();

    const runs = await Promise.all(
      new Array(5)
        .fill(undefined)
        .map(() => dbHelpers.createRun(trx, project.uid)),
    );
    const plans = await Promise.all(
      new Array(5)
        .fill(undefined)
        .map(() => dbHelpers.createPlan(trx, project.uid)),
    );
    await trx.commit();

    const planUids = plans.map((p) => p.uid);
    const runUids = runs.map((r) => r.uid);

    const res = await request(app)
      .patch(basePath)
      .send({
        uids: planUids,
        action: 'addRuns',
        runUids: runUids,
      })
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    const attachments = await tenantDb('testPlanRuns')
      .whereIn('planUid', planUids)
      .whereIn('runUid', runUids)
      .returning('*');

    expect(attachments.length).toBe(planUids.length * runUids.length);
    for (const a of attachments) expect(a.deletedAt).toBe(null);
  });
});

describe('Remove Runs from Plans', () => {
  it('successfully unlinks test runs from a test plan', async () => {
    const trx = await tenantDb.transaction();

    const plans = await Promise.all(
      new Array(5)
        .fill(undefined)
        .map(() => dbHelpers.createPlan(trx, project.uid)),
    );

    const runs = await Promise.all(
      new Array(5)
        .fill(undefined)
        .map(() =>
          dbHelpers.createRun(
            trx,
            project.uid,
            faker.helpers.arrayElement(plans).uid,
          ),
        ),
    );

    await trx.commit();

    const planUids = plans.map((p) => p.uid);
    const runUids = runs.map((r) => r.uid);

    const res = await request(app)
      .patch(basePath)
      .send({
        uids: planUids,
        action: 'removeRuns',
        runUids: runUids,
      })
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    const attachments = await tenantDb('testPlanRuns')
      .whereIn('planUid', planUids)
      .whereIn('runUid', runUids)
      .returning('*');

    for (const a of attachments) expect(a.deletedAt).not.toBe(null);
  });
});

describe('Fetch Plan By ID', () => {
  it('fails on invalid plan id', async () => {
    let uid;
    await tenantDb.transaction(async (trx) => {
      uid = await getNextId(trx, <any>{ tableName: 'tags' });
    });

    const res = await request(app)
      .get(`${basePath}/${uid}`)
      .set('Cookie', session);
    expect(res.statusCode).toBe(404);
  });

  it('fails if plan is deleted', async () => {
    let uid;
    await tenantDb.transaction(async (trx) => {
      const plan = await dbHelpers.createPlan(trx, project.uid, {
        deletedAt: faker.date.recent(),
      });
      uid = plan.uid;
    });

    const res = await request(app)
      .get(`${basePath}/${uid}`)
      .set('Cookie', session);
    expect(res.statusCode).toBe(404);
  });

  it('retrieves a plan by its id', async () => {
    let plan;
    await tenantDb.transaction(async (trx) => {
      plan = await dbHelpers.createPlan(trx, project.uid);
      for (let i = 0; i < 5; i++)
        await dbHelpers.createRun(trx, project.uid, plan.uid);
    });

    const res = await request(app)
      .get(`${basePath}/${plan.uid}`)
      .set('Cookie', session);
    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('runs');
  });
});

describe('Update Plan', () => {
  it('updates the primary details of a plan', async () => {
    const trx = await tenantDb.transaction();
    const { uid } = await dbHelpers.createPlan(trx, project.uid);
    await trx.commit();

    const update = {
      name: faker.lorem.lines(1),
      description: faker.lorem.paragraph(),
    };

    const res = await request(app)
      .patch(`${basePath}/${uid}`)
      .send(update)
      .set('Cookie', session);
    expect(res.statusCode).toBe(200);

    const plan = await tenantDb('tags').where({ uid }).first('*');
    expect(plan).toBeDefined();
    expect(plan).not.toBeNull();

    expect(plan.name).toBe(update.name);
    expect(plan.description).toBe(update.description);
  });

  it('adds new milestones to a plans', async () => {
    let uid;
    const milestoneUids: number[] = [];
    await tenantDb.transaction(async (trx) => {
      const plan = await dbHelpers.createPlan(trx, project.uid);
      uid = plan.uid;

      for (let i = 0; i < 5; i++) {
        const mst = await dbHelpers.createMilestone(trx, project.uid);
        milestoneUids.push(mst.uid);
      }
    });

    const update = { milestoneUids };

    const res = await request(app)
      .patch(`${basePath}/${uid}`)
      .send(update)
      .set('Cookie', session);
    expect(res.statusCode).toBe(200);

    const milestones = await tenantDb('testMilestonePlans').where(
      'planUid',
      uid,
    );
    expect(milestones.length).toBe(milestoneUids.length);

    for (const m of milestones) expect(milestoneUids).toContain(m.milestoneUid);
  });

  it('deletes milestone attachments from a plan', async () => {
    let uid;
    const milestoneUids: number[] = [];
    await tenantDb.transaction(async (trx) => {
      const plan = await dbHelpers.createPlan(trx, project.uid);
      uid = plan.uid;

      for (let i = 0; i < 5; i++) {
        const mst = await dbHelpers.createMilestone(trx, project.uid);
        milestoneUids.push(mst.uid);
      }
    });

    // create the plan-milestone attachment
    await tenantDb('testMilestonePlans').insert(
      milestoneUids.map((m) => ({ planUid: uid, milestoneUid: m })),
    );

    const update = { milestoneUids: milestoneUids.slice(2) };
    const res = await request(app)
      .patch(`${basePath}/${uid}`)
      .send(update)
      .set('Cookie', session);
    expect(res.statusCode).toBe(200);

    const milestones = await tenantDb('testMilestonePlans').where({
      planUid: uid,
      deletedAt: null,
    });
    expect(milestones.length).toBe(update.milestoneUids.length);
  });

  it('pseudo-adds new runs by replicating and attaching the duplicates', async () => {
    let uid;
    const runUids: number[] = [];
    await tenantDb.transaction(async (trx) => {
      const plan = await dbHelpers.createPlan(trx, project.uid);
      uid = plan.uid;

      for (let i = 0; i < 3; i++) {
        const run = await dbHelpers.createRun(trx, project.uid);
        runUids.push(run.uid);
      }
    });

    const update = { runUids };
    const res = await request(app)
      .patch(`${basePath}/${uid}`)
      .send(update)
      .set('Cookie', session);
    expect(res.statusCode).toBe(200);

    const planRuns = await tenantDb('testPlanRuns').where({
      planUid: uid,
      deletedAt: null,
    });
    expect(planRuns.length).toBe(update.runUids.length);
    for (const { runUid } of planRuns) expect(runUids).not.toContain(runUid);
  });

  it('detaches existing runs from a plan', async () => {
    let uid;
    const runUids: number[] = [];
    await tenantDb.transaction(async (trx) => {
      const plan = await dbHelpers.createPlan(trx, project.uid);
      uid = plan.uid;

      for (let i = 0; i < 3; i++) {
        const run = await dbHelpers.createRun(trx, project.uid, plan.uid);
        runUids.push(run.uid);
      }
    });

    const update = { runUids: [runUids[0]] };
    const res = await request(app)
      .patch(`${basePath}/${uid}`)
      .send(update)
      .set('Cookie', session);
    expect(res.statusCode).toBe(200);

    const runs = await tenantDb('testPlanRuns').where({
      planUid: uid,
      deletedAt: null,
    });
    expect(runs.length).toBe(update.runUids.length);
  });

  it('errors on providing invalid plan id', async () => {
    let uid;
    await tenantDb.transaction(async (trx) => {
      uid = await getNextId(trx, <any>{ tableName: 'tags' });
    });

    const update = { name: faker.company.name() };
    const res = await request(app)
      .patch(`${basePath}/${uid}`)
      .send(update)
      .set('Cookie', session);
    expect(res.statusCode).toBe(404);
  });
});

describe('Count Plan Relations', () => {
  it('returns the count of runs in a plan', async () => {
    let plan;
    const runCount = 5;

    await tenantDb.transaction(async (trx) => {
      plan = await dbHelpers.createPlan(trx, project.uid);
      for (let i = 0; i < runCount; i++) {
        await dbHelpers.createRun(trx, project.uid, plan.uid);
      }
    });

    const res = await request(app)
      .get(`${basePath}/${plan.uid}/runs/count`)
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    expect(res.body.count).toBe(runCount);
  });

  it('returns the count of milestones in a plan', async () => {
    let plan;
    const milestoneCount = 3;
    const milestones: any[] = [];

    await tenantDb.transaction(async (trx) => {
      plan = await dbHelpers.createPlan(trx, project.uid);

      for (let i = 0; i < milestoneCount; i++) {
        const milestone = await dbHelpers.createMilestone(trx, project.uid);
        milestones.push({
          milestoneUid: milestone.uid,
          planUid: plan.uid,
          deletedAt: null,
        });
      }

      await trx('testMilestonePlans').insert(milestones);
    });

    const res = await request(app)
      .get(`${basePath}/${plan.uid}/milestones/count`)
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    expect(res.body.count).toBe(milestoneCount);
  });
});

describe('Count Plans', () => {
  it('returns the number of active and archived plans', async () => {
    await tenantDb.transaction(async (trx) => {
      const archive = [];
      for (let i = 0; i < 10; i++) {
        const plan = await dbHelpers.createPlan(trx, project.uid);
        if (i % 2 === 1) archive.push(plan.uid);
      }

      await trx('tags')
        .whereIn('uid', archive)
        .update({ archivedAt: new Date() });
    });

    const res = await request(app)
      .get(
        `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${project.key}/entities/count`,
      )
      .query({ entityType: 'plan' })
      .set('Cookie', session);
    expect(res.status).toBe(200);
    expect(res.body).toHaveProperty('active');
    expect(res.body).toHaveProperty('archived');
    expect(res.body.archived).toBeGreaterThanOrEqual(5);
    expect(res.body.active).toBeGreaterThanOrEqual(5);
  });
});
