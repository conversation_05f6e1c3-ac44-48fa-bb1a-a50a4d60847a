import { Application } from 'express';
import { buildApp } from '../src/app';
import { closeQueues } from '../src/lib/queue';
import { expect } from 'chai';
import { newProject } from './data/index';
import orgRoutes from '../src/routes/internal/org';
import projectRoutes from '../src/routes/internal/project';
import request from 'supertest';
import { setupDB } from '../src/config/db';
import { setupUserTenant } from './utils/tenant';
import templateRoutes from '../src/routes/internal/template';
import { tenantManager } from '../src/lib/tenants';

let app: Application,
  session: any,
  basePath: string,
  projectId: string,
  template;
const db = setupDB();

beforeAll(async () => {
  app = buildApp(db, [orgRoutes, projectRoutes, templateRoutes]);

  const tenant = await setupUserTenant(db, app);
  const signin = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signin`)
    .send({ email: tenant.user.email, password: tenant.password });
  session = signin.headers['set-cookie'];

  const createProject = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/${tenant.handle}/projects`)
    .set('Accept', 'application/json')
    .set('Cookie', session)
    .send(newProject());
  projectId = createProject.body.key;
  basePath = `${process.env.API_INTERNAL_ROUTE}/${tenant.handle}/projects/${projectId}`;
});

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

describe('Template Endpoints', () => {
  it('should be able to create template', async () => {
    const res = await request(app)
      .post(`${basePath}/templates`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        name: 'Template #1',
        isDefault: true,
        templateFields: [
          {
            name: 'Field #1',
            type: 'text',
            isRequired: true,
          },
          {
            name: 'Field #2',
            type: 'text',
            isRequired: false,
          },
        ],
      });
    expect(res.statusCode).to.be.eq(200);
    template = res.body;

    expect(res.body.name).to.be.equal('Template #1');
    expect(res.body.isDefault).to.be.equal(true);
    expect(res.body.customFields.templateFields).not.empty;
  });

  it('should be able to get all templates', async () => {
    const res = await request(app)
      .get(`${basePath}/templates`)
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).to.be.eq(200);
    expect(res.body.templates).not.empty;
  });

  it('should be able to get a single template', async () => {
    let res = await request(app)
      .post(`${basePath}/templates`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        name: 'Template #2',
      });

    res = await request(app)
      .get(`${basePath}/templates/${res.body.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).to.be.eq(200);
    expect(res.body).not.empty;
  });

  it('should delete a template', async () => {
    let res = await request(app)
      .post(`${basePath}/templates`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        name: 'Template #1',
      });

    res = await request(app)
      .delete(`${basePath}/templates/${res.body.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send();

    expect(res.statusCode).to.be.eq(200);
    expect(res.body.message).not.empty;
  });

  it('should filter templates from name', async () => {
    const templateName = 'Template filter #1';

    let res = await request(app)
      .post(`${basePath}/templates`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        name: templateName,
      });

    res = await request(app)
      .get(`${basePath}/templates`)
      .query({ name: templateName })
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).to.be.eq(200);
    expect(res.body.templates[0].name).equal(templateName);
  });

  it('should update all template to isDefault false and set new created to true', async () => {
    await request(app)
      .post(`${basePath}/templates`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        name: 'Template #2',
        isDefault: true,
      });

    const res = await request(app)
      .get(`${basePath}/templates/${template.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).to.be.eq(200);
    expect(res.body.isDefault).to.be.equal(false);
  });

  it('should update current template to isDefault true and others to false', async () => {
    // Set template1 as default
    let res = await request(app)
      .patch(`${basePath}/templates/${template.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        isDefault: true,
      });

    // Verify template1 is now default
    expect(res.statusCode).to.be.eq(200);
    expect(res.body.isDefault).to.be.equal(true);

    const allTemplates = await request(app)
      .get(`${basePath}/templates`)
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(allTemplates.statusCode).to.be.eq(200);
    expect(allTemplates.body.templates).not.empty;

    for (const _template of allTemplates.body.templates) {
      if (_template.uid === template.uid) {
        expect(_template.isDefault).to.be.true;
      } else {
        expect(_template.isDefault).to.be.false;
      }
    }
  });
});
