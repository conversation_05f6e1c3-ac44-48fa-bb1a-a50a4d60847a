import * as data from './data/index';

import { SharedStepItem, TestCaseStepItem } from '../src/types/step';

import { Application } from 'express';
import { buildApp } from '../src/app';
import caseRoutes from '../src/routes/internal/case';
import { closeQueues } from '../src/lib/queue';
import { expect } from 'chai';
import folderRoutes from '../src/routes/internal/folder';
import orgRoutes from '../src/routes/internal/org';
import projectRoutes from '../src/routes/internal/project';
import request from 'supertest';
import { setupDB } from '../src/config/db';
import { setupOrgTenant } from './utils/tenant';
import sharedStepRoutes from '../src/routes/internal/sharedStep';
import tagRoutes from '../src/routes/internal/tag';
import { tenantManager } from '../src/lib/tenants';
import { v4 as uuidv4 } from 'uuid';

let session, project, folder, testCase, orgId, dbServer;
let handle;

const db = setupDB();

let app: Application, basePath: string;

beforeAll(async () => {
  app = buildApp(db, [
    orgRoutes,
    caseRoutes,
    projectRoutes,
    folderRoutes,
    sharedStepRoutes,
    tagRoutes,
  ]);

  const tenant = await setupOrgTenant(db, app);
  const email = tenant.admin.email;
  const password = tenant.password;
  orgId = tenant.org.uid;
  dbServer = tenant.dbServer;
  handle = tenant.handle;

  const signin = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signin`)
    .send({ email, password });
  session = signin.headers['set-cookie'];

  // create default project and retrieve it's latest folder
  const createProject = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
    .set('Cookie', session)
    .send(data.newProject());
  project = createProject.body;
  basePath = `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${project.key}`;
  const projectFolders = await request(app)
    .get(`${basePath}/folders`)
    .set('Cookie', session)
    .send();
  folder = projectFolders.body.folders[0];
});

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

describe('Test Case Endpoints', () => {
  it('should be able to create a new test case', async () => {
    const res = await request(app)
      .post(`${basePath}/cases`)
      .set('Cookie', session)
      .send({ ...data.newCase(), parentId: folder.uid });
    expect(res.statusCode).to.be.oneOf([200]);
    testCase = res.body;
  });

  it('should be able to create a new test case in a folder', async () => {
    const res = await request(app)
      .post(`${basePath}/cases`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({ ...data.newCase(), parentId: folder.uid });
    expect(res.statusCode).to.be.oneOf([200]);
  });

  it('should be able to import a new test case in a new or existing folder according to folder name in test case', async () => {
    const testCases = [
      {
        ...data.newCase(),
        parentName: 'First Folder',
      },
      {
        ...data.newCase(),
        parentName: 'Second Folder',
      },
      {
        ...data.newCase(),
        parentName: 'First Folder',
      },
    ];
    const res = await request(app)
      .post(`${basePath}/cases/import`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({ cases: testCases });

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body).to.be.an('array');
    expect(res.body.length).to.equal(3);
    // Verify the created cases
    for (const testCase of res.body) {
      expect(testCase).to.have.property('uid');
      expect(testCase).to.have.property('name');
      expect(testCase).to.have.property('parentUid').that.is.a('number');
    }

    // Verify folders were created
    const foldersRes = await request(app)
      .get(`${basePath}/folders`)
      .set('Accept', 'application/json')
      .set('Cookie', session);
    expect(foldersRes.statusCode).to.be.oneOf([200]);
    expect(foldersRes.body.folders).to.be.an('array');

    expect(
      foldersRes.body.folders[0].children.some(
        (f) => f.name === 'First Folder',
      ),
    ).to.be.true;
    expect(
      foldersRes.body.folders[0].children.some(
        (f) => f.name === 'Second Folder',
      ),
    ).to.be.true;
    folder = foldersRes.body.folders[0];
  });

  it('should not create new folder while importing', async () => {
    // // Verify the test cases were created in the existing folders
    const singleTestCase = [
      {
        ...data.newCase(),
        parentName: 'First Folder',
      },
    ];

    const verifyFolderRes = await request(app)
      .post(`${basePath}/cases/import`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({ cases: singleTestCase });

    expect(verifyFolderRes.statusCode).to.be.oneOf([200]);
    expect(verifyFolderRes.body).to.be.an('array');
    expect(verifyFolderRes.body[0]).to.have.property('parentUid');

    // Verify the test case was created in the existing folder
    const existingFolder = folder.children.find(
      (f) => f.name === 'First Folder',
    );
    expect(existingFolder).to.not.be.undefined;
    expect(verifyFolderRes.body[0].parentUid).to.equal(existingFolder.uid);
  });

  it('should get folders and cases in a folder', async () => {
    const res = await request(app)
      .get(`${basePath}/cases`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .query({ parentUid: folder.uid });

    expect(res.statusCode).to.be.oneOf([200]);

    const tenantDB = setupDB(orgId, dbServer);
    for (const testCase of res.body.items) {
      const tags = await tenantDB('tags')
        .where({ uid: testCase.parentUid, systemType: 'folder' })
        .first();
      expect(tags).not.oneOf([null, undefined]);
      expect(tags).haveOwnProperty('uid');
    }
  });

  it('should be able to search test cases', async () => {
    const res = await request(app)
      .get(`${basePath}/cases`)
      .query({ query: testCase.name })
      .set('Cookie', session);

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body.items.length).greaterThanOrEqual(1);
  });

  it('should update a test case and create a new version', async () => {
    const update = data.newCase();
    const res = await request(app)
      .patch(`${basePath}/cases/${testCase.testCaseRef}`)
      .set('Cookie', session)
      .send(update);
    expect(res.statusCode).to.be.oneOf([200]);

    // expect a new version
    const newCase = res.body;
    expect(newCase.uid).not.eq(testCase.uid);
    expect(newCase.version).eq(testCase.version + 1);
    expect(newCase.name).eq(update.name);
    expect(newCase.externalId).eq(update.externalId);
    expect(newCase.source).eq(update.source);
  });

  it('should update multiple test cases and create new versions', async () => {
    const res = await request(app)
      .patch(`${basePath}/cases`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        cases: [
          {
            testCaseRef: testCase.testCaseRef,
            externalId: testCase.externalId,
            parentId: testCase.parentUid,
            name: testCase.name,
            source: 'testing the fiesta again',
          },
        ],
      });
    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body).to.not.be.eq({});
  });

  it('should be able to create a new test case with its steps', async () => {
    const steps: Array<TestCaseStepItem> = [data.newStep(), data.newStep()];

    const res = await request(app)
      .post(`${basePath}/cases`)
      .set('Cookie', session)
      .send({
        ...data.newCase(),
        parentId: folder.uid,
        steps,
      });

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body.steps).to.be.lengthOf(2);
  });

  it('should be able to create a new test case with its steps and its children', async () => {
    const steps: Array<TestCaseStepItem> = [
      {
        ...data.newStep(),
        children: [data.newStep()],
      },
      {
        ...data.newStep(),
        children: [data.newStep(), data.newStep()],
      },
    ];

    const res = await request(app)
      .post(`${basePath}/cases`)
      .set('Cookie', session)
      .send({
        ...data.newCase(),
        parentId: folder.uid,
        steps,
      });

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body.steps).to.be.lengthOf(2);
    expect(res.body.steps[0].children).to.be.lengthOf(1);
    expect(res.body.steps[1].children).to.be.lengthOf(2);
  });

  it('should remove test case step when shared steps are removed from a test case while updating', async () => {
    const sharedSteps: Array<SharedStepItem> = [];
    const sharedStepIds: string[] = [];

    for (let i = 1; i <= 5; i++) {
      sharedSteps.push({
        id: uuidv4(),
        title: `Step Name #${i}`,
        description: `Step Description #${i}`,
        expectedResult: `Step Expected Result #${i}`,
      });
    }

    for (let i = 1; i <= 5; i++) {
      const res = await request(app)
        .post(`${basePath}/shared-steps`)
        .set('Accept', 'application/json')
        .set('Cookie', session)
        .send({
          name: 'Version Test #1',
          steps: sharedSteps,
        });

      sharedStepIds.push(res.body.uid);
    }

    const testCaseSteps: Array<TestCaseStepItem> = [
      {
        id: uuidv4(),
        title: 'Test step title #1',
        description: 'Test Step Description #1',
        expectedResult: 'Test Expected Result #1',
      },
      {
        id: uuidv4(),
        title: 'Test step title #2',
        description: 'Test Step Description #2',
        expectedResult: 'Test Expected Result #2',
      },
      ...sharedStepIds.map((sharedStepId) => ({
        id: sharedStepId,
        sharedStepUId: sharedStepId,
        title: `Shared Step title #${sharedStepId}`,
        description: `Shared Step description #${sharedStepId}`,
        expectedResult: `Shared Step expectedResult #${sharedStepId}`,
      })),
    ];

    let res = await request(app)
      .post(`${basePath}/cases`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        ...data.newCase(),
        parentId: folder.uid,
        steps: testCaseSteps,
      });

    const remainingCaseSteps = testCaseSteps
      .filter((caseStep) => caseStep.sharedStepUid)
      .slice(0, 2);

    res = await request(app)
      .patch(`${basePath}/cases/${res.body.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        externalId: '123123',
        customFields: { version: '2.0', platform: 'app' },
        source: 'testfiesta',
        name: 'Create Case #1 Updated',
        steps: [...remainingCaseSteps],
      });

    expect(res.statusCode).to.be.oneOf([200]);
  });

  it('should create a test case with tags attached', async () => {
    const tagIds: number[] = [];

    for (let i = 1; i <= 3; i++) {
      const res = await request(app)
        .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/tags`)
        .set('Accept', 'application/json')
        .set('Cookie', session)
        .send({
          ...data.newTag(),
          entityTypes: ['cases', 'runs'],
        });

      tagIds.push(res.body.uid);
    }

    let res = await request(app)
      .post(`${basePath}/cases`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        ...data.newCase(),
        parentId: folder.uid,
        tagIds,
      });

    const cases = res.body;

    res = await request(app)
      .get(`${basePath}/cases/${cases.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send();

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body.tags).not.empty;
  });

  it('should update a test case with new tags and replace exisiting tags', async () => {
    const insertedTagIds: number[] = [];

    // create 5 tags
    for (let i = 0; i < 5; i++) {
      const res = await request(app)
        .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/tags`)
        .set('Accept', 'application/json')
        .set('Cookie', session)
        .send({
          ...data.newTag(),
          entityTypes: ['cases', 'runs'],
        });

      insertedTagIds.push(res.body.uid);
    }
    const initial = insertedTagIds.slice(0, 2);
    const replacements = insertedTagIds.slice(2, 4);
    const toAppend = insertedTagIds.slice(4);

    // add the first 3 tags to the case
    let res = await request(app)
      .post(`${basePath}/cases`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        ...data.newCase(),
        parentId: folder.uid,
        tagIds: insertedTagIds,
      });

    const newTestCase = res.body;

    res = await request(app)
      .patch(`${basePath}/cases`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        cases: [
          {
            testCaseRef: newTestCase.uid,
            externalId: newTestCase.externalId,
            parentId: newTestCase.parentUid,
            name: testCase.name,
            source: 'testing the fiesta again',
            tagReplacements: [
              {
                existingTagUids: initial,
                newTagUids: replacements,
              },
            ],
            tagIds: toAppend,
          },
        ],
      });

    expect(res.statusCode).to.be.oneOf([200]);

    const tenantDB = setupDB(orgId, dbServer);

    const testCaseTags = await tenantDB('testCaseTags')
      .innerJoin('tags', 'tags.uid', 'tagUid')
      .where({
        testCaseRef: newTestCase.testCaseRef,
        systemType: 'tag',
        'testCaseTags.deletedAt': null,
      });

    expect(testCaseTags.length).equal(3);
  });

  it('should return correct cases count for a project', async () => {
    const countRes = await request(app)
      .get(`${basePath}/cases/count`)
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(countRes.statusCode).to.be.oneOf([200]);
    expect(countRes.body.count).to.greaterThan(1);
  });
});
