import * as data from './data/index';
import * as dbHelpers from './utils/dbHelpers';

import preferences, { PreferenceMap } from '../src/models/preferences';

import { Application } from 'express';
import { Knex } from 'knex';
import { ModelObject } from 'objection';
import { PaginatedResult } from '../src/lib/model';
import { Tag } from '../src/models/tag';
import { TestExecution } from '../src/models/testExecution';
import { TestMilestoneRun } from '../src/models/testMilestoneRun';
import { TestPlanRun } from '../src/models/testPlanRun';
import { TestRun } from '../src/models/testRun';
import { buildApp } from '../src/app';
import { closeQueues } from '../src/lib/queue';
import { faker } from '@faker-js/faker';
import milestoneRoutes from '../src/routes/internal/milestone';
import projectRoutes from '../src/routes/internal/project';
import request from 'supertest';
import runRoutes from '../src/routes/internal/run';
import { setupDB } from '../src/config/db';
import { setupUserTenant } from './utils/tenant';
import { tenantManager } from '../src/lib/tenants';

let app: Application,
  session: any,
  user,
  password: string,
  handle: string,
  projectId: number,
  projectKey: string,
  basePath: string,
  run: ModelObject<TestRun>,
  tenantDb: Knex,
  defaults: PreferenceMap,
  tagUids: number[];

const db = setupDB();

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

beforeAll(async () => {
  app = buildApp(db, [runRoutes, milestoneRoutes, projectRoutes]);

  const tenant = await setupUserTenant(db, app);
  user = tenant.user;
  password = tenant.password;
  handle = tenant.handle;

  tenantDb = setupDB(tenant.user.uid);

  const signin = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signin`)
    .send({ email: user.email, password });
  session = signin.headers['set-cookie'];

  const createProject = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
    .set('Accept', 'application/json')
    .set('Cookie', session)
    .send(data.newProject());
  projectId = createProject.body.uid;
  projectKey = createProject.body.key;

  basePath = `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${createProject.body.key}/runs`;

  defaults = await preferences.getDefaults(db, 'user', tenant.user.uid);

  tagUids = (await Tag.query(tenantDb).where('systemType', 'tag')).map(
    (t) => t.uid,
  );
});

describe('Create Run', () => {
  const baseDto = (extra?) =>
    ({
      name: faker.string.alphanumeric(32),
      description: faker.lorem.paragraph(2),
      dueAt: faker.date.future(),
      ...extra,
    }) as Record<string, any>;

  it('creates a simple test run', async () => {
    const dto = baseDto();

    const res = await request(app)
      .post(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send(dto);

    expect(res.statusCode).toBe(200);
    run = res.body;
    expect(run.customFields?.status).toBe(defaults.testRun?.status);
    expect(run.customFields?.priority).toBe(defaults.testRun?.priority);
    expect(run.customFields.progress).toBeNull();
    expect(run.systemType).toBe('run');
  });

  it('creates a test run with tags', async () => {
    const tags = faker.helpers.arrayElements(
      tagUids,
      faker.number.int() % tagUids.length,
    );

    const dto = baseDto({
      tagUids: tags,
    });

    const res = await request(app)
      .post(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send(dto);
    expect(res.statusCode).toBe(200);

    const savedTags = (
      await tenantDb('testRunTags').where({
        runUid: res.body.uid,
        deletedAt: null,
      })
    )
      .map((t) => t.tagUid)
      .sort();
    expect(savedTags).toEqual(tags.sort());
  });

  it('creates a test run with milestones', async () => {
    const milestoneUids: number[] = [];
    await tenantDb.transaction(async (trx) => {
      for (let i = 0; i < 5; i++) {
        const milestone = await dbHelpers.createMilestone(trx, projectId);

        milestoneUids.push(milestone.uid);
      }
    });
    const dto = baseDto({ milestoneUids });

    const res = await request(app)
      .post(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send(dto);
    expect(res.statusCode).toBe(200);

    // check if the relationship is created
    const milestoneRuns = await TestMilestoneRun.query(tenantDb).where({
      runUid: res.body.uid,
    });
    expect(milestoneRuns.length).toBe(milestoneUids.length);
  });

  it('creates a test run with attached executions', async () => {
    const caseUids: number[] = [];
    await tenantDb.transaction(async (trx) => {
      for (let i = 0; i < 5; i++) {
        const testCase = await dbHelpers.createCase(trx, projectId);
        caseUids.push(testCase.uid);
      }
    });
    const dto = baseDto({ caseUids });

    const res = await request(app)
      .post(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send(dto);
    expect(res.statusCode).toBe(200);

    const executions = await TestExecution.query(tenantDb).where({
      testRunUid: res.body.uid,
    });
    expect(executions.length).toBe(caseUids.length);
  });
});

describe('Duplicate Run', () => {
  it('duplicates an existing run using configs', async () => {
    let run: TestRun;
    await tenantDb.transaction(async (trx) => {
      run = await dbHelpers.createRun(trx, projectId, undefined, true);
    });

    const dto = {
      testRuns: [
        {
          uid: run!.uid,
          configuration: {
            type: 'simple',
            sets: [['browser::safari', 'browser::chrome']],
          },
        },
      ],
      configuration: { type: 'simple', sets: [['os::windows']] },
    };

    const res = await request(app)
      .post(`${basePath}/duplicate`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send(dto);

    expect(res.statusCode).toBe(200);
    expect(res.body.length).toBe(2);

    const expected = [
      ['browser::safari', 'os::windows'].sort().join(),
      ['browser::chrome', 'os::windows'].sort().join(),
    ];
    for (const r of res.body) {
      expect(r.customFields).toHaveProperty('configs');
      const exists = expected.includes(r.customFields.configs.sort().join());
      expect(exists).toBe(true);
    }
  });

  it('duplicates runs without configs', async () => {
    let run: TestRun;
    await tenantDb.transaction(async (trx) => {
      run = await dbHelpers.createRun(trx, projectId, undefined, true);
    });

    const dto = {
      testRuns: [
        {
          uid: run!.uid,
          configuration: {},
        },
      ],
      configuration: {},
    };

    const res = await request(app)
      .post(`${basePath}/duplicate`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send(dto);
    expect(res.statusCode).toBe(200);
    expect(res.body.length).toBe(1);

    expect(res.body[0].customFields.configs).toEqual([]);
  });
});

describe('Bulk Update Runs', () => {
  const planTestRunUids: number[] = [];
  const planUids: number[] = [];

  it('updates the due date of multiple Runs', async () => {
    const runUids: number[] = [];
    await tenantDb.transaction(async (trx) => {
      for (let i = 0; i < 2; i++) {
        runUids.push((await dbHelpers.createRun(trx, projectId)).uid);
      }
    });

    const dueDate = faker.date.future().toISOString();

    const res = await request(app)
      .patch(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        uids: runUids,
        dueAt: dueDate,
        action: 'updateDueDate',
      });
    expect(res.statusCode).toBe(200);

    for (const run of res.body) {
      expect(run.customFields.dueAt).toEqual(dueDate);
    }
  });

  it('adds multiple Runs to at least one milestone', async () => {
    const runUids: number[] = [],
      milestoneUids: number[] = [];
    await tenantDb.transaction(async (trx) => {
      for (let i = 0; i < 2; i++) {
        runUids.push((await dbHelpers.createRun(trx, projectId)).uid);
      }

      for (let i = 0; i < 2; i++) {
        milestoneUids.push(
          (await dbHelpers.createMilestone(trx, projectId)).uid,
        );
      }
    });

    const res = await request(app)
      .patch(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        uids: runUids,
        milestoneUids,
        action: 'addMilestones',
      });
    expect(res.statusCode).toBe(200);

    // each run should be related to all selected milestones
    for (const run of res.body) {
      const runMilestones = await TestMilestoneRun.query(tenantDb).where(
        'runUid',
        run.uid,
      );
      for (const runMilestone of runMilestones) {
        expect(milestoneUids).toContain(runMilestone.milestoneUid);
      }
    }
  });

  it('add multiple runs to at least one plan', async () => {
    await tenantDb.transaction(async (trx) => {
      for (let i = 0; i < 2; i++) {
        planTestRunUids.push((await dbHelpers.createRun(trx, projectId)).uid);
      }

      for (let i = 0; i < 2; i++) {
        planUids.push((await dbHelpers.createPlan(trx, projectId)).uid);
      }
    });

    const res = await request(app)
      .patch(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        uids: planTestRunUids,
        planUids,
        action: 'addPlans',
      });
    expect(res.statusCode).toBe(200);

    // each run should be related to all selected milestones
    for (const run of res.body) {
      const runPlans = await TestPlanRun.query(tenantDb).where(
        'runUid',
        run.uid,
      );
      for (const runPlan of runPlans) {
        expect(planUids).toContain(runPlan.planUid);
      }
    }
  });

  it('removes multiple plans from runs', async () => {
    const res = await request(app)
      .patch(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        uids: planTestRunUids,
        planUids,
        action: 'removePlans',
      });

    expect(res.statusCode).toBe(200);
  });

  it('archives multiple runs', async () => {
    const runUids: number[] = [];
    await tenantDb.transaction(async (trx) => {
      for (let i = 0; i < 2; i++) {
        runUids.push((await dbHelpers.createRun(trx, projectId)).uid);
      }
    });

    const res = await request(app)
      .patch(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        uids: runUids,
        action: 'archive',
      });
    expect(res.statusCode).toBe(200);

    // each run should be related to all selected milestones
    for (const run of res.body) {
      expect(run.archivedAt).not.toBeNull();
    }
  });

  it('unarchives multiple runs', async () => {
    const runUids: number[] = [];
    await tenantDb.transaction(async (trx) => {
      for (let i = 0; i < 2; i++) {
        runUids.push((await dbHelpers.createRun(trx, projectId)).uid);
      }
    });

    const res = await request(app)
      .patch(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        uids: runUids,
        action: 'unarchive',
      });
    expect(res.statusCode).toBe(200);

    // each run should be related to all selected milestones
    for (const run of res.body) {
      expect(run.archivedAt).toBeNull();
    }
  });
});

describe('Update Run', () => {
  it('makes a simple update to a test run', async () => {
    const trx = await tenantDb.transaction();
    const run = await dbHelpers.createRun(trx, projectId);
    await trx.commit();
    const dto = {
      name: faker.lorem.words(3),
      description: faker.lorem.paragraph(2),
      status: defaults.testRun?.status,
      priority: defaults.testRun?.priority,
      dueAt: faker.date.past().toISOString(),
      addTagUids: tagUids,
      customFields: {
        caseCount: 42,
      },
    };

    const res = await request(app)
      .patch(`${basePath}/${run.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send(dto);
    expect(res.statusCode).toBe(200);

    const updatedRun = (await TestRun.query(tenantDb).findById(
      run.uid,
    )) as TestRun;
    expect(updatedRun).toBeDefined();
    expect(updatedRun.name).toBe(dto.name);
    expect(updatedRun.description).toBe(dto.description);
    expect(updatedRun.customFields.status).toBe(dto.status);
    expect(updatedRun.customFields.dueAt).toEqual(dto.dueAt);

    const updatedTagUids = (
      await tenantDb('testRunTags').where({
        runUid: updatedRun.uid,
        deletedAt: null,
      })
    )
      .map((t) => t.tagUid)
      .sort();
    expect(updatedTagUids).toEqual(dto.addTagUids.sort());
  });

  it('archives a test run', async () => {
    const trx = await tenantDb.transaction();
    const run = await dbHelpers.createRun(trx, projectId);
    await trx.commit();

    const dto = {
      archive: true,
    };
    const res = await request(app)
      .patch(`${basePath}/${run.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send(dto);
    expect(res.statusCode).toBe(200);

    const updatedRun = (await TestRun.query(tenantDb).findById(
      run.uid,
    )) as TestRun;
    expect(updatedRun.archivedAt).not.toBeNull();
  });

  it('unarchives a test run', async () => {
    const trx = await tenantDb.transaction();
    const run = await dbHelpers.createRun(trx, projectId);
    trx<TestRun>(TestRun.tableName)
      .where({ uid: run.uid })
      .update({ archivedAt: trx.fn.now() });
    await trx.commit();

    const dto = {
      archive: false,
    };
    const res = await request(app)
      .patch(`${basePath}/${run.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send(dto);
    expect(res.statusCode).toBe(200);

    const updatedRun = (await TestRun.query(tenantDb).findById(
      run.uid,
    )) as TestRun;
    expect(updatedRun.archivedAt).toBeNull();
  });

  it('updates the list of related plans to a run', async () => {
    let runUid: number = null!,
      planUids: number[] = [];
    await tenantDb.transaction(async (trx) => {
      runUid = (await dbHelpers.createRun(trx, projectId)).uid;
      for (let i = 0; i < 5; i++) {
        const plan = await dbHelpers.createPlan(trx, projectId);
        planUids.push(plan.uid);
      }
      // attach first 2 plans to run as initial state
      await trx('testPlanRuns').insert([
        { planUid: planUids[0], runUid },
        { planUid: planUids[1], runUid },
      ]);
    });

    const dto = {
      addPlanUids: planUids.slice(2), // add the remaining 3
      removePlanUids: [planUids[0]], // remove only one existing plan
    };

    const expectedPlans = planUids.slice(1); // all except the first that was removed
    const res = await request(app)
      .patch(`${basePath}/${runUid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send(dto);
    expect(res.statusCode).toBe(200);

    const planRuns = await TestPlanRun.query(tenantDb).where({
      runUid,
      deletedAt: null,
    });
    expect(planRuns.length).toBe(4);
    for (const planRun of planRuns) {
      expect(expectedPlans).toContain(planRun.planUid);
    }
  });

  it('updates the list of related milestones to a run', async () => {
    let runUid: number = null!,
      milestoneUids: number[] = [];
    await tenantDb.transaction(async (trx) => {
      runUid = (await dbHelpers.createRun(trx, projectId)).uid;
      for (let i = 0; i < 5; i++) {
        const mstone = await dbHelpers.createMilestone(trx, projectId);
        milestoneUids.push(mstone.uid);
      }
      // attach first 2 milestones to run as initial state
      await trx('testMilestoneRuns').insert(
        milestoneUids
          .slice(0, 2)
          .map((milestoneUid) => ({ runUid, milestoneUid })),
      );
    });
    const dto = {
      addMilestoneUids: milestoneUids.slice(2), // add the remaining 3
      removeMilestoneUids: [milestoneUids[0]], // remove only one existing milestone
    };
    const expectedMilestones = milestoneUids.slice(1); // all except the first that was removed
    const res = await request(app)
      .patch(`${basePath}/${runUid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send(dto);
    expect(res.statusCode).toBe(200);
    const milestoneRuns = await TestMilestoneRun.query(tenantDb).where({
      runUid,
      deletedAt: null,
    });
    expect(milestoneRuns.length).toBe(4);
    for (const milestoneRun of milestoneRuns) {
      expect(expectedMilestones).toContain(milestoneRun.milestoneUid);
    }
  });

  it('updates the configuration set for the run', async () => {
    const configs = ['browser::safari', 'os::windows'];
    const trx = await tenantDb.transaction();
    const run = await dbHelpers.createRun(trx, projectId);
    await trx.commit();

    const res = await request(app)
      .patch(`${basePath}/${run.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({ configs });
    expect(res.statusCode).toBe(200);

    const updatedRun = (await TestRun.query(tenantDb).findById(
      run.uid,
    )) as TestRun;
    expect(updatedRun).toBeDefined();
    expect(updatedRun.customFields.configs).toEqual(configs);
  });

  it('updates the list of related executions to a run', async () => {});
});

describe('List Runs', () => {
  it('retrieves a paginated list of runs', async () => {
    await tenantDb.transaction(async (trx) => {
      const milestone = await dbHelpers.createMilestone(trx, projectId);
      // const plan = await dbHelpers.createPlan(trx, projectId);
      for (let i = 0; i < 5; i++) {
        const run = await dbHelpers.createRun(trx, projectId);
        await TestMilestoneRun.query(trx).insert({
          runUid: run.uid,
          milestoneUid: milestone.uid,
        });
      }
    });

    const res = await request(app)
      .get(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session);
    expect(res.statusCode).toBe(200);
  });

  it('filters by planUid', async () => {
    let plan;
    let runUids: number[] = [];
    await tenantDb.transaction(async (trx) => {
      plan = await dbHelpers.createPlan(trx, projectId);
      for (let i = 0; i < 5; i++) {
        const run = await dbHelpers.createRun(trx, projectId);
        await TestPlanRun.query(trx).insert({
          runUid: run.uid,
          planUid: plan.uid,
        });
        runUids.push(run.uid);
      }
    });

    const res = await request(app)
      .get(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .query({ planUid: plan.uid });
    expect(res.statusCode).toBe(200);

    const body: PaginatedResult<TestRun> = res.body;
    expect(body.count).toBe(5);

    for (const item of body.items) {
      expect(runUids).toContain(item.uid);
    }
  });

  it('filters by run name', async () => {
    let run;
    await tenantDb.transaction(async (trx) => {
      run = await dbHelpers.createRun(trx, projectId);
    });

    const res = await request(app)
      .get(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .query({ q: run.name });
    expect(res.statusCode).toBe(200);

    const body: PaginatedResult<TestRun> = res.body;

    const regex = new RegExp(run.name, 'ig');

    for (const item of body.items) {
      expect(item.name).toMatch(regex);
    }
  });
});

describe('Get Run', () => {
  it('retrieves the case count for a run', async () => {
    const trx = await tenantDb.transaction();
    const run = await dbHelpers.createRun(trx, projectId);

    await TestRun.query(trx)
      .where('uid', run.uid)
      .patch({
        customFields: {
          ...run.customFields,
          caseCount: 42,
        },
      });

    await trx.commit();

    const res = await request(app)
      .get(`${basePath}/${run.uid}/cases/count`)
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('count');
    expect(res.body.count).toBe(42);
  });

  it('retrieves a run details by it ID', async () => {
    const trx = await tenantDb.transaction();
    const run = await dbHelpers.createRun(trx, projectId);
    let milestoneRuns: any[] = [],
      planRuns: any[] = [];
    for (let i = 0; i < 5; i++) {
      const mstone = await dbHelpers.createMilestone(trx, projectId);
      const plan = await dbHelpers.createPlan(trx, projectId);
      milestoneRuns.push({ milestoneUid: mstone.uid, runUid: run.uid });
      planRuns.push({ planUid: plan.uid, runUid: run.uid });
    }
    await trx('testMilestoneRuns').insert(milestoneRuns);
    await trx('testPlanRuns').insert(planRuns);
    await trx.commit();

    const res = await request(app)
      .get(`${basePath}/${run.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session);
    expect(res.statusCode).toBe(200);

    const retrievedRun = res.body;
    expect(retrievedRun).toBeDefined();
    expect(retrievedRun.name).toBe(run.name);
    expect(retrievedRun).toHaveProperty('description');
    expect(retrievedRun).toHaveProperty('createdAt');
    expect(retrievedRun).toHaveProperty('updatedAt');
    expect(retrievedRun).toHaveProperty('archivedAt');
    expect(retrievedRun).toHaveProperty('customFields');
    expect(retrievedRun).toHaveProperty('configs');
    expect(retrievedRun).toHaveProperty('testMilestones');
    expect(retrievedRun.testMilestones.length).toBe(milestoneRuns.length);
    for (const m of retrievedRun.testMilestones) {
      expect(m).toHaveProperty('name');
      expect(m).toHaveProperty('uid');
    }
    expect(retrievedRun).toHaveProperty('testPlans');
    expect(retrievedRun.testPlans.length).toBe(planRuns.length);
    for (const p of retrievedRun.testPlans) {
      expect(p).toHaveProperty('name');
      expect(p).toHaveProperty('uid');
    }
  });

  it('fails on providing invalid id', async () => {
    const trx = await tenantDb.transaction();
    const run = await dbHelpers.createRun(trx, projectId);
    await trx.rollback();

    const res = await request(app)
      .get(`${basePath}/${run.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session);
    expect(res.statusCode).toBe(404);
  });

  it('fails if run has been deleted', async () => {
    const trx = await tenantDb.transaction();
    const run = await dbHelpers.createRun(trx, projectId);
    await trx('tags').where('uid', run.uid).update('deletedAt', trx.fn.now()); // mark as deleted
    await trx.commit();

    const res = await request(app)
      .get(`${basePath}/${run.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session);
    expect(res.statusCode).toBe(404);
  });
});

describe('Delete Runs', () => {
  it('deletes the list of test runs provided', async () => {
    const trx = await tenantDb.transaction();
    const runUids: number[] = [];
    for (let i = 0; i < 5; i++) {
      const run = await dbHelpers.createRun(trx, projectId);
      runUids.push(run.uid);
    }

    const res = await request(app)
      .get(basePath)
      .set('Accept', 'application/json')
      .query({ runUids: runUids.join(',') })
      .set('Cookie', session);
    expect(res.statusCode).toBe(200);

    const runs = await TestRun.findByIds(tenantDb, runUids);
    for (const run of runs) expect(run.deletedAt).not.toBeNull();
  });
});

describe('List Run Executions', () => {
  it('retrieves a paginated list of test run executions', async () => {
    const trx = await tenantDb.transaction();
    const testCase = await dbHelpers.createCase(trx, projectId);
    const run = await dbHelpers.createRun(trx, projectId);
    const execs: any[] = [];
    for (let i = 0; i < 10; i++) {
      execs.push({
        status: faker.number.int(100),
        testCaseRef: testCase.testCaseRef,
        testRunUid: run.uid,
        priority: faker.number.int(100),
        testCaseUid: testCase.testCaseUid,
        projectUid: projectId,
        assignedTo: user.uid,
      });
    }
    await trx('testExecutions').insert(execs);
    await trx.commit();

    const res = await request(app)
      .get(`${basePath}/${run.uid}/cases`)
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.body.count).toBe(10);

    for (const item of res.body.items) expect(item.testRunUid).toBe(run.uid);
  });
});

describe('Update Test Run Cases', () => {
  it('adds more test cases to a test run', async () => {
    const trx = await tenantDb.transaction();
    const testCase = await dbHelpers.createCase(trx, projectId);
    const run = await dbHelpers.createRun(trx, projectId);
    await trx.commit();

    const res = await request(app)
      .patch(`${basePath}/${run.uid}/cases`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({ addCaseUids: [testCase.uid] });

    expect(res.body.added).toBe(1);

    const execs = await tenantDb('testExecutions')
      .where('testRunUid', run.uid)
      .select('*');
    expect(execs.length).toBe(1);
    expect(execs[0].testCaseRef).toBe(testCase.testCaseRef);
  });

  it('removes executions from a test run', async () => {
    const trx = await tenantDb.transaction();

    const run = await dbHelpers.createRun(trx, projectId);

    let execs: any[] = [];

    for (let i = 0; i < 10; i++) {
      const testCase = await dbHelpers.createCase(trx, projectId);
      execs.push({
        status: faker.number.int(100),
        testCaseRef: testCase.testCaseRef,
        testRunUid: run.uid,
        priority: faker.number.int(100),
        testCaseUid: testCase.testCaseUid,
        projectUid: projectId,
        assignedTo: user.uid,
      });
    }
    execs = await trx('testExecutions').insert(execs).returning('*');
    await trx.commit();

    const removeExecUids = faker.helpers
      .arrayElements(execs, 5)
      .map((e) => e.uid);

    const res = await request(app)
      .patch(`${basePath}/${run.uid}/cases`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({ removeExecUids });

    expect(res.body.removed).toBe(removeExecUids.length);

    const updatedExecs = await tenantDb('testExecutions')
      .where('testRunUid', run.uid)
      .select('*');
    expect(updatedExecs.length).toBe(execs.length - removeExecUids.length);
  });

  it('should return correct runs count for a project', async () => {
    const countRes = await request(app)
      .get(`${basePath}/count`)
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(countRes.statusCode).toBe(200);
    expect(countRes.body.count).toBeGreaterThan(1);
  });
});

describe('Count Runs', () => {
  it('returns the number of active and archived runs', async () => {
    await tenantDb.transaction(async (trx) => {
      const archive = [];
      for (let i = 0; i < 5; i++) {
        const run = await dbHelpers.createRun(trx, projectId);
        if (i % 2 === 1) archive.push(run.uid);
      }

      await trx('tags')
        .whereIn('uid', archive)
        .update({ archivedAt: new Date() });
    });

    const res = await request(app)
      .get(
        `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${projectKey}/entities/count`,
      )
      .query({ entityType: 'run' })
      .set('Cookie', session);
    expect(res.status).toBe(200);
    expect(res.body).toHaveProperty('active');
    expect(res.body).toHaveProperty('archived');
    expect(res.body.archived).toBeGreaterThanOrEqual(2);
    expect(res.body.active).toBeGreaterThanOrEqual(3);
  });
});
