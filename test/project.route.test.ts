import * as data from './data/index';

import { Application } from 'express';
import { buildApp } from '../src/app';
import caseRoutes from '../src/routes/internal/case';
import { closeQueues } from '../src/lib/queue';
import dayjs from 'dayjs';
import folderRoutes from '../src/routes/internal/folder';
import { generateProjectKey } from './data/index';
import projectRoutes from '../src/routes/internal/project';
import defectRoutes from '../src/routes/internal/defect';
import request from 'supertest';
import runRoutes from '../src/routes/internal/run';
import { setupDB } from '../src/config/db';
import { setupUserTenant } from './utils/tenant';
import { tenantManager } from '../src/lib/tenants';

let app: Application;
let user, password: string, handle: string, session, userId, dbServer;

const db = setupDB();

beforeAll(async () => {
  app = buildApp(db, [caseRoutes, folderRoutes, runRoutes, projectRoutes, defectRoutes]);

  const tenant = await setupUserTenant(db, app);
  user = tenant.user;
  password = tenant.password;
  handle = tenant.handle;
  dbServer = tenant.dbServer;

  const res = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signIn`)
    .set('Accept', 'application/json')
    .send({ email: user.email, password });

  session = res.headers['set-cookie'];
  userId = res.body.user.uid;
});

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

describe('Project', () => {
  let testProject;

  it('should not pass create test projects when empty body', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({});
    expect(res.statusCode).toBe(400);
  });

  it('should be able to create a new test projects', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        externalId: '123123',
        customFields: { version: '2.0', platform: 'app' },
        source: 'testfiesta',
        name: 'projecttest',
        key: generateProjectKey(),
      });
    expect(res.statusCode).toBe(200);
    testProject = res.body;
  });

  it('should be able to get list test projects', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
  });

  it('should be able to get project members', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${testProject.key}/users`)
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    expect(Array.isArray(res.body.users)).toBe(true);
  });

  it('should be able to get search test projects', async () => {
    const res = await request(app)
      .get(
        `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/search?query=123123`,
      )
      .set('Accept', 'application/json')
      .set('Cookie', session);
    expect(res.statusCode).toBe(200);
  });

  it('should be able to get a test project by projectId', async () => {
    const res = await request(app)
      .get(
        `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${testProject.key}`,
      )
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    for (const key of Object.keys(testProject)) {
      expect(res.body[key]).toEqual(testProject[key]);
    }
  });

  it('should be able to delete test projects', async () => {
    let res = await request(app)
      .delete(
        `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${testProject.key}`,
      )
      .set('Accept', 'application/json')
      .set('Cookie', session);
    expect(res.statusCode).toBe(200);

    res = await request(app)
      .get(
        `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${testProject.key}`,
      )
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    expect(res.body).toBe(null);
  });

  it('should be able to archive a test project', async () => {
    let res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        externalId: '123123',
        customFields: { version: '2.0', platform: 'app' },
        source: 'testfiesta',
        name: 'projecttestarchive',
        key: generateProjectKey(),
      });

    const testProjectId = res.body.key;

    expect(res.statusCode).toBe(200);

    res = await request(app)
      .patch(
        `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${testProjectId}`,
      )
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        archived: true,
      });

    expect(res.statusCode).toBe(200);

    res = await request(app)
      .get(
        `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${testProjectId}`,
      )
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    expect(res.body.archivedAt).not.toBe(null);
  });

  it('should not be able to update an archived test project', async () => {
    let res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        externalId: '123123',
        customFields: { version: '2.0', platform: 'app' },
        source: 'testfiesta',
        name: 'projecttestarchive2',
        key: generateProjectKey(),
      });

    const testProjectId = res.body.key;

    expect(res.statusCode).toBe(200);

    res = await request(app)
      .patch(
        `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${testProjectId}`,
      )
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        archived: true,
      });

    expect(res.statusCode).toBe(200);

    res = await request(app)
      .patch(
        `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${testProjectId}`,
      )
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        name: 'projecttestarchive2 updated',
        key: generateProjectKey(),
      });

    expect(res.statusCode).toBe(422);
  });

  it('should be able to unarchive a test project', async () => {
    let res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        externalId: '123123',
        customFields: { version: '2.0', platform: 'app' },
        source: 'testfiesta',
        name: 'projecttestunarchive',
        key: generateProjectKey(),
      });

    const testProjectId = res.body.key;

    expect(res.statusCode).toBe(200);

    res = await request(app)
      .patch(
        `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${testProjectId}/unarchive`,
      )
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    expect(res.body.message).not.toBe(null);
  });

  it('should include meta field when includeCount is enabled on query param', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
      .query({
        includeCount: true,
      })
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    expect(res.body.meta).not.toBe(null);
    expect(res.body.meta.archivedCount).not.toBe(null);
    expect(res.body.meta.activeCount).not.toBe(null);
  });

  it('should be able to get list test projects with creationDate filter', async () => {
    const tenantDB = setupDB(userId, dbServer);

    const projects = await tenantDB('projects')
      .select('uid')
      .whereNull('deletedAt')
      .limit(3);
    const projectIds = projects.map((project) => project.uid);

    await tenantDB('projects')
      .update({
        createdAt: dayjs().add(4, 'days').toISOString(),
      })
      .whereIn('uid', projectIds)
      .whereNull('deletedAt');

    const afterFourDays = dayjs().add(4, 'days').format('YYYY-MM-DD');

    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
      .query({
        includeCount: true,
        creationStartDate: afterFourDays,
        creationEndDate: afterFourDays,
      })
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    expect(res.body.items.length).toEqual(3);
  });

  it('should return correct defects count for a project', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
      .set('Cookie', session)
      .send(data.newProject());

    const newProject = res.body;
    const tenantDB = setupDB(userId, dbServer);

    await tenantDB('defects').insert([
      { projectUids: [newProject.uid], name: 'Test Defect 1', deletedAt: null },
      { projectUids: [newProject.uid], name: 'Test Defect 2', deletedAt: null },
      { projectUids: [newProject.uid], name: 'Test Defect 3', deletedAt: null },
      { projectUids: [newProject.uid], name: 'Deleted Defect', deletedAt: new Date() }
    ]);

    const countRes = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${newProject.key}/defects/count`)
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(countRes.statusCode).toBe(200);
    expect(countRes.body.count).toBe(3);
  });
});
