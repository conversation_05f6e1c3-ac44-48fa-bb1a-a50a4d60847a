import { newOrg, setupOrgTenant, setupUserTenant } from './utils/tenant';

import { Application } from 'express';
import { Knex } from 'knex';
import crypto from "crypto";
import { buildApp } from '../src/app';
import { closeQueues } from '../src/lib/queue';
import errorConstants from '../src/constants/errors';
import { faker } from '@faker-js/faker';
import { newSSOConfig } from './data';
import orgRoutes from '../src/routes/internal/org';
import request from 'supertest';
import { setupDB } from '../src/config/db';
import ssoConfigRoutes from '../src/routes/internal/ssoConfig';
import { tenantManager } from '../src/lib/tenants';
import { generateKeyPair } from './utils/encrypt';

let app: Application,
  session: any,
  orgTenant: any,
  userTenant: any,
  userSession: any;

async function createSSOConfig(db: Knex, orgUid: string) {
  const [config, symmetricKeyData] = await newSSOConfig(publicKeyObj);
  const uid = crypto.randomUUID();
  await db('ssoConfigs')
    .insert({
      uid,
      orgUid,
      config,
    })
    .returning('*');
  await db('symmetricKeys')
    .insert({
      uid: crypto.randomUUID(),
      entityId: uid,
      ...symmetricKeyData,
    });
  return uid;
}

const db = setupDB();
let publicKeyObj: any;
let service = 'auth';

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

beforeAll(async () => {
  publicKeyObj = await generateKeyPair(db, service)
  app = buildApp(db, [orgRoutes, ssoConfigRoutes]);

  orgTenant = await setupOrgTenant(db, app);
  const email = orgTenant.admin.email;
  const password = orgTenant.password;

  const signin = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signin`)
    .send({ email, password });
  session = signin.headers['set-cookie'];

  userTenant = await setupUserTenant(db, app);
  const userSignin = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signin`)
    .send({ email: userTenant.user.email, password: userTenant.password });
  userSession = userSignin.headers['set-cookie'];
});

afterEach(async () => {
  await db('ssoConfigs').delete();
});

describe('Create SSO Config', () => {
  it('should be able to create new SSO Config', async () => {
    const [config, symmetricKeyData] = await newSSOConfig(publicKeyObj);
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/${orgTenant.handle}/sso/config`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({ config, symmetricKeyData });

    expect(res.statusCode).toBe(200);
    expect(res.body).not.toHaveProperty('config');

    const ssoConfig = await db('ssoConfigs')
      .where({ uid: res.body.uid })
      .first();

    expect(ssoConfig).toBeDefined();
    expect(ssoConfig.uid).toBe(res.body.uid);
  });

  it('should throw error when handle is not an organization', async () => {
    const [config, symmetricKeyData] = await newSSOConfig(publicKeyObj);
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/${userTenant.handle}/sso/config`)
      .set('Accept', 'application/json')
      .set('Cookie', userSession)
      .send({ config, symmetricKeyData });

    expect(res.statusCode).toBe(403);
  });

  it('throws error when config is invalid', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/${orgTenant.handle}/sso/config`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        config: {
          oidc: {
            url: faker.internet.url(),
          },
        },
      });
    expect(res.statusCode).toBe(400);
  });

  it('throws error when config does not have valid protocol', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/${orgTenant.handle}/sso/config`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        config: {
          invalid: {
            clientId: faker.string.uuid(),
            clientSecret: faker.string.uuid(),
            url: faker.internet.url(),
            whitelistDomains: [faker.internet.domainName()],
            allowOnlyInvitedAccounts: true,
            defaultRole: 'member',
            groupMappings: [],
          },
        },
      });
    expect(res.statusCode).toBe(400);
  });
});

describe('Delete SSO Config', () => {
  it('should be able to delete SSO Config', async () => {
    const uid = await createSSOConfig(db, orgTenant.org.uid);
    const res = await request(app)
      .delete(
        `${process.env.API_INTERNAL_ROUTE}/${orgTenant.handle}/sso/config/${uid}`,
      )
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    expect(res.body).not.toHaveProperty('config');

    const ssoConfig = await db('ssoConfigs')
      .whereNull('deletedAt')
      .where({ uid: res.body.uid })
      .first();

    expect(ssoConfig).toBeFalsy();
  });

  it('should throw error when handle is not an organization', async () => {
    const org = await db('orgs')
      .insert({ name: newOrg().name, createdBy: userTenant.user.uid })
      .returning('uid');
    const uid = await createSSOConfig(db, org[0].uid);

    const res = await request(app)
      .delete(
        `${process.env.API_INTERNAL_ROUTE}/${userTenant.handle}/sso/config/${uid}`,
      )
      .set('Accept', 'application/json')
      .set('Cookie', userSession);

    expect(res.statusCode).toBe(403);
    expect(res.body.message).toBe(errorConstants.SSO_CONFIG_NOT_ALLOWED);
  });
});