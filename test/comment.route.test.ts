import request, { Response } from 'supertest';

import { Application } from 'express';
import { buildApp } from '../src/app';
import { closeQueues } from '../src/lib/queue';
import commentRoutes from '../src/routes/internal/comment';
import { expect } from 'chai';
import { faker } from '@faker-js/faker';
import { setupDB } from '../src/config/db';
import { setupUserTenant } from './utils/tenant';
import { tenantManager } from '../src/lib/tenants';

let app: Application;
let user, password: string, handle: string;

const db = setupDB();

beforeAll(async () => {
  app = buildApp(db, [commentRoutes]);

  const tenant = await setupUserTenant(db, app);
  password = tenant.password;
  user = tenant.user;
  handle = tenant.handle;
});

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

describe('Comments', () => {
  const caseId = faker.number.int();
  let signin: Response;
  let comment;

  it('should sign in a user', async () => {
    signin = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/signIn`)
      .set('Accept', 'application/json')
      .send({ email: user.email, password });
    expect(signin.statusCode).to.be.oneOf([200]);
  });

  it('should create a new comment', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/comments`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send({
        entityType: 'case',
        entityUid: caseId,
        body: 'This is a test comment',
      });
    expect(res.statusCode).to.equal(200);
    comment = res.body;
  });

  it('should get comments for a specific entity', async () => {
    const res = await request(app)
      .get(
        `${process.env.API_INTERNAL_ROUTE}/${handle}/comments/case/${caseId}`,
      )
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie']);
    expect(res.statusCode).to.equal(200);
    expect(res.body).to.be.an('array');
  });

  it('should update a comment', async () => {
    const res = await request(app)
      .put(
        `${process.env.API_INTERNAL_ROUTE}/${handle}/comments/${comment.uid}`,
      )
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send({
        body: 'This is updated a test comment',
      });
    expect(res.statusCode).to.equal(200);
  });

  it('should delete a comment', async () => {
    const res = await request(app)
      .delete(
        `${process.env.API_INTERNAL_ROUTE}/${handle}/comments/${comment.uid}`,
      )
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie']);
    expect(res.statusCode).to.equal(200);
  });
});
