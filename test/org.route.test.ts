import './__mocks__/payments';
import * as data from './data/index';

import { FgaService } from '@ss-libs/ss-component-auth';
import { newOrg, newUser, setupOrgTenant } from './utils/tenant';
import request, { Response } from 'supertest';

import { Application } from 'express';
import { BLACKLISTED_TOKENS } from '../src/constants/blacklist';
import { Invite } from '../src/models/invite';
import { Knex } from 'knex';
import { OpenFgaClient } from '@openfga/sdk';
import { Tag } from '../src/models/tag';
import { buildApp } from '../src/app';
import env from '../src/config/env';
/* eslint-disable max-len */
import { expect } from 'chai';
import { faker } from '@faker-js/faker';
import orgRoutes from '../src/routes/internal/org';
import roleRoutes from '../src/routes/internal/role';

import { setupDB } from '../src/config/db';
import { setupOpenfga } from '../src/config/openfga';
import userRoutes from '../src/routes/internal/user';
import { Role } from '../src/models/role';
import projectRoutes from '../src/routes/internal/project';
import { closeQueues } from '../src/lib/queue';
import { setupAccount } from '../src/temporal/activities/account';
import { tenantManager } from '../src/lib/tenants';
import InviteRoutes from '../src/routes/internal/invite';

let app: Application;
let admin, password: string;
let tenantDB: Knex, tenantFGA: FgaService;
let role, org, dbServer;
const db = setupDB();

const testOrg = newOrg();

beforeAll(async () => {
  app = buildApp(db, [orgRoutes, userRoutes, roleRoutes, projectRoutes, InviteRoutes]);

  const tenant = await setupOrgTenant(db, app);
  admin = tenant.admin;
  password = tenant.password;
  org = tenant.org;
  dbServer = tenant.dbServer;
  org.handle = tenant.handle;
  tenantDB = setupDB(org.uid, tenant.dbServer);
  const fga = await setupOpenfga({ storeName: org.uid });
  tenantFGA = new FgaService(fga);
  role = await tenantDB('roles').first('*');

  
});

afterAll(async () => {
  await db.destroy();
  await tenantDB.destroy()
  await closeQueues();
  await tenantManager.shutdown();
});

describe('Org', () => {
  let signin: Response, secondUser;

  // sign in user
  it('should sign in a user', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/signIn`)
      .set('Accept', 'application/json')
      .send({ email: admin.email, password });
    expect(res.statusCode).to.be.oneOf([200]);
    signin = res;
  });

  // add new org
  it('should be able to create a new organization', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/orgs`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send(testOrg);
    expect(res.statusCode).to.be.oneOf([200]);
  });

  it('ensures user db and openfga store is created', async () => {
    await setupAccount(
      { ownerType: 'org', ownerUid: org.uid, dbServerUid: dbServer?.uid },
      db,
    );

    const tenant = await db('tenants')
      .where({ tenantType: 'org', tenantUid: org.uid })
      .first('*');

    expect(tenant).not.null;
    expect(!!tenant.openfgaAuthModelId).eq(true);
    expect(!!tenant.openfgaStoreId).eq(true);
    let failed = false;
    try {
      tenantDB = setupDB(tenant.tenantUid, dbServer);
    } catch (err) {
      failed = true;
    }

    expect(failed).eq(false);

    expect(tenantDB).not.undefined;
    expect(tenantDB).not.null;

    const fgaClient = new OpenFgaClient({
      apiHost: env.OPENFGA_DATASTORE_HOST,
      apiScheme: env.OPENFGA_DATASTORE_API_SCHEME,
      storeId: tenant.openfgaStoreId,
      authorizationModelId: tenant.openfgaAuthModelId,
    });
    const store = await fgaClient.getStore();
    expect(store.name).eq(tenant.tenantUid);

    role = await tenantDB('roles').first('*');
  });

  it('should fail if org handle is already taken', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/orgs`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send(testOrg);

    expect(res.statusCode).eq(409);
  });

  it('should fails if org handle is a reserved keyword', async () => {
    const dto = newOrg();
    dto.handle = faker.helpers.arrayElement(BLACKLISTED_TOKENS);

    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/orgs`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send(dto);
    expect(res.statusCode).eq(400);
  });

  // get all members of organization
  it('should get all members of the an organization', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/orgs/${org.handle}/users`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie']);
    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body.users.length).eq(1);
  });

  // search users
  it('should search using email and name and return list of users if available', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/users/search?`)
      .query({ query: admin.email })
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie']);
    expect(res.statusCode).to.be.oneOf([200]);
  });

  // update organization
  it('update organization profile', async () => {
    const res = await request(app)
      .patch(`${process.env.API_INTERNAL_ROUTE}/orgs/${org.handle}`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send({
        avatarUrl: faker.image.url(),
        preferences: { foo: 'bar' },
      });
    expect(res.statusCode).to.be.oneOf([200]);
  });

  // update organization throw error
  it("Org Update: throw 422 error if request dosen't have any body value", async () => {
    const res = await request(app)
      .patch(`${process.env.API_INTERNAL_ROUTE}/orgs/${org.handle}`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send();
    expect(res.statusCode).to.be.oneOf([422]);
  });
  /**
   * ===================
   * Organization Invite
   * ===================
   **/

  it('should fail to send invite if user is not found', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/${org.handle}/invite`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send({ roleUid: role.uid, userUid: faker.string.uuid()});
    expect(res.statusCode).to.be.oneOf([422]);
  });

  it('should fail to invite an existing member', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/${org.handle}/invite`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send({ roleUid: role.uid, userUid: admin.uid, orgScope: true });
    expect(res.statusCode).to.be.oneOf([422]);
  });

  it('invite existing user to org', async () => {
    const signup = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/signup`)
      .send(newUser());

    const tags: Tag[] = await Tag.query(tenantDB)
      .where({ systemType: 'tag' })
      .limit(3);
    const tagIds = tags.map((t) => t.uid).sort();

    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/${org.handle}/invite`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send({
        roleUid: role.uid,
        userUid: signup.body.user.uid,
        tagUids: tagIds,
        orgScope: true,
      });
    expect(res.statusCode).to.be.oneOf([200]);

    const invite: Invite = await Invite.query(tenantDB)
      .where({
        email: signup.body.user.email,
      })
      .first();
    expect(invite.tagUids.sort()).deep.eq(tagIds);
  });

  it('invite user to project in an org', async () => {
    const signup = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/signup`)
      .send(newUser());

    const tags: Tag[] = await Tag.query(tenantDB)
      .where({ systemType: 'tag' })
      .limit(3);
    const tagIds = tags.map((t) => t.uid).sort();

    const createProject = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/${org.handle}/projects`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send(data.newProject());

    const project = createProject.body;

    const projectRole = await tenantDB('roles').insert([{
      name: 'role 1',
      slug: 'project-role',
      description: 'project role',
    },{
      name: 'role 2',
      slug: 'org-role',
      description: 'org role',
    }]).returning('*')
    
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/${org.handle}/invite`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send({
        userEmail: signup.body.user.email,
        tagUids: tagIds,
        roleUid: projectRole[0].uid,
        roles: [{
          projectUid: project.uid,
          roleUid: projectRole[1].uid,
        }],
      });

    expect(res.statusCode).to.be.oneOf([200]);
      
    const invite = await Invite.query(tenantDB)
    .where({
      email: signup.body.user.email,
      }).innerJoin('projectInvites', 'projectInvites.inviteUid', 'invites.uid')
      .select('projectInvites.projectUid')

      const inviteProjects = (invite as any).map(i => i.projectUid);
      
    expect(inviteProjects).deep.eq([project.uid]);
  });

  it('should invite non existing user to signup', async () => {
    const email = faker.internet.email();
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/${org.handle}/invite`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send({ roleUid: role.uid, userEmail: email, orgScope: true });
    expect(res.statusCode).eq(200);
  });

  const url = (path: string) => `${process.env.API_INTERNAL_ROUTE}${path}`;

  it('should decline current invite', async () => {
    const user = newUser();
    const signup = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/signup`)
      .set('Accept', 'application/json')
      .send(user);
    expect(signup.statusCode).eq(200);
    const [invite] = await tenantDB('invites').insert(
      {
        inviterUid: admin.uid,
        roleUid: role.uid,
        email: user.email,
        token: faker.string.uuid(),
        expiresAt: faker.date.future(),
        acceptedAt: null,
        accepted: null,
      },
      '*',
    );

    const res = await request(app)
      .post(
        `${process.env.API_INTERNAL_ROUTE}/orgs/${org.handle}/invite/${invite.token}/decline`,
      )
      .set('Cookie', signup.header['set-cookie'])
      .send({ token: invite.token });
    expect(res.statusCode).eq(200);

    const updatedInvite = await tenantDB('invites')
      .where({ uid: invite.uid })
      .first('*');
    expect(updatedInvite.accepted).eq(false);
  });

  it('should accept current invite', async () => {
    const user = newUser();
    const signup = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/signup`)
      .set('Accept', 'application/json')
      .send(user);
    expect(signup.statusCode).eq(200);
    secondUser = signup.body.user;
    const [invite] = await tenantDB('invites').insert(
      {
        inviterUid: admin.uid,
        roleUid: role.uid,
        email: user.email,
        token: faker.string.uuid(),
        expiresAt: faker.date.future(),
        acceptedAt: null,
        accepted: null,
      },
      '*',
    );

    const res = await request(app)
      .post(
        `${process.env.API_INTERNAL_ROUTE}/orgs/${org.handle}/invite/${invite.token}/accept`,
      )
      .set('Cookie', signup.header['set-cookie'])
      .send({ token: invite.token });
    expect(res.statusCode).eq(200);

    const updatedInvite = await tenantDB('invites')
      .where({ uid: invite.uid })
      .first('*');
    expect(updatedInvite.accepted).eq(true);
  });

  /**
   * =====================
   * Get Org Members count
   * =====================
   */
  it('should return total number of pending and onboarded org members', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/orgs/${org.handle}/users/count`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie']);
    expect(res.body).haveOwnProperty('members');
    expect(res.body).haveOwnProperty('pending');
  });

  // get all members of organization
  it('returns an updated list of org memebers', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/orgs/${org.handle}/users`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie']);
    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body.users.length).eq(2);
  });

  /**
   * =====================
   * list pending invites
   * =====================
   */
  it('should return a list of pending invites for an org', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${org.handle}/invites`)
      .query({ status: 'pending' })
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie']);
    expect(res.statusCode).eq(200);
  });

  it('should return a list of pending invites for an org', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${org.handle}/invites`)
      .query({ status: 'expired' })
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie']);
    expect(res.statusCode).eq(200);
  });

  // recover invite
  it('should  throw 404 for incorrect invite', async () => {
    const res = await request(app)
      .post(
        `${process.env.API_INTERNAL_ROUTE}/orgs/${org.handle}/invite/incorrect-token/accept`,
      )
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send({ token: `incorrect-token` });
    expect(res.statusCode).to.be.oneOf([404]);
  });

  // failed  invite link
  it('should throw 422 for expired link current invite', async () => {
    const signup = await request(app).post(url('/signup')).send(newUser());
    const { user } = signup.body;

    const token = faker.string.uuid();
    const role = await tenantDB('roles').first('*');

    // create the invite
    await tenantDB('invites').insert({
      inviterUid: admin.uid,
      email: user.email,
      token,
      expiresAt: faker.date.past(),
      accepted: null,
      acceptedAt: null,
      roleUid: role.uid,
    });

    const res = await request(app)
      .post(
        `${process.env.API_INTERNAL_ROUTE}/orgs/${org.handle}/invite/${token}/decline`,
      )
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie']);
    expect(res.statusCode).to.be.oneOf([422]);
  });

  // delete an unavalilabe
  it('should throw 404 for trying to delete unavailable org', async () => {
    const res = await request(app)
      .delete(`${process.env.API_INTERNAL_ROUTE}/handle/${org.handle}5345`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send();
    expect(res.statusCode).to.be.eq(404);
  });

  // remove member of an organization
  it('should remove member of an organization', async () => {
    const res = await request(app)
      .delete(
        `${process.env.API_INTERNAL_ROUTE}/orgs/${org.handle}/users/${secondUser.uid}`,
      )
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send();
    expect(res.statusCode).to.be.eq(200);
  });

  // remove member of an organization
  it('should throw an error for trying to remove member that doesnt exist', async () => {
    const res = await request(app)
      .delete(
        `${process.env.API_INTERNAL_ROUTE}/orgs/${org.handle}/users/${signin.body.user.uid}fdsafds`,
      )
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send();
    expect(res.statusCode).to.be.eq(400);
  });

  it('returns an updated list of org memebers', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/orgs/${org.handle}/users`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie']);
    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body.users.length).eq(1);
  });

  // it("bulk updates user's roles and/or tags", async () => { // Endpoint useless
  //   const creates: FGARawWrite[] = [];
  //   const roles = await tenantDB('roles').whereNot('slug', 'owner').select('*');

  //   const [initialRole, newRole] = faker.helpers.arrayElements(roles, 2);

  //   const userUids: string[] = [];
  //   for (let i = 0; i < 3; i++) {
  //     const signup = await request(app).post(url('/signup')).send(newUser());
  //     const { user } = signup.body;

  //     creates.push(
  //       {
  //         objectId: org.uid,
  //         objectType: 'org',
  //         relation: 'member',
  //         subjectId: user.uid,
  //         subjectType: 'user',
  //       },
  //       {
  //         subjectType: 'user',
  //         subjectId: user.uid,
  //         objectId: initialRole.uid,
  //         objectType: 'role',
  //         relation: 'assignee',
  //         condition: {
  //           name: 'default_role',
  //           context: {
  //             excluded: []
  //           }
  //         }
  //       },
  //     );
  //     userUids.push(user.uid);
  //   }
  //   await tenantFGA.create(...creates);
  //   await Membership.query(db).insert(
  //     userUids.map((uid) => ({
  //       userUid: uid,
  //       accountUid: org.uid,
  //       accountType: <const>'org',
  //     })),
  //   );

  //   const res = await request(app)
  //     .patch(`${process.env.API_INTERNAL_ROUTE}/orgs/${org.handle}/users`)
  //     .set('Accept', 'application/json')
  //     .set('Cookie', signin.header['set-cookie'])
  //     .send({
  //       roleUid: newRole.uid,
  //       userUids,
  //       tagUids: [1, 2, 3],
  //     });

  //   expect(res.statusCode).to.be.eq(200);
      
  //   for (const userId of userUids) {
  //     const memberTags = (
  //       await tenantDB('memberTags').where('userUid', userId).select('*')
  //     ).map((a) => a.tagUid);
  //     expect(memberTags.sort()).deep.eq([1, 2, 3]);

  //     const hasPreviousRole = await tenantFGA.check(
  //       `user:${userId}`,
  //       `role:${initialRole.uid}`,
  //       'assignee',
  //       {current_project: ''}
  //     );
  //     const hasNewRole = await tenantFGA.check(
  //       `user:${userId}`,
  //       `role:${newRole.uid}`,
  //       'assignee',
  //       {current_project: ''}
  //     );

  //     expect(hasPreviousRole).eq(false);
  //     expect(hasNewRole).eq(true);
  //   }
  // });

  it('should resend invites', async () => {
    const user = newUser();
    const signup = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/signup`)
      .set('Accept', 'application/json')
      .send(user);
    expect(signup.statusCode).eq(200);
    secondUser = signup.body.user;
    const [invite] = await tenantDB('invites').insert(
      {
        inviterUid: admin.uid,
        roleUid: role.uid,
        email: user.email,
        token: faker.string.uuid(),
        expiresAt: faker.date.future(),
        acceptedAt: null,
        accepted: null,
      },
      '*',
    );
    const res = await request(app)
      .post(
        `${process.env.API_INTERNAL_ROUTE}/${org.handle}/invite/resend`,
      )
      .set('Cookie', signin.header['set-cookie'])
      .send({ emails: [invite.email] });
    expect(res.statusCode).eq(200);
  });

  it('bulk updates invites', async () => {
    const user = newUser();
    const signup = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/signup`)
      .set('Accept', 'application/json')
      .send(user);
    expect(signup.statusCode).eq(200);
    secondUser = signup.body.user;
    const [invite] = await tenantDB('invites').insert(
      {
        inviterUid: admin.uid,
        roleUid: role.uid,
        email: user.email,
        token: faker.string.uuid(),
        expiresAt: faker.date.future(),
        acceptedAt: null,
        accepted: null,
      },
      '*',
    );

    const tags: Tag[] = await Tag.query(tenantDB)
      .where({ systemType: 'tag' })
      .limit(3);
    const tagIds = tags.map((t) => t.uid).sort();
    const newRole = await Role.query(tenantDB)
      .whereNot('uid', role.uid)
      .first();

    const res = await request(app)
      .patch(`${process.env.API_INTERNAL_ROUTE}/${org.handle}/invites`)
      .set('Cookie', signin.header['set-cookie'])
      .send({
        updates: [
          { email: invite.email, roleUid: newRole.uid, tagUids: tagIds },
        ],
      });
    expect(res.statusCode).eq(200);

    const updatedInvite = await tenantDB('invites')
      .where({ uid: invite.uid })
      .first('*');
    expect(updatedInvite.roleUid).eq(newRole.uid);
    expect(updatedInvite.tagUids).deep.eq(tagIds);
  });

  it('ensures a user can leave an organisation', async () => {
    const signup = await request(app).post(url('/signup')).send(newUser());
    const { user } = signup.body;
    await setupAccount(
      { ownerType: 'user', ownerUid: user.uid, dbServerUid: dbServer?.uid },
      db,
    );

    await tenantFGA.create({
      objectId: org.uid,
      objectType: 'org',
      subjectType: 'user',
      subjectId: user.uid,
      relation: 'member',
    });

    const res = await request(app)
      .delete(
        `${process.env.API_INTERNAL_ROUTE}/${user.handle}/orgs/${org.handle}`,
      )
      .set('Cookie', signup.header['set-cookie']);
    expect(res.statusCode).eq(200);
  });

  it('fails on deleting organisation with invalid password', async () => {
    const res = await request(app)
      .delete(`${process.env.API_INTERNAL_ROUTE}/orgs/${org.handle}`)
      .send({ password: faker.string.uuid() })
      .set('Cookie', signin.header['set-cookie']);
    expect(res.statusCode).eq(409);
  });

  it('should be able to delete an organization ', async () => {
    const res = await request(app)
      .delete(`${process.env.API_INTERNAL_ROUTE}/orgs/${org.handle}/`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send({ password });

    expect(res.statusCode).to.be.oneOf([200]);
  });
});
