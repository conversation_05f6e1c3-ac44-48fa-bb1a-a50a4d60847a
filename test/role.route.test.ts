import './__mocks__/payments';

import { newUser, setupOrgTenant } from './utils/tenant';

import { Application } from 'express';
import { OpenFgaClient } from '@openfga/sdk';
import { Tenant } from '../src/models/tenant';
import { buildApp } from '../src/app';
/* eslint-disable max-len */
import { FgaService } from '@ss-libs/ss-component-auth';
import { generateProjectKey } from './data/index';
import { expect } from 'chai';
import { faker } from '@faker-js/faker';
import orgRoutes from '../src/routes/internal/org';
import { permissions } from '../src/constants/auth';
import request from 'supertest';
import roleRoutes from '../src/routes/internal/role';
import runRoutes from '../src/routes/internal/run';
import projectRoutes from '../src/routes/internal/project';
import { setupDB } from '../src/config/db';
import { setupOpenfga } from '../src/config/openfga';
import { Knex } from 'knex';
import { closeQueues } from '../src/lib/queue';
import { tenantManager } from '../src/lib/tenants';
import { v4 } from 'uuid';

let app: Application;
let user,
  memberSession,
  additionalMemberSession,
  handle: string,
  orgId: string,
  password: string,
  member: Record<string, string> = newUser(),
  additionalMember: Record<string, string> = newUser(),
  basePath: string,
  fga: FgaService;

const role = {
  name: faker.lorem.word(2),
  description: 'custom testfiesta role',
  permissions: [permissions.write_role],
};
const ownerRoleUid = v4();
const ownerUid = v4();
const db = setupDB();
let tenantDb: Knex;

beforeAll(async () => {
  app = buildApp(db, [orgRoutes, roleRoutes, projectRoutes, runRoutes]);

  const tenant = await setupOrgTenant(db, app);
  user = tenant.admin;
  orgId = tenant.org.uid;
  password = tenant.password;
  handle = tenant.handle;
  basePath = `${process.env.API_INTERNAL_ROUTE}/${handle}/roles`;  

  const memberSignup = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signup`)
    .set('Accept', 'application/json')
    .send(member);
  member = memberSignup.body.user;
  memberSession = memberSignup.headers['set-cookie'];

  const additionalMemberSignup = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signup`)
    .set('Accept', 'application/json')
    .send(additionalMember);
  additionalMember = additionalMemberSignup.body.user;
  additionalMemberSession = additionalMemberSignup.headers['set-cookie'];

  tenantDb = setupDB(orgId, tenant.dbServer);

  const creds: Tenant = await db('tenants')
    .where({ tenantUid: tenant.org.uid })
    .first('*');
  const fgaSetup : OpenFgaClient = await setupOpenfga({
    storeName: creds.tenantUid,
    authModelId: creds.openfgaAuthModelId,
    storeId: creds.openfgaStoreId,
  });

  fga = new FgaService(fgaSetup);


  // Setting write_member requires read_role and read_member permissions

  await fga.create(...[
    {
      objectType: 'org',
      objectId: `${tenant.org.uid}`,
      relation: 'member',
      subjectType: 'user',
      subjectId: `${member.uid}`,
    },
    {
      objectType: 'org',
      objectId: `${tenant.org.uid}`,
      relation: 'read_role',
      subjectType: 'user',
      subjectId: `${member.uid}`,
    },
    {
      objectType: 'org',
      objectId: `${tenant.org.uid}`,
      relation: 'member',
      subjectType: 'user',
      subjectId: `${ownerUid}`,
    },
    {
      subjectType: 'org',
      subjectId: `${tenant.org.uid}`,
      relation: 'owner',
      objectType: 'role',
      objectId: ownerRoleUid,
    },
    {
      subjectType: 'role',
      subjectId: `${ownerRoleUid}#assignee`,
      relation: 'owner',
      objectType: 'org',
      objectId: `${tenant.org.uid}`,
    },
    {
      subjectType: 'user',
      subjectId: ownerUid,
      relation: 'assignee',
      objectType: 'role',
      objectId: ownerRoleUid,
      condition: {
        name: 'default_role',
        context: {
          excluded: [],
        },
      },
    },
    {
      objectType: 'org',
      objectId: `${tenant.org.uid}`,
      relation: 'read_member',
      subjectType: 'user',
      subjectId: `${member.uid}`,
    },
    {
      objectType: 'org',
      objectId: `${tenant.org.uid}`,
      relation: 'write_member',
      subjectType: 'user',
      subjectId: `${member.uid}`,
    },
    {
      objectType: 'org',
      objectId: `${tenant.org.uid}`,
      relation: 'member',
      subjectType: 'user',
      subjectId: `${additionalMember.uid}`,
    }
  ]);
});

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

describe('Roles', () => {
  let totalRoles: number, session, ownerRole, otherRole;

  it('should sign in a user', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/signIn`)
      .set('Accept', 'application/json')
      .send({ email: user.email, password });
    expect(res.statusCode).to.be.oneOf([200]);
    session = res.headers['set-cookie'];
  });

  it('should fetch preset roles', async () => {
    const res = await request(app).get(basePath).set('Cookie', session);
    expect(res.statusCode).eq(200);

    totalRoles = res.body.roles.length;
  });

  it('should create a new role', async () => {
    const res = await request(app)
      .post(basePath)
      .send(role)
      .set('Cookie', session);
    expect(res.statusCode).eq(200);
  });

  it('fails if role name already exists in org', async () => {
    const res = await request(app)
      .post(basePath)
      .send(role)
      .set('Cookie', session);
    expect(res.statusCode).eq(409);
  });

  it('retrieves a list of all roles in an organisation', async () => {
    const res = await request(app).get(basePath).set('Cookie', session);
    expect(res.statusCode).eq(200);
    expect(res.body.roles.length).eq(totalRoles + 1);

    otherRole = res.body.roles.filter((r) => {
      if (r.system !== true) return true;
      ownerRole = r;
      return false;
    })[0];
  });

  it('Default role should be inherited for the new created project, and overridden roles should work as expected', async () => {
    const projectResponse = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
    .set('Accept', 'application/json')
    .set('Cookie', session)
    .send({
      externalId: faker.string.uuid(),
      customFields: { version: '2.0', platform: 'app' },
      source: 'testfiesta',
      name: 'projecttest',
      key: generateProjectKey(),
    })
    
    const additionalProjectResponse = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
    .set('Accept', 'application/json')
    .set('Cookie', session)
    .send({
      externalId: faker.string.uuid(),
      customFields: { version: '2.0', platform: 'app' },
      source: 'testfiesta',
      name: 'projecttest',
      key: generateProjectKey(),
    })

    const createRoleDto = {
      name: faker.lorem.word(5),
      description: 'custom testfiesta role',
      permissions: [permissions.read_entity, permissions.write_entity],
    };

  
    const roleResponse = await request(app)
    .post(basePath)
    .send(createRoleDto)
    .set('Cookie', session);

    expect(roleResponse.statusCode).eq(200);

    const newPermissionsRole = {
      name: faker.lorem.word(5),
      describetion: 'zero permissions testfiesta role',
      permissions: ['write_tag'],
    }

    const newPermissionsRoleResponse = await request(app)
    .post(basePath)
    .send(newPermissionsRole)
    .set('Cookie', session);

    expect(newPermissionsRoleResponse.statusCode).eq(200);

    const reAssignUser = await request(app)
    .post(`${basePath}/${roleResponse.body.uid}/members/reassign`)
    .set('Cookie', session)
    .send({
      members: [
        {
          userId: additionalMember.uid,
        }
      ],
      overriddenRoles: [
        {
          roleUid: newPermissionsRoleResponse.body.uid,
          projectUid: additionalProjectResponse.body.uid,
        }
      ]
    })

    expect(reAssignUser.statusCode).eq(200);

    const project1RunsResponse = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${projectResponse.body.key}/runs`)
      .set('Accept', 'application/json')
      .set('Cookie', additionalMemberSession);
    
    expect(project1RunsResponse.statusCode).eq(200);

    const project2RunsResponse = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${additionalProjectResponse.body.key}/runs`)
      .set('accept', 'application/json')
      .set('Cookie', additionalMemberSession)

    expect(project2RunsResponse.statusCode).eq(403);
  })

  it('should fail to reassign the owner since an organization must always have at least one owner role', async () => {

    let res = await request(app)
    .post(basePath)
    .send({
      name: 'Add a new custom role',
      description: 'another custom testfiesta role',
      permissions: [permissions.write_role],
    })
    .set('Cookie', session);

    const newRoleId = res.body.uid;
    
    const ownerReassignment = await request(app)
    .post(`${basePath}/${newRoleId}/members/reassign`)
    .set('Cookie', session)
    .send({
      members: [
        {
          userId: ownerUid,
        },
        {
          userId: user.uid
        }
      ]
    })
    
    expect(ownerReassignment.statusCode).eq(403);
  })

  it('populates the list of roles with its assignees', async () => {
    const res = await request(app)
      .get(basePath)
      .set('Cookie', session)
      .query({ includeUsers: true });
    expect(res.statusCode).eq(200);
    for (const role of res.body.roles) {
      expect(role).haveOwnProperty('assignees');
    }
  });

  it('gets the details of a single role', async () => {
    const res = await request(app)
      .get(`${basePath}/${otherRole.uid}`)
      .set('Cookie', session);

    expect(res.statusCode).eq(200);
    expect(res.body.uid).eq(otherRole.uid);
    expect(res.body).haveOwnProperty('members');
  });

  it("fails on fetching a role that doesn't belong to the org", async () => {
    const res = await request(app)
      .get(`${basePath}/${faker.string.uuid()}`)
      .set('Cookie', session);
    expect(res.statusCode).eq(404);
  });

  it('deletes an existing role', async () => {
    let res = await request(app)
      .post(basePath)
      .send({
        name: faker.company.name(),
        permissions: [permissions.read_entity],
      })
      .set('Cookie', session);
    expect(res.statusCode).eq(200);

    res = await request(app)
      .delete(`${basePath}/${res.body.uid}`)
      .set('Cookie', session);
    expect(res.statusCode).eq(200);
  });

  it("updates a role's details", async () => {
    const createRoleDto = {
      name: faker.lorem.word(5),
      description: 'custom testfiesta role',
      permissions: [permissions.write_role],
    };

    let res = await request(app)
      .post(basePath)
      .send(createRoleDto)
      .set('Cookie', session);
    expect(res.statusCode).eq(200);
    const role = res.body;
    const tags = await tenantDb('tags').where({ systemType: 'tag' }).limit(5);
    const tagIds = tags.map((t) => t.uid).sort();

    res = await request(app)
      .patch(`${basePath}/${role.uid}`)
      .send({ tagUids: tagIds })
      .set('Cookie', session);
    expect(res.statusCode).eq(200);

    const roleTags = await tenantDb('roleTags')
      .where({ roleUid: role.uid })
      .select('*');

    expect(roleTags.map((r) => r.tagUid).sort()).deep.eq(tagIds);
  });

  it("updates a role's permissions", async () => {
    const _permissions = { ...permissions };
    delete (<any>_permissions).owner;

    // add 10 more permissions
    const newPermissions = [
      ...new Set([
        ...role.permissions,
        ...faker.helpers.arrayElements(Object.values(_permissions), 10),
        permissions.read_role,
        permissions.write_member,
      ]),
    ];

    const res = await request(app)
      .patch(`${basePath}/${otherRole.uid}`)
      .send({ permissions: newPermissions })
      .set('Cookie', session);
    expect(res.statusCode).eq(200);

    const getRole = await request(app)
      .get(`${basePath}/${otherRole.uid}`)
      .set('Cookie', session);
    expect(getRole.status).eq(200);
    expect(getRole.body.permissions.sort()).deep.eq(newPermissions.sort());
  });

  it('ensures system role permissions cannot be updated', async () => {
    const res = await request(app)
      .patch(`${basePath}/${ownerRole.uid}`)
      .send({ permissions: [permissions.delete_entity] })
      .set('Cookie', session);
    expect(res.statusCode).eq(405);
  });

  it('assigns org members to roles', async () => {
    const res = await request(app)
      .post(`${basePath}/${otherRole.uid}/members`)
      .send({ userIds: [member.uid] })
      .set('Cookie', session);
    expect(res.statusCode).eq(200);
  });

  it('ensures only existing owners can assign ownership to members', async () => {
    const newSignup = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/signup`)
      .set('Accept', 'application/json')
      .send(newUser());
    const userId = newSignup.body.user.uid;

    await fga.create({
      objectType: 'org',
      objectId: `${orgId}`,
      subjectType: 'user',
      subjectId: `${userId}`,
      relation: 'member'
    })

    const res = await request(app)
      .post(`${basePath}/${ownerRole.uid}/members`)
      .send({ userIds: [userId] })
      .set('Cookie', memberSession);
    expect(res.statusCode).eq(403);
  });

  it('fetches the correct roles of a member', async () => {
    const res = await request(app)
      .get(
        `${process.env.API_INTERNAL_ROUTE}/${handle}/members/${member.uid}/roles`,
      )
      .set('Cookie', session);

    expect(res.statusCode).eq(200);

    const [expectedRole] = res.body;
    expect(expectedRole.uid).eq(otherRole.uid);
  });

  it('fails on assigning role to non org member', async () => {
    const userIds = [faker.string.uuid()];
    const res = await request(app)
      .post(`${basePath}/${otherRole.uid}/members`)
      .send({ userIds })
      .set('Cookie', session);

    expect(res.statusCode).eq(409);
    expect(res.body.data).deep.eq(userIds);
  });

  it('fails to remove everyone from the owner role', async () => {
    const  tuples  = await fga.query('', `role:${ownerRole.uid}`, 'assignee')
    const userIds = tuples?.map((t) => t.key?.user?.split(':').pop());
    const res = await request(app)
      .delete(`${basePath}/${ownerRole.uid}/members`)
      .send({ userIds })
      .set('Cookie', session);

    expect(res.statusCode).eq(405);
  });

  it('removes org members from a role', async () => {
    let res = await request(app)
      .delete(`${basePath}/${otherRole.uid}/members`)
      .set('Accept', 'application/json')
      .send({ userIds: [member.uid] })
      .set('Cookie', session);

    expect(res.statusCode).eq(200);

    res = await request(app)
      .get(
        `${process.env.API_INTERNAL_ROUTE}/${handle}/members/${member.uid}/roles`,
      )
      .set('Cookie', session);

    expect(res.statusCode).eq(200);
    expect(res.body).to.be.empty;
  });

  it('reassign members to a different role', async () => {
    let res = await request(app)
      .post(basePath)
      .send({
        name: 'another test role',
        description: 'another custom testfiesta role',
        permissions: [permissions.write_role],
      })
      .set('Cookie', session);

    expect(res.statusCode).eq(200);

    const newRoleId = res.body.uid;

    res = await request(app)
      .post(`${basePath}/${otherRole.uid}/members`)
      .send({ userIds: [member.uid] })
      .set('Cookie', session);

    expect(res.statusCode).eq(200);

    res = await request(app)
      .post(`${basePath}/${newRoleId}/members/reassign`)
      .send({ members: [{ roleId: otherRole.uid, userId: member.uid }] })
      .set('Cookie', session);

    expect(res.statusCode).eq(200);

    res = await request(app)
      .get(
        `${process.env.API_INTERNAL_ROUTE}/${handle}/members/${member.uid}/roles`,
      )
      .set('Cookie', session);

    const [expectedRole] = res.body;
    expect(expectedRole.uid).eq(newRoleId);
  });

  it('deletes existing roles in bulk', async () => {
    const roleIds: string[] = [];
    for (let i = 1; i <= 3; i++) {
      const res = await request(app)
        .post(basePath)
        .send({
          name: `test role ${i}`,
          description: `another custom testfiesta role ${i}`,
          permissions: [permissions.write_role],
        })
        .set('Cookie', session);

      expect(res.statusCode).eq(200);
      roleIds.push(res.body.uid);
    }

    for (const role of roleIds) {
      const res = await request(app)
        .post(`${basePath}/${role}/members`)
        .send({ userIds: [member.uid] })
        .set('Cookie', session);

      expect(res.statusCode).eq(200);
    }

    const res = await request(app)
      .delete(basePath)
      .set('Accept', 'application/json')
      .send({ roleIds })
      .set('Cookie', session);

    expect(res.statusCode).eq(200);
    expect(res.body).eq(null);
  });
});
