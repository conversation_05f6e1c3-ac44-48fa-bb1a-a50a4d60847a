import { Application } from 'express';
import { buildApp } from '../src/app';
import { closeQueues } from '../src/lib/queue';
import request from 'supertest';
import { setupDB } from '../src/config/db';
import { setupUserTenant } from './utils/tenant';
import { tenantManager } from '../src/lib/tenants';
import executionRoutes from '../src/routes/internal/execution';
import projectRoutes from '../src/routes/internal/project';
import dayjs from 'dayjs';
import * as data from './data/index';

let app: Application;
let user, password: string, handle: string, session, userId, dbServer, project;

const db = setupDB();

beforeAll(async () => {
  app = buildApp(db, [projectRoutes,executionRoutes]);

  const tenant = await setupUserTenant(db, app);
  user = tenant.user;
  password = tenant.password;
  handle = tenant.handle;
  dbServer = tenant.dbServer;

  const res = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signIn`)
    .set('Accept', 'application/json')
    .send({ email: user.email, password });

  session = res.headers['set-cookie'];

  project = (await request(app)
   .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
   .set('Cookie', session)
   .send(data.newProject())).body;
  
  userId = res.body.user.uid;
});

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

let testProject;
let testMilestone;
let testPlan;
let testRun;
let testExecution;
let testExecution2;

const setupTestData = async () => {
  const tenantDB = setupDB(userId, dbServer);

  const [milestone] = await tenantDB('tags')
    .insert({
      name: 'Test Milestone',
      systemType: 'milestone',
      projectUid: project.uid,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    })
    .returning('uid');

  const [plan] = await tenantDB('tags')
    .insert({
      name: 'Test Plan',
      systemType: 'plan',
      projectUid: project.uid,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    })
    .returning('uid');

  const [run] = await tenantDB('testRuns')
    .insert({
      name: 'Test Run',
      projectUid: project.uid,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    })
    .returning('uid');

  await tenantDB('testMilestoneRuns').insert({
    milestoneUid: milestone.uid,
    runUid: run.uid,
  });

  await tenantDB('testPlanRuns').insert({
    planUid: plan.uid,
    runUid: run.uid,
  });

  const [testCase] = await tenantDB('testCases')
    .insert({
      name: 'Test Case',
      projectUid: project.uid,
      externalId: 'TEST-' + Math.floor(Math.random() * 10000),
      source: 'local',
      version: 1,
      active: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      testCaseRef: 1,
    })
    .returning('uid');

  const [execution] = await tenantDB('testExecutions')
    .insert({
      testCaseUid: testCase.uid,
      testRunUid: run.uid,
      projectUid: project.uid,
      assignedTo: userId,
      customFields: {
        tags: [{ uid: 1, name: 'Test Tag' }],
      },
      status: 1,
      priority: 1,
      lastAssignedAt: new Date().toISOString(),
      dueAt: dayjs().add(7, 'days').toISOString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    })
    .returning('uid');

    const [execution2] = await tenantDB('testExecutions')
    .insert({
      testCaseUid: testCase.uid,
      testRunUid: run.uid,
      projectUid: project.uid,
      assignedTo: userId,
      customFields: {
        tags: [{ uid: 1, name: 'Test Tag' }],
      },
      status: 1,
      priority: 1,
      lastAssignedAt: new Date().toISOString(),
      dueAt: dayjs().add(7, 'days').toISOString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    })
    .returning('uid');
  return {
    projectId: project.uid,
    milestoneId: milestone.uid,
    planId: plan.uid,
    runId: run.uid,
    caseId: testCase.uid,
    executionId: execution.uid,
    execution2Id: execution2.uid,
  };
};

beforeAll(async () => {
  const testData = await setupTestData();
  testProject = testData.projectId;
  testMilestone = testData.milestoneId;
  testPlan = testData.planId;
  testRun = testData.runId;
  testExecution = testData.executionId;
  testExecution2 = testData.execution2Id;
});

describe('Get Execution Users', () => {
  it('should be able to get executions users', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/executions/users`)
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    expect(Array.isArray(res.body.users)).toBe(true);
  });
});

describe('Get Execution Count', () => {
  it('should be able to get executions count', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/executions/count`)
      .query({ 'status[]': [1,2,3,4] })
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
  });
});

describe('Get Execution Projects', () => {
  it('should be able to get projects', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/executions/projects`)
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('projects');
    expect(Array.isArray(res.body.projects)).toBe(true);
    expect(
      res.body.projects.some((project) => project.uid === testProject),
    ).toBe(true);
  });
});

describe('Get Execution Milestones', () => {
  it('should be able to get milestones', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/executions/milestones`)
      .query({ 'projectUids[]': [testProject] })
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('milestones');
    expect(Array.isArray(res.body.milestones)).toBe(true);
    expect(
      res.body.milestones.some((milestone) => milestone.uid === testMilestone),
    ).toBe(true);
  });
});

describe('Get Execution Plans', () => {
  it('should be able to get executions plans', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/executions/plans`)
      .query({ 'milestoneUids[]': [testMilestone] })
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('testPlans');
    expect(Array.isArray(res.body.testPlans)).toBe(true);
  });
});

describe('Get Execution Runs', () => {
  it('should be able to get executions runs', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/executions/runs`)
      .query({ 'planUids[]': [testPlan] })
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('testRuns');
    expect(Array.isArray(res.body.testRuns)).toBe(true);
    expect(res.body.testRuns.some((run) => run.uid === testRun)).toBe(true);
  });
});

describe('Get Execution Relations', () => {
  it('should be able to get executions relations testRun', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/executions/relations`)
      .query({ relation: 'testRun', 'executionUids': [testExecution, testExecution2] })
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
  });

  it('should be able to get executions relations testPlan and return correct mapping', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/executions/relations`)
      .query({ relation: 'testPlan', 'executionUids': [testExecution, testExecution2] })
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
  });

  it('should be able to get executions relations milestone', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/executions/relations`)
      .query({ relation: 'milestone', 'executionUids': [testExecution, testExecution2] })
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
  });

  it('should be able to get executions relations project', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/executions/relations`)
      .query({ relation: 'project', 'executionUids': [testExecution, testExecution2] })
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
  });

  it('should be able to get executions relations tags', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/executions/relations`)
      .query({ relation: 'tag', 'executionUids': [testExecution, testExecution2] })
      .set('Cookie', session);

    expect(res.statusCode).toBe(200);
  });
});
