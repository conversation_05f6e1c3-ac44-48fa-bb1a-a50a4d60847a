import * as data from './data/index';
import * as typeConfig from '@testfiesta/tacotruck/configs/testfiesta.json';

import { getCase, newCaseByAssist } from './utils/assist';

import { Application } from 'express';
import assistRoutes from '../src/routes/internal/assist';
import { assistService } from './../src/services/index';
import { authSchemas } from '@testfiesta/tacotruck/src/utils/auth';
import { buildApp } from '../src/app';
import { closeQueues } from '../src/lib/queue';
import logger from '../src/config/logger';
import { pushData } from '@testfiesta/tacotruck/src/controllers/api';
import request from 'supertest';
import { setupDB } from '../src/config/db';
import { tenantManager } from '../src/lib/tenants';
import { v4 as uuidv4 } from 'uuid';
let session;
let handle;
const db = setupDB();

let app: Application,
  basePath: string,
  baseTFPath: string,
  projectId: string,
  accessToken: any;

beforeAll(async () => {
  app = buildApp(db, [assistRoutes], true);

  // hard coding credentials for testing purposes
  const email = '<EMAIL>';
  const password = 'testfiesta@123';
  handle = 'testfiesta-1';

  const signin = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signin`)
    .send({ email, password });
  session = signin.headers['set-cookie'];

  accessToken = `testfiesta_39d61310cf204c2e84e80f2ca0a7892f.e80fb727f8e6b5254d3b520375052654`;
  projectId = 'AI-SCORING';

  basePath = `${process.env.API_INTERNAL_ROUTE}/${handle}/assist`;
  baseTFPath = `${process.env.API_VERSION_1_ROUTE}/${handle}/projects/${projectId}/data`;
});

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

it('This should establish a valid end-to-end AI testing pipeline.', async () => {
  const userRequest = newCaseByAssist();
  const cases: any[] = getCase().map((cs) => {
    const caseName = userRequest['caseName'];

    return {
      ...cs,
      caseName,
    };
  });

  const counts = Math.floor(Math.random() * (10 - 3 + 1)) + 3;

  for (let i = 0; i < counts; i++) {
    const res = await request(app)
      .post(basePath)
      .set('Cookie', session)
      .set('Accept', 'application/json')
      .send(userRequest);

    if (res.status == 200) {
      const cs = {
        caseName: userRequest['caseName'],
        inputFields: res.body.inputFields?.map((item) => ({
          fieldName: item.fieldName,
          fieldValue: item.fieldValue,
        })),
        steps: res.body.steps,
      };
      cases.push(cs);
    }
  }
  const runs = Array.from({ length: cases.length }, () => {
    // each executions should attached to run
    const run = data.newRun();
    return {
      ...run,
      externalId: uuidv4(),
    };
  });

  const res = await assistService.scoreCases(cases, [
    'thoroughnessScore',
    'clarityScore',
    'relevanceScore',
  ]); // Scoring cases based on our selected attributes (customizable)

  const executions: { [key: string]: any }[] = [];
  const caseRef = uuidv4();

  // Creates multiple executions of the same test case for statistical analysis -> testCase
  for (let i = 0; i < cases.length; i++) {
    executions.push({
      name: cases[i].caseName,
      ...res.scores[i],
      ...(cases[i].steps ? { steps: cases[i].steps } : {}),
      ...(cases[i].inputFields
        ? {
            templateFields: cases[i].inputFields.map((i) => ({
              name: i.fieldName,
              value: i.fieldValue,
              dataType: 'text',
              id: uuidv4(),
            })),
          }
        : {}),
      caseRef,
      source: 'pipeline-testing',
      runRef: runs[i].externalId,
    });
  }

  typeConfig.multi_target.path = baseTFPath; // Bulk import endpoint for handle/project for tacotruck config
  const config = {
    authSchema: authSchemas['bearer'],
    typeConfig,
    baseUrl: `${process.env.BACKEND_URL}`,
    authPayload: `Bearer ${accessToken}`,
    endpointSet: ['executions', 'runs'],
  };
  const payload = {
    executions,
    runs,
  };

  try {
    await pushData(config, payload);
    logger.info('Data successfully pushed to TacoTruck');
  } catch (err) {
    fail(`Test failed: Unable to push data to TacoTruck: ${err}`);
  }
});
