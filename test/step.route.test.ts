import { SharedStepItem, TestCaseStepItem } from '../src/types/step';
import { getSteps, newCase, newProject } from './data/index';

import { Application } from 'express';
import { buildApp } from '../src/app';
import caseRoutes from '../src/routes/internal/case';
import { closeQueues } from '../src/lib/queue';
import { expect } from 'chai';
import folderRoutes from '../src/routes/internal/folder';
import orgRoutes from '../src/routes/internal/org';
import projectRoutes from '../src/routes/internal/project';
import request from 'supertest';
import { setupDB } from '../src/config/db';
import { setupUserTenant } from './utils/tenant';
import stepRoutes from '../src/routes/internal/sharedStep';
import { tenantManager } from '../src/lib/tenants';
import { v4 as uuidv4 } from 'uuid';

let app: Application, session: any, basePath: string, folderId: string;

const db = setupDB();

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

beforeAll(async () => {
  app = buildApp(db, [
    orgRoutes,
    stepRoutes,
    caseRoutes,
    projectRoutes,
    folderRoutes,
  ]);

  const tenant = await setupUserTenant(db, app);
  const email = tenant.user.email;
  const password = tenant.password;
  const handle = tenant.handle;

  const signin = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signin`)
    .send({ email, password });
  session = signin.headers['set-cookie'];

  const createProject = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
    .set('Accept', 'application/json')
    .set('Cookie', session)
    .send(newProject());

  basePath = `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${createProject.body.key}`;
  const projectFolders = await request(app)
    .get(`${basePath}/folders`)
    .set('Cookie', session)
    .send();
  folderId = projectFolders.body.folders[0].uid;
});

describe('Shared Steps Endpoints', () => {
  it('should be able to create new shared step', async () => {
    const res = await request(app)
      .post(`${basePath}/shared-steps`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        name: 'Access the User Profile',
        steps: getSteps(),
        expectedResultByStep: true,
      });
    expect(res.statusCode).to.be.eq(200);
    expect(res.body.expectedResultByStep).to.be.true;
  });

  it('should be able to create a new shared step with its steps', async () => {
    const res = await request(app)
      .post(`${basePath}/shared-steps`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        name: 'Navigate to the Login Page',
        steps: getSteps(),
        expectedResultByStep: true,
      });

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body.steps).to.be.lengthOf(2);
    expect(res.body.expectedResultByStep).to.be.true;
  });

  it('should be able to get all shared steps', async () => {
    const res = await request(app)
      .get(`${basePath}/shared-steps`)
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).to.be.eq(200);
    expect(res.body.sharedSteps.length).greaterThan(0);
    expect(res.body.sharedSteps[0].creator).not.empty;
  });

  it('should be able to get a shared step', async () => {
    let res = await request(app)
      .post(`${basePath}/shared-steps`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        name: 'Single shared step test',
        steps: getSteps(),
      });

    res = await request(app)
      .get(`${basePath}/shared-steps/${res.body.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).to.be.eq(200);
    expect(res.body).not.empty;
    expect(res.body.creator).not.empty;
  });

  it('should delete a shared step', async () => {
    let res = await request(app)
      .post(`${basePath}/shared-steps`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        name: 'Shared Step #1',
        steps: getSteps(),
      });

    res = await request(app)
      .delete(`${basePath}/shared-steps/${res.body.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send();

    expect(res.statusCode).to.be.eq(200);
    expect(res.body.message).not.empty;
  });

  it('should delete shared steps in bulk', async () => {
    const sharedStepIds: string[] = [];

    for (let i = 1; i <= 3; i++) {
      const res = await request(app)
        .post(`${basePath}/shared-steps`)
        .set('Accept', 'application/json')
        .set('Cookie', session)
        .send({
          name: `Shared Step #${i}`,
          steps: getSteps(),
        });

      sharedStepIds.push(res.body.uid);
    }

    const res = await request(app)
      .delete(`${basePath}/shared-steps`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        sharedStepIds,
      });

    expect(res.statusCode).to.be.eq(200);
    expect(res.body.message).not.empty;
  });

  it('should archive a shared step', async () => {
    let res = await request(app)
      .post(`${basePath}/shared-steps`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        name: 'Shared Step #1',
        steps: getSteps(),
      });

    res = await request(app)
      .patch(`${basePath}/shared-steps`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        sharedSteps: [{ id: res.body.uid, archived: true }],
      });

    expect(res.statusCode).to.be.eq(200);
    expect(res.body.message).not.empty;
  });

  it('should archive shared steps in bulk', async () => {
    const sharedSteps: any = [];

    for (let i = 1; i <= 3; i++) {
      const res = await request(app)
        .post(`${basePath}/shared-steps`)
        .set('Accept', 'application/json')
        .set('Cookie', session)
        .send({
          name: `Shared Step #${i}`,
          steps: getSteps(),
        });

      sharedSteps.push({ id: res.body.uid, archived: true });
    }

    const res = await request(app)
      .patch(`${basePath}/shared-steps`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        sharedSteps,
      });

    expect(res.statusCode).to.be.eq(200);
    expect(res.body.message).not.empty;
  });

  it('should filter shared steps from name', async () => {
    const sharedStepName = 'Filter Shared Step #1';

    let res = await request(app)
      .post(`${basePath}/shared-steps`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        name: sharedStepName,
        steps: getSteps(),
      });

    res = await request(app)
      .get(`${basePath}/shared-steps`)
      .query({ name: 'Filter Shared' })
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).to.be.eq(200);
    expect(res.body.sharedSteps.length).equal(1);
    expect(res.body.sharedSteps[0].name).equal(sharedStepName);
  });

  it('should filter shared steps from minSteps and maxSteps', async () => {
    const steps: Array<SharedStepItem> = [];

    for (let i = 1; i <= 5; i++) {
      steps.push({
        id: uuidv4(),
        title: `Step Name #${i}`,
        description: `Step Description #${i}`,
        expectedResult: `Step Expected Result #${i}`,
      });
    }

    let res = await request(app)
      .post(`${basePath}/shared-steps`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        name: 'Shared Step name #1',
        steps,
      });

    res = await request(app)
      .get(`${basePath}/shared-steps`)
      .query({ minSteps: 3, maxSteps: 5 })
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).to.be.eq(200);
    expect(res.body.sharedSteps.length).equal(1);
  });

  it('should get a single shared step along with referenced test cases', async () => {
    const steps: Array<SharedStepItem> = [];

    for (let i = 1; i <= 5; i++) {
      steps.push({
        id: uuidv4(),
        title: `Step Name #${i}`,
        description: `Step Description #${i}`,
        expectedResult: `Step Expected Result #${i}`,
      });
    }
    let res = await request(app)
      .post(`${basePath}/shared-steps`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        name: 'Access the User Profile',
        steps,
      });

    const sharedStepId = res.body.uid;

    const testCaseSteps: Array<TestCaseStepItem> = [
      {
        id: sharedStepId,
        sharedStepUid: sharedStepId,
        title: 'Shared Step title',
        description: 'Shared Step description',
        expectedResult: `Shared Step Expected Result`,
      },
    ];

    res = await request(app)
      .post(`${basePath}/cases`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        ...newCase(),
        parentId: folderId,
        steps: testCaseSteps,
      });

    res = await request(app)
      .get(`${basePath}/shared-steps/${sharedStepId}`)
      .query({
        includeCount: true,
      })
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).to.be.eq(200);
    expect(res.body).not.empty;
  });

  it('should create a new shared test with version increment when updated', async () => {
    const steps: Array<SharedStepItem> = [];

    for (let i = 1; i <= 5; i++) {
      steps.push({
        id: uuidv4(),
        title: `Step Name #${i}`,
        description: `Step Description #${i}`,
        expectedResult: `Step Expected Result #${i}`,
      });
    }

    let res = await request(app)
      .post(`${basePath}/shared-steps`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        name: 'Version Test #1',
        steps,
      });

    const sharedStep = res.body;

    const updateSharedStepPayload = {
      name: 'Version Test Updated #1',
    };

    res = await request(app)
      .patch(`${basePath}/shared-steps/${sharedStep.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        ...updateSharedStepPayload,
      });

    expect(res.statusCode).to.be.eq(200);
    expect(res.body.version).to.eq(2);
    expect(res.body.active).to.eq(true);
    expect(sharedStep.version).to.eq(1);
  });
});
