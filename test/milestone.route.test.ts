import * as data from './data/index';
import * as dbHelpers from './utils/dbHelpers';

import { Application } from 'express';
import { Knex } from 'knex';
import { buildApp } from '../src/app';
import { closeQueues } from '../src/lib/queue';
import { expect } from 'chai';
import milestoneRoute from '../src/routes/internal/milestone';
import planRoutes from '../src/routes/internal/plan';
import projectRoutes from '../src/routes/internal/project';
import request from 'supertest';
import runRoutes from '../src/routes/internal/run';
import { setupDB } from '../src/config/db';
import { setupUserTenant } from './utils/tenant';
import { updateMilestoneState } from '../src/temporal/activities/milestone';
import { tenantManager } from '../src/lib/tenants';

let app: Application,
  session: any,
  user,
  password: string,
  handle: string,
  projectKey: number,
  projectUid: number,
  basePath: string,
  tenantDb: Knex;

const db = setupDB();
let tenant: any = null;
beforeAll(async () => {
  app = buildApp(db, [projectRoutes, milestoneRoute, runRoutes, planRoutes]);

  tenant = await setupUserTenant(db, app);
  user = tenant.user;
  password = tenant.password;
  handle = tenant.handle;

  tenantDb = setupDB(tenant.user.uid, tenant.dbServer);

  const signin = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signin`)
    .send({ email: user.email, password });
  session = signin.headers['set-cookie'];

  // create default project and retrieve it's latest folder
  const createProject = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
    .set('Cookie', session)
    .send(data.newProject());
  projectKey = createProject.body.key;
  projectUid = createProject.body.uid;

  basePath = `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${projectKey}/milestones`;
});

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

describe('Milestone', () => {
  let milestone;

  it('should not pass create test milestones when empty body', async () => {
    const res = await request(app)
      .post(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({});
    expect(res.statusCode).eq(400);
  });

  let initialCount: number;

  it('get initial counts', async () => {
    const res = await request(app)
      .get(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session);
    expect(res.statusCode).to.be.oneOf([200]);
    initialCount = res.body.items.length;
  });

  it('should be able to create a new test milestones', async () => {
    const res = await request(app)
      .post(`${basePath}`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send(data.newMilestone());
    expect(res.statusCode).to.be.oneOf([200]);
    milestone = res.body;
  });

  it('should be able to get list test milestones', async () => {
    const res = await request(app)
      .get(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session);
    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body.items.length).eq(initialCount + 1);
  });

  it('should be able to get search test milestones', async () => {
    const res = await request(app)
      .get(`${basePath}/search?query=${milestone.name.slice(1)}`)
      .set('Cookie', session);
    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body.milestones.length).eq(initialCount + 1);
  });

  it('should be able to get a test milestone by milestoneId', async () => {
    const res = await request(app)
      .get(`${basePath}/${milestone.uid}`)
      .set('Cookie', session);
    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body).haveOwnProperty('customFields');
    expect(res.body).haveOwnProperty('testPlans');
    expect(res.body).haveOwnProperty('testRuns');
    expect(res.body).haveOwnProperty('tags');
  });

  it('should be able to update a test milestone by its id', async () => {
    const update = data.newMilestone();
    const res = await request(app)
      .patch(`${basePath}/${milestone.uid}`)
      .set('Cookie', session)
      .send({
        ...update,
      });

    expect(res.statusCode).eq(200);
    expect(res.body.description).eq(update.description);
  });

  it('should be able to attach milestone to runs', async () => {
    const runsToInsert = [data.newRun(), data.newRun(), data.newRun()];
    const update = data.newMilestone();
    const insertedRunIds: string[] = [];
    for (const run of runsToInsert) {
      const runResponse = await request(app)
        .post(
          `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${projectKey}/runs`,
        )
        .set('Accept', 'application/json')
        .set('Cookie', session)
        .send(run);
      insertedRunIds.push(runResponse.body.uid);
    }

    const res = await request(app)
      .patch(`${basePath}/${milestone.uid}`)
      .set('Cookie', session)
      .set('Accept', 'application/json')
      .send({
        ...update,
        runIds: insertedRunIds,
      });
    expect(res.statusCode).to.be.oneOf([200]);
  });

  it('should be able to attach milestone to plans', async () => {
    const update = data.newMilestone();
    const planIds: number[] = [];
    await tenantDb.transaction(async (trx) => {
      for (let i = 0; i < 3; i++) {
        const plan = await dbHelpers.createPlan(trx, milestone.projectUid);
        planIds.push(plan.uid);
      }
    });

    const res = await request(app)
      .patch(`${basePath}/${milestone.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        ...update,
        planIds,
      });
    expect(res.statusCode).to.be.oneOf([200]);

    const plans = await tenantDb('testMilestonePlans').where({
      milestoneUid: milestone.uid,
    });
    expect(plans.length).eq(planIds.length);
    for (const p of plans) expect(p.planUid).oneOf(planIds);
  });

  it('should get milestone runs count', async () => {
    const res = await request(app)
      .get(`${basePath}/${milestone.uid}/runs/count`)
      .set('Cookie', session);

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body).haveOwnProperty('count');
    expect(typeof res.body.count).eq('number');
    expect(res.body.count).gte(0);
  });

  it('should get milestone plans count', async () => {
    const res = await request(app)
      .get(`${basePath}/${milestone.uid}/plans/count`)
      .set('Cookie', session);

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body).haveOwnProperty('count');
    expect(typeof res.body.count).eq('number');
    expect(res.body.count).gte(0);
  });

  it('should get milestone cases count', async () => {
    const res = await request(app)
      .get(`${basePath}/${milestone.uid}/cases/count`)
      .set('Cookie', session);

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body).haveOwnProperty('count');
    expect(typeof res.body.count).eq('number');
    expect(res.body.count).gte(0);
  });

  it('should be able to delete test milestones', async () => {
    let res = await request(app)
      .delete(`${basePath}/${milestone.uid}`)
      .set('Cookie', session);
    expect(res.statusCode).to.be.oneOf([200]);

    res = await request(app)
      .get(`${basePath}/${milestone.uid}`)
      .set('Cookie', session);
    expect(res.statusCode).to.be.oneOf([404]);
  });

  it('should archive a milestone', async () => {
    let res = await request(app)
      .post(`${basePath}`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send(data.newMilestone());

    expect(res.statusCode).to.be.oneOf([200]);

    const newMilestone = res.body;

    res = await request(app)
      .patch(`${basePath}/${newMilestone.uid}`)
      .set('Cookie', session)
      .send({
        archived: true,
        ...data.newMilestone(),
      });

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body.archivedAt).not.null;

    res = await request(app)
      .delete(`${basePath}/${newMilestone.uid}`)
      .set('Cookie', session);
    expect(res.statusCode).to.be.oneOf([200]);
  });

  it('should unarchive a milestone', async () => {
    let res = await request(app)
      .post(`${basePath}`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send(data.newMilestone());

    expect(res.statusCode).to.be.oneOf([200]);

    const newMilestone = res.body;

    res = await request(app)
      .patch(`${basePath}/${newMilestone.uid}`)
      .set('Cookie', session)
      .set('Accept', 'application/json')
      .send({
        archived: true,
        ...data.newMilestone(),
      });

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body.archivedAt).not.null;

    res = await request(app)
      .patch(`${basePath}/${newMilestone.uid}`)
      .set('Cookie', session)
      .set('Accept', 'application/json')
      .send({
        archived: false,
        ...data.newMilestone(),
      });

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body.archivedAt).null;
  });

  it('simulates milestone worker', async () => {
    const milestones = await tenantDb('tags').where({
      systemType: 'milestone',
    });
    await updateMilestoneState(
      {
        milestoneUids: milestones.map((m) => m.uid),
        ownerType: 'user',
        ownerUid: user.uid,
      },
      db,
    );
  });

  it('should return a list of milestones', async () => {
    const res = await request(app)
      .get(`${basePath}`)
      .query({ status: 21 })
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body).haveOwnProperty('items');
    for (const m of res.body.items) {
      expect(m).haveOwnProperty('name');
      expect(m).haveOwnProperty('status');
      expect(m).haveOwnProperty('startDate');
      expect(m).haveOwnProperty('dueAt');
      expect(m).haveOwnProperty('progress');
      expect(m).haveOwnProperty('archivedAt');
      expect(m).haveOwnProperty('uid');
      expect(m).haveOwnProperty('tags');
    }
  });
});

describe('Count Milestones', () => {
  it('returns the number of active and archived milestones', async () => {
    await tenantDb.transaction(async (trx) => {
      const archive = [];
      for (let i = 0; i < 10; i++) {
        const mstone = await dbHelpers.createMilestone(trx, projectUid);
        if (i % 2 === 1) archive.push(mstone.uid);
      }

      await trx('tags')
        .whereIn('uid', archive)
        .update({ archivedAt: new Date() });
    });

    const res = await request(app)
      .get(
        `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${projectKey}/entities/count`,
      )
      .query({ entityType: 'milestone' })
      .set('Cookie', session);
    expect(res.status).eq(200);
    expect(res.body).haveOwnProperty('active');
    expect(res.body).haveOwnProperty('archived');
    expect(res.body.archived).greaterThanOrEqual(5);
    expect(res.body.active).greaterThanOrEqual(5);
  });
});
