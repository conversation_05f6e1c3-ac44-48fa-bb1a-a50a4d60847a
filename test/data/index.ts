import { CustomFieldTypes } from '../../src/models/customField';
import { faker } from '@faker-js/faker';
import { v4 as uuidv4 } from 'uuid';
import { encryptObject } from '../utils/encrypt';
import { PublicKey } from '@ss-libs/ss-component-encryption';

export const newUser = () => ({
  firstName: faker.person.firstName(),
  lastName: faker.person.lastName(),
  handle: faker.string.alphanumeric({
    casing: 'lower',
    length: { min: 5, max: 10 },
  }),
  email: faker.internet.email().toLowerCase(),
  password: faker.internet.password(),
  last_sign_in_ip: faker.internet.ip(),
});

export const newOrg = () => ({
  name: faker.company.name(),
  handle: faker.string.alphanumeric({
    casing: 'lower',
    length: { min: 5, max: 10 },
  }),
});

export const newProject = () => ({
  externalId: faker.string.alphanumeric(10),
  customFields: { version: '2.0', platform: 'app' },
  source: faker.string.alpha(),
  name: faker.lorem.word(),
  key: generateProjectKey(),
});

export const getSteps = () => {
  const steps: Array<any> = [
    {
      id: uuidv4(),
      title: 'Navigate to Login Page',
      description: 'Test Step Description #1',
      expectedResult: 'Test Expected Result #1',
    },
    {
      id: uuidv4(),
      title: 'Navigate to User Profile Page',
      description: 'Test Step Description #2',
      expectedResult: 'Test Expected Result #2',
    },
  ];

  return steps;
};

export const newCase = () => ({
  externalId: faker.string.alphanumeric(10),
  customFields: { version: '2.0', platform: 'app' },
  source: faker.string.alpha(),
  name: faker.lorem.word(),
});

export const newBranch = () => ({
  externalId: faker.string.alphanumeric(10),
  customFields: {
    [faker.lorem.word()]: faker.word.adjective(),
    [faker.lorem.word()]: faker.word.adjective(),
    [faker.lorem.word()]: faker.word.adjective(),
  },
  source: faker.string.alpha(),
  name: faker.lorem.word(),
});

export const newFolder = () => ({
  name: faker.lorem.word(),
  externalId: faker.string.alphanumeric(10),
  source: faker.string.alpha(),
});

export const newStep = () => ({
  id: faker.string.alphanumeric(1),
  title: faker.lorem.lines(1),
  description: faker.lorem.lines(1),
  expectedResult: faker.lorem.lines(1),
});

export const newMilestone = () => ({
  name: faker.lorem.words(),
  description: faker.lorem.lines(1),
  startDate: faker.date.past(),
  dueAt: faker.date.recent(),
});

export const newRun = (includeCustomFields: boolean = true) => ({
  externalId: faker.string.alphanumeric(10),
  ...(includeCustomFields
    ? {
        customFields: {
          [faker.lorem.word()]: faker.word.adjective(),
          [faker.lorem.word()]: faker.word.adjective(),
          [faker.lorem.word()]: faker.word.adjective(),
        },
      }
    : {}),
  source: faker.string.alpha(),
  name: faker.lorem.word(),
});

export const newPlan = () =>
  ({
    name: faker.lorem.word(),
    description: faker.lorem.lines(1),
    status: faker.number.int(100),
    priority: faker.number.int(100),
  }) as any;

export const newTag = () => ({
  name: `#${faker.lorem.word()}`,
  description: faker.lorem.lines(1),
});

export const newIntegration = () => ({
  name: faker.lorem.word(),
  description: faker.lorem.lines(1),
});

export const newIntegrationToken = () => ({
  url: faker.internet.url(),
  accessToken: faker.string.alphanumeric(15),
  refreshToken: faker.string.alphanumeric(15),
  expiresAt: faker.date.future(),
});

export const newCustomField = (type: CustomFieldTypes) => ({
  name: faker.lorem.word(),
  type,
  options: [
    faker.word.adjective(),
    faker.word.adjective(),
    faker.word.adjective(),
  ],
  source: faker.string.alpha(),
});

export const newSSOConfig = async (publicKey: PublicKey) => {
  const { encryptedFields, symmetricKeyData } = encryptObject({
    key: faker.internet.password(),
    secret: faker.internet.password(),
  }, publicKey);
  return [
    {
      oidc: {
        url: faker.internet.url(),
        allowedOrigins: [faker.internet.domainName()],
        allowOnlyInvitedAccounts: true,
        defaultRole: 'member',
        groupMappings: {},
        ...encryptedFields,
      },
    },
    symmetricKeyData];
};


export function generateProjectKey(length?: number): string {
  const keyLength = length || faker.number.int({ min: 2, max: 10 });

  return faker.string.alpha({
    length: keyLength,
    casing: 'mixed',
  });
}
