import { Application } from 'express';
import { DBServer } from '../src/models/dbServer';
import { OpenFgaClient } from '@openfga/sdk';
import { buildApp } from '../src/app';
import { closeQueues } from '../src/lib/queue';
import dayjs from 'dayjs';
import env from '../src/config/env';
import { expect } from 'chai';
import { faker } from '@faker-js/faker';
import { newUser } from './utils/tenant';
import request from 'supertest';

import { setupDB } from '../src/config/db';
import { setupAccount } from '../src/temporal/activities/account';
import { tenantManager } from '../src/lib/tenants';

let app: Application;
const db = setupDB();

beforeAll(async () => {
  app = buildApp(db, []);
});

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

describe('Auth', function () {
  const resetToken = null;
  const userDto = newUser();
  let user;

  it('signup request validation error test', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/signup`)
      .set('Accept', 'application/json')
      .send();
    expect(res.status).to.eq(422);
  });

  it('signup new user', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/signup`)
      .set('Accept', 'application/json')
      .send(userDto);
    expect(res.statusCode).eq(200);
    user = res.body.user;
  });

  it('ensures user db and openfga store is created', async () => {
    await setupAccount({ ownerType: 'user', ownerUid: user.uid }, db);

    const tenant = await db('tenants')
      .select('tenants.*', 'dbServers.*')
      .leftJoin('dbServers', 'tenants.dbServerUid', 'dbServers.uid')
      .where({ 'tenants.tenantType': 'user', 'tenants.tenantUid': user.uid })
      .first();

    expect(tenant).not.null;
    expect(!!tenant.openfgaAuthModelId).eq(true);
    expect(!!tenant.openfgaStoreId).eq(true);

    let dbSetup = true;
    try {
      setupDB(tenant.tenantUid, {
        host: tenant.host,
        username: tenant.username,
        password: tenant.password,
        port: tenant.port,
      } as DBServer);
    } catch (err) {
      dbSetup = false;
    }
    expect(dbSetup).eq(true);

    const fgaClient = new OpenFgaClient({
      apiHost: env.OPENFGA_DATASTORE_HOST,
      apiScheme: env.OPENFGA_DATASTORE_API_SCHEME,
      storeId: tenant.openfgaStoreId,
      authorizationModelId: tenant.openfgaAuthModelId,
    });
    const store = await fgaClient.getStore();
    expect(store.name).eq(tenant.tenantUid);
  });

  it('forgot password request test', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/forgot-password`)
      .set('Accept', 'application/json')
      .send({
        email: user.email,
      });
    expect(res.status).to.eq(200);
  });

  it('signup request error: email in use', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/signup`)
      .set('Accept', 'application/json')
      .send({ ...newUser(), email: user.email });
    expect(res.status).to.be.oneOf([422, 409]);
  });

  it('signup request error: username in use', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/signup`)
      .set('Accept', 'application/json')
      .send({ ...newUser(), handle: user.handle });
    expect(res.status).to.be.oneOf([409]);
  });
  it('should sign in a user', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/signin`)
      .set('Accept', 'application/json')
      .send(userDto);
    expect(res.statusCode).to.be.oneOf([200]);
  });

  it('forgot password link expired test', async () => {
    await db('users')
      .where({ email: user.email })
      .update({ passwordResetAt: dayjs().subtract(1, 'day').toDate() });

    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/forgot-password`)
      .set('Accept', 'application/json')
      .send({ email: user.email });
    expect(res.status).to.eq(200);
  });

  it('login request validation error', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/signin`)
      .send({
        email: '',
        password: '',
      });
    expect(res.status).to.eq(422);
  });

  it('login request password is not correct', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/signin`)
      .send({
        email: user.email,
        password: 'fdaf',
      });
    expect(res.status).to.eq(401);
  });

  it('login request email unregistered', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/signin`)
      .send({
        email: faker.internet.email(),
        password: faker.internet.password(),
      });
    expect(res.status).to.eq(401);
  });

  it('forgot password send link too many attempts', async () => {
    await db('users').where({ email: user.email }).update({
      passwordResetAt: dayjs().toDate(),
      passwordResetAttemptsCount: 3,
    });

    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/forgot-password`)
      .set('Accept', 'application/json')
      .send({ email: user.email });
    expect(res.status).to.eq(429);
  });

  it('forgot password send link success', async () => {
    await db('users').where({ email: user.email }).update({
      passwordResetAt: dayjs().toDate(),
      passwordResetAttemptsCount: 1,
    });
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/forgot-password`)
      .set('Accept', 'application/json')
      .send({ email: user.email });
    expect(res.status).to.eq(200);
  });

  it('forgot password email not exist error test', async () => {
    const res2 = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/forgot-password`)
      .set('Accept', 'application/json')
      .send({ email: '<EMAIL>' });
    expect(res2.status).to.eq(401);
  });

  it('reset password  test', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/reset-password`)
      .set('Accept', 'application/json')
      .send({
        token: resetToken,
        password: '123456789',
      });
    expect(res.status).to.eq(422);
  });
  it('reset password failed test', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/reset-password`)
      .set('Accept', 'application/json')
      .send({
        token: 'token',
        password: 'fdsfaf34324',
      });
    expect(res.status).to.eq(422);
  });
});
