import { newOrg, setupUserTenant } from './utils/tenant';
import request, { Response } from 'supertest';

import { Application } from 'express';
import { buildApp } from '../src/app';
import { closeQueues } from '../src/lib/queue';
import { expect } from 'chai';
import { faker } from '@faker-js/faker';
import handleRoutes from '../src/routes/internal/handle';
import internalHandleRoutes from '../src/routes/internal/handle';
import orgRoutes from '../src/routes/internal/org';

import { setupDB } from '../src/config/db';
import { setupAccount } from '../src/temporal/activities/account';
import { tenantManager } from '../src/lib/tenants';

let app: Application;
let user, handle: string, password: string, orgHandle: string;

const db = setupDB();

beforeAll(async () => {
  app = buildApp(db, [orgRoutes, handleRoutes, internalHandleRoutes]);

  const tenant = await setupUserTenant(db, app);
  user = tenant.user;
  password = tenant.password;
  handle = tenant.handle;
});

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

describe('handle', () => {
  let signin: Response;

  it('should sign in a user', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/signIn`)
      .set('Accept', 'application/json')
      .send({ email: user.email, password });
    expect(res.statusCode).to.be.oneOf([200]);
    signin = res;
  });

  it('should create a new organization', async () => {
    const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/orgs`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send(newOrg());
    expect(res.statusCode).to.be.oneOf([200]);
    orgHandle = res.body.handle;
    await setupAccount(
      {
        ownerType: 'org',
        ownerUid: res.body.uid,
        dbServerUid: res.body.dbServerUid,
      },
      db,
    );
  });

  it('should return 409 or 400 if create the new handle', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/handle/${handle}`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie']);
    expect(res.statusCode).to.be.oneOf([400, 409]);
  });

  it("should return 200 if handle doesn't exist", async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/handle/uavilab_handle`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie']);
    expect(res.statusCode).to.equal(200); // Fixed comparison
    expect(res.body.handleInUse).to.equal(false);
  });

  it('gets the handle in session', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/handle/${handle}/profile`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie']);
    expect(res.statusCode).to.be.oneOf([200]);

    expect(res.body).haveOwnProperty('handle');
    expect(res.body).haveOwnProperty('setupStatus');
    expect(res.body.setupStatus).eq('completed');
  });

  it('should return 409 if new handle name is already in use', async () => {
    const res = await request(app)
      .patch(`${process.env.API_INTERNAL_ROUTE}/handle/${orgHandle}`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send({
        name: handle,
      });
    expect(res.statusCode).eq(409);
  });

  it('should return 200 if handle was updated', async () => {
    const newName = faker.string.alphanumeric({
      casing: 'lower',
      length: { min: 5, max: 10 },
    });
    const res = await request(app)
      .patch(`${process.env.API_INTERNAL_ROUTE}/handle/${orgHandle}`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send({
        name: newName,
      });
    expect(res.statusCode).eq(200);
    orgHandle = newName;
  });

  it('should return 404 if handle is not found', async () => {
    const res = await request(app)
      .patch(`${process.env.API_INTERNAL_ROUTE}/handle/${orgHandle}_invalid`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send({
        name: 'hand_update_test_new',
      });
    expect(res.statusCode).to.be.oneOf([404]);
  });

  it("should fetch a handle's preferences", async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${orgHandle}/preferences`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie']);
    expect(res.statusCode).to.be.oneOf([200]);

    for (const field of ['statusColors', 'priorityColors'])
      expect(res.body).haveOwnProperty(field);
  });

  it("should update a handle's preferences", async () => {
    const res = await request(app)
      .patch(`${process.env.API_INTERNAL_ROUTE}/${orgHandle}/preferences`)
      .set('Accept', 'application/json')
      .set('Cookie', signin.header['set-cookie'])
      .send({ preferences: { statusColors: [], priorityColors: [] } });
    expect(res.statusCode).to.be.oneOf([200]);
    for (const field of ['statusColors', 'priorityColors'])
      expect(res.body).haveOwnProperty(field);
  });
});
