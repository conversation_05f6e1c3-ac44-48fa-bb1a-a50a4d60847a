import { Application } from 'express';
import { buildApp } from '../src/app';
import { closeQueues } from '../src/lib/queue';
import request from 'supertest';
import { setupDB } from '../src/config/db';
import { setupUserTenant } from './utils/tenant';
import { tenantManager } from '../src/lib/tenants';
import workspaceRoutes from '../src/routes/internal/workspace';
import dayjs from 'dayjs';

let app: Application;
let user, password: string, handle: string, session, userId, dbServer;

const db = setupDB();

beforeAll(async () => {
  app = buildApp(db, [workspaceRoutes]);

  const tenant = await setupUserTenant(db, app);
  user = tenant.user;
  password = tenant.password;
  handle = tenant.handle;
  dbServer = tenant.dbServer;

  const res = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signIn`)
    .set('Accept', 'application/json')
    .send({ email: user.email, password });

  session = res.headers['set-cookie'];
  userId = res.body.user.uid;
});

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

describe('Workspace', () => {
  let testProject;
  let testMilestone;
  let testPlan;
  let testRun;
  
  let queryParams;

  const setupTestData = async () => {
    const tenantDB = setupDB(userId, dbServer);

    const [project] = await tenantDB('projects')
      .insert({
        name: 'Test Project',
        key: 'TEST' + Math.floor(Math.random() * 10000),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .returning('uid');

    const [milestone] = await tenantDB('tags')
      .insert({
        name: 'Test Milestone',
        systemType: 'milestone',
        projectUid: project.uid,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .returning('uid');

    const [plan] = await tenantDB('tags')
      .insert({
        name: 'Test Plan',
        systemType: 'plan',
        projectUid: project.uid,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .returning('uid');

    const [run] = await tenantDB('testRuns')
      .insert({
        name: 'Test Run',
        projectUid: project.uid,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .returning('uid');

    await tenantDB('testMilestoneRuns').insert({
      milestoneUid: milestone.uid,
      runUid: run.uid,
    });

    await tenantDB('testPlanRuns').insert({
      planUid: plan.uid,
      runUid: run.uid,
    });

    const [testCase] = await tenantDB('testCases')
      .insert({
        name: 'Test Case',
        projectUid: project.uid,
        externalId: 'TEST-' + Math.floor(Math.random() * 10000),
        source: 'local',
        version: 1,
        active: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        testCaseRef: 1,
      })
      .returning('uid');

    const [execution] = await tenantDB('testExecutions')
      .insert({
        testCaseUid: testCase.uid,
        testRunUid: run.uid,
        projectUid: project.uid,
        assignedTo: userId,
        status: 1,
        priority: 1,
        lastAssignedAt: new Date().toISOString(),
        dueAt: dayjs().add(7, 'days').toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .returning('uid');

    return {
      projectId: project.uid,
      milestoneId: milestone.uid,
      planId: plan.uid,
      runId: run.uid,
      caseId: testCase.uid,
      executionId: execution.uid,
    };
  };

  beforeAll(async () => {
    const testData = await setupTestData();
    testProject = testData.projectId;
    testMilestone = testData.milestoneId;
    testPlan = testData.planId;
    testRun = testData.runId;

    queryParams = {
      'projectUids[]': [testProject],
      'runUids[]': [testRun],
      'milestoneUids[]': [testMilestone],
      'planUids[]': [testPlan],
      'userUids[]': [userId],
      'priorities[]': [1],
      'status[]': [1],
      'tags[]': [],
      limit: 10,
      offset: 0,
    };
  });


  it('should be able to get workspace executions', async () => {
    const res = await request(app)
      .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/workspace/executions`)
      .query(queryParams)
      .set('Accept', 'application/json')
      .set('Cookie', session);
    expect(res.statusCode).toBe(200);

    expect(res.body).toHaveProperty('items');
    expect(res.body).toHaveProperty('count');
  });
});
