import * as data from './data/index';

import { Application } from 'express';
import { buildApp } from '../src/app';
import { closeQueues } from '../src/lib/queue';
import { expect } from 'chai';
import { faker } from '@faker-js/faker';
import folderRoutes from '../src/routes/internal/folder';
import { newFolder } from './data/index';
import orgRoutes from '../src/routes/internal/org';
import projectRoutes from '../src/routes/internal/project';
import request from 'supertest';
import { setupDB } from '../src/config/db';
import { setupOrgTenant } from './utils/tenant';
import { tenantManager } from '../src/lib/tenants';

let app: Application,
  session: any,
  basePath: string,
  projectKey: string,
  rootFolder: any;

const db = setupDB();
beforeAll(async () => {
  app = buildApp(db, [orgRoutes, projectRoutes, folderRoutes]);

  const tenant = await setupOrgTenant(db, app);
  const user = tenant.admin;
  const password = tenant.password;
  const handle = tenant.handle;

  const signin = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signin`)
    .send({ email: user.email, password });
  session = signin.headers['set-cookie'];

  // create default project and retrieve it's latest folder
  const createProject = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
    .set('Cookie', session)
    .send(data.newProject());
  projectKey = createProject.body.key;

  basePath = `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${projectKey}/folders`;

  const projectFolders = await request(app)
    .get(basePath)
    .set('Cookie', session)
    .send();
  rootFolder = projectFolders.body.folders[0];
});

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

describe('Folder', () => {
  let testFolderId: string | null = null;

  it('should not pass create test folders when empty body', async () => {
    const res = await request(app)
      .post(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({});

    expect(res.statusCode).to.be.oneOf([400]);
  });

  it('should create a test folder', async () => {
    const res = await request(app)
      .post(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({ parentId: rootFolder.uid, ...newFolder() });
    expect(res.statusCode).to.be.oneOf([200]);
    testFolderId = res.body.uid;
  });

  it('should create similar folder but in another directory', async () => {
    const res = await request(app)
      .post(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        ...newFolder(),
        parentId: rootFolder.uid,
        name: rootFolder.name,
      });
    expect(res.statusCode).eq(200);
  });

  it('should fail to create similar folder in same directory', async () => {
    const res = await request(app)
      .post(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        ...newFolder(),
        parentId: rootFolder.uid,
        name: rootFolder.name,
      });
    expect(res.statusCode).eq(409);
  });

  it('should be able to get list test folders', async () => {
    const res = await request(app)
      .get(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session);
    expect(res.statusCode).to.be.oneOf([200]);
  });

  it('should be able to get search test folders', async () => {
    const res = await request(app)
      .get(`${basePath}/search?query=123123`)
      .set('Accept', 'application/json')
      .set('Cookie', session);
    expect(res.statusCode).to.be.oneOf([200]);
  });

  it('should be able to get a test folder by id', async () => {
    const res = await request(app)
      .get(`${basePath}/${testFolderId}`)
      .set('Accept', 'application/json')
      .set('Cookie', session);
    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body).to.not.be.eq({});
  });

  it('should update a test folder', async () => {
    const name = faker.string.alphanumeric(10);
    const res = await request(app)
      .patch(`${basePath}/${testFolderId}`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({ name });

    expect(res.statusCode).eq(200);
    expect(res.body.uid).eq(testFolderId);
    expect(res.body.name).eq(name);
  });

  it('should avoid renaming folder name to an existing folder in same directory', async () => {
    let res = await request(app)
      .post(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({ parentId: rootFolder.uid, ...newFolder() });
    expect(res.statusCode).to.be.oneOf([200]);

    res = await request(app)
      .patch(`${basePath}/${res.body.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({ name: rootFolder.name });

    expect(res.statusCode).eq(409);
  });

  it('should be able to delete test folders', async () => {
    let res = await request(app)
      .delete(`${basePath}/${testFolderId}`)
      .set('Accept', 'application/json')
      .set('Cookie', session);
    expect(res.statusCode).to.be.oneOf([200]);

    res = await request(app)
      .get(`${basePath}/${testFolderId}`)
      .set('Accept', 'application/json')
      .set('Cookie', session);
    expect(res.statusCode).eq(404);
  });
});
