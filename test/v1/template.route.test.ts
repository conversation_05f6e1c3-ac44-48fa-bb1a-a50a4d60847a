import { TestContext, setupTestEnvironment } from '../utils/testContext';

import { closeQueues } from '../../src/lib/queue';
import { permissions } from '../../src/constants/auth';
import { randomUUID } from 'node:crypto';
import request from 'supertest';
import { tenantManager } from '../../src/lib/tenants';

let testContext: TestContext;

async function createTemplate(name: string) {
  const templateField = {
    id: randomUUID(),
    name: 'Field 1',
    dataType: 'text',
    defaultValue: 'default',
    value: 'field1',
  };

  const templateFromDb = await testContext
    .tenantDb('testTemplates')
    .insert({
      name,
      customFields: JSON.stringify({ templateFields: [templateField] }),
      createdBy: testContext.accessToken!.ownerUid,
      projectUid: testContext.projectUid,
    })
    .returning('*');

  return templateFromDb[0];
}

beforeAll(async () => {
  testContext = await setupTestEnvironment({
    primaryVersion: 'v1',
    permissions: [permissions.write_template, permissions.delete_template],
  });
});

afterAll(async () => {
  if (testContext) {
    await testContext.tenantDb.destroy();
    await testContext.db.destroy();
  }
  await closeQueues();
  await tenantManager.shutdown();
});

describe('External Api Create Template', () => {
  it('should create a new template', async () => {
    const templateData = {
      name: 'Test Template',
      templateFields: [
        {
          id: randomUUID(),
          name: 'Field 1',
          dataType: 'text',
          defaultValue: 'default',
          value: 'field1',
        },
      ],
    };

    const res = await request(testContext.app)
      .post(`${testContext.basePath.v1}/templates`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(templateData);

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('uid');
    expect(res.body.name).toBe(templateData.name);

    const template = await testContext
      .tenantDb('testTemplates')
      .where({ uid: res.body.uid })
      .first();

    expect(template).toBeTruthy();
    expect(template.name).toBe(templateData.name);
    expect(template.customFields).toEqual(
      expect.objectContaining({ templateFields: templateData.templateFields }),
    );
  });
});

describe('External Api Get Templates', () => {
  it('should return list of templates', async () => {
    const queryParams = {
      limit: 10,
      offset: 0,
    };
    const res = await request(testContext.app)
      .get(`${testContext.basePath.v1}/templates`)
      .query(queryParams)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res.statusCode).toBe(200);
    expect(Array.isArray(res.body.items)).toBe(true);
    expect(res.body.items.length).toBeGreaterThan(0);

    const templates = await testContext
      .tenantDb('testTemplates')
      .where({ projectUid: testContext.projectUid })
      .limit(queryParams.limit)
      .offset(queryParams.offset);

    expect(templates.length).toBe(res.body.items.length);
    expect(templates.some((t) => t.name === 'Test Template')).toBe(true);
  });
});

describe('Get Template by ID', () => {
  it('should get template by ID', async () => {
    const templateFromDb = await createTemplate('Test Template 2');

    const res = await request(testContext.app)
      .get(`${testContext.basePath.v1}/templates/${templateFromDb.uid}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res.statusCode).toBe(200);
    expect(res.body.uid).toBe(templateFromDb.uid);
    expect(res.body.name).toBe('Test Template 2');
    expect(res.body.customFields.templateFields).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          id: templateFromDb.customFields.templateFields[0].id,
          name: templateFromDb.customFields.templateFields[0].name,
          dataType: templateFromDb.customFields.templateFields[0].dataType,
          defaultValue:
            templateFromDb.customFields.templateFields[0].defaultValue,
          value: templateFromDb.customFields.templateFields[0].value,
        }),
      ]),
    );
  });
});

describe('Update Template', () => {
  it('should update a template', async () => {
    const createdTemplate = await createTemplate('Test Template 3');
    const updateData = {
      name: 'Updated Template Name',
      templateFields: [
        {
          id: randomUUID(),
          name: 'New Field 1',
          dataType: 'text',
          defaultValue: 'new default value',
          value: 'field1 updated',
        },
      ],
    };

    const res = await request(testContext.app)
      .patch(`${testContext.basePath.v1}/templates/${createdTemplate.uid}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(updateData);

    expect(res.statusCode).toBe(200);
    expect(res.body.name).toBe(updateData.name);

    const template = await testContext
      .tenantDb('testTemplates')
      .where({ uid: createdTemplate.uid })
      .first();

    expect(template).toBeTruthy();
    expect(template.name).toBe(res.body.name);
    expect(template.customFields.templateFields[0]).toEqual(
      expect.objectContaining({
        id: updateData.templateFields[0].id,
        name: updateData.templateFields[0].name,
        dataType: updateData.templateFields[0].dataType,
        defaultValue: updateData.templateFields[0].defaultValue,
        value: updateData.templateFields[0].value,
      }),
    );
  });
});

describe('Delete Template', () => {
  it('should delete a template', async () => {
    const templateFromDb = await createTemplate('Test Template 4');
    const res = await request(testContext.app)
      .delete(`${testContext.basePath.v1}/templates/${templateFromDb.uid}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res.statusCode).toBe(200);
    expect(res.body.message).toBe('Template deleted successfully');

    const template = await testContext
      .tenantDb('testTemplates')
      .whereNull('deletedAt')
      .where({ uid: templateFromDb.uid })
      .first();

    expect(template).toBeFalsy();
  });
});
