import { TestContext, setupTestEnvironment } from '../utils/testContext';

import { closeQueues } from '../../src/lib/queue';
import { newMilestone } from '../data';
import { permissions } from '../../src/constants/auth';
import request from 'supertest';
import { tenantManager } from '../../src/lib/tenants';

let testContext: TestContext;

beforeAll(async () => {
  testContext = await setupTestEnvironment({
    primaryVersion: 'v1',
    permissions: [
      permissions.read_entity,
      permissions.write_entity,
      permissions.delete_entity,
    ],
  });
});

afterAll(async () => {
  if (testContext) {
    await testContext.tenantDb.destroy();
    await testContext.db.destroy();
  }
  await closeQueues();
  await tenantManager.shutdown();
});

describe('External Api Create Milestone', () => {
  it('should create a new milestone', async () => {
    const milestoneData = newMilestone();
    const res = await request(testContext.app)
      .post(`${testContext.basePath.v1}/milestones`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send({
        ...milestoneData,
        status: 1,
      });

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('uid');
    expect(res.body.name).toBe(milestoneData.name);

    const milestone = await testContext
      .tenantDb('tags')
      .where({ uid: res.body.uid, systemType: 'milestone' })
      .first();

    expect(milestone).toBeTruthy();
    expect(milestone.name).toBe(milestoneData.name);
    expect(milestone.projectUid).toBe(res.body.projectUid);
  });
});

describe('External Api Get Milestones', () => {
  it('should get milestones', async () => {
    const queryParams = {
      limit: 10,
      offset: 0,
    };
    const res = await request(testContext.app)
      .get(`${testContext.basePath.v1}/milestones`)
      .query(queryParams)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res.statusCode).toBe(200);
    expect(Array.isArray(res.body.items)).toBe(true);
    expect(res.body.items.length).toBeGreaterThan(0);

    const milestones = await testContext
      .tenantDb('tags')
      .where({ systemType: 'milestone' })
      .where({ projectUid: testContext.projectUid })
      .limit(queryParams.limit)
      .offset(queryParams.offset);

    expect(milestones.length).toBe(res.body.items.length);
    expect(milestones.some((m) => m.name === res.body.items[0].name)).toBe(
      true,
    );
  });
});

describe('External Api Get Milestone by ID', () => {
  it('should get milestone by id', async () => {
    const milestoneData = newMilestone();

    let res = await request(testContext.app)
      .post(`${testContext.basePath.v1}/milestones`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send({
        ...milestoneData,
        status: 1,
      });

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('uid');
    expect(res.body.name).toBe(milestoneData.name);

    const milestoneId = res.body.uid;

    res = await request(testContext.app)
      .get(`${testContext.basePath.v1}/milestones/${milestoneId}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res.statusCode).toBe(200);
    expect(res.body.uid).toBe(milestoneId);
    expect(res.body.name).toBe(milestoneData.name);

    const milestone = await testContext
      .tenantDb('tags')
      .whereNull('deletedAt')
      .where({ uid: milestoneId, systemType: 'milestone' })
      .first();

    expect(milestone).toBeTruthy();
    expect(milestone.name).toBe(res.body.name);
    expect(milestone.description).toBe(res.body.description);
    expect(milestone.customFields.dueAt).toBe(res.body.customFields.dueAt);
    expect(milestone.customFields.status).toBe(res.body.customFields.status);
    expect(milestone.customFields.startDate).toBe(
      res.body.customFields.startDate,
    );
  });
});

describe('External Api Update Milestone', () => {
  it('should update a milestone', async () => {
    const milestoneData = newMilestone();
    const res = await request(testContext.app)
      .post(`${testContext.basePath.v1}/milestones`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send({
        ...milestoneData,
        status: 1,
      });

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('uid');
    const milestoneId = res.body.uid;

    const updateData = {
      name: 'Updated Milestone Name',
      dueAt: new Date().toISOString(),
      status: 2,
      description: 'Updated description',
      startDate: new Date().toISOString(),
    };

    const updateRes = await request(testContext.app)
      .patch(`${testContext.basePath.v1}/milestones/${milestoneId}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(updateData);

    expect(updateRes.statusCode).toBe(200);
    expect(updateRes.body.uid).toBe(milestoneId);
    expect(updateRes.body.name).toBe(updateData.name);
    expect(updateRes.body.description).toBe(updateData.description);
    expect(updateRes.body.customFields.dueAt).toBe(updateData.dueAt);
    expect(updateRes.body.customFields.status).toBe(updateData.status);
    expect(updateRes.body.customFields.startDate).toBe(updateData.startDate);

    const updatedMilestone = await testContext
      .tenantDb('tags')
      .whereNull('deletedAt')
      .where({ uid: milestoneId, systemType: 'milestone' })
      .first();

    expect(updatedMilestone.name).toBe(updateData.name);
    expect(updatedMilestone.description).toBe(updateData.description);
    expect(updatedMilestone.customFields.dueAt).toBe(updateData.dueAt);
    expect(updatedMilestone.customFields.status).toBe(updateData.status);
    expect(updatedMilestone.customFields.startDate).toBe(updateData.startDate);
  });
});

describe('External Api Delete Milestone', () => {
  it('should delete a milestone', async () => {
    const milestoneData = newMilestone();
    const res = await request(testContext.app)
      .post(`${testContext.basePath.v1}/milestones`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send({
        ...milestoneData,
        status: 1,
      });

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('uid');

    const milestoneId = res.body.uid;
    const deleteRes = await request(testContext.app)
      .delete(`${testContext.basePath.v1}/milestones/${milestoneId}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(deleteRes.statusCode).toBe(200);
    expect(deleteRes.body.message).toBe('Milestone deleted successfully');

    const deletedMilestone = await testContext
      .tenantDb('tags')
      .whereNull('deletedAt')
      .where({ uid: milestoneId, systemType: 'milestone' })
      .first();

    expect(deletedMilestone).toBeFalsy();
  });
});
