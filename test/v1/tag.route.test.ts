import { TestContext, setupTestEnvironment } from '../utils/testContext';

import { closeQueues } from '../../src/lib/queue';
import { permissions } from '../../src/constants/auth';
import request from 'supertest';
import { tenantManager } from '../../src/lib/tenants';

let testContext: TestContext;
let basePath: string;

async function createTag(name: string, entityTypes: string[]) {
  const tagData = {
    name,
    description: 'Test Description',
    entityTypes,
    systemType: 'tag',
  };

  const tag = await testContext.tenantDb('tags').insert(tagData).returning('*');
  return tag[0];
}

beforeAll(async () => {
  testContext = await setupTestEnvironment({
    primaryVersion: 'v1',
    permissions: [permissions.write_tag, permissions.delete_tag],
  });
  basePath = `/v1/${testContext.tenant.user.handle}/tags`;
});

afterAll(async () => {
  if (testContext) {
    await testContext.tenantDb.destroy();
    await testContext.db.destroy();
  }
  await closeQueues();
  await tenantManager.shutdown();
});

beforeEach(async () => {
  await testContext.tenantDb('tags').del();
});

describe('External Api Create Tags', () => {
  it('should create a new tag', async () => {
    const tagData = {
      name: 'Test Tag',
      description: 'Test Description',
      entityTypes: ['runs', 'plans'],
    };

    const res = await request(testContext.app)
      .post(basePath)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(tagData);

    expect(res.statusCode).toBe(200);
    expect(res.body.name).toBe(tagData.name);
    expect(res.body.description).toBe(tagData.description);
    expect(res.body.entityTypes).toEqual(tagData.entityTypes);

    const tagFromDb = await testContext
      .tenantDb('tags')
      .where({ uid: res.body.uid })
      .first();

    expect(tagFromDb.name).toEqual(res.body.name);
    expect(tagFromDb.description).toEqual(res.body.description);
    expect(tagFromDb.entityTypes).toEqual(res.body.entityTypes);
  });

  it('should throw 422 when required fields are missing', async () => {
    const tagData = {
      description: 'Test Description',
    };
    const res = await request(testContext.app)
      .post(basePath)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(tagData);

    expect(res.statusCode).toBe(422);
  });
});

describe('External Api Get Tags', () => {
  it('should get tags', async () => {
    const tag = await createTag('Test Tag #1', ['runs', 'plans']);
    const queryParams = {
      limit: 10,
      offset: 0,
    };
    const res = await request(testContext.app)
      .get(basePath)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .query(queryParams);

    expect(res.statusCode).toBe(200);
    expect(Array.isArray(res.body.items)).toBe(true);
    expect(res.body.items.length).toBeGreaterThan(0);
    expect(res.body.items.at(0)?.name).toEqual(tag.name);
    expect(res.body.items.at(0)?.description).toEqual(tag.description);
    expect(res.body.items.at(0)?.entityTypes).toEqual(tag.entityTypes);
  });

  it('should get tags by entityType', async () => {
    const queryParams = {
      limit: 10,
      offset: 0,
      entityType: 'executions',
    };
    const tag = await createTag('Test Tag #2', ['executions']);
    await createTag('Test Tag #3', ['runs']);

    const res = await request(testContext.app)
      .get(`${basePath}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .query(queryParams);

    expect(res.statusCode).toBe(200);
    expect(Array.isArray(res.body.items)).toBe(true);
    expect(res.body.items.length).toBe(1);
    expect(res.body.items.at(0)?.name).toEqual(tag.name);
    expect(res.body.items.at(0)?.description).toEqual(tag.description);
    expect(res.body.items.at(0)?.entityTypes).toEqual(tag.entityTypes);
  });
});

describe('External Api Update Tags', () => {
  it('should update a tag', async () => {
    const tag = await createTag('Test Tag #1', ['runs', 'plans']);
    const updatedTagData = {
      name: 'Updated Tag #1',
      description: 'Updated Description',
      entityTypes: ['runs', 'executions'],
    };

    const res = await request(testContext.app)
      .patch(`${basePath}/${tag.uid}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(updatedTagData);

    expect(res.statusCode).toBe(200);
    expect(res.body.name).toBe(updatedTagData.name);
    expect(res.body.description).toBe(updatedTagData.description);
    expect(res.body.entityTypes).toEqual(updatedTagData.entityTypes);

    const tagFromDb = await testContext
      .tenantDb('tags')
      .whereNull('deletedAt')
      .where({ uid: tag.uid })
      .first();

    expect(tagFromDb.name).toEqual(res.body.name);
    expect(tagFromDb.description).toEqual(res.body.description);
    expect(tagFromDb.entityTypes).toEqual(res.body.entityTypes);
  });

  it('should archive a tag', async () => {
    const tag = await createTag('Test Tag #1', ['runs', 'plans']);

    const res = await request(testContext.app)
      .patch(`${basePath}/${tag.uid}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send({ archived: true });

    expect(res.statusCode).toBe(200);
    expect(res.body.archivedAt).not.toBeNull();

    const tagFromDb = await testContext
      .tenantDb('tags')
      .whereNull('deletedAt')
      .where({ uid: tag.uid })
      .first();

    expect(tagFromDb.archivedAt).not.toBeNull();
  });

  it('should unarchive a tag', async () => {
    const tag = await createTag('Test Tag #1', ['runs', 'plans']);

    const res = await request(testContext.app)
      .patch(`${basePath}/${tag.uid}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send({ archived: true });

    expect(res.statusCode).toBe(200);
    expect(res.body.archivedAt).not.toBeNull();

    const unarchiveRes = await request(testContext.app)
      .patch(`${basePath}/${tag.uid}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send({ archived: false });

    expect(unarchiveRes.statusCode).toBe(200);
    expect(unarchiveRes.body.archivedAt).toBeNull();

    const tagFromDb = await testContext
      .tenantDb('tags')
      .whereNull('deletedAt')
      .where({ uid: tag.uid })
      .first();

    expect(tagFromDb.archivedAt).toBeNull();
  });
});

describe('External Api Delete Tags', () => {
  it('should delete a tag', async () => {
    const tag = await createTag('Test Tag #1', ['runs', 'plans']);
    const res = await request(testContext.app)
      .delete(`${basePath}/${tag.uid}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    const tagFromDb = await testContext
      .tenantDb('tags')
      .whereNull('deletedAt')
      .where({ uid: tag.uid })
      .first();

    expect(res.statusCode).toBe(200);
    expect(tagFromDb).toBeFalsy();
  });
});
