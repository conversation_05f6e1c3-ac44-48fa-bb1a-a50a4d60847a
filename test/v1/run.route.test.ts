import * as data from '../data/index';
import * as dbHelpers from '../utils/dbHelpers';

import { TestContext, setupTestEnvironment } from '../utils/testContext';

import { TestMilestoneRun } from '../../src/models/testMilestoneRun';
import { closeQueues } from '../../src/lib/queue';
import { permissions } from '../../src/constants/auth';
import request from 'supertest';
import { tenantManager } from '../../src/lib/tenants';

let testContext: TestContext;

async function createRun() {
  let run;
  await testContext.tenantDb.transaction(async (trx) => {
    run = await dbHelpers.createRun(trx, testContext.projectUid);
  });
  return run;
}

beforeAll(async () => {
  testContext = await setupTestEnvironment({
    primaryVersion: 'v1',
    permissions: [
      permissions.read_entity,
      permissions.write_entity,
      permissions.read_activity,
    ],
  });
});

afterAll(async () => {
  if (testContext) {
    await testContext.tenantDb.destroy();
    await testContext.db.destroy();
  }
  await closeQueues();
  await tenantManager.shutdown();
});

describe('External Api Create Run', () => {
  it('should create a new run', async () => {
    const runData = data.newRun(false);
    const body = { ...runData, caseUids: [] };

    const res = await request(testContext.app)
      .post(`${testContext.basePath.v1}/runs`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(body);

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('uid');
    expect(res.body.name).toBe(runData.name);
    expect(res.body.externalId).toBe(runData.externalId);
    expect(res.body.source).toBe(runData.source);

    const runFromDb = await testContext
      .tenantDb('tags')
      .where({ uid: res.body.uid, systemType: 'run' })
      .first();

    expect(runFromDb).toBeTruthy();
    expect(runFromDb.name).toBe(res.body.name);
    expect(runFromDb.source).toBe(res.body.source);
    expect(runFromDb.externalId).toBe(res.body.externalId);
    expect(runFromDb.customFields).toEqual(res.body.customFields);
  });
});

describe('External Api Get Runs', () => {
  it('should return list of runs', async () => {
    await testContext.tenantDb.transaction(async (trx) => {
      const milestone = await dbHelpers.createMilestone(
        trx,
        testContext.projectUid,
      );
      // const plan = await dbHelpers.createPlan(trx, projectId);
      for (let i = 0; i < 5; i++) {
        const run = await dbHelpers.createRun(trx, testContext.projectUid);
        await TestMilestoneRun.query(trx).insert({
          runUid: run.uid,
          milestoneUid: milestone.uid,
        });
      }
    });
    const queryParams = {
      limit: 10,
      offset: 0,
    };
    const res = await request(testContext.app)
      .get(`${testContext.basePath.v1}/runs`)
      .query(queryParams)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res.statusCode).toBe(200);
    expect(Array.isArray(res.body.items)).toBe(true);
    expect(res.body.items.length).toBeGreaterThan(0);

    const runs = await testContext
      .tenantDb('tags')
      .where({ projectUid: testContext.projectUid, systemType: 'run' })
      .limit(queryParams.limit)
      .offset(queryParams.offset);

    expect(runs.length).toBe(res.body.items.length);
    expect(runs.some((t) => t.name === res.body.items[0].name)).toBe(true);
  });
});

describe('External Api Get Run by ID', () => {
  it('should get run by ID', async () => {
    const run = await createRun();

    const res = await request(testContext.app)
      .get(`${testContext.basePath.v1}/runs/${run.uid}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res.statusCode).toBe(200);
    expect(res.body.uid).toBe(run.uid);
    expect(res.body.externalId).toBe(run.externalId);
    expect(res.body.source).toBe(run.source);
    expect(res.body.name).toBe(run.name);

    const runFromDb = await testContext
      .tenantDb('tags')
      .where({ uid: run.uid })
      .first();

    expect(runFromDb.systemType).toBe('run');
    expect(runFromDb.name).toBe(res.body.name);
    expect(runFromDb.externalId).toBe(res.body.externalId);
    expect(runFromDb.source).toBe(res.body.source);
  });

  it('should return 404 when run not found', async () => {
    const res = await request(testContext.app)
      .get(`${testContext.basePath.v1}/runs/123`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res.statusCode).toBe(404);
  });
});

describe('External Api Update Run', () => {
  it('should update a run', async () => {
    const run = await createRun();

    const res = await request(testContext.app)
      .patch(`${testContext.basePath.v1}/runs/${run.uid}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send({
        name: 'Updated Run',
      });

    expect(res.statusCode).toBe(200);
    expect(res.body.uid).toBe(run.uid);

    const updatedRun = await testContext
      .tenantDb('tags')
      .where({ uid: res.body.uid })
      .first();

    expect(res.body.uid).toBe(updatedRun.uid);
    expect(res.body.name).toBe(updatedRun.name);
  });
});

describe('External Api Delete Run', () => {
  it('should delete a run', async () => {
    const run = await createRun();
    const res = await request(testContext.app)
      .delete(`${testContext.basePath.v1}/runs/${run.uid}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res.statusCode).toBe(200);
    expect(res.body.message).toBe('Test run deleted successfully');

    const deletedRun = await testContext
      .tenantDb('testRuns')
      .whereNull('deletedAt')
      .where({ uid: run.uid })
      .first();

    expect(deletedRun).toBeFalsy();
  });

  it('should return 404 when run not found', async () => {
    const res = await request(testContext.app)
      .delete(`${testContext.basePath.v1}/runs/123`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res.statusCode).toBe(404);
  });
});
