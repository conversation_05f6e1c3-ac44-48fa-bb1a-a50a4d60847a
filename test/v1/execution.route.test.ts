import { TestContext, setupTestEnvironment } from '../utils/testContext';
import { createCase, createExec, createRun } from '../utils/dbHelpers';

import { closeQueues } from '../../src/lib/queue';
import { permissions } from '../../src/constants/auth';
import request from 'supertest';
import { tenantManager } from '../../src/lib/tenants';

let testContext: TestContext;

beforeAll(async () => {
  testContext = await setupTestEnvironment({
    primaryVersion: 'v1',
    permissions: [permissions.read_activity, permissions.write_activity],
  });
});

afterAll(async () => {
  if (testContext) {
    await testContext.tenantDb.destroy();
    await testContext.db.destroy();
  }
  await closeQueues();
  await tenantManager.shutdown();
});

describe('External Api Create Execution', () => {
  it('should create a new execution', async () => {
    const executionData = {
      externalId: '12345',
      source: 'manual',
      status: 1,
      customFields: {
        name: 'Test Execution custom field',
      },
    };

    const res = await request(testContext.app)
      .post(`${testContext.basePath.v1}/executions`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(executionData);

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('uid');
    expect(res.body.externalId).toBe(executionData.externalId);
    expect(res.body.source).toBe(executionData.source);
    expect(res.body.status).toBe(executionData.status);
    expect(res.body.customFields.name).toBe(executionData.customFields.name);

    const execution = await testContext
      .tenantDb('testExecutions')
      .where({ uid: res.body.uid })
      .first();

    expect(execution).toBeTruthy();
    expect(execution.externalId).toBe(executionData.externalId);
    expect(execution.source).toBe(executionData.source);
    expect(execution.status).toBe(executionData.status);
    expect(execution.customFields.name).toBe(executionData.customFields.name);
  });
});

describe('External Api Get Executions', () => {
  it('should return list of executions', async () => {
    async function prepareDb() {
      const transaction = await testContext.tenantDb.transaction();
      const run = await createRun(transaction, testContext.projectUid);
      const caseRef = await createCase(transaction, testContext.projectUid);
      await createExec(
        transaction,
        testContext.projectUid,
        caseRef.uid,
        run.uid,
      );
      await transaction.commit();
    }

    await prepareDb();

    const queryParams = {
      limit: 10,
      offset: 0,
    };
    const res = await request(testContext.app)
      .get(`${testContext.basePath.v1}/executions`)
      .query(queryParams)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res.statusCode).toBe(200);
    expect(Array.isArray(res.body.items)).toBe(true);
    expect(res.body.items.length).toBeGreaterThan(0);

    const executions = await testContext
      .tenantDb('testExecutions')
      .where({ projectUid: testContext.projectUid })
      .limit(queryParams.limit)
      .offset(queryParams.offset);

    expect(executions.length).toBe(res.body.items.length);
    expect(
      executions.some((e) => e.externalId === res.body.items[0].externalId),
    ).toBe(true);
  });
});

describe('Get Execution by ID', () => {
  it('should get execution by ID', async () => {
    async function prepareDb() {
      const transaction = await testContext.tenantDb.transaction();
      const run = await createRun(transaction, testContext.projectUid);
      const caseRef = await createCase(transaction, testContext.projectUid);
      await createExec(
        transaction,
        testContext.projectUid,
        caseRef.uid,
        run.uid,
      );
      await transaction.commit();
    }

    await prepareDb();

    const executionFromDb = await testContext
      .tenantDb('testExecutions')
      .where({ projectUid: testContext.projectUid })
      .first();

    const res = await request(testContext.app)
      .get(`${testContext.basePath.v1}/executions/${executionFromDb.uid}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res.statusCode).toBe(200);
    expect(res.body.uid).toBe(executionFromDb.uid);
    expect(res.body.priority).toBe(executionFromDb.priority);
    expect(res.body.testCaseRef).toBe(executionFromDb.testCaseRef);
    expect(res.body.testRunUid).toBe(executionFromDb.testRunUid);
    expect(res.body.projectUid).toBe(executionFromDb.projectUid);
    expect(res.body.testCaseUid).toBe(executionFromDb.testCaseUid);
    expect(res.body.status).toBe(executionFromDb.status);
  });
});

describe('Update Execution', () => {
  it('should update a execution', async () => {
    async function prepareDb() {
      const transaction = await testContext.tenantDb.transaction();
      const run = await createRun(transaction, testContext.projectUid);
      const caseRef = await createCase(transaction, testContext.projectUid);
      const exec = await createExec(
        transaction,
        testContext.projectUid,
        caseRef.uid,
        run.uid,
      );
      await transaction.commit();
      return exec;
    }

    const executionFromDb = await prepareDb();

    const updateData = {
      status: 2,
      priority: 4,
      steps: [],
    };

    const res = await request(testContext.app)
      .patch(`${testContext.basePath.v1}/executions/${executionFromDb.uid}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(updateData);

    expect(res.statusCode).toBe(200);
    expect(res.body.status).toBe(updateData.status);
    expect(res.body.priority).toBe(updateData.priority);

    const execution = await testContext
      .tenantDb('testExecutions')
      .where({ uid: executionFromDb.uid })
      .first();

    expect(execution).toBeTruthy();
    expect(execution.status).toBe(res.body.status);
    expect(execution.priority).toBe(res.body.priority);
  });
});
