import request from 'supertest';
import * as data from '../data/index';
import { setupTestEnvironment, TestContext } from '../utils/testContext';
import { permissions } from '../../src/constants/auth';
import * as dbHelpers from '../utils/dbHelpers';
import { getNextId } from '../../src/lib/model';
import { faker } from '@faker-js/faker';
import dayjs from 'dayjs';
import { closeQueues } from '../../src/lib/queue';
import { tenantManager } from '../../src/lib/tenants';

let testContext: TestContext;

beforeAll(async () => {
  testContext = await setupTestEnvironment({
    primaryVersion: 'v1',
    permissions: [
      permissions.write_entity,
      permissions.delete_entity,
      permissions.read_entity,
    ],
  });
});

afterAll(async () => {
  if (testContext) {
    await testContext.tenantDb.destroy();
    await testContext.db.destroy();
  }
  await closeQueues();
  await tenantManager.shutdown();
});

describe('External Api Create Plan', () => {
  async function testCreate(dto) {
    const res = await request(testContext.app)
      .post(`${testContext.basePath.v1}/plans`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(dto);
    expect(res.statusCode).toBe(200);
    expect(res.body.systemType).toBe('plan');

    const plan = await testContext
      .tenantDb('tags')
      .where('uid', res.body.uid)
      .first('*');
    expect(plan).toBeDefined();
    expect(plan.systemType).toBe('plan');
    return { returned: res.body, saved: plan };
  }

  it('should create a basic plan', async () => {
    const planData = data.newPlan();

    const res = await request(testContext.app)
      .post(`${testContext.basePath.v1}/plans`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(planData);

    expect(res.statusCode).toBe(200);
    expect(res.body.systemType).toBe('plan');

    const plan = await testContext
      .tenantDb('tags')
      .where('uid', res.body.uid)
      .first('*');
    expect(plan).toBeDefined();
    expect(plan.systemType).toBe('plan');
  });

  it('creates a plan with milestones', async () => {
    const m = data.newMilestone();
    const [milestone] = await testContext
      .tenantDb('tags')
      .insert({
        name: m.name,
        description: m.description,
        customFields: { startDate: m.startDate, dueAt: m.dueAt },
        systemType: 'milestone',
      })
      .returning('*');

    const dto = data.newPlan();
    dto.milestoneUids = [milestone.uid];

    const { saved } = await testCreate(dto);

    const attachedMilestones = await testContext
      .tenantDb('testMilestonePlans')
      .where('milestoneUid', milestone.uid);
    expect(attachedMilestones.length).toBe(1);
    expect(attachedMilestones[0].planUid).toBe(saved.uid);
  });
});

describe('External Api Fetch Plan By ID', () => {
  it('fails on invalid plan id', async () => {
    let uid;
    await testContext.tenantDb.transaction(async (trx) => {
      uid = await getNextId(trx, <any>{ tableName: 'tags' });
    });

    const res = await request(testContext.app)
      .get(`${testContext.basePath.v1}/plans/${uid}`)
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });
    expect(res.statusCode).toBe(404);
  });

  it('fails if plan is deleted', async () => {
    let uid;
    await testContext.tenantDb.transaction(async (trx) => {
      const plan = await dbHelpers.createPlan(trx, testContext.projectUid, {
        deletedAt: faker.date.recent(),
      });
      uid = plan.uid;
    });

    const res = await request(testContext.app)
      .get(`${testContext.basePath.v1}/plans/${uid}`)
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });
    expect(res.statusCode).toBe(404);
  });

  it('retrieves a plan by its id', async () => {
    let plan;
    await testContext.tenantDb.transaction(async (trx) => {
      plan = await dbHelpers.createPlan(trx, testContext.projectUid);
      for (let i = 0; i < 5; i++)
        await dbHelpers.createRun(trx, testContext.projectUid, plan.uid);
    });

    const res = await request(testContext.app)
      .get(`${testContext.basePath.v1}/plans/${plan.uid}`)
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });
    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('runs');
  });
});

describe('External Api List Plans', () => {
  it('returns a list of plans', async () => {
    await testContext.tenantDb.transaction(async (trx) => {
      for (let i = 0; i < 10; i++) {
        await dbHelpers.createPlan(trx, testContext.projectUid);
      }
    });

    const queryParams = {
      minRunCount: 0,
      minCreatedAt: dayjs().subtract(1, 'hour').toISOString(),
      minProgress: 0,
      limit: 5,
      offset: 0,
      archived: false,
    };
    const res = await request(testContext.app)
      .get(`${testContext.basePath.v1}/plans`)
      .query(queryParams)
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });
    expect(res.statusCode).toBe(200);

    const savedPlans = await testContext
      .tenantDb('tags')
      .where('systemType', 'plan')
      .whereNull('deletedAt')
      .select('uid');
    const page = res.body;

    expect(page.count).toBe(savedPlans.length);
    expect(page.items.length).toEqual(queryParams.limit);

    for (const plan of page.items) expect(plan.systemType).toBe('plan');
  });
});
