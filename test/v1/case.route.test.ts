import * as data from '../data/index';

import { TestContext, setupTestEnvironment } from '../utils/testContext';

import { TestCaseStepItem } from '../../src/types/step';
import { closeQueues } from '../../src/lib/queue';
import { createCase } from '../utils/dbHelpers';
import { permissions } from '../../src/constants/auth';
import request from 'supertest';
import { tenantManager } from '../../src/lib/tenants';

let testContext: TestContext;

beforeAll(async () => {
  testContext = await setupTestEnvironment({
    primaryVersion: 'v1',
    permissions: [
      permissions.write_entity,
      permissions.delete_entity,
      permissions.read_entity,
    ],
  });
});

afterAll(async () => {
  if (testContext) {
    await testContext.tenantDb.destroy();
    await testContext.db.destroy();
  }
  await closeQueues();
  await tenantManager.shutdown();
});

describe('External Api Create Case', () => {
  it('should create a new case', async () => {
    const caseData = data.newCase();
    const steps: Array<TestCaseStepItem> = [
      {
        ...data.newStep(),
        children: [data.newStep()],
      },
      {
        ...data.newStep(),
        children: [data.newStep(), data.newStep()],
      },
    ];

    const rootFolderId = (
      await testContext
        .tenantDb('tags')
        .whereNull('parentUid')
        .whereNull('deletedAt')
        .where({ projectUid: testContext.projectUid, systemType: 'folder' })
        .first()
    )?.uid;

    const res = await request(testContext.app)
      .post(`${testContext.basePath.v1}/cases`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send([
        {
          ...caseData,
          parentId: rootFolderId,
          repoUID: 'test',
          projectId: testContext.projectUid.toString(),
          steps,
        },
      ]);

    expect(res.statusCode).toBe(200);
    expect(res.body[0]).toHaveProperty('uid');
    expect(res.body[0].name).toBe(caseData.name);
    expect(res.body[0].source).toBe(caseData.source);
    expect(res.body[0].externalId).toBe(caseData.externalId);

    const caseFromDb = await testContext
      .tenantDb('testCases')
      .where({ uid: res.body[0].uid })
      .first();

    expect(caseFromDb).toBeTruthy();
    expect(caseFromDb.name).toBe(caseData.name);
    expect(caseFromDb.source).toBe(caseData.source);
    expect(caseFromDb.externalId).toBe(caseData.externalId);
  });
});

describe('External Api Create Cases', () => {
  it('should create multiple cases', async () => {
    const steps: Array<TestCaseStepItem> = [
      {
        ...data.newStep(),
        children: [data.newStep()],
      },
      {
        ...data.newStep(),
        children: [data.newStep(), data.newStep()],
      },
    ];
    const rootFolderId = (
      await testContext
        .tenantDb('tags')
        .whereNull('parentUid')
        .whereNull('deletedAt')
        .where({ projectUid: testContext.projectUid, systemType: 'folder' })
        .first()
    )?.uid;
    const casesData = [
      {
        ...data.newCase(),
        repoUID: 'test',
        projectId: testContext.projectUid.toString(),
        steps,
        parentId: rootFolderId,
      },
      {
        ...data.newCase(),
        repoUID: 'test',
        projectId: testContext.projectUid.toString(),
        steps,
        parentId: rootFolderId,
      },
    ];

    const res = await request(testContext.app)
      .post(`${testContext.basePath.v1}/cases`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(casesData);

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveLength(2);
    expect(res.body[0]).toHaveProperty('uid');
    expect(res.body[1]).toHaveProperty('uid');
  });
});

describe('External Api Get Case by ID', () => {
  it('should get a case by id', async () => {
    async function prepareDb() {
      const trx = await testContext.tenantDb.transaction();
      const caseItem = await createCase(trx, testContext.projectUid);
      await trx.commit();
      return caseItem;
    }

    const caseFromDb = await prepareDb();
    const res = await request(testContext.app)
      .get(`${testContext.basePath.v1}/cases/${caseFromDb.uid}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res.body).toHaveProperty('uid');
    expect(res.body.source).toBe(caseFromDb.source);
    expect(res.body.externalId).toBe(caseFromDb.externalId);
    expect(res.body.name).toBe(caseFromDb.name);
    expect(res.statusCode).toBe(200);

    const caseFromDb2 = await testContext
      .tenantDb('testCases')
      .where({ uid: caseFromDb.uid })
      .first();

    expect(caseFromDb2.name).toBe(res.body.name);
    expect(caseFromDb2.externalId).toBe(res.body.externalId);
    expect(caseFromDb2.source).toBe(res.body.source);
    expect(caseFromDb2.uid).toBe(res.body.uid);
    expect(caseFromDb2.deletedAt).toBeNull();
  });
});

describe('External Api Delete Case', () => {
  it('should delete a case', async () => {
    async function prepareDb() {
      const trx = await testContext.tenantDb.transaction();
      const caseItem = await createCase(trx, testContext.projectUid);
      await trx.commit();
      return caseItem;
    }

    const caseFromDb = await prepareDb();

    const res = await request(testContext.app)
      .delete(`${testContext.basePath.v1}/cases/${caseFromDb.uid}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res.statusCode).toBe(200);

    const caseItem = await testContext
      .tenantDb('testCases')
      .whereNull('deletedAt')
      .where({ uid: caseFromDb.uid })
      .first();

    expect(caseItem).toBeFalsy();
  });
});
