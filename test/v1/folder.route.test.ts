import { TestContext, setupTestEnvironment } from '../utils/testContext';

import { closeQueues } from '../../src/lib/queue';
import { errors } from '../utils/errors';
import { newFolder } from '../data';
import { permissions } from '../../src/constants/auth';
import request from 'supertest';
import { tenantManager } from '../../src/lib/tenants';

let testContext: TestContext;
let rootFolderId;

beforeAll(async () => {
  testContext = await setupTestEnvironment({
    primaryVersion: 'v1',
    permissions: [
      permissions.read_entity,
      permissions.read_activity,
      permissions.write_entity,
      permissions.delete_entity,
    ],
  });
  rootFolderId = (
    await testContext
      .tenantDb('tags')
      .whereNull('parentUid')
      .whereNull('deletedAt')
      .where({ projectUid: testContext.projectUid, systemType: 'folder' })
      .first()
  )?.uid;
});

afterAll(async () => {
  if (testContext) {
    await testContext.tenantDb.destroy();
    await testContext.db.destroy();
  }
  await closeQueues();
  await tenantManager.shutdown();
});

describe('External Api Create Folder', () => {
  it('should create a new folder', async () => {
    const folderData = newFolder();

    const res = await request(testContext.app)
      .post(`${testContext.basePath.v1}/folders`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send({
        parentId: rootFolderId,
        ...folderData,
      });

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('uid');
    expect(res.body.name).toBe(folderData.name);

    const folder = await testContext
      .tenantDb('tags')
      .where({ uid: res.body.uid, systemType: 'folder' })
      .first();

    expect(folder).toBeTruthy();
    expect(folder.name).toBe(folderData.name);
    expect(folder.systemType).toBe('folder');
    expect(folder.projectUid).toBe(res.body.projectUid);
    expect(folder.parentUid).toBe(res.body.parentUid);
  });
});

describe('External Api Get Folders', () => {
  it('should get all folders', async () => {
    const queryParams = {
      limit: 10,
      offset: 0,
    };
    const res = await request(testContext.app)
      .get(`${testContext.basePath.v1}/folders`)
      .query(queryParams)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res.statusCode).toBe(200);
    expect(Array.isArray(res.body.items)).toBe(true);
    expect(res.body.items.length).toBeGreaterThan(0);

    const folders = await testContext
      .tenantDb('tags')
      .whereNull('deletedAt')
      .where({ projectUid: testContext.projectUid, systemType: 'folder' })
      .limit(queryParams.limit)
      .offset(queryParams.offset);

    expect(folders.length).toBe(res.body.items.length);
    expect(folders.some((f) => f.name === res.body.items[0].name)).toBe(true);
  });
});

describe('External Api Update Folder', () => {
  it('should update an existing folder', async () => {
    const folderData = newFolder();
    const res = await request(testContext.app)
      .post(`${testContext.basePath.v1}/folders`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send({
        parentId: rootFolderId,
        ...folderData,
      });

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('uid');
    expect(res.body.name).toBe(folderData.name);

    const folder = await testContext
      .tenantDb('tags')
      .where({ uid: res.body.uid, systemType: 'folder' })
      .first();

    expect(folder).toBeTruthy();
    expect(folder.name).toBe(folderData.name);
    expect(folder.systemType).toBe('folder');
    expect(folder.projectUid).toBe(res.body.projectUid);
    expect(folder.parentUid).toBe(res.body.parentUid);
  });
});

describe('External Api Delete Folder', () => {
  it('should not delete a root folder', async () => {
    const res = await request(testContext.app)
      .delete(`${testContext.basePath.v1}/folders/${rootFolderId}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res.statusCode).toBe(405);
    expect(res.body.message).toBe(errors.CANNOT_DELETE_ROOT_FOLDER);
  });

  it('should delete an existing folder', async () => {
    const folderData = newFolder();
    const res = await request(testContext.app)
      .post(`${testContext.basePath.v1}/folders`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send({
        parentId: rootFolderId,
        ...folderData,
      });

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('uid');
    expect(res.body.name).toBe(folderData.name);

    const folder = await testContext
      .tenantDb('tags')
      .where({ uid: res.body.uid, systemType: 'folder' })
      .whereNull('deletedAt')
      .first();

    expect(folder).toBeTruthy();
    expect(folder.name).toBe(folderData.name);
    expect(folder.systemType).toBe('folder');
    expect(folder.projectUid).toBe(res.body.projectUid);
    expect(folder.parentUid).toBe(res.body.parentUid);

    const res2 = await request(testContext.app)
      .delete(`${testContext.basePath.v1}/folders/${res.body.uid}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res2.statusCode).toBe(200);
    expect(res2.body.message).toBe('Folder deleted successfully');

    const folder2 = await testContext
      .tenantDb('tags')
      .whereNull('deletedAt')
      .where({ uid: res.body.uid, systemType: 'folder' })
      .first();

    expect(folder2).toBeFalsy();
  });
});
