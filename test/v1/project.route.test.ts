import { TestContext, setupTestEnvironment } from '../utils/testContext';

import { closeQueues } from '../../src/lib/queue';
import { permissions } from '../../src/constants/auth';
import request from 'supertest';
import { tenantManager } from '../../src/lib/tenants';

let testContext: TestContext;
let basePath: string;

beforeAll(async () => {
  testContext = await setupTestEnvironment({
    primaryVersion: 'v1',
    permissions: [permissions.write_project, permissions.delete_project],
  });
  basePath = `/v1/${testContext.tenant.user.handle}/projects`;
});

afterAll(async () => {
  if (testContext) {
    await testContext.tenantDb.destroy();
    await testContext.db.destroy();
  }
  await closeQueues();
  await tenantManager.shutdown();
});

describe('External Api Create Projects', () => {
  it('should create a new project', async () => {
    const projectData = {
      name: 'Test Project',
      key: 'PRJ-123',
      customFields: { status: 1, priority: 1 },
    };

    const res = await request(testContext.app)
      .post(basePath)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(projectData);

    expect(res.statusCode).toBe(200);
    expect(res.body.name).toBe(projectData.name);
    expect(res.body.key).toBe(projectData.key);

    const projectFromDb = await testContext
      .tenantDb('projects')
      .where({ uid: res.body.uid })
      .first();

    expect(projectFromDb.name).toEqual(res.body.name);
    expect(projectFromDb.key).toEqual(res.body.key);
  });

  it('should throw 422 when required fields are missing', async () => {
    const projectData = {
      source: 'testfiesta',
    };
    const res = await request(testContext.app)
      .post(basePath)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(projectData);

    expect(res.statusCode).toBe(422);
  });

  it('should throw 422 when key is not unique', async () => {
    const projectData = {
      name: 'Test Project',
      key: 'PRJ-1',
      customFields: { status: 1, priority: 1 },
    };
    let res = await request(testContext.app)
      .post(basePath)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(projectData);

    expect(res.statusCode).toBe(200);

    res = await request(testContext.app)
      .post(basePath)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(projectData);

    expect(res.statusCode).toBe(422);

    const projectFromDb = await testContext
      .tenantDb('projects')
      .where({ key: projectData.key })
      .count('*')
      .first();

    expect(projectFromDb?.count).toBe('1');
  });

  it('should throw 422 if the key is not valid', async () => {
    const projectData = {
      name: 'Test Project',
      key: 'abc',
    };
    const res = await request(testContext.app)
      .post(basePath)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(projectData);

    expect(res.statusCode).toBe(422);
  });

  it('should throw 422 if the key is blacklisted', async () => {
    const projectData = {
      name: 'Test Project',
      key: 'cases',
    };
    const res = await request(testContext.app)
      .post(basePath)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(projectData);

    expect(res.statusCode).toBe(422);
  });
});

describe('External Api Get Projects', () => {
  it('should return a list of projects', async () => {
    const queryParams = {
      limit: 5,
      offset: 0,
    };
    const res = await request(testContext.app)
      .get(basePath)
      .set('Accept', 'application/json')
      .query(queryParams)
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res.statusCode).toBe(200);
    expect(Array.isArray(res.body.items)).toBe(true);
    expect(res.body.items.length).toBeGreaterThan(0);

    const projectsFromDb = await testContext
      .tenantDb('projects')
      .where({ deletedAt: null })
      .select('uid')
      .limit(queryParams.limit)
      .offset(queryParams.offset);

    const projectsFromDbIds = projectsFromDb.map((project) => project.uid);
    expect(res.body.items.map((item) => item.uid)).toEqual(projectsFromDbIds);
  });
});
