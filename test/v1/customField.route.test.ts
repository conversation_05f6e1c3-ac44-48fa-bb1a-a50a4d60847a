import { TestContext, setupTestEnvironment } from '../utils/testContext';

import { closeQueues } from '../../src/lib/queue';
import { newCustomField } from '../data';
import { permissions } from '../../src/constants/auth';
import request from 'supertest';
import { tenantManager } from '../../src/lib/tenants';

let testContext: TestContext;

beforeAll(async () => {
  testContext = await setupTestEnvironment({
    primaryVersion: 'v1',
    permissions: [
      permissions.write_custom_field,
      permissions.delete_custom_field,
    ],
  });
});

afterAll(async () => {
  if (testContext) {
    await testContext.tenantDb.destroy();
    await testContext.db.destroy();
  }
  await closeQueues();
  await tenantManager.shutdown();
});

describe('External Api Create CustomField', () => {
  it('should create a new customField', async () => {
    const customFieldData = newCustomField('text');

    const res = await request(testContext.app)
      .post(`${testContext.basePath.v1}/customFields`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(customFieldData);

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('uid');
    expect(res.body.name).toBe(customFieldData.name);

    const customField = await testContext
      .tenantDb('customFields')
      .where({ uid: res.body.uid })
      .first();

    expect(customField).toBeTruthy();
    expect(customField.name).toBe(customFieldData.name);
    expect(customField.source).toBe(customFieldData.source);
    expect(customField.type).toBe(customFieldData.type);
  });
});

describe('External Api Get CustomFields', () => {
  it('should get list of customFields', async () => {
    const queryParams = {
      limit: 10,
      offset: 0,
    };
    const res = await request(testContext.app)
      .get(`${testContext.basePath.v1}/customFields`)
      .query(queryParams)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res.statusCode).toBe(200);
    expect(Array.isArray(res.body.items)).toBe(true);
    expect(res.body.items.length).toBeGreaterThan(0);

    const customFields = await testContext
      .tenantDb('customFields')
      .where({ projectUid: testContext.projectUid })
      .limit(queryParams.limit)
      .offset(queryParams.offset);

    expect(customFields.length).toBe(res.body.items.length);
    expect(customFields.some((c) => c.name === res.body.items[0].name)).toBe(
      true,
    );
  });
});

describe('External Api Get CustomField by id', () => {
  it('should get customField by id', async () => {
    const customFieldData = newCustomField('text');
    let res = await request(testContext.app)
      .post(`${testContext.basePath.v1}/customFields`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(customFieldData);

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('uid');
    expect(res.body.name).toBe(customFieldData.name);

    res = await request(testContext.app)
      .get(`${testContext.basePath.v1}/customFields/${res.body.uid}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res.statusCode).toBe(200);
    expect(res.body.uid).toBe(res.body.uid);

    const customField = await testContext
      .tenantDb('customFields')
      .whereNull('deletedAt')
      .where({ uid: res.body.uid })
      .first();

    expect(customField).toBeTruthy();
    expect(customField.name).toBe(res.body.name);
  });
});

describe('External Api Update CustomField', () => {
  it('should update a customField', async () => {
    const customFieldData = newCustomField('text');
    let res = await request(testContext.app)
      .post(`${testContext.basePath.v1}/customFields`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(customFieldData);

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('uid');
    expect(res.body.name).toBe(customFieldData.name);
    expect(res.body.uid).toBeTruthy();

    const customFieldId = res.body.uid;

    const updatedCustomFieldData = {
      name: 'updated name',
      type: 'radio',
      options: ['option1', 'option2'],
    };

    res = await request(testContext.app)
      .patch(`${testContext.basePath.v1}/customFields/${customFieldId}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(updatedCustomFieldData);

    expect(res.statusCode).toBe(200);
    expect(res.body.name).toBe(updatedCustomFieldData.name);
    expect(res.body.type).toBe(updatedCustomFieldData.type);
    expect(res.body.options).toEqual(updatedCustomFieldData.options);

    const customField = await testContext
      .tenantDb('customFields')
      .whereNull('deletedAt')
      .where({ uid: customFieldId })
      .first();

    expect(customField).toBeTruthy();
    expect(customField.name).toBe(updatedCustomFieldData.name);
    expect(customField.type).toBe(updatedCustomFieldData.type);
    expect(customField.options).toEqual(updatedCustomFieldData.options);
  });
});

describe('External Api Delete CustomField', () => {
  it('should delete a customField', async () => {
    const customFieldData = newCustomField('text');
    let res = await request(testContext.app)
      .post(`${testContext.basePath.v1}/customFields`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(customFieldData);

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('uid');
    expect(res.body.name).toBe(customFieldData.name);
    expect(res.body.uid).toBeTruthy();

    const customFieldId = res.body.uid;

    res = await request(testContext.app)
      .delete(`${testContext.basePath.v1}/customFields/${customFieldId}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' });

    expect(res.statusCode).toBe(200);
    expect(res.body.message).toBe('Custom Field deleted successfully');

    const customField = await testContext
      .tenantDb('customFields')
      .whereNull('deletedAt')
      .where({ uid: customFieldId })
      .first();

    expect(customField).toBeFalsy();
  });
});
