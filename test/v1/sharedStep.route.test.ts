import request from 'supertest';
import { setupTestEnvironment, TestContext } from '../utils/testContext';
import { getSteps } from '../data/index';
import { permissions } from '../../src/constants/auth';
import { getNextId } from '../../src/lib/model';
import { closeQueues } from '../../src/lib/queue';
import { tenantManager } from '../../src/lib/tenants';

let testContext: TestContext;

async function createSharedStep(name: string, steps: any[]) {
  const trx = await testContext.tenantDb.transaction();
  const uid = await getNextId(trx, <any>{ tableName: 'sharedTestSteps' });
  const sharedStep = await trx('sharedTestSteps')
    .insert({
      uid,
      name,
      version: 1,
      active: true,
      sharedTestStepRef: uid,
      steps: JSON.stringify(steps),
      createdBy: testContext.accessToken!.ownerUid,
      projectUid: testContext.projectUid,
    })
    .returning('*');

  await trx.commit();

  return sharedStep[0];
}

beforeAll(async () => {
  testContext = await setupTestEnvironment({
    primaryVersion: 'v1',
    permissions: [permissions.write_step, permissions.delete_step],
  });
});

afterAll(async () => {
  if (testContext) {
    await testContext.tenantDb.destroy();
    await testContext.db.destroy();
  }
  await closeQueues();
  await tenantManager.shutdown();
});

describe('External Api Create Shared Step', () => {
  it('should create a basic shared step', async () => {
    const sharedStepData = {
      name: 'Test Shared Step',
      steps: getSteps(),
    };

    const res = await request(testContext.app)
      .post(`${testContext.basePath.v1}/shared-steps`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(sharedStepData);

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('uid');
    expect(res.body.name).toBe(sharedStepData.name);

    const sharedStep = await testContext
      .tenantDb('sharedTestSteps')
      .where({ uid: res.body.uid })
      .first();

    expect(sharedStep).toBeTruthy();
    expect(sharedStep.name).toBe(sharedStepData.name);
  });
});

describe('External Api Get Shared Step', () => {
  it('should get shared steps', async () => {
    const queryParams = {
      limit: 10,
      offset: 0,
    };
    const res = await request(testContext.app)
      .get(`${testContext.basePath.v1}/shared-steps`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .query(queryParams)
      .send();

    expect(res.statusCode).toBe(200);
    expect(Array.isArray(res.body.items)).toBe(true);
    expect(res.body.items.length).toBeGreaterThan(0);
  });
});

describe('External Api Get Shared Step by ID', () => {
  it('should get shared step by ID', async () => {
    const sharedStep = await createSharedStep('Test Shared Step', getSteps());

    const res = await request(testContext.app)
      .get(`${testContext.basePath.v1}/shared-steps/${sharedStep.uid}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send();

    expect(res.statusCode).toBe(200);
    expect(res.body.uid).toBe(sharedStep.uid);
    expect(res.body.name).toBe(sharedStep.name);
  });
});

describe('External Api Update Shared Step', () => {
  it('should update shared step', async () => {
    const sharedStep = await createSharedStep('Test Shared Step', getSteps());

    const updateData = {
      name: 'Updated Shared Step',
    };

    const res = await request(testContext.app)
      .patch(`${testContext.basePath.v1}/shared-steps/${sharedStep.uid}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send(updateData);

    const sharedStepUid = res.body.uid;

    expect(res.statusCode).toBe(200);
    expect(res.body.name).toBe(updateData.name);
    expect(res.body.version).toBe(sharedStep.version + 1);

    const updatedSharedStep = await testContext
      .tenantDb('sharedTestSteps')
      .where({ uid: sharedStepUid })
      .first();

    expect(updatedSharedStep).toBeTruthy();
    expect(updatedSharedStep.name).toBe(res.body.name);
    expect(updatedSharedStep.version).toBe(res.body.version);
  });
});

describe('External Api Delete Shared Step', () => {
  it('should delete shared step', async () => {
    const sharedStep = await createSharedStep('Test Shared Step', getSteps());
    const res = await request(testContext.app)
      .delete(`${testContext.basePath.v1}/shared-steps/${sharedStep.uid}`)
      .set('Accept', 'application/json')
      .auth(testContext.accessToken!.secretKey, { type: 'bearer' })
      .send();

    expect(res.statusCode).toBe(200);
    expect(res.body.message).toBe('Shared Step deleted');

    const deletedSharedStep = await testContext
      .tenantDb('sharedTestSteps')
      .whereNull('deletedAt')
      .where({ uid: sharedStep.uid })
      .first();

    expect(deletedSharedStep).toBeFalsy();
  });
});
