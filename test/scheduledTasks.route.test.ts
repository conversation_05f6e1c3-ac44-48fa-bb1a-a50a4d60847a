import * as dbHelpers from './utils/dbHelpers';

import { Application } from 'express';
import { buildApp } from '../src/app';
import { closeQueues } from '../src/lib/queue';
import { expect } from 'chai';
import crypto from "crypto";
import request from 'supertest';
import { setupDB } from '../src/config/db';
import { setupUserTenant } from './utils/tenant';
import { tenantManager } from '../src/lib/tenants';
import { StatusCodes } from 'http-status-codes';
import { errors } from './utils/error';
import scheduledTaskRoutes from '../src/routes/internal/scheduledTask';

let app: Application,
    session: any,
    user,
    password: string,
    handle: string,
    basePath: string

const db = setupDB();
let tenant: any = null;

let scheduledTask;

beforeAll(async () => {
    app = buildApp(db, [scheduledTaskRoutes], true);

    tenant = await setupUserTenant(db, app);
    user = tenant.user;
    password = tenant.password;
    handle = tenant.handle;
    const signin = await request(app)
        .post(`${process.env.API_INTERNAL_ROUTE}/signin`)
        .send({ email: user.email, password });
    session = signin.headers['set-cookie'];

    basePath = `${process.env.API_INTERNAL_ROUTE}/${handle}/tasks`;
    scheduledTask = await dbHelpers.createScheduledTask(db, {
        ownerUid: user.uid,
        ownerType: 'user',
        name: 'testWorkflow',
        queue: 'testQueue',
        status: 'not_started',
        taskData: { test: 'data' }
    });
});

afterAll(async () => {
    await db.destroy();
    await closeQueues();
    await tenantManager.shutdown();
});


describe('GET /tasks/:id', () => {
    it('should return 404 for non-existent task', async () => {
        const res = await request(app)
            .get(`${basePath}/${crypto.randomUUID()}`)
            .set('Cookie', session);
        expect(res.statusCode).to.equal(StatusCodes.NOT_FOUND);
        expect(res.body.message).to.equal(errorConstants.SCHEDULED_TASK_NOT_FOUND);
    });

    it('should return task details for valid task ID', async () => {
        const res = await request(app)
            .get(`${basePath}/${scheduledTask.uid}`)
            .set('Cookie', session);

        expect(res.statusCode).to.equal(StatusCodes.OK);
        expect(res.body).to.have.property('uid', scheduledTask.uid);
        expect(res.body).to.have.property('status', 'not_started');
        expect(res.body).to.have.property('message');
        expect(res.body).to.have.property('percentage');
        expect(res.body).to.have.property('createdAt');
        expect(res.body).to.have.property('completedAt');
    });

    it('should not return task from another user', async () => {
        // Create a task for a different user
        const otherUserTask = await dbHelpers.createScheduledTask(db, {
            ownerUid: crypto.randomUUID(), // Different user ID
            ownerType: 'user',
            name: 'otherWorkflow',
            queue: 'otherQueue',
            status: 'not_started',
        });

        const res = await request(app)
            .get(`${basePath}/${otherUserTask.uid}`)
            .set('Cookie', session);

        expect(res.statusCode).to.equal(StatusCodes.NOT_FOUND);
    });
});

describe('POST /scheduled-tasks/:id/run', () => {
    it('should return 404 for non-existent task', async () => {
        const res = await request(app)
            .post(`${basePath}/${crypto.randomUUID()}/run`)
            .set('Cookie', session);
        expect(res.statusCode).to.equal(StatusCodes.NOT_FOUND);
    });

    it('should return 400 if task is already running', async () => {
        // Create a task with running status
        const runningTask = await dbHelpers.createScheduledTask(db, {
            ownerUid: user.uid,
            ownerType: 'user',
            name: 'runningWorkflow',
            queue: 'runningQueue',
            status: 'running'
        });

        const res = await request(app)
            .post(`${basePath}/${runningTask.uid}/run`)
            .set('Cookie', session);

        expect(res.statusCode).to.equal(StatusCodes.BAD_REQUEST);
        expect(res.body.message).to.equal(errorConstants.SCHEDULED_TASK_ALREADY_RUNNING);
    });

    it('should not allow running another user\'s task', async () => {
        const otherUserTask = await dbHelpers.createScheduledTask(db, {
            ownerUid: crypto.randomUUID(), // Different user ID
            ownerType: 'user',
            name: 'otherWorkflow',
            queue: 'otherQueue',
            status: 'not_started'
        });

        const res = await request(app)
            .post(`${basePath}/${otherUserTask.uid}/run`)
            .set('Cookie', session);

        expect(res.statusCode).to.equal(StatusCodes.NOT_FOUND);
    });
});
