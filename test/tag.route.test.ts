import * as data from './data/index';

import { Application } from 'express';
import { buildApp } from '../src/app';
import { closeQueues } from '../src/lib/queue';
import { expect } from 'chai';
import projectRoutes from '../src/routes/internal/project';
import request from 'supertest';
import { setupDB } from '../src/config/db';
import { setupUserTenant } from './utils/tenant';
import tagRoute from '../src/routes/internal/tag';
import { tenantManager } from '../src/lib/tenants';

const db = setupDB();
let app: Application,
  handle: string,
  session,
  basePath: string,
  tag,
  project,
  project2,
  projectBasePath: string,
  project2BasePath: string,
  projectTag;

beforeAll(async () => {
  app = buildApp(db, [tagRoute, projectRoutes]);
  const tenant = await setupUserTenant(db, app);
  const user = tenant.user;
  const password = tenant.password;
  handle = tenant.handle;

  const signin = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signin`)
    .send({ email: user.email, password });
  session = signin.headers['set-cookie'];

  const createProject = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
    .set('Accept', 'application/json')
    .set('Cookie', session)
    .send(data.newProject());
  project = createProject.body;

  projectBasePath = `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${project.key}/tags`;

  const createProject2 = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
    .set('Accept', 'application/json')
    .set('Cookie', session)
    .send(data.newProject());
  project2 = createProject2.body;

  project2BasePath = `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${project2.key}/tags`;

  basePath = `${process.env.API_INTERNAL_ROUTE}/${handle}/tags`;
});

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

describe('Tag', () => {
  it('should not pass create tags when empty body', async () => {
    const res = await request(app)
      .post(basePath)
      .set('Cookie', session)
      .send({});

    expect(res.statusCode).to.be.oneOf([400]);
  });

  it('should create tags', async () => {
    const res = await request(app)
      .post(basePath)
      .set('Cookie', session)
      .send({
        name: 'test',
        description: 'test description',
        entityTypes: ['runs', 'plans'],
      });

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body.name).to.be.equal('test');
    expect(res.body.description).to.be.equal('test description');
    expect(res.body.entityTypes).to.be.an('array');
    expect(res.body.entityTypes).to.include('runs');
    expect(res.body.entityTypes).to.include('plans');
    tag = res.body;
  });

  it('should get organization tags', async () => {
    const res = await request(app).get(basePath).set('Cookie', session);

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body).to.be.an('array');

    for (const tag of res.body) expect(tag.projectUid).to.be.null;
  });

  it('should get tags by organization entityType', async () => {
    const entityType = 'cases';
    const res = await request(app)
      .get(basePath)
      .query({ entityType })
      .set('Cookie', session);
    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body).to.be.an('array');

    for (const tag of res.body) expect(tag.entityTypes).include(entityType);
  });

  it('should update organization tags', async () => {
    const res = await request(app)
      .patch(`${basePath}/${tag.uid}`)
      .set('Cookie', session)
      .send({
        name: 'test updated',
        description: 'test description updated',
        entityTypes: ['runs', 'milestones'],
      });

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body.name).to.be.equal('test updated');
    expect(res.body.description).to.be.equal('test description updated');
    expect(res.body.entityTypes).to.be.an('array');
    expect(res.body.entityTypes).to.include('runs');
    expect(res.body.entityTypes).to.not.include('plans');
    expect(res.body.entityTypes).to.include('milestones');
  });

  it('should delete organization tags', async () => {
    const res = await request(app)
      .delete(`${basePath}/${tag.uid}`)
      .set('Cookie', session);

    expect(res.statusCode).to.be.oneOf([200]);
  });

  it('should create tags for project', async () => {
    const res = await request(app)
      .post(projectBasePath)
      .set('Cookie', session)
      .send({
        name: 'test',
        description: 'test description',
        entityTypes: ['runs', 'plans'],
      });

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body.name).to.be.equal('test');
    expect(res.body.description).to.be.equal('test description');
    expect(res.body.entityTypes).to.be.an('array');
    expect(res.body.entityTypes).to.include('runs');
    expect(res.body.entityTypes).to.include('plans');
    expect(res.body.projectUid).to.equal(project.uid);

    projectTag = res.body;
  });

  it('should get tags by entityType for project', async () => {
    const entityType = 'runs';
    const res = await request(app)
      .get(projectBasePath)
      .query({ entityType })
      .set('Cookie', session);

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body).to.be.an('array');

    for (const tag of res.body) {
      expect(tag.entityTypes).include(entityType);

      if (tag.projectUid) {
        expect(tag.projectUid).to.equal(project.uid);
      } else {
        expect(tag.projectUid).to.be.null;
      }
    }
  });

  it('should get tags for specific project', async () => {
    await request(app)
      .post(project2BasePath)
      .set('Cookie', session)
      .send({
        name: 'test',
        description: 'test description',
        entityTypes: ['runs', 'plans'],
      });

    const res = await request(app).get(projectBasePath).set('Cookie', session);

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body).to.be.an('array');

    let tagWithProjectUid = res.body.find(
      (tag) => tag.projectUid === project2.uid,
    );
    expect(tagWithProjectUid).to.be.undefined;

    tagWithProjectUid = res.body.find((tag) => tag.projectUid === project.uid);
    expect(tagWithProjectUid).to.not.be.undefined;
    expect(tagWithProjectUid.projectUid).to.equal(project.uid);
  });

  it('should update project tags', async () => {
    const res = await request(app)
      .patch(`${projectBasePath}/${projectTag.uid}`)
      .set('Cookie', session)
      .send({
        name: 'test updated',
        description: 'test description updated',
        entityTypes: ['runs', 'milestones'],
      });

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body.name).to.be.equal('test updated');
    expect(res.body.description).to.be.equal('test description updated');
    expect(res.body.entityTypes).to.be.an('array');
    expect(res.body.entityTypes).to.include('runs');
    expect(res.body.entityTypes).to.not.include('plans');
    expect(res.body.entityTypes).to.include('milestones');
    expect(res.body.projectUid).to.be.equal(project.uid);
  });

  it('should delete project tags', async () => {
    const res = await request(app)
      .delete(`${projectBasePath}/${projectTag.uid}`)
      .set('Cookie', session);

    expect(res.statusCode).to.be.oneOf([200]);
  });
});
