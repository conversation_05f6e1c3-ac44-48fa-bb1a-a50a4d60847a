import * as data from './data/index';

import { Application } from 'express';
import { buildApp } from '../src/app';
import caseRoutes from '../src/routes/internal/case';
import { closeQueues } from '../src/lib/queue';
import customFieldRoutes from '../src/routes/internal/customField';
import { expect } from 'chai';
import { faker } from '@faker-js/faker';
import folderRoutes from '../src/routes/internal/folder';
import orgRoutes from '../src/routes/internal/org';
import projectRoutes from '../src/routes/internal/project';
import request from 'supertest';
import { setupDB } from '../src/config/db';
import { setupOrgTenant } from './utils/tenant';
import sharedStepRoutes from '../src/routes/internal/sharedStep';
import tagRoutes from '../src/routes/internal/tag';
import { tenantManager } from '../src/lib/tenants';

let session, project;
let handle;

const db = setupDB();

let app: Application, basePath: string;

beforeAll(async () => {
  app = buildApp(db, [
    orgRoutes,
    caseRoutes,
    projectRoutes,
    folderRoutes,
    sharedStepRoutes,
    tagRoutes,
    customFieldRoutes,
  ]);

  const tenant = await setupOrgTenant(db, app);
  const email = tenant.admin.email;
  const password = tenant.password;
  handle = tenant.handle;

  const signin = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signin`)
    .send({ email, password });
  session = signin.headers['set-cookie'];

  const createProject = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
    .set('Cookie', session)
    .send(data.newProject());

  project = createProject.body;
  basePath = `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${project.key}/customFields`;
});

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

describe('Custom Fields Routes', () => {
  let customField;

  it('should create a new custom field successfully', async () => {
    const dto = {
      name: faker.lorem.word(3),
      type: 'text',
      options: [],
      source: 'Manual',
    };

    const res = await request(app)
      .post(basePath)
      .send(dto)
      .set('Cookie', session);

    expect(res.statusCode).to.equal(200);
    customField = res.body;
  });

  it('should fail on creating duplicate custom field', async () => {
    const dto = {
      name: customField.name,
      type: 'checkbox',
      options: [],
      source: 'Manual',
    };

    const res = await request(app)
      .post(basePath)
      .send(dto)
      .set('Cookie', session);

    expect(res.statusCode).to.equal(409);
  });

  it('should return a list of custom fields', async () => {
    const res = await request(app).get(basePath).set('Cookie', session);

    expect(res.statusCode).to.equal(200);
    expect(res.body).to.be.an('array');
  });

  it('should fail on updating a custom field name to another existing custom field name', async () => {
    const initial = {
      name: faker.lorem.words(5),
      type: 'text',
      options: [],
      source: 'Manual',
    };
    let res = await request(app)
      .post(basePath)
      .send(initial)
      .set('Cookie', session);
    expect(res.status).eq(200);

    res = await request(app)
      .patch(`${basePath}/${res.body.uid}`)
      .set('Cookie', session)
      .send({ ...initial, name: customField.name });
    expect(res.status).eq(409);
  });

  it('should update the custom field successfully', async () => {
    const updatedCustomField = {
      name: faker.lorem.word(4),
      type: 'text',
      options: [],
      source: 'Manual',
      uid: '',
    };

    const res = await request(app)
      .patch(`${basePath}/${customField.uid}`)
      .set('Cookie', session)
      .send(updatedCustomField);

    expect(res.statusCode).to.equal(200);
    expect(res.body.name).to.equal(updatedCustomField.name);
  });

  it('should delete the custom field successfully', async () => {
    const res = await request(app)
      .delete(`${basePath}/${customField.uid}`)
      .set('Cookie', session);

    expect(res.statusCode).to.equal(200);
  });

  it('should return 404 when trying to get a non-existent custom field', async () => {
    const res = await request(app)
      .get(`${basePath}/${customField.uid}`)
      .set('Cookie', session);

    expect(res.statusCode).to.equal(404);
  });
});
