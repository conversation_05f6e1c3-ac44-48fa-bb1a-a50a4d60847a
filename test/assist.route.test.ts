import { existsCase, newCaseByAssist } from './utils/assist';

import { Application } from 'express';
import assistRoutes from '../src/routes/internal/assist';
import { buildApp } from '../src/app';
import { closeQueues } from '../src/lib/queue';
// import { expect } from 'chai';
import request from 'supertest';
import { setupDB } from '../src/config/db';
import { setupOrgTenant } from './utils/tenant';
import { tenantManager } from '../src/lib/tenants';

let session;
let handle;

const db = setupDB();

let app: Application, basePath: string;

beforeAll(async () => {
  app = buildApp(db, [assistRoutes]);

  const tenant = await setupOrgTenant(db, app);
  const email = tenant.admin.email;
  const password = tenant.password;
  handle = tenant.handle;

  const signin = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signin`)
    .send({ email, password });
  session = signin.headers['set-cookie'];

  basePath = `${process.env.API_INTERNAL_ROUTE}/${handle}/assist`;
});

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

describe('Test Case Assist Routes', () => {
  it('should return a valid test case generated by Assist', async () => {
    const userRequest = newCaseByAssist();
    const res = await request(app)
      .post(basePath)
      .set('Cookie', session)
      .set('Accept', 'application/json')
      .send(userRequest);
    const receivedFields = res.body.inputFields
      .map((item: any) => item.fieldName)
      .sort();
    const bodyFields = userRequest['inputFields']
      .map((item: any) => item.fieldName)
      .sort();
    expect(receivedFields).toEqual(bodyFields);
    expect(res.body).toHaveProperty('steps');
    expect(res.body.steps.length).toBeGreaterThan(-1);
  });

  it('should fail to create a test case using Assist due to invalid information', async () => {
    const userRequest = newCaseByAssist();
    userRequest['userPrompt'] = 'blablabla';
    const res = await request(app)
      .post(basePath)
      .set('Cookie', session)
      .set('Accept', 'application/json')
      .send(userRequest);

    expect(res.statusCode).toBe(400);
    expect(res.body.message).toBe('No response found.');
  });

  it('should fail to create a test case using Assist due to missing inputs', async () => {
    const userRequest = newCaseByAssist();
    userRequest['userPrompt'] = undefined;
    const res = await request(app)
      .post(basePath)
      .set('Cookie', session)
      .set('Accept', 'application/json')
      .send(userRequest);

    expect(res.statusCode).toBe(400);
    expect(res.body.message).toBe('Your request is invalid');
  });

  it('should improve the given test case', async () => {
    const testCase = existsCase();

    const payload = {
      ...testCase,
      type: 'caseImprovement',
    };

    const res = await request(app)
      .post(basePath)
      .set('Cookie', session)
      .set('Accept', 'application/json')
      .send(payload);

    const receivedFields = res.body.inputFields
      .map((item: any) => item.fieldName)
      .sort();
    const bodyFields = payload['inputFields']
      .map((item: any) => item.fieldName)
      .sort();
    expect(receivedFields).toEqual(bodyFields);
    expect(res.body.steps.length).toBe(payload['steps'].length);
  });

  it('should fail to improve the given test case due to missing inputs', async () => {
    const testCase = existsCase();

    const payload = {
      ...testCase,
      type: 'caseImprovement',
    };
    payload['steps'] = [];
    const res = await request(app)
      .post(basePath)
      .set('Cookie', session)
      .set('Accept', 'application/json')
      .send(payload);

    expect(res.statusCode).toBe(400);
    expect(res.body.message).toBe('Your request is invalid');
  });

  it('should improve the given input field', async () => {
    const testCase = existsCase(1);
    const prompts = [
      'Improve clarity and conciseness',
      'Optimize flow and coherence.',
      'Strengthen vocabulary choices',
      'Eliminate redundant phrases',
      'Simplify complex sentence structures',
    ];

    const payload = {
      inputFields: testCase['inputFields'],
      type: 'fieldImprovement',
      userPrompt: prompts[Math.floor(Math.random() * prompts.length)],
    };

    const res = await request(app)
      .post(basePath)
      .set('Cookie', session)
      .set('Accept', 'application/json')
      .send(payload);

    expect(res.body).toHaveProperty('inputFields');
    expect(res.body.inputFields[0].fieldName).toBe(
      testCase['inputFields'][0].fieldName,
    );
    expect(res.body.inputFields[0]).toHaveProperty('fieldValue');
    expect(res.body.inputFields[0].fieldValue).not.toBe('');
  });

  it('should not improve the given input field due to missing inputs', async () => {
    const prompts = [
      'Improve clarity and conciseness',
      'Optimize flow and coherence.',
      'Strengthen vocabulary choices',
      'Eliminate redundant phrases',
      'Simplify complex sentence structures',
    ];

    const payload = {
      inputFields: [],
      type: 'fieldImprovement',
      userPrompt: prompts[Math.floor(Math.random() * prompts.length)],
    };

    const res = await request(app)
      .post(basePath)
      .set('Cookie', session)
      .set('Accept', 'application/json')
      .send(payload);

    expect(res.statusCode).toBe(400);
    expect(res.body.message).toBe('Your request is invalid');
  });
});
