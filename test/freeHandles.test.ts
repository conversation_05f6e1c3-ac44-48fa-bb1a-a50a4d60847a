import { closeQueues } from '../src/lib/queue';
import dayjs from 'dayjs';
import { expect } from 'chai';
import { faker } from '@faker-js/faker';
import { freeHandles } from '../src/temporal/activities/handle';
import { setupDB } from '../src/config/db';
import { tenantManager } from '../src/lib/tenants';

const db = setupDB();

let userId: string;

afterAll(async () => {
  await closeQueues();
  await tenantManager.shutdown();
});

describe('freeHandles test', () => {
  it('should be able to delete all the unused users handle deleted more than 6 months ago', async () => {
    const sevenMonthsAgo = dayjs().subtract(7, 'month').toDate();
    const fiveMonthsAgo = dayjs().subtract(5, 'month').toDate();

    const newUser = () => ({
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      email: faker.internet.email().toLowerCase(),
      passwordHash: faker.internet.password(),
      lastSignInIp: faker.internet.ip(),
    });

    const users = await db('users')
      .insert([
        { ...newUser(), deletedAt: sevenMonthsAgo },
        { ...newUser(), deletedAt: fiveMonthsAgo },
        { ...newUser(), deletedAt: null },
      ])

      .returning('*');

    userId = users[0].uid;

    const handles: any[] = [];

    for (const user of users) {
      handles.push({
        name: faker.internet.userName().toLowerCase(),
        ownerUid: user.uid,
        ownerType: 'user',
        current: true,
      });
    }

    await db('handles').insert(handles);

    await freeHandles(db);

    const remainingHandles = await db('handles')
      .select('*')
      .whereIn(
        'ownerUid',
        users.map((user) => user.uid),
      );
    expect(remainingHandles).to.have.lengthOf(2);
  });

  it('should be able to delete all the unused orgs handle deleted more than 6 months ago', async () => {
    const sevenMonthsAgo = dayjs().subtract(7, 'month').toDate();
    const fiveMonthsAgo = dayjs().subtract(5, 'month').toDate();

    const newOrg = () => ({
      name: faker.company.name(),
      createdBy: userId,
    });

    const orgs = await db('orgs')
      .insert([
        { ...newOrg(), deletedAt: sevenMonthsAgo },
        { ...newOrg(), deletedAt: fiveMonthsAgo },
        { ...newOrg(), deletedAt: null },
      ])

      .returning('*');

    const handles: any[] = [];

    for (const org of orgs) {
      handles.push({
        name: faker.internet.userName().toLowerCase(),
        ownerUid: org.uid,
        ownerType: 'org',
        current: true,
      });
    }

    await db('handles').insert(handles);

    await freeHandles(db);

    const remainingHandles = await db('handles')
      .select('*')
      .whereIn(
        'ownerUid',
        orgs.map((org) => org.uid),
      );
    expect(remainingHandles).to.have.lengthOf(2);
  });
});
