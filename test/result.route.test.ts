import * as data from './data/index';

import { Application } from 'express';
import { buildApp } from '../src/app';
import caseRoutes from '../src/routes/internal/case';
import { closeQueues } from '../src/lib/queue';
import executionRoutes from '../src/routes/internal/execution';
import { expect } from 'chai';
import folderRoutes from '../src/routes/internal/folder';
import projectRoutes from '../src/routes/internal/project';
import request from 'supertest';
import resultRoutes from '../src/routes/internal/result';
import runRoutes from '../src/routes/internal/run';
import { setupDB } from '../src/config/db';
import { setupUserTenant } from './utils/tenant';
import { tenantManager } from '../src/lib/tenants';

let app: Application;
let executionUid: string;
let resultUid = null;
let basePath, user, password, handle;
let testCase: any;
let session: any;
let testRun: any;
const newRun: any = data.newRun();
const db = setupDB();

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

beforeAll(async () => {
  app = buildApp(db, [
    executionRoutes,
    resultRoutes,
    projectRoutes,
    caseRoutes,
    folderRoutes,
    runRoutes,
  ]);

  const tenant = await setupUserTenant(db, app);
  user = tenant.user;
  password = tenant.password;
  handle = tenant.handle;

  const signin = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signIn`)
    .set('Accept', 'application/json')
    .send({ email: user.email, password });
  session = signin.headers['set-cookie'];

  const createProject = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
    .set('Accept', 'application/json')
    .set('Cookie', session)
    .send(data.newProject());
  const { key } = createProject.body;

  basePath = `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${key}`;

  const getFolders = await request(app)
    .get(`${basePath}/folders`)
    .set('Cookie', session);
  const [folder] = getFolders.body.folders;

  const cases = await request(app)
    .post(`${basePath}/cases`)
    .set('Accept', 'application/json')
    .set('Cookie', session)
    .send({
      ...data.newCase(),
      parentId: folder.uid,
    });
  testCase = cases.body;
});

describe('Result', () => {
  // crate new test run
  it('should be able to create a new test run', async () => {
    newRun.caseUids = [testCase.testCaseRef];
    const res = await request(app)
      .post(`${basePath}/runs`)
      .set('Cookie', session)
      .send(newRun);
    expect(res.statusCode).to.equal(200);
    testRun = res.body;
  });

  it('should retrieve executions by test run', async () => {
    const res = await request(app)
      .get(`${basePath}/runs/${testRun.uid}/executions`)
      .set('Cookie', session)
      .send();

    expect(res.statusCode).to.equal(200);
    executionUid = res.body.executions[0].uid;
  });

  it('should create a new test result', async () => {
    const res = await request(app)
      .post(`${basePath}/executions/${executionUid}/results`)
      .set('Cookie', session)
      .send({
        status: 1,
        comment: 'This is a test result comment',
      });
    expect(res.statusCode).to.equal(200);
    resultUid = res.body.uid;
  });

  it('should retrieve test results by execution UID', async () => {
    const res = await request(app)
      .get(`${basePath}/executions/${executionUid}/results`)
      .set('Cookie', session);
    expect(res.statusCode).to.equal(200);
    expect(res.body).to.be.an('array');
  });

  it('should update a test result', async () => {
    const res = await request(app)
      .patch(`${basePath}/results/${resultUid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({ status: 2, comment: 'Updated result comment' });
    expect(res.statusCode).to.equal(200);
  });

  it('should delete a test result', async () => {
    const res = await request(app)
      .delete(`${basePath}/results/${resultUid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session);
    expect(res.statusCode).to.equal(200);
  });

  // delete the execution
  it('should delete a test execution', async () => {
    const res = await request(app)
      .delete(`${basePath}/executions/${executionUid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session);
    expect(res.statusCode).to.equal(200);
  });
});
