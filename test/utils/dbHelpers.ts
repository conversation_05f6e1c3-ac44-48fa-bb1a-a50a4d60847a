import { Knex } from 'knex';
import { getNextId } from '../../src/lib/model';
import { faker } from '@faker-js/faker';

export async function createRun(
  db: Knex.Transaction,
  projectUid: number,
  testPlanUid?: number,
  withTags?: boolean,
) {
  const [run] = await db('tags')
    .insert({
      name: faker.lorem.words(),
      projectUid: projectUid,
      customFields: {
        priority: faker.number.int(100),
        status: faker.number.int(100),
      },
      systemType: 'run',
      entityTypes: [],
    })
    .returning('*');
  if (withTags) {
    const [tag] = await db('tags')
      .insert({
        name: faker.lorem.words(),
        description: faker.lorem.sentence(),
        entityTypes: ['runs', 'plans'],
        projectUid,
      }).returning('*');

    await db('testRunTags').insert({
      runUid: run.uid,
      tagUid: tag.uid,
    });
    run.tagUids = [tag.uid];
  }

  if (testPlanUid) {
    await db('testPlanRuns').insert({ planUid: testPlanUid, runUid: run.uid });
  }
  return run;
}

export async function createCase(db: Knex.Transaction, projectUid: number) {
  const uid = await getNextId(db, <any>{ tableName: 'testCases' });
  const [c] = await db('testCases')
    .insert({
      uid,
      testCaseRef: uid,
      version: 1,
      active: true,
      name: faker.lorem.words(),
      priority: faker.number.int(100),
      status: faker.number.int(100),
      projectUid,
    })
    .returning('*');
  return c;
}

export async function createExec(
  db: Knex.Transaction,
  projectUid: number,
  caseRef: number,
  runUid: number,
) {
  const [exec] = await db('testExecutions')
    .insert({
      status: faker.number.int(100),
      testCaseRef: caseRef,
      testRunUid: runUid,
      priority: faker.number.int(100),
      testCaseUid: caseRef,
      projectUid: projectUid,
    })
    .returning('*');

  return exec;
}

export async function createPlan(
  db: Knex.Transaction,
  projectUid: number,
  extra?: Record<string, any>,
) {
  const [plan] = await db('tags')
    .insert({
      name: faker.lorem.words(),
      slug: faker.string.uuid(),
      description: faker.lorem.lines(1),
      systemType: 'plan',
      customFields: {
        status: faker.number.int(100),
        priority: faker.number.int(100),
      },
      entityTypes: [],
      projectUid,
      ...extra,
    })
    .returning('*');
  return plan;
}

export async function createMilestone(
  db: Knex.Transaction,
  projectUid: number,
) {
  const [milestone] = await db('tags')
    .insert({
      name: faker.lorem.words(3),
      description: faker.lorem.paragraph(),
      projectUid,
      systemType: 'milestone',
      customFields: {
        dueAt: faker.date.future(),
        status: faker.number.int(100),
      },
    })
    .returning('*');
  return milestone;
}


export async function createScheduledTask(
  db: Knex,
  data: {
    ownerUid: string;
    ownerType?: 'org' | 'user';
    name: string;
    queue: string;
    status: 'not_started' | 'running' | 'completed' | 'failed';
    taskData?: Record<string, any>;
    message?: string;
    percentage?: number;
    createdAt?: Date;
    completedAt?: Date;
  }
) {
  const defaultData = {
    name: data.name || faker.lorem.words(),
    queue: data.queue || 'default-queue',
    status: data.status || 'not_started',
    taskData: data.taskData || { test: 'data' },
    message: data.message || faker.lorem.sentence(),
    percentage: data.percentage || 0,
    createdAt: data.createdAt || new Date(),
    completedAt: data.completedAt || null,
    ownerUid: data.ownerUid,
    ownerType: data.ownerType
  };

  const [task] = await db('scheduledTasks')
    .insert(defaultData)
    .returning('*');
  return task;
}