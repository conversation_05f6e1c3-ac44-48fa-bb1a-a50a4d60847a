import { Application } from 'express';
import { Knex } from 'knex';
import { StatusCodes } from 'http-status-codes';
import { faker } from '@faker-js/faker';
import request from 'supertest';
import { setupAccount } from '../../src/temporal/activities/account';

export const newUser = () => ({
  firstName: faker.person.firstName(),
  lastName: faker.person.lastName(),
  handle: (
    faker.string.alphanumeric({
      casing: 'lower',
      length: { min: 5, max: 10 },
    }) + faker.string.alphanumeric(6)
  ).toLowerCase(),
  email: faker.internet.email().toLowerCase(),
  password: faker.internet.password(),
  lastSignInIp: faker.internet.ip(),
});

export const newOrg = () => ({
  name: faker.company.name(),
  handle: (
    faker.string.alphanumeric({
      casing: 'lower',
      length: { min: 5, max: 10 },
    }) + faker.string.alphanumeric(6)
  ).toLowerCase(),
});

export async function setupUserTenant(
  db: Knex,
  app: Application,
  bootstrap = true,
) {
  const user = newUser();

  const signup = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signup`)
    .set('Accept', 'application/json')
    .send(user);

  const server = await db('dbServers')
    .join('appNodes', 'dbServers.appNodeUid', 'appNodes.uid')
    .select('dbServers.*')
    .where({ 'dbServers.isDefault': true, 'appNodes.isDefault': true })
    .first();

  if (StatusCodes.OK !== signup.status) {
    throw new Error(JSON.stringify(signup.body));
  }

  if (bootstrap)
    await setupAccount(
      { ownerType: 'user', ownerUid: signup.body.user.uid, dbServerUid: server?.uid },
      db,
    );

  return {
    user: signup.body.user,
    password: user.password,
    handle: user.handle,
    dbServer: server,
  };
}

export async function setupOrgTenant(db: Knex, app: Application) {
  const userTenant = await setupUserTenant(db, app, false);

  const dto = newOrg();
  let org;

  const server = await db('dbServers')
    .join('appNodes', 'dbServers.appNodeUid', 'appNodes.uid')
    .select('dbServers.*')
    .where({ 'dbServers.isDefault': true, 'appNodes.isDefault': true })
    .first();

  await db.transaction(async (trx) => {
    [org] = await trx('orgs')
      .insert({ name: dto.name, createdBy: userTenant.user.uid })
      .returning('*');

    await trx('memberships').insert({
      userUid: userTenant.user.uid,
      accountUid: org.uid,
      accountType: 'org',
    });

    await trx('handles')
      .insert({
        name: dto.handle,
        ownerType: 'org',
        current: true,
        ownerUid: org.uid,
      })
      .returning('*');
    await trx.commit();
  });
  await setupAccount({ ownerType: 'org', ownerUid: org.uid, dbServerUid: server?.uid }, db);

  return {
    org,
    handle: dto.handle,
    admin: userTenant.user,
    password: userTenant.password,
    dbServer: server,
  };
}
