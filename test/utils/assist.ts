import _ from 'lodash'; 
import * as data from '../data/index';
import { faker } from '@faker-js/faker';

export function generateInputFields(length: number, withValues?: Boolean): Array<Object>{
  const inputFields = [
    "pre-condition",
    "timeout",
    "post-condition",
    "execution-time",
    "environment",
    "expected-output",
    "priority-level",
    "status",
    "assigned-to",
    "test-data",
    "dependency",
    "test-case-id",
    "test-step",
    "severity",
    "browser",
    "operating-system",
    "start-date",
    "end-date",
    "last-updated",
    "test-result"
  ];

  const selectedFields = _.sampleSize(inputFields, length);

  return selectedFields.map(field => ({
    fieldName: field,
    fieldValue: withValues ? faker.lorem.words({min: 3, max: 6}) : '' 
  }));
}
export function newCaseByAssist(inputFieldsLength: number = 5): Object {
  const stagingPrompts = [
    {
      prompt: "Generate a functional test case for Password Strength Validation, focused on the following features:Ensuring that users' passwords meet strength criteria (e.g., length, complexity, special characters) during account creation or password change.",
      caseName: 'Password Strength Validation'
    },
    {
      prompt: "Generate a functional test case for Remember Me functionality, focused on the following features: Persistence of user session across sessions User should remain logged in after closing and reopening the browser (based on 'Remember Me' option)",
      caseName: 'Remember Me functionality'
    },
    {
      prompt: "Create a functional test case to validate the Email Verification functionality, focusing on the following features: Ensuring that users receive a verification email upon registration and are able to verify their email address through the provided link.",
      caseName: 'validate the Email Verification functionality'
    },
    {
      prompt: 'Design a functional test case to verify the User Session Timeout functionality, concentrating on the following features: Ensuring that user sessions automatically expire after a set period of inactivity and that the user is logged out after the timeout',
      caseName: 'verify the User Session Timeout functionality'
    }
  ];

  const caseRandomSelection = stagingPrompts[ Math.floor(Math.random() * stagingPrompts.length)];
  return {
    type: 'caseCreation',
    userPrompt: caseRandomSelection.prompt,
    caseName: caseRandomSelection.caseName,
    inputFields: generateInputFields(inputFieldsLength),
    files: []
  }
}

export const getCase = () => {
  const cases: any[] = Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () => {
    return {
      inputFields: Array.from({ length: faker.number.int({ min: 2, max: 4 }) }, () => {
        return {
          fieldName: faker.lorem.word(),
          fieldValue: faker.lorem.lines(1),
        };
      }),
      steps: Array.from({ length: faker.number.int({ min: 2, max: 4 }) }, () => data.newStep())
    };
  });

  return cases;
};

export function existsCase(inputFieldsLength: number = 3): Object{
  const {name, source, externalId} = data.newCase();
  return {
    name,
    source,
    externalId,
    inputFields: generateInputFields(inputFieldsLength, true),
    steps: data.getSteps(),
  };
}
