import { Express, Router } from 'express';

import { Knex } from 'knex';
import accessTokenRoutes from '../../src/routes/internal/accessToken';
import { buildApp } from '../../src/app';
import { faker } from '@faker-js/faker';
import { newProject } from '../data/index';
import projectRoutes from '../../src/routes/internal/project';
import request from 'supertest';
import { setupDB } from '../../src/config/db';
import { setupUserTenant } from '../utils/tenant';

export type ApiVersion = 'core' | 'v1';

interface Tenant {
  user: {
    uid: string;
    email: string;
    handle: string;
  };
  password: string;
}

export interface TestContext {
  app: Express;
  tenant: Tenant;
  tenantDb: Knex;
  db: Knex;
  session: string;
  accessToken?: { secretKey: string; ownerUid: string; ownerType: string };
  projectKey: string;
  projectUid: number;
  basePath: {
    core: string;
    v1: string;
  };
  apiRoutes: {
    core: string;
    v1: string;
  };
}

export interface TestEnvironmentOptions {
  permissions?: string[];
  primaryVersion?: ApiVersion;
  routes?: Router[];
}

/**
 * Centralized test setup utility for creating a test environment
 * @param {TestEnvironmentOptions} options - Configuration options for test setup
 * @returns {Promise<TestContext>} An object containing setup details for tests
 */
export async function setupTestEnvironment(
  options: TestEnvironmentOptions = {},
): Promise<TestContext> {
  const {
    permissions = [],
    primaryVersion = 'core',
    routes = [accessTokenRoutes, projectRoutes],
  } = options;

  const db = setupDB();
  const app = buildApp(db, routes, primaryVersion === 'v1' ? true : false);
  const tenant = await setupUserTenant(db, app);

  const apiRoutes = {
    core: process.env.API_CORE_ROUTE || '/core',
    v1: process.env.API_VERSION_1_ROUTE || '/v1',
  };

  const tenantDb = setupDB(tenant.user.uid, tenant.dbServer);

  const signin = await request(app).post(`${apiRoutes.core}/signin`).send({
    email: tenant.user.email,
    password: tenant.password,
  });

  const session = signin.headers['set-cookie'];

  const createdAccessTokenResponse = await request(app)
    .post(`${apiRoutes.core}/${tenant.handle}/accessTokens`)
    .set('Accept', 'application/json')
    .set('Cookie', session)
    .send({
      name: faker.word.noun(5),
      days: 2,
      permissions,
    });

  const accessToken = createdAccessTokenResponse.body;

  const createdProject = await request(app)
    .post(`${apiRoutes.core}/${tenant.handle}/projects`)
    .set('Accept', 'application/json')
    .set('Cookie', session)
    .send(newProject());

  const projectKey = createdProject.body.key;
  const projectUid = createdProject.body.uid;

  const basePath = {
    core: `${apiRoutes.core}/${tenant.handle}/projects/${projectKey}`,
    v1: `${apiRoutes.v1}/${tenant.handle}/projects/${projectKey}`,
  };

  return {
    app,
    db,
    tenant,
    tenantDb,
    session,
    accessToken,
    projectKey,
    projectUid,
    basePath,
    apiRoutes,
  };
}
