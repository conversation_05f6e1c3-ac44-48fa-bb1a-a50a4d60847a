const pinata_data = {
  sessionID: '728a3644-2d5c-4a64-b1bb-5e637cbde569',
  charterID: '8911a644-2d5c-4a64-b1bb-5e637cbde581',
  state: {
    title: 'UI test',
    charter: {
      content: '<p>Test the UI</p>',
      text: 'Test the UI',
    },
    preconditions: {
      content: '<ol><li>Open the app.</li></ol>',
      text: 'Open the app.',
    },
    duration: 0,
    status: 'start',
    timer: 73567,
    started: '07:28 | 08-26-2023',
    ended: '',
    quickTest: true,
    path: '/main/workspace',
    mindmap: {
      nodes: [
        {
          id: '5e274797-4db7-4fe8-a983-8b8abf8771c5',
          text: 'System Under Test',
          url: 'https://features.pinata.ai',
          fx: -210.9125181819311,
          fy: -583.1010883631283,
        },
        {
          id: '4763495c-62b7-4625-9083-2d40045b6550',
          text: 'Feature #1',
          fx: 99.1983655368465,
          fy: -582.6407249084972,
        },
      ],
      connections: [
        {
          source: '5e274797-4db7-4fe8-a983-8b8abf8771c5',
          target: '4763495c-62b7-4625-9083-2d40045b6550',
        },
      ],
    },
  },
  items: [
    {
      id: 'ed220f6c-b5a9-4ff0-b5b8-bd4870df4c9b',
      sessionType: 'Video',
      fileType: 'video',
      fileName: 'video-ed220.mp4',
      filePath:
        '/home/<USER>/.config/pinata/sessions/728a3644-2d5c-4a64-b1bb-5e637cbde569/video-ed220.mp4',
      poster:
        '/home/<USER>/.config/pinata/sessions/728a3644-2d5c-4a64-b1bb-5e637cbde569/poster-1b55a.png',
      timer_mark: 300,
      comment: {
        type: 'Comment',
        content: '',
        text: '',
      },
      tags: [],
      emoji: [],
      followUp: false,
      createdAt: 1692926592841,
    },
    {
      id: '641518b5-a611-4e36-8490-e40afbd3c397',
      sessionType: 'Screenshot',
      fileType: 'image',
      fileName: 'image-64151.png',
      filePath:
        '/home/<USER>/.config/pinata/sessions/728a3644-2d5c-4a64-b1bb-5e637cbde569/image-64151.png',
      timer_mark: 431,
      comment: {
        type: 'Comment',
        content: '<p>Testing this</p>',
        text: 'Testing this',
      },
      tags: [],
      emoji: [],
      followUp: true,
      createdAt: 1692926609765,
    },
    {
      id: 'd8f733aa-9697-49de-b146-064845148fea',
      sessionType: 'Audio',
      fileType: 'audio',
      fileName: 'audio-d8f73.mp3',
      filePath:
        '/home/<USER>/.config/pinata/sessions/728a3644-2d5c-4a64-b1bb-5e637cbde569/audio-d8f73.mp3',
      timer_mark: 445,
      poster:
        '/home/<USER>/.config/pinata/sessions/728a3644-2d5c-4a64-b1bb-5e637cbde569/poster-08228.png',
      comment: {
        type: 'Comment',
        content: '',
        text: '',
      },
      tags: [],
      emoji: [],
      followUp: false,
      createdAt: 1692926621231,
    },
  ],
  notes: {
    content: '<p>These are my notes.</p>',
    text: 'These are my notes',
  },
};

export { pinata_data };
