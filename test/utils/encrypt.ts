import forge from "node-forge";
import crypto from "crypto";
import { encryptHybrid, newSymmetricKey } from "@ss-libs/ss-component-encryption";
import { Knex } from "knex";

export function encryptObject(dataObject, latestPublicKey) {
    const publicKey = forge.pki.publicKeyFromPem(forge.util.decode64(latestPublicKey.key));
    const symmetricKey = newSymmetricKey();
    const encryptedFields = {};
    const fieldMetadata = {};

    for (const [key, value] of Object.entries(dataObject)) {
        if (value) {
            const { iv, tag, encryptedData } = encryptHybrid({ data: value as string, symmetricKey, encodeOutput: true });
            encryptedFields[key] = encryptedData;
            fieldMetadata[key] = {
                iv: forge.util.encode64(iv),
                tag: forge.util.encode64(tag.bytes()),
            };
        }
    }
    const rsaEncryptedKey = publicKey.encrypt(symmetricKey, 'RSA-OAEP');
    const symmetricKeyData = {
        fieldMetadata,
        version: latestPublicKey.version,
        key: forge.util.encode64(rsaEncryptedKey),
        service: latestPublicKey.service,
        publicKeyUid: latestPublicKey.uid,
    };
    return { encryptedFields, symmetricKeyData };
}

export const generateKeyPair = async (db: Knex, service: string) => {
    const { publicKey, privateKey } = crypto.generateKeyPairSync("rsa", {
        modulusLength: 4096,
        publicKeyEncoding: { type: "spki", format: "pem" },
        privateKeyEncoding: { type: "pkcs8", format: "pem" },
    });
    const uid = crypto.randomUUID()
    await db("publicKeys").insert({
        uid,
        key: forge.util.encode64(publicKey),
        service,
        version: 1,
        createdAt: db.fn.now(),
    });

    const formattedPrivateKey = privateKey.replace(/\n/g, "\\n")
    return {
        uid,
        key: forge.util.encode64(publicKey),
        service,
        version: 1,
        privateKey: formattedPrivateKey,
    }
}
