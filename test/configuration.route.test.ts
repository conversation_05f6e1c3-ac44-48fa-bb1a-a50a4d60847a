import * as data from './data/index';

import { Application } from 'express';
import { buildApp } from '../src/app';
import { closeQueues } from '../src/lib/queue';
import configurationRoutes from '../src/routes/internal/configuration';
import { expect } from 'chai';
import orgRoutes from '../src/routes/internal/org';
import projectRoutes from '../src/routes/internal/project';
import request from 'supertest';
import { setupDB } from '../src/config/db';
import { setupOrgTenant } from './utils/tenant';
import { tenantManager } from '../src/lib/tenants';

let app: Application, basePath: string, session, project, configuration;
const db = setupDB();

afterAll(async () => {
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

beforeAll(async () => {
  app = buildApp(db, [orgRoutes, configurationRoutes, projectRoutes]);

  const tenant = await setupOrgTenant(db, app);
  const email = tenant.admin.email;
  const password = tenant.password;
  const handle = tenant.handle;

  const signin = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signin`)
    .send({ email, password });
  session = signin.headers['set-cookie'];

  // create default project and retrieve it's latest folder
  const createProject = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
    .set('Cookie', session)
    .send(data.newProject());
  project = createProject.body;
  basePath = `${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${project.key}/configurations`;
});

describe('Configuration', () => {
  const options = ['Chrome', 'Firefox', 'Safari'];

  it('should not pass create test configurations when empty body', async () => {
    const res = await request(app)
      .post(basePath)
      .set('Cookie', session)
      .send({});

    expect(res.statusCode).to.be.oneOf([400]);
  });

  it('should be able to create a new test configuration', async () => {
    const res = await request(app)
      .post(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        name: 'Browsers',
        options: options.map((name) => ({ name })),
        description: 'Browsers to run tests on',
      });

    configuration = res.body;
    expect(res.statusCode).to.be.oneOf([200]);
    expect(configuration.name).to.equal('Browsers');
    expect(configuration.options).to.be.an('array');
    expect(configuration.description).to.equal('Browsers to run tests on');
    expect(configuration.options).to.have.lengthOf(3);

    for (const opt of configuration.options) expect(opt.name).oneOf(options);
  });

  it('should be able to get a test configuration by its id', async () => {
    const res = await request(app)
      .get(`${basePath}/${configuration.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session);

    const options = ['Chrome', 'Firefox', 'Safari'];

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body).to.not.be.eq({});
    expect(res.body.name).to.equal('Browsers');
    expect(res.body.options).to.be.an('array');

    for (const opt of res.body.options) expect(opt.name).oneOf(options);
  });

  it('should be able to get all test configurations', async () => {
    const res = await request(app)
      .get(basePath)
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body).to.not.be.eq({});
    expect(res.body.configurations).to.be.an('array');
  });

  it('should be able to update a test configuration by its id', async () => {
    const options = [...configuration.options];
    options.pop();

    const res = await request(app)
      .patch(`${basePath}/${configuration.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send({
        name: 'Browsers',
        options,
        description: 'Browsers to run tests on',
      });

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body).to.not.be.eq({});
    expect(res.body.name).to.equal('Browsers');
    expect(res.body.options).to.be.an('array');
    expect(res.body.options).to.have.lengthOf(options.length);

    const names = options.map((o) => o.name);
    for (const opt of res.body.options) expect(opt.name).oneOf(names);
  });

  it('should be able to delete a test configuration by its id', async () => {
    let res = await request(app)
      .delete(`${basePath}/${configuration.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).to.be.oneOf([200]);

    res = await request(app)
      .get(`${basePath}/${configuration.uid}`)
      .set('Accept', 'application/json')
      .set('Cookie', session);

    expect(res.statusCode).to.be.oneOf([200]);
    expect(res.body).to.equal(null);
  });
});
