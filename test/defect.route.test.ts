import { Application } from 'express';
import { buildApp } from '../src/app';
import caseRoutes from '../src/routes/internal/case';
import { closeQueues } from '../src/lib/queue';
import folderRoutes from '../src/routes/internal/folder';
import { generateProjectKey } from './data/index';
import projectRoutes from '../src/routes/internal/project';
import defectRoutes from '../src/routes/internal/defect';
import request from 'supertest';
import runRoutes from '../src/routes/internal/run';
import { setupDB } from '../src/config/db';
import { setupUserTenant } from './utils/tenant';
import { tenantManager } from '../src/lib/tenants';
import { IntegrationServiceFactory } from '../src/integrations';

let app: Application;
let user, password: string, handle: string, session, userId, dbServer;
let testProject;
let tenantDB;

const db = setupDB();

// Mock the IntegrationServiceFactory
jest.mock('../src/integrations', () => {
  const mockService = {
    prepareAuthHeader: jest.fn().mockResolvedValue('Bearer mock-token'),
    createEntity: jest.fn().mockResolvedValue({
      success: true,
      data: {
        externalId: 'MOCK-123',
        priority: 1,
        status: 2,
        customFields: {
          apiUrl: 'https://mock-integration.com/defects/MOCK-123',
          tags: ['bug', 'critical']
        }
      }
    }),
    createRemoteLink: jest.fn().mockResolvedValue({
      success: true
    })
  };

  return {
    IntegrationServiceFactory: {
      getService: jest.fn().mockReturnValue(mockService)
    }
  };
});

beforeAll(async () => {
  app = buildApp(db, [caseRoutes, folderRoutes, runRoutes, projectRoutes, defectRoutes]);

  const tenant = await setupUserTenant(db, app);
  user = tenant.user;
  password = tenant.password;
  handle = tenant.handle;
  dbServer = tenant.dbServer;

  const res = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/signIn`)
    .set('Accept', 'application/json')
    .send({ email: user.email, password });

  session = res.headers['set-cookie'];
  userId = res.body.user.uid;
  
  // Initialize tenantDB here
  tenantDB = setupDB(userId, dbServer);

  // Create a test project for the defect tests
  const projectRes = await request(app)
    .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects`)
    .set('Accept', 'application/json')
    .set('Cookie', session)
    .send({
      externalId: 'defect-test-project',
      customFields: { version: '1.0', platform: 'test' },
      source: 'testfiesta',
      name: 'Defect Test Project',
      key: generateProjectKey(),
    });

  testProject = projectRes.body;
});

afterAll(async () => {
  if (tenantDB) await tenantDB.destroy();
  await db.destroy();
  await closeQueues();
  await tenantManager.shutdown();
});

describe('Defect Controller', () => {
   it('should create a defect and return it', async () => {

   // Create an integration record
   const [integration] = await tenantDB('integrations')
      .insert({
         name: 'Test Integration',
         service: 'jira',
         type: 'external',
         status: 'active',
         configuration: {
         url: 'https://mock-jira.com'
         }
      })
      .returning('*');

   // Create an integration token
   await tenantDB('integrationTokens')
      .insert({
         integrationUid: integration.uid,
         ownerUid: userId,
         accessToken: 'mock-token',
         refreshToken: 'mock-refresh-token',
         url: 'https://mock-jira.com',
         expiresAt: new Date(Date.now() + 3600000).toISOString()
      });

   // Create a test case and execution for linking
   const [testCase] = await tenantDB('testCases')
      .insert({
         name: 'Test Case for Defect',
         projectUid: testProject.uid,
         testCaseRef: 1,
         version: 1,
         active: true,
         parentUid: 1
      })
      .returning('*');

   const [testRun] = await tenantDB('tags')
      .insert({
         name: 'Test Run for Defect',
         projectUid: testProject.uid,
         systemType: 'run',
         entityTypes: []
      })
      .returning('*');

   const [execution] = await tenantDB('testExecutions')
      .insert({
         testCaseRef: testCase.testCaseRef,
         testRunUid: testRun.uid,
         testCaseUid: testCase.uid,
         projectUid: testProject.uid,
         status: 1
      })
      .returning('*');

   // Create external entity mappings for priority and status
   await tenantDB('externalEntities')
      .insert([
         {
         source: 'jira',
         sourceId: '1',
         entityType: 'tag',
         entityUid: '1',
         customFields: { type: 'priority' }
         },
         {
         source: 'jira',
         sourceId: '2',
         entityType: 'tag',
         entityUid: '2',
         customFields: { type: 'status' }
         }
      ]);

   // Request body for creating a defect
   const defectData = {
      name: 'Test Defect',
      description: 'This is a test defect',
      integrationUid: integration.uid,
      executionId: execution.uid,
      testRunUid: testRun.uid,
      priority: 1,
      status: 2
   };

   // Make the request to create a defect
   const res = await request(app)
      .post(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${testProject.key}/defects`)
      .set('Accept', 'application/json')
      .set('Cookie', session)
      .send(defectData);

   // Assertions
   expect(res.statusCode).toBe(200);
   expect(res.body).toHaveProperty('uid');
   expect(res.body.name).toBe(defectData.name);
   expect(res.body.externalId).toBe('MOCK-123');
   expect(res.body.integrationSourceUid).toBe(String(integration.uid));

   // Verify the defect was created in the database
   const defectInDb = await tenantDB('defects')
      .where('uid', res.body.uid)
      .first();

   expect(defectInDb).toBeTruthy();
   expect(defectInDb.name).toBe(defectData.name);
   expect(defectInDb.externalId).toBe('MOCK-123');
   
   // Verify the defect-execution link was created
   const defectExecution = await tenantDB('defectExecutions')
      .where({
         defectUid: res.body.uid,
         executionUid: execution.uid
      })
      .first();

   expect(defectExecution).toBeTruthy();
   expect(defectExecution.executionUrl).toContain(String(execution.uid));

   // Verify the integration service was called correctly
   expect(IntegrationServiceFactory.getService).toHaveBeenCalledWith('jira');
   const mockService = IntegrationServiceFactory.getService('jira');
   expect(mockService.prepareAuthHeader).toHaveBeenCalled();
   expect(mockService.createEntity).toHaveBeenCalledWith(
      'Bearer mock-token',
      'defect',
      expect.objectContaining({
         name: defectData.name,
         description: defectData.description
      })
   );
   expect(mockService.createRemoteLink).toHaveBeenCalled();
   });

   it('should get open defects count', async () => {
     // Create an integration record
      const [integration] = await tenantDB('integrations')
         .insert({
            name: 'Test Integration',
            service: 'jira',
            type: 'external',
            status: 'active',
            configuration: {
               url: 'https://mock-jira.com'
            }
         })
         .returning('*');
      
      // Create open defects for testing
      await tenantDB('defects')
         .insert([
            {
               name: 'Open Defect 1',
               externalId: 'OPEN-001',
               projectUids: [testProject.uid],
               integrationSourceUid: integration.uid.toString(),
               customFields: {},
               archivedAt: null
            },
            {
               name: 'Open Defect 2',
               externalId: 'OPEN-002',
               projectUids: [testProject.uid],
               integrationSourceUid: integration.uid.toString(),
               customFields: {},
               archivedAt: null
            }
         ]);
   
      const res = await request(app)
         .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${testProject.key}/defects/open/count`)
         .query({ integrationService: integration.service })
         .set('Accept', 'application/json')
         .set('Cookie', session);
      
      expect(res.statusCode).toBe(200);
      expect(res.body).toHaveProperty('count');
      expect(res.body.count).toBeGreaterThanOrEqual(2);
    });

   it('should get closed defects count', async () => {
      // Create an integration record
      const [integration] = await tenantDB('integrations')
         .insert({
            name: 'Test Integration',
            service: 'jira',
            type: 'external',
            status: 'active',
            configuration: {
               url: 'https://mock-jira.com'
            }
         })
         .returning('*');
      
      // Create closed defects for testing
      await tenantDB('defects')
         .insert([
            {
               name: 'Closed Defect 1',
               externalId: 'CLOSED-001',
               projectUids: [testProject.uid],
               integrationSourceUid: integration.uid.toString(),
               customFields: {},
               archivedAt: new Date().toISOString()
            },
            {
               name: 'Closed Defect 2',
               externalId: 'CLOSED-002',
               projectUids: [testProject.uid],
               integrationSourceUid: integration.uid.toString(),
               customFields: {},
               archivedAt: new Date().toISOString()
            }
         ]);
      
      const res = await request(app)
         .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${testProject.key}/defects/closed/count`)
         .query({ integrationService: integration.service })
         .set('Accept', 'application/json')
         .set('Cookie', session);
      
      expect(res.statusCode).toBe(200);
      expect(res.body).toHaveProperty('count');
      expect(res.body.count).toBeGreaterThanOrEqual(2);
   });

   it('should get defect attachments', async () => {
      // Create a defect
      const [defect] = await tenantDB('defects')
         .insert({
            name: 'Defect with Attachments',
            externalId: 'ATTACH-001',
            projectUids: [testProject.uid],
            customFields: {}
         })
         .returning('*');
      
      // Create an attachment
      const [attachment] = await tenantDB('attachments')
         .insert({
            name: 'test-attachment.png',
            fileType: 'image/png',
            mediaType: 'attachment',
            size: 1024,
            ownerUid: userId
         })
         .returning('*');
      
      // Link attachment to defect
      await tenantDB('defectAttachments')
         .insert({
            defectUid: defect.uid,
            attachmentUid: attachment.uid
         });
      
      const res = await request(app)
         .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${testProject.key}/defects/${defect.uid}/attachments`)
         .set('Accept', 'application/json')
         .set('Cookie', session);
      
      expect(res.statusCode).toBe(200);
      expect(Array.isArray(res.body)).toBe(true);
      expect(res.body.length).toBe(1);
      expect(res.body[0]).toHaveProperty('uid', attachment.uid);
      expect(res.body[0]).toHaveProperty('name', 'test-attachment.png');
      expect(res.body[0]).toHaveProperty('previewUrl');
   });

   it('should get defect executions', async () => {
      // Create a defect
      const [defect] = await tenantDB('defects')
         .insert({
            name: 'Defect with Executions',
            externalId: 'EXEC-001',
            projectUids: [testProject.uid],
            customFields: {}
         })
         .returning('*');
      
      // Create a test case
      const [testCase] = await tenantDB('testCases')
         .insert({
            name: 'Test Case for Execution',
            projectUid: testProject.uid,
            testCaseRef: 2,
            version: 1,
            active: true,
            parentUid: 1
         })
         .returning('*');
      
      // Create a test execution
      const [execution] = await tenantDB('testExecutions')
         .insert({
            testCaseRef: testCase.testCaseRef,
            testCaseUid: testCase.uid,
            projectUid: testProject.uid,
            status: 1
         })
         .returning('*');
      
      // Link execution to defect
      await tenantDB('defectExecutions')
         .insert({
            defectUid: defect.uid,
            executionUid: execution.uid,
            executionUrl: `http://example.com/executions/${execution.uid}`
         });
      
      const res = await request(app)
         .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${testProject.key}/defects/${defect.uid}/executions`)
         .set('Accept', 'application/json')
         .set('Cookie', session);
      
      expect(res.statusCode).toBe(200);
      expect(Array.isArray(res.body)).toBe(true);
      expect(res.body.length).toBe(1);
      expect(res.body[0]).toHaveProperty('uid', execution.uid);
      expect(res.body[0]).toHaveProperty('name', testCase.name);
      expect(res.body[0]).toHaveProperty('url');
   });

   it('should get defect runs', async () => {   
      // Create a defect
      const [defect] = await tenantDB('defects')
         .insert({
            name: 'Defect with Runs',
            externalId: 'RUN-001',
            projectUids: [testProject.uid],
            customFields: {}
         })
         .returning('*');
      
      // Create a test case
      const [testCase] = await tenantDB('testCases')
         .insert({
            name: 'Test Case for Run',
            projectUid: testProject.uid,
            testCaseRef: 3,
            version: 1,
            active: true,
            parentUid: 1
         })
         .returning('*');
      
      // Create a test run
      const [testRun] = await tenantDB('tags')
         .insert({
            name: 'Test Run for Defect Runs',
            projectUid: testProject.uid,
            systemType: 'run',
            entityTypes: []
         })
         .returning('*');
      
      // Create a test execution linked to the run
      const [execution] = await tenantDB('testExecutions')
         .insert({
            testCaseRef: testCase.testCaseRef,
            testRunUid: testRun.uid,
            testCaseUid: testCase.uid,
            projectUid: testProject.uid,
            status: 1
         })
         .returning('*');
      
      // Link execution to defect
      await tenantDB('defectExecutions')
         .insert({
            defectUid: defect.uid,
            executionUid: execution.uid,
            executionUrl: `http://example.com/executions/${execution.uid}`
         });
      
      const res = await request(app)
         .get(`${process.env.API_INTERNAL_ROUTE}/${handle}/projects/${testProject.key}/defects/${defect.uid}/runs`)
         .set('Accept', 'application/json')
         .set('Cookie', session);
      
      expect(res.statusCode).toBe(200);
      expect(Array.isArray(res.body)).toBe(true);
      expect(res.body.length).toBe(1);
      expect(res.body[0]).toHaveProperty('uid', testRun.uid);
      expect(res.body[0]).toHaveProperty('name', testRun.name);
   });
});