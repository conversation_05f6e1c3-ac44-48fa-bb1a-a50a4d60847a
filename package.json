{"name": "testfiesta-backend", "version": "0.62.8", "description": "", "main": "src/app.ts", "dependencies": {"@atlaskit/adf-utils": "^19.13.1", "@langchain/core": "^0.3.42", "@langchain/openai": "^0.4.4", "@openfga/sdk": "^0.3.5", "@otplib/preset-default": "^12.0.1", "@sentry/cli": "^2.39.1", "@sentry/node": "^8.30.0", "@sentry/profiling-node": "^8.55.0", "@ss-libs/ss-component-admin": "^1.4.2", "@ss-libs/ss-component-auth": "^5.4.0", "@ss-libs/ss-component-db": "3.3.0", "@ss-libs/ss-component-encryption": "^1.9.1", "@ss-libs/ss-component-media": "^1.18.0", "@ss-libs/ss-component-notifications": "^1.2.1", "@ss-libs/ss-component-payments": "^3.4.0", "@temporalio/activity": "^1.11.7", "@temporalio/client": "^1.11.7", "@temporalio/worker": "^1.11.7", "@temporalio/workflow": "^1.11.7", "@testfiesta/tacotruck": "^0.3.0", "@tsoa/runtime": "^6.6.0", "axios": "^1.4.0", "bcrypt": "^5.1.0", "bottleneck": "^2.19.5", "bullmq": "^5.41.5", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cpx": "^1.5.0", "dayjs": "^1.11.7", "dotenv": "^14.3.2", "dotenv-expand": "^12.0.1", "express": "^4.17.3", "express-fileupload": "^1.5.1", "express-validator": "^7.0.1", "helmet": "^5.0.2", "http-status-codes": "^2.3.0", "i18n": "^0.15.1", "joi": "^17.13.3", "knex": "^3.1.0", "langchain": "^0.3.19", "lodash": "^4.17.21", "mime-types": "^2.1.35", "module-alias": "^2.2.3", "morgan": "^1.10.0", "node-forge": "^1.3.1", "node-html-parser": "^6.1.13", "objection": "^3.1.2", "otplib": "^12.0.1", "passport": "^0.6.0", "pg": "^8.11.3", "qrcode": "^1.5.3", "stripe": "^13.7.0", "tsoa": "^6.6.0", "unique-names-generator": "^4.7.1", "uuid": "^8.3.2", "winston": "^3.6.0", "zod": "^3.24.2"}, "devDependencies": {"@faker-js/faker": "^8.4.1", "@types/bcrypt": "^5.0.1", "@types/chai": "^4.3.10", "@types/compression": "^1.7.4", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.15", "@types/express": "^4.17.21", "@types/i18n": "^0.13.8", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.6", "@types/mime-types": "^2.1.4", "@types/module-alias": "^2.0.4", "@types/morgan": "^1.9.7", "@types/node": "^20.17.16", "@types/node-forge": "^1.3.11", "@types/passport": "^1.0.14", "@types/pg": "^8.11.6", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.6", "@typescript-eslint/eslint-plugin": "^7.5.0", "@typescript-eslint/parser": "^7.5.0", "chai": "^4.3.7", "eslint": "^8.57.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.2", "husky": "^8.0.3", "jest": "^29.7.0", "knip": "^5.43.1", "nodemon": "^3.0.1", "prettier": "^3.5.3", "sinon": "^14.0.0", "supertest": "^6.2.4", "ts-jest": "^29.2.4", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.4.3", "yargs": "^17.7.2"}, "scripts": {"watch": "tsc -p tsconfig.json --watch", "api:make-docs:prod": "tsoa spec --host 'api.testfiesta.com' && perl -pi -e 's/api\\.testfiesta\\.com\\//api.testfiesta.com/g' dist/swagger.json", "api:make-routes": "tsoa routes", "api:test": "jest --runInBand --testTimeout=10000000 --detectOpenHandles --forceExit --testRegex=test/v1", "build": "tsc -p tsconfig.json && npm run copy", "copy": "cpx \"src/**/*.hbs\" dist/src", "start": "npm run copy && node dist/src/index.js", "start:dev": "npm run copy && nodemon dist/src/index.js", "start:worker": "npm run copy && node dist/src/worker.js", "start:worker:dev": "npm run copy && nodemon dist/src/worker.js", "fga": "npm run copy && npx ts-node db/fga-migrator.ts", "coverage": "nyc --reporter=html --reporter=text-summary npm run test", "pretests": "npm run build", "test:assist": "jest --runInBand --testTimeout=10000 --detectOpenHandles --forceExit --testRegex=test/assist.*.test.ts$ --testPathIgnorePatterns=test/assist.pipeline.test.ts", "test:assist:pipeline": "jest --runInBand --testTimeout=10000000 --detectOpenHandles --forceExit test/assist.pipeline.test.ts", "test": "jest --runInBand --forceExit --detectOpenHandles --testTimeout=10000000 --testPathIgnorePatterns=test/assist.*.test.ts$  --testPathIgnorePatterns=test/v1", "lint": "npx eslint 'src/**/*.{js,jsx,ts,tsx}'", "lint:fix": "npx eslint --fix './**/*.{js,jsx,ts,tsx}'", "format": "npx prettier --write './**/*.{js,jsx,ts,tsx}'", "migrate:latest": "npx knex migrate:latest", "tenant:copy": "npx ts-node db/copy-tenant.ts copy", "migrate:up": "npx knex migrate:up", "migrate:down": "npx knex migrate:down", "seed:billing-plans": "npx ts-node ./db/utils/stripe-plan-sync.ts", "seed:encryptionKeys": "npx ts-node ./db/utils/encryptionKeys.ts", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org testfiesta --project backend ./dist/src && sentry-cli sourcemaps upload --org testfiesta --project backend ./dist/src", "knip": "knip --config knip.jsonc", "migrate:encryption": "npx ts-node ./db/encryption-migrator.ts", "maintenance:on": "npx ts-node ./db/utils/tenantMaintenance.ts true", "maintenance:off": "npx ts-node ./db/utils/tenantMaintenance.ts false", "integration:syncDuration": "npx ts-node ./src/utils/integrationSyncSchedule.ts", "integration:resync": "npx ts-node ./db/utils/integrationSync.ts", "integration:resume": "npx ts-node ./db/utils/resumeIntegrationsSync.ts", "integration:addSyncDuration": "npx ts-node ./db/utils/integrationSyncDuration.ts", "migrate:version": "npx ts-node db/version-migrator.ts $VERSION_ARGS && node db/migrator.js $DBM_ARGS && npx ts-node db/fga-migrator.ts migrate $FGM_ARGS", "version:insert": "npx ts-node ./db/utils/addAppVersion.ts"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "author": "TestFiesta, Inc", "license": "Closed", "_moduleAliases": {"@app": "./dist/src", "@docs": "../dist/docs"}}