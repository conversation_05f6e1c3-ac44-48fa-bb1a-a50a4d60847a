services:
  server:
    image: testfiesta-backend
    entrypoint: ['docker-entrypoint.sh']
    command: [npm, start]
    ports:
      - 5050:5050
    env_file:
      - ./.env.local
    depends_on:
      build-app:
        condition: service_completed_successfully
    networks:
      - testfiesta

  worker:
    image: testfiesta-backend
    entrypoint: ['docker-entrypoint.sh']
    command: [npm, run, start:worker]
    env_file:
      - ./.env.local
    depends_on:
      build-app:
        condition: service_completed_successfully
    networks:
      - testfiesta

  build-app:
    image: testfiesta-backend
    entrypoint: ['docker-entrypoint.sh']
    command: [npm, run, build]
    depends_on:
      migrate-db:
        condition: service_completed_successfully
      migrate-openfga:
        condition: service_completed_successfully

  migrate-db:
    build:
      context: ./
      dockerfile: Dockerfile
      args:
        - GITHUB_NPM_TOKEN=${GITHUB_NPM_TOKEN}
    image: testfiesta-backend
    container_name: testfiesta-backend
    command: [npm, run, migrate:latest]
    entrypoint: ['docker-entrypoint.sh']
    env_file:
      - ./.env.local
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - testfiesta

  openfga:
    depends_on:
      migrate-openfga:
        condition: service_completed_successfully
    image: openfga/openfga:latest
    environment:
      - OPENFGA_DATASTORE_ENGINE=postgres
      - OPENFGA_DATASTORE_URI=postgres://${DB_USERNAME}:${DB_PASSWORD}@postgres:5432/openfga
      - OPENFGA_LOG_FORMAT=json
    command: run
    networks:
      - testfiesta
    ports:
      - '8080:8080'
      - '8081:8081'
      - '3000:3000'

  migrate-openfga:
    depends_on:
      postgres:
        condition: service_healthy
    image: openfga/openfga:latest
    command: migrate
    environment:
      - OPENFGA_DATASTORE_ENGINE=postgres
      - OPENFGA_DATASTORE_URI=postgres://${DB_USERNAME}:${DB_PASSWORD}@postgres:5432/openfga
    networks:
      - testfiesta

  redis:
    image: redis:alpine
    restart: always
    ports:
      - '6379:6379'
    networks:
      - testfiesta
    healthcheck:
      test: ['CMD', 'redis-cli', '--raw', 'incr', 'ping']
      interval: 30s
      timeout: 3s
      retries: 3

  postgres:
    image: postgres:15
    restart: always
    environment:
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_DB=${DB_DATABASE}
      - POSTGRES_USER=${DB_USERNAME}
    ports:
      - '5432:5432'
    networks:
      - testfiesta
    healthcheck:
      test:
        ['CMD-SHELL', "sh -c 'pg_isready -U ${DB_USERNAME} -d ${DB_DATABASE}'"]
      interval: 3s
      timeout: 3s
      retries: 3
    volumes:
      - ./db/openfga.sql:/docker-entrypoint-initdb.d/openfga.sql
      
  temporal:
    image: temporalio/auto-setup:1.24.1
    container_name: temporal
    environment:
      - DB=postgres12
      - DB_PORT=5432
      - POSTGRES_USER=${DB_USERNAME}
      - POSTGRES_PWD=${DB_PASSWORD}
      - POSTGRES_SEEDS=postgres
    ports:
      - '7233:7233'
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - testfiesta
    healthcheck:
      test: ["CMD", "temporal-server", "health"]
      interval: 5s
      timeout: 5s
      retries: 5

  temporal-ui:
    image: temporalio/ui:2.27.2
    container_name: temporal-ui
    environment:
      - TEMPORAL_ADDRESS=temporal:7233
      - TEMPORAL_CORS_ORIGINS=http://localhost:8080
    ports:
      - '8083:8080'
    networks:
      - testfiesta
    healthcheck:
      test: ["CMD", "wget", "--spider", "http://localhost:8083"]
      interval: 10s
      timeout: 10s
      retries: 5


networks:
  testfiesta:
    driver: bridge
