{"[dotenv]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "[properties]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "[ignore]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "[dockerfile]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "always"}, "eslint.validate": ["typescript", "javascript"], "editor.defaultFormatter": "dbaeumer.vscode-eslint"}