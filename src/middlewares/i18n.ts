import i18n from 'i18n';
import path from 'path';
import { Request, Response, NextFunction } from 'express';

i18n.configure({
  locales: ['en', 'fr'],
  directory: path.join(process.cwd(), 'locales'),
  defaultLocale: 'en',
  queryParameter: 'lang',
  autoReload: true,
  updateFiles: false,
  objectNotation: true,
});

export function configureI18n(req: Request, res: Response, next: NextFunction) {
  i18n.init(req, res);
  translateRes(res);
  next();
}

export function translateRes(res: Response) {
  const originalJson = res.json;

  res.json = function (data) {
    if (data && typeof data === 'object') {
      if (data.message && typeof data.message === 'string') {
        data.message = res.__(data.message);
      }
    }

    return originalJson.call(this, data);
  };
}
