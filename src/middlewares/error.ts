import { NextFunction, Request, Response } from 'express';
import { ValidateError } from 'tsoa';

import { ApplicationError } from '@app/lib/http';
import { ApplicationError as SSLibApplicationError } from '@ss-libs/ss-component-auth/dist/lib/http';

import { StatusCodes } from 'http-status-codes';
import errorConstants from '@app/constants/errors';
import logger from '@app/config/logger';
import { serializeErr } from '@app/lib/error';

export function notFound(_req: Request, res: Response, next: NextFunction) {
  return next(
    new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.RESOURCE_NOT_FOUND,
    ),
  );
}

/**
 * Middleware for automatically interpreting `ApplicationError`. It responds
 * with `INTERNAL_SERVER_ERROR` if the error is not recognized
 * @param logger octonet logger
 */
export function errorHandler(
  err: any,
  req: Request,
  res: Response,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  next: NextFunction,
) {
  // handling for asynchronous situations where error is thrown after response has been sent
  if (res.headersSent) return;

  if (
    err instanceof ApplicationError
    || err instanceof SSLibApplicationError
    || err.constructor.name === 'ApplicationError'
  ) {
    res.status(err.code).json({ message: err.message, data: err.data });
  } else if (err instanceof ValidateError) {
    return res.status(422).json({
      message: 'Validation Failed',
      details: err?.fields,
    });
  } else {
    logger.log('error', serializeErr(err));
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: errorConstants.INTERNAL_SERVER_ERROR,
    });
  }

  // @TODO: log error here
}
