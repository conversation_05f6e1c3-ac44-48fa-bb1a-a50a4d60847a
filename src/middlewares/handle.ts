import {
  NextFunction, Request, Response, Router,
} from 'express';
import { tenantConfig as defaultTenantConfigs } from '@app/constants/tenant';
import { ApplicationError } from '@app/lib/http';
import { BLACKLISTED_TOKENS } from '@app/constants/blacklist';
import { FgaService } from '@ss-libs/ss-component-auth';
import { asyncLocalStorage } from '@app/middlewares/requestTraceId';
import { Handle } from '@app/models/handle';
import { StatusCodes } from 'http-status-codes';
import { Tenant } from '@app/models/tenant';
import dbComponent from '@ss-libs/ss-component-db';
import { prepareModel } from '@ss-libs/ss-component-db/dist/hooks';
import logger from '@app/config/logger';
import { tenantManager } from '@app/lib/tenants';
import errors from '../constants/errors';
import { sharedModels, tenantModels } from '../models';
import requestAppVersion from './requestAppVersion';

export function tenantContext(param?: string) {
  const router = Router({ mergeParams: true });
  router.use(loadHandle(param));

  router.use(async (req, res, next) => {
    const tenant = await Tenant.query(req.sharedKnexDB)
      .where({ tenantUid: req.locals.handle.ownerUid })
      .withGraphFetched('dbServer')
      .first();
    logger.log('info', `Request for tenant (${req.locals.handle.ownerUid})`);
    if (tenant?.isMaintenance) {
      return next(
        new ApplicationError(
          StatusCodes.LOCKED,
          'Tenant is in maintenance mode',
        ),
      );
    }
    if (tenant?.setupStatus !== 'completed') {
      return next(
        new ApplicationError(
          StatusCodes.FAILED_DEPENDENCY,
          'We\'re yet to setup your account',
        ),
      );
    }

    router.use(tenantConfig(tenant.config));

    const conn = await tenantManager.loadTenant(tenant);
    req.knexDB = conn.db;
    req.fga = new FgaService(conn.fga);

    // bind tenant models to tenant DB
    dbComponent({
      auditLogger: { useRequest: true, db: conn.db, tablename: 'auditLogs' },
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      getKnexForRequest: (req) => conn.db,
      models: tenantModels,
    })(req, res, next);
  });

  router.use(bindSharedModels);

  router.use((req, res, next) => {
    if (
      req.params.key
      && /projects/.test(req.originalUrl)
      && !/projects\/keys\//.test(req.originalUrl)
    ) {
      loadProject(req, res, next);
    } else next();
  });

  router.use(requestAppVersion);

  return router;
}

export function tenantConfig(tenantConfigs: Record<string, any>) {
  return (req: Request & { configs: Record<string, any> }, res: Response, next: NextFunction) => {
    try {
      const routeConfig = defaultTenantConfigs[req.method]?.find((e) => (Array.isArray(req.route.path)
        ? req.route.path.some((path) => e.paths.includes(path))
        : e.paths.includes(req.route.path)));

      req.configs = { ...(routeConfig?.configs || {}) };
      const endpointConfigs = Object.keys(routeConfig?.configs || {});

      Object.keys(tenantConfigs || {}).forEach((key) => {
        if (endpointConfigs.includes(key)) {
          logger.log(
            'debug',
            `Using tenant override for "${key}" - tenant "${req.locals.handle.ownerUid}"`,
          );
        }

        req.configs[key] = tenantConfigs[key];
      });
      next();
    } catch (err) {
      next(err);
    }
  };
}

export function tenantContextExternalApi() {
  return async (req: Request, res: Response, next: NextFunction) => {
    const tenant = await Tenant.query(req.sharedKnexDB)
      .where({ tenantUid: req.locals.accessToken.ownerUid })
      .withGraphFetched('dbServer')
      .first();
    logger.log(
      'info',
      `Request for tenant (${req.locals.accessToken.ownerUid})`,
    );
    if (tenant?.setupStatus !== 'completed') {
      return next(
        new ApplicationError(
          StatusCodes.FAILED_DEPENDENCY,
          'We\'re yet to setup your account',
        ),
      );
    }
    const conn = await tenantManager.loadTenant(tenant);
    req.knexDB = conn.db;
    req.fga = new FgaService(conn.fga);

    next();
  };
}

export function loadDbComponent() {
  return async (req: Request, res: Response, next: NextFunction) => {
    dbComponent({
      auditLogger: { useRequest: true, db: req.knexDB, tablename: 'auditLogs' },
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      getKnexForRequest: (req) => req.knexDB,
      models: tenantModels,
    })(req, res, next);
  };
}

export function loadHandle(param: string = 'handle') {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (req.locals.handle) return next();
      const name: string = (req.params[param] || req.headers['x-owner-handle'] as string)?.toLowerCase();
      if (!name) {
        throw new ApplicationError(
          StatusCodes.NOT_FOUND,
          errors.HANDLE_NOT_FOUND,
        );
      }

      const blacklisted = BLACKLISTED_TOKENS.some((token) => new RegExp(`^${token}$`, 'ig').test(name));

      if (blacklisted) {
        throw new ApplicationError(
          StatusCodes.NOT_FOUND,
          errors.HANDLE_NOT_FOUND,
        );
      }

      const handle = await Handle.query(req.sharedKnexDB)
        .where({ name })
        .first();
      if (!handle) return res.status(404).json({ errors: errors.HANDLE_NOT_FOUND });

      req.locals.handle = handle;
      next();
    } catch (err) {
      next(err);
    }
  };
}

export function bindSharedModels(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  req.models = req.models ?? <any>{};
  const db = req.sharedKnexDB;
  const logger = db('auditLogs');

  const userAgent = req.get('user-agent');
  const requestIp = req.ip;
  const requestTraceId = asyncLocalStorage.getStore() as unknown as string;

  const auditMeta = {
    userAgent,
    requestIp,
    actor: req.locals && req.locals.user ? req.locals.user.uid : null,
    requestTraceId,
  };

  for (const key of Object.keys(sharedModels)) {
    req.models[key] = prepareModel(sharedModels[key], db, logger, auditMeta);
  }
  next();
}

export async function loadProject(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  const owner: string = req.locals.handle.ownerUid;
  const { ownerType } = req.locals.handle;
  const projectKey: string = req.params?.key;
  try {
    if (!projectKey) return res.status(404).json({ errors: errors.PROJECT_NOT_FOUND });

    const testKey = projectKey.toLowerCase();

    const blacklisted = BLACKLISTED_TOKENS.some((token) => new RegExp(`^${token}$`, 'ig').test(testKey));

    if (blacklisted) return res.status(404).json({ errors: errors.HANDLE_NOT_FOUND });

    const upperProjectKey = projectKey.toUpperCase();

    const project = await req.models.Project.query()
      .where({ key: upperProjectKey })
      .first();

    if (!project) return res.status(404).json({ errors: errors.PROJECT_NOT_FOUND });

    const handleIsOwner = await req.fga.check(
      `${ownerType}:${owner}`,
      `project:${project.uid}`,
      'owner',
      { current_project: project.uid },
    );

    if (!handleIsOwner) return res.status(404).json({ errors: errors.PROJECT_NOT_FOUND });
    req.locals.project = project;
    next();
  } catch (err) {
    next(err);
  }
}
export function accessableProjects() {
  return async (req, res: Response, next: NextFunction) => {
    const { resource } = req.locals;
    const { ownerUid, ownerType } = req.locals.handle;

    const projects = await req.fga.query(`${ownerType}:${ownerUid}`, 'project:', 'owner').then((result) => result.map((tuple: any) => tuple.key.object));
    const ownerProjects = projects?.map((p) => p.split(':')[1]) || [];

    if (req.locals?.accessableProjects && Array.isArray(req.locals?.accessableProjects)) { next(); }

    req.locals.accessableProjects = ownerProjects;
    if (projects.length === 0) { return next(); }

    if (ownerType !== 'org') {
      return next();
    }

    if (req.locals.project && req.locals.project?.uid) {
      return next();
    }
    const accessibleProjects = [];

    const permission = {
      execution: 'read_activity',
      dashboard: 'read_dashboard',
    };

    const checkPermissions = projects.map((p) => ({
      user: `user:${req.locals.user?.uid}`,
      object: p,
      relation: permission[resource],
      context: {
        current_project: p,
      },
    }));

    const grantAccess = await req.fga.batchCheck(checkPermissions);

    accessibleProjects.push(
      ...grantAccess
        .filter((a) => a.allowed)
        .map((a) => a._request.object.split(':')[1]),
    );

    if (!accessibleProjects.length) { return res.status(403).json({ errors: errors.FORBIDDEN }); }

    req.locals.accessableProjects = accessibleProjects;
    next();
  };
}

export function setResource(resource: string) {
  return (req, res: Response, next: NextFunction) => {
    req.locals.resource = resource;
    next();
  };
}
