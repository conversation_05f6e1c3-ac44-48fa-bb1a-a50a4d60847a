import { Request, Response, NextFunction } from 'express';
import { auth } from '@ss-libs/ss-component-auth';
import config from '@app/constants/config';
import { Attachment } from '@app/models/attachment';

export function accessAttachment() {
  return async (req: Request, res: Response, next: NextFunction) => {
    /**
      * If the request is GET, it will be read_x (e.g., read_entity, read_project).
      * Otherwise, if the request is DELETE, it will be delete_x (e.g., delete_entity, delete_activity, etc.).
    */
    const { type } = req.params;
    const isGetRequest = req.method === 'GET';

    const permissionsMapping = {
      projects: isGetRequest ? undefined : 'write_project',
      results: isGetRequest ? 'read_activity' : 'write_activity',
      cases: isGetRequest ? 'read_entity' : 'write_entity',
      executions: isGetRequest ? 'read_activity' : 'write_activity',
      integrations: isGetRequest ? 'read_integration' : 'write_integration',
      defects: isGetRequest ? 'read_activity' : 'write_activity',
    };

    const permission = permissionsMapping[type];

    return permission ? auth().authz(permission)(req, res, next) : next();
  };
}

export async function enforceStorageLimit(req: Request, res: Response, next: NextFunction) {
  const { size } = req.body;
  let ownerUid: string;
  if (req?.locals?.handle) {
    ownerUid = req.locals.handle.ownerUid;
  } else {
    ownerUid = req?.locals?.user?.uid;
  }
  if (!ownerUid) {
    return next();
  }
  const trx = await req.knexDB.transaction();
  const totalSize = await Attachment.getTotalSize(trx);
  await trx.commit();

  if ((Number(totalSize) || 0) + (Number(size) || 0) > config.MAX_STORAGE_LIMIT) {
    return res.status(507).json({ error: 'Storage limit exceeded.' });
  }
  return next();
}
