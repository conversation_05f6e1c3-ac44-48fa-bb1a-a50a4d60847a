import { NextFunction, Response, Request } from 'express';
import { Tenant } from '@app/models/tenant';
import logger from '@app/config/logger';
import serverVersion from '@app/config/app';
import env from '@app/config/env';

const requestAppVersion = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    let redirectCount = Number(req.get('x-tf-backend-redirect')) || 0;

    if (!req.locals?.handle?.ownerUid) {
      logger.error('Missing ownerUid in request locals');
      return next();
    }

    const { ownerUid } = req.locals.handle;
    const tenant = await Tenant.query(req.sharedKnexDB)
      .where({ tenantUid: ownerUid })
      .withGraphFetched('[frontendVersion, backendVersion]')
      .first();

    if (!tenant) {
      logger.warn(`No tenant found for ownerUid: ${ownerUid}`);
      return next();
    }

    // WARNING - versions are disabled in development. Remove this to test them
    if (['development'].includes(env.NODE_ENV))
      return next();

    if (!tenant.backendVersion || !tenant.frontendVersion) {
      logger.warn(
        `Missing version(s) for tenant ${ownerUid} - front: [${tenant.frontendVersion?.version}] back: [${tenant.backendVersion?.version}]`,
      );
    }

    const headerBackendVersion = req.header('x-tf-backend-version');
    const headerFrontendVersion = req.header('x-tf-frontend-version');

    res.header('x-tf-backend-version', tenant.backendVersion.version);
    res.header('x-tf-frontend-version', tenant.frontendVersion.version);

    if (res.get('x-tf-backend-version') !== headerBackendVersion) {
      logger.info(
        `Backend version mismatch: sent [${headerBackendVersion}] - new [${res.get('x-tf-backend-version')}]`,
      );
    }
    if (res.get('x-tf-frontend-version') !== headerFrontendVersion) {
      logger.info(
        `Frontend version mismatch: sent [${headerFrontendVersion}] - new [${res.get('x-tf-frontend-version')}]`,
      );
    }

    if (
      res.get('x-tf-backend-version')
      && res.get('x-tf-backend-version') !== `v${serverVersion}`
    ) {
      logger.info(
        `Backend version or server version mismatch [${res.get('x-tf-backend-version')}:v${serverVersion}]- redirecting to ${tenant.backendVersion.version}`,
      );
      try {
        if (redirectCount < 2) {
          redirectCount += 1;
          res.header('x-tf-backend-redirect', `${redirectCount}`);
          return res.status(204).send();
        }
      } catch (urlError) {
        logger.error('Error creating redirect URL:', urlError);
        next();
      }
    }
  } catch (error) {
    logger.error('Error in requestAppVersion middleware:', error);
  }
  next();
};

export default requestAppVersion;
