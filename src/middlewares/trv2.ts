import { Request, Response, NextFunction } from 'express';

// ===== Middleware: Parse TestRail style query =====
export const parseEndpointQuery = (req: Request, res: Response, next: NextFunction) => {
  try {
    const queryString = req.originalUrl.split('?')[1];
    if (!queryString) {
      return res.status(400).json({ message: 'Invalid endpoint format.' });
    }

    const [endpoint, ...paramsArray] = queryString.split('&');
    const endpointParts = endpoint.split('/');

    if (endpointParts.length < 4) {
      return res.status(400).json({ message: 'Invalid endpoint format.' });
    }

    const action = endpointParts[3]; // e.g., get_cases
    const id = endpointParts[4]; // e.g., 34 (optional)
    const id2 = endpointParts[5];

    const filters: Record<string, string> = {};
    paramsArray.forEach((param) => {
      const [key, value] = param.split('=');
      if (key && value) {
        filters[key] = value;
      }
    });

    // Attach parsed data to request object
    req.query._action = action;
    req.query._id = id;
    req.query._id2 = id2;
    req.query._filters = filters;

    next(); // Continue to route handling
  } catch (err) {
    res.status(500).json({ message: 'Internal server error.' });
  }
};

export const entityRouter = (routesMap: Record<string, Record<string, any>>) => function entityMiddleware(req: Request, res: Response, next: NextFunction) {
  const action = req.query._action as string;

  if (!action) {
    return res.status(400).json({ error: 'Invalid endpoint format.' });
  }

  const methodRoutes = routesMap[action];
  if (!methodRoutes) {
    return next('route');
  }

  const {
    controller,
    middlewares,
    method,
  } = methodRoutes;

  if (method !== req.method) {
    return res.status(404).json({ error: `Unsupported HTTP method "${req.method}" for this action.` });
  }

  // Execute middlewares one by one sequentially
  let currentMiddlewareIndex = 0;

  // Helper function to run the next middleware
  const executeNextMiddleware = async () => {
    if (currentMiddlewareIndex < middlewares.length) {
      const currentMiddleware = middlewares[currentMiddlewareIndex];
      currentMiddlewareIndex++;
      try {
        currentMiddleware(req, res, (err: any) => {
          if (err) {
            return next(err);
          }
          executeNextMiddleware();
        });
      } catch (err) {
        return next(err);
      }
    } else {
      // After all middlewares are executed, call the controller
      return controller(req, res, next);
    }
  };

  // Start the middleware execution chain
  executeNextMiddleware();
};
