import { Request, Response } from 'express';

import { auth, FgaService } from '@ss-libs/ss-component-auth';
import { Handle } from '@app/models/handle';
import { Tenant } from '@app/models/tenant';
import { tenantManager } from '@app/lib/tenants';

export function configureStorageType(req, res, next) {
  req.locals.useDefaultStorage = true;
  next();
}

export async function authorizeUpload(req: Request, res: Response) {
  auth().authenticateHybrid(req, res, () => {});
  if (res.headersSent) return null; // user not authenticated, so return

  const name = req.headers['x-owner-handle'] as string;
  if (!name) return null;

  const handle = await Handle.query(req.sharedKnexDB)
    .where({ name: name.toLowerCase() })
    .first();

  if (!handle) return null;

  let authorized = false;
  if (handle.ownerType === 'org') {
    const tenant = await Tenant.query(req.sharedKnexDB)
      .where({ tenantUid: handle.ownerUid })
      .first();
    const conn = await tenantManager.loadTenant(tenant);
    const fga = new FgaService(conn.fga);
    const subject = `user:${req.locals.user.uid}`;
    const object = `${handle.ownerType}:${handle.ownerUid}`;
    authorized = await fga.check(subject, object, 'member');
  } else {
    authorized = handle.ownerUid === req.locals.user.uid;
  }

  return authorized ? handle.ownerUid : null;
}

export function createLocalStorage(req, res, next) {
  req.locals = { ...req.locals };
  next();
}
