import { DataValidationError, validate as _validate } from '@app/lib/joi';
import { NextFunction, Request, Response } from 'express';
import {
  ValidationChain,
  body,
  param,
  query,
  validationResult,
} from 'express-validator';
import joi, { SchemaLike } from 'joi';

import { ApplicationError } from '@app/lib/http';
import { StatusCodes } from 'http-status-codes';
import config from '@app/constants/config';

// Validation result checker middleware
export function validate(validations: ValidationChain[]) {
  return async (req: Request, res: Response, next: NextFunction) => {
    await Promise.all(validations.map((validation) => validation.run(req)));

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      next(
        new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Your request is invalid',
          errors.array(),
        ),
      );
    }
    next();
  };
}

/**
 * Creates a middleware that validate the given request based on the
 * context and respond with status code `400`(with appropriate metadata) when
 * schema validation fails.
 * @param schema schema to use for validation
 * @param context whether to validate the request body or its query. Defaults to request body
 * @returns a middleware
 */
export function joiValidate(
  schema: SchemaLike,
  context: 'body' | 'query' | 'params' = 'body',
) {
  return (req: Request, _res: Response, next: NextFunction) => {
    try {
      req[context] = _validate(req[context], schema);
      next();
    } catch (err) {
      if (err instanceof DataValidationError) {
        return next(
          new ApplicationError(
            StatusCodes.BAD_REQUEST,
            'Your request invalid',
            err.messages,
          ),
        );
      }

      throw err;
    }
  };
}

const handler = {
  body,
  param,
  query,
};

export const isUUID = (
  field: string,
  ctx: 'body' | 'param' | 'query' = 'param',
) => {
  const fn = handler[ctx];
  return validate([fn(field).notEmpty().isUUID('4')]);
};

export const isPaginatedQuery = {
  limit: joi.number().integer().min(0).default(config.DEFAULT_PAGE_SIZE),
  offset: joi.number().integer().min(0).default(0),
  q: joi.string(),
};
