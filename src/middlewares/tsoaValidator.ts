import { FieldErrors, ValidateError } from '@tsoa/runtime';

// Function parameter Decorator Factory
import { SchemaLike } from 'joi';
import { DataValidationError, validate } from '../lib/joi';

// Overrides tsoa Body Decorator
/**
 * Decorator to mark a method parameter as the body of the request.
 * @returns {Function} A decorator function.
 */
export function Body() {
  return function BodyDecorator(
    target: object,
    propertyKey: string | symbol,
    parameterIndex: number,
  ) {
    const existingMetadata = Reflect.getOwnMetadata('Body', target, propertyKey) || [];
    existingMetadata.push(parameterIndex);
    Reflect.defineMetadata('Body', existingMetadata, target, propertyKey);
  };
}

/**
 * Decorator to validate the body of the request against a Joi schema.
 * @param {SchemaLike} validationSchema - The Joi schema to validate the body against.
 * @returns {Function} A decorator function that wraps the original method.
 */
export function ValidateBody(validationSchema: SchemaLike) {
  return function ValidateBodyDecorator(
    target: object,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor,
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = function ValidateBodyMethod(...args: any[]) {
      const bodyCandidates: number[] = Reflect.getOwnMetadata('Body', target, propertyKey) || [];
      if (bodyCandidates.length === 0) {
        throw new ValidateError(
          {
            body: {
              message: 'Body parameter is missing',
            },
          },
          'Body parameter is missing',
        );
      }
      const bodyIndex = bodyCandidates[0] as number;
      const errorPayload: FieldErrors = {};

      try {
        const validatedValue = validate(args[bodyIndex], validationSchema);
        args[bodyIndex] = validatedValue;
      } catch (error) {
        if (error instanceof DataValidationError) {
          Object.entries(error.messages).forEach(([key, message]) => {
            errorPayload[key] = {
              message,
              value: args[bodyIndex][key],
            };
          });
          throw new ValidateError(errorPayload, '');
        }
        throw error;
      }

      return originalMethod.apply(this, args);
    };
  };
}
