import { NextFunction, Response } from 'express';
import { AsyncLocalStorage } from 'node:async_hooks';

export const asyncLocalStorage = new AsyncLocalStorage();

const requestTraceId = (req, _res: Response, next: NextFunction) => {
  const traceId = req.headers['x-trace-id'];

  if (traceId) {
    asyncLocalStorage.run(traceId, () => {
      next();
    });
  } else {
    next();
  }
};

export default requestTraceId;
