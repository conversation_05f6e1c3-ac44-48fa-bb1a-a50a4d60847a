import 'module-alias/register';

import { Client, Connection } from '@temporalio/client';

import { Knex } from 'knex';
import env from '@app/config/env';
import logger from '@app/config/logger';
import { setupDB } from '@app/config/db';
import { TaskQueue, TemporalNamespace } from '@app/constants/temporal';
import * as workflows from './workflows';
import { getNamespaceForQueue } from './namespace';

const clients: Map<string, Client> = new Map();
let clientConnection;
let sharedDb = null;
export type WorkflowName = keyof typeof workflows;

export async function getTemporalClientConnection(): Promise<Connection> {
  if (env.NODE_ENV === 'test') {
    throw new Error('Test environment not supported');
  }
  if (clientConnection) {
    return clientConnection;
  }
  clientConnection = await Connection.connect({
    address: `${env.TEMPORAL_HOST}:${env.TEMPORAL_PORT}`,
  });
  return clientConnection;
}

export async function getTemporalClient(namespace: TemporalNamespace): Promise<Client> {
  if (clients.has(namespace)) return clients.get(namespace);

  try {
    const connection = await getTemporalClientConnection();
    const client = new Client({
      connection,
      namespace,
    });

    clients.set(namespace, client);
    logger.info('Connected to Temporal successfully');

    return client;
  } catch (error) {
    logger.error('Failed to create Temporal client:', error);
    throw error;
  }
}
export async function getSharedDb(): Promise<Knex> {
  if (!sharedDb) {
    sharedDb = setupDB();
  }
  return sharedDb;
}

export async function startWorkflow<T>(
  workflowName: WorkflowName,
  options: {
    taskQueue: TaskQueue;
    workflowId: string;
    args: T[];
    cronSchedule?: string;
  },
): Promise<void> {
  try {
    const temporalClient = await getTemporalClient(getNamespaceForQueue(options.taskQueue));
    await temporalClient.workflow.start(workflowName, {
      taskQueue: options.taskQueue,
      workflowId: options.workflowId,
      args: options.args,
      cronSchedule: options.cronSchedule,
    });

    logger.info(
      `Started workflow ${workflowName} with ID ${options.workflowId}`,
    );
  } catch (error) {
    logger.error(`Failed to start workflow ${workflowName}:`, error);
  }
}

export async function terminateWorkflow(taskQueue: TaskQueue, workflowId: string, reason?: string) {
  try {
    const temporalClient = await getTemporalClient(getNamespaceForQueue(taskQueue));
    const handle = await temporalClient.workflow.getHandle(workflowId);
    await handle.terminate(reason || 'User terminated workflow');
  } catch (error) {
    logger.error(`Failed to terminate workflow ${workflowId}:`, error);
  }
}

export async function stopTemporalClient() {
  if (clients) {
    for (const client of clients.values()) {
      await client.connection.close();
    }

    clients.clear();
  }

  if (sharedDb) {
    await sharedDb.destroy();
    sharedDb = null;
  }
}
