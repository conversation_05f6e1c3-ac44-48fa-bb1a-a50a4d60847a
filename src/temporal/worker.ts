import { NativeConnection, Worker } from '@temporalio/worker';
import env from '@app/config/env';
import { TaskQueue } from '@app/constants/temporal';
import logger from '../config/logger';
import * as activities from './activities/index';
import { stopTemporalClient } from './client';
import { getNamespaceForQueue } from './namespace';

interface WorkerConfig {
  taskQueue: TaskQueue;
  maxConcurrentActivities?: number;
  namespace?: string;
  workflowsPath?: string;
}
const workers: Record<string, Worker> = {};
let connection: NativeConnection | null = null;

async function getTemporalNativeConnection() {
  if (!connection) {
    try {
      connection = await NativeConnection.connect({
        address: `${env.TEMPORAL_HOST}:${env.TEMPORAL_PORT}`,
      });
    } catch (error) {
      logger.error('Failed to connect to Temporal server:', error);
      return null;
    }
  }
  return connection;
}

export async function createTemporalWorker(config: WorkerConfig) {
  const {
    taskQueue,
    maxConcurrentActivities = 500,
    workflowsPath = './workflows',
  } = config;

  try {
    if (workers[taskQueue]) {
      logger.warn(`Worker for task queue ${taskQueue} already exists`);
      return workers[taskQueue];
    }
    const namespace = getNamespaceForQueue(taskQueue);

    const connection = await getTemporalNativeConnection();

    const worker = await Worker.create({
      connection,
      namespace,
      workflowsPath: require.resolve(workflowsPath),
     activities,
      taskQueue,
      maxConcurrentActivityTaskExecutions: maxConcurrentActivities,
    });

    workers[taskQueue] = worker;
    logger.info(`Temporal worker started for task queue: ${taskQueue}`);
    await worker.run();
    return worker;
  } catch (error) {
    logger.error(`Failed to start Temporal worker for task queue ${taskQueue}:`, error);
    throw error;
  }
}

export async function stopTemporalWorker(taskQueue?: string) {
  await stopTemporalClient();
  const connection = await getTemporalNativeConnection();

  if (connection) {
    await connection.close();
  }

  if (taskQueue) {
    const worker = workers[taskQueue];
    if (worker) {
      // await worker.shutdown();
      delete workers[taskQueue];
      logger.info(`Temporal worker stopped for task queue: ${taskQueue}`);
    }
  } else {
    await Promise.all(
      Object.entries(workers).map(async ([queue]) => {
        try {
          // await worker.shutdown();
          delete workers[queue];
          logger.info(`Temporal worker stopped for task queue: ${queue}`);
        } catch (error) {
          logger.error(`Failed to stop Temporal worker for task queue ${queue}:`, error);
        }
      }),
    );
  }
}
