import { proxyActivities } from '@temporalio/workflow';
import type * as activities from '../activities/dashboard';

const { handleCreateDashboard } = proxyActivities<typeof activities>({
  startToCloseTimeout: '1 hour',
  retry: {
    maximumAttempts: 3,
    initialInterval: '10 seconds',
  },
});

export async function dashboardWorkflow(params): Promise<void> {
  await handleCreateDashboard(params);
}
