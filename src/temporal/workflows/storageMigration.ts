import { proxyActivities } from '@temporalio/workflow';
import type * as activities from '../activities/storageMigration';

const { handleStorageMigration } = proxyActivities<typeof activities>({
  startToCloseTimeout: '3 hour',
  retry: {
    maximumAttempts: 3,
    initialInterval: '10 seconds',
  },
});

export async function storageMigrationWorkflow(params): Promise<void> {
  await handleStorageMigration(params);
}
