import { proxyActivities } from '@temporalio/workflow';
import type * as activities from '../activities/integrationEntities';

const {
  handleEntityUpdate,
  handleEntityAttachmentsSync,
} = proxyActivities<typeof activities>({
  startToCloseTimeout: '1 hour',
  retry: {
    maximumAttempts: 3,
    initialInterval: '10 seconds',
  },
});

export interface IntegrationEntitiesWorkflowResult {
  success: boolean;
  error?: string;
}

export interface IntegrationEntitiesJob {
  tenantUid: string;
  task: 'updateEntity' | 'syncEntityAttachments';
  entityType: string;
  data: any;
}

export async function integrationEntitiesWorkflow(job: IntegrationEntitiesJob): Promise<IntegrationEntitiesWorkflowResult> {
  try {
    switch (job.task) {
      case 'updateEntity':
        await handleEntityUpdate(job.tenantUid, job.entityType, job.data);
        break;
      case 'syncEntityAttachments':
        await handleEntityAttachmentsSync(job.tenantUid, job.entityType, job.data);
        break;
      default:
        throw new Error(`Invalid task: ${job.task}`);
    }
    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
