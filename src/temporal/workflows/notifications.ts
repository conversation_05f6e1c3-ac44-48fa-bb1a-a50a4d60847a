import { WebhookEvent } from '@ss-libs/ss-component-notifications';
import { proxyActivities } from '@temporalio/workflow';
import type * as activities from '../activities';

export interface EmailEvent extends WebhookEvent {
  tenantUid?: string;
  inviteUid?: string;
}

const { processInviteMails } = proxyActivities<typeof activities>({
  startToCloseTimeout: '1 hour',
  retry: {
    maximumAttempts: 3,
    initialInterval: '10 seconds',
  },
});

export async function processEmailEventsWorkflow({
  events,
}: {
  events: EmailEvent[];
}) {
  const inviteEvents: EmailEvent[] = [];

  for (const e of events) {
    if (e.inviteUid && e.tenantUid) {
      inviteEvents.push(e);
    }
    // other clauses for other email event types can be handled here
  }
  if (inviteEvents.length > 0) {
    await processInviteMails(inviteEvents);
  }
}
