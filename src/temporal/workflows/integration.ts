import { proxyActivities } from '@temporalio/workflow';
import type * as activities from '../activities/integration';

const {
  fetchAndUpdateUser, fetchAndProcessEntities, handleIntegrationDeletion, handleIntegrationError,
} = proxyActivities<typeof activities>({
  startToCloseTimeout: '1 hour',
  retry: {
    maximumAttempts: 3,
    initialInterval: '10 seconds',
  },
});

export async function integrationSyncWorkflow(tenantUid: string, integrationUid: string): Promise<void> {
  try {
    await fetchAndProcessEntities(integrationUid, tenantUid, true);
  } catch (error) {
    await handleIntegrationError(tenantUid, integrationUid);
    throw error;
  }
}

export async function integrationUpdateWorkflow(tenantUid: string, integrationUid: string): Promise<void> {
  await fetchAndProcessEntities(integrationUid, tenantUid, false);
}

export async function fetchUserWorkFlow(tenantUid: string, tokenUid: string): Promise<void> {
  await fetchAndUpdateUser(tokenUid, tenantUid);
}

export async function integrationDeleteWorkflow(tenantUid: string, integrationUid: string, fullDelete: boolean): Promise<void> {
  await handleIntegrationDeletion(tenantUid, integrationUid, fullDelete);
}
