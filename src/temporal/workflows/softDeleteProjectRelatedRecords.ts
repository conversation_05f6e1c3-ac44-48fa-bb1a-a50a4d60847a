import { proxyActivities } from '@temporalio/workflow';
import type * as activities from '../activities/softDeleteProjectRelatedRecords';

const { handleSoftDeleteProjectRelatedRecords } = proxyActivities<typeof activities>({
  startToCloseTimeout: '1 hour',
  retry: {
    maximumAttempts: 3,
    initialInterval: '10 seconds',
  },
});

export async function softDeleteProjectRelatedRecordsWorkflow(params): Promise<void> {
  await handleSoftDeleteProjectRelatedRecords(params);
}
