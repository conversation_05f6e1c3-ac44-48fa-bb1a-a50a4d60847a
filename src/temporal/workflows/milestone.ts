import { proxyActivities } from '@temporalio/workflow';
import type * as activities from '../activities/milestone';

const { updateMilestoneState } = proxyActivities<typeof activities>({
  startToCloseTimeout: '1 hour',
  retry: {
    maximumAttempts: 3,
    initialInterval: '10 seconds',
  },
});

export async function updateMilestoneWorkflow(params): Promise<void> {
  await updateMilestoneState(params);
}
