import {
  TaskQueue, TemporalNamespace, queueConfig,
  temporalNamespaces,
} from '@app/constants/temporal';
import logger from '@app/config/logger';
import { isGrpcServiceError } from '@temporalio/client/lib/errors';
import { getTemporalClientConnection } from './client';

export function getNamespaceForQueue(queue: TaskQueue): TemporalNamespace {
  return queueConfig[queue].namespace;
}

export async function registerNamespaces(): Promise<void> {
  const connection = await getTemporalClientConnection();

  const threeDays: any = 3 * 24 * 60 * 60;
  for (const namespace of temporalNamespaces) {
    try {
      await connection.workflowService.registerNamespace({
        namespace,
        workflowExecutionRetentionPeriod: {
          seconds: threeDays,
        },
      });

      logger.info(`Successfully registered namespace "${namespace}"`);
    } catch (error) {
      const grpcError = isGrpcServiceError(error);
      // Status.ALREADY_EXISTS === 6
      if (grpcError && error.code === 6) {
        logger.info(`Namespace "${namespace}" already exists`);
      } else {
        throw error;
      }
    }
  }
}
