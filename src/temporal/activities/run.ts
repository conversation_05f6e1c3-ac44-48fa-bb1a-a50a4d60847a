import { Tenant } from '@app/models/tenant';
import { TestRun } from '@app/models/testRun';
import logger from '@app/config/logger';
import preferences from '@app/models/preferences';
import { serializeErr } from '@app/lib/error';
import { tenantManager } from '@app/lib/tenants';
import { getSharedDb, startWorkflow } from '@app/temporal/client';
import { UpdatePlanStateDTO } from '@app/temporal/activities/plan';
import { UpdateMilestoneData } from '@app/temporal/activities/milestone';

export type UpdateRunStateDTO = {
  ownerUid: Tenant['tenantUid'];
  ownerType: Tenant['tenantType'];
  runUids: number[];
};

export async function updateRunState(dto: UpdateRunStateDTO) {
  try {
    const sharedDb = await getSharedDb();
    const tenant = await Tenant.query(sharedDb)
      .where({ tenantType: dto.ownerType, tenantUid: dto.ownerUid })
      .withGraphFetched('dbServer')
      .first();
    if (!tenant) {
      log(`tenant ${dto.ownerType}:${dto.ownerUid}`);
      return;
    }

    const pref = await preferences.getCompleted(sharedDb, dto.ownerType, dto.ownerUid);
    const { db: tdb } = await tenantManager.loadTenant(tenant);

    const runs = await TestRun.query(tdb)
      .whereIn('uid', dto.runUids)
      .withGraphFetched('[testMilestones,testPlans]');

    const planUids = new Set<number>();
    const milestoneUids = new Set<number>();

    for (const run of runs) {
      await TestRun.updateProgress(tdb, run, pref);
      for (const plan of run.testPlans) planUids.add(plan.uid);
      for (const m of run.testMilestones) milestoneUids.add(m.uid);
    }

    if (planUids.size > 0) {
      const param: UpdatePlanStateDTO = {
        ownerType: dto.ownerType,
        ownerUid: dto.ownerUid,
        planUids: [...planUids],
      };
      await startWorkflow('updatePlanWorkflow', {
        taskQueue: 'update-plan-queue',
        workflowId: `update.plan.${dto.ownerUid}.${Date.now()}`,
        args: [param],
      });
    }

    if (milestoneUids.size > 0) {
      const param: UpdateMilestoneData = {
        ownerType: dto.ownerType,
        ownerUid: dto.ownerUid,
        milestoneUids: [...milestoneUids],
      };
      await startWorkflow('updateMilestoneWorkflow', {
        taskQueue: 'update-milestone-queue',
        workflowId: `update.milestone.${dto.ownerUid}.${Date.now()}`,
        args: [param],
      });
    }
  } catch (err) {
    log(`error syncing changes: ${serializeErr(err)}`, 'error');
    throw err;
  }
}

function log(msg: string, level: 'info' | 'error' = 'info') {
  logger[level](`[updateRunState]: ${msg}`);
}
