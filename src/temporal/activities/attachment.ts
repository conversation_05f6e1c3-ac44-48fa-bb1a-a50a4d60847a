import * as media from '@ss-libs/ss-component-media';
import { storageProvider } from '@ss-libs/ss-component-media';
import env from '@app/config/env';
import fs from 'fs';
import { authorizeUpload } from '@app/middlewares/storage';
import logger from '@app/config/logger';
import { serializeErr } from '@app/lib/error';
import { getSharedDb } from '@app/temporal/client';
import { KEY_MANAGER } from '@app/config/keyManagerLoader';

export type AttachmentData = {
  type: 'delete';
  url?: string;
  key?: string;
  creatorUid?: string;
  ownerUid: string;
};

export async function handleAttachment(data: AttachmentData) {
  try {
    const isNonUpstream = ['development', 'test'].includes(env.NODE_ENV);
    const sharedDb = await getSharedDb();

    const gcpCredsVar = env.GC_SERVICE_KEY_FILE;
    let gcpCredentials: any;
    if (isNonUpstream) {
      if (fs.existsSync(gcpCredsVar)) {
        gcpCredentials = {
          ...JSON.parse(fs.readFileSync(env.GC_SERVICE_KEY_FILE).toString()),
          bucket: env.GCS_BUCKET_NAME,
        };
      } else {
        logger.log('warn', 'Failed to init shared cloud storage!');
      }
    } else {
      gcpCredentials = {
        ...JSON.parse(env.GC_SERVICE_KEY_FILE),
        bucket: env.GCS_BUCKET_NAME,
      };
    }

    media.init({
      db: sharedDb,
      default: {
        provider: 'gcp',
        credential: {
          ...gcpCredentials,
        },
      },
      getOwner: (req, res) => authorizeUpload(req, res),
      encryptionConfig: {
        keyManager: KEY_MANAGER,
      },
      types: {
        attachment: { byos: 'always' },
        'profile-picture': { byos: 'never', publicRead: true },
      },
      migrationTaskConfig: {
        workflowName: 'storageMigrationWorkflow',
        queueName: 'storage-migration-queue',
      },
    });

    if (data.type === 'delete') {
      const key = data.key ?? new URL(data.url).pathname.slice(1);
      await storageProvider(data.ownerUid).delete(key);
    }
  } catch (err) {
    logger.error('Failed to process attachment');
    logger.error(serializeErr(err));
    throw err;
  }
}
