import { Invite } from '@app/models/invite';
import { Tenant } from '@app/models/tenant';
import logger from '@app/config/logger';
import { tenantManager } from '@app/lib/tenants';
import { getSharedDb } from '../client';
import { EmailEvent } from '../workflows/notifications';

/**
 * procedure for invite email events
 * @param events
 */
export async function processInviteMails(events: EmailEvent[]) {
  const groups: Record<string, EmailEvent[]> = {};
  const tenantUids: string[] = [];

  for (const e of events) {
    if (groups[e.tenantUid]) groups[e.tenantUid].push(e);
    else groups[e.tenantUid] = [e];
    tenantUids.push(e.tenantUid);
  }

  const tenants = await Tenant.findByTenantUids(
    await getSharedDb(),
    tenantUids,
  );
  await Promise.all(
    tenants.map((t) => updateTenantInvites(t, groups[t.tenantUid])),
  );
}

async function updateTenantInvites(tenant: Tenant, events: EmailEvent[]) {
  const { db } = await tenantManager.loadTenant(tenant);
  const trx = await db.transaction();
  try {
    for (const e of events) {
      const [invite] = await Invite.query(trx)
        .where({
          uid: e.inviteUid,
          accepted: null,
          acceptedAt: null,
          deletedAt: null,
        })
        .where((b) => b.where('status', 'pending').orWhereNull('status'));
      if (!invite) continue;

      let status: Invite['status'];

      switch (e.status) {
        case 'delivered':
          status = 'delivered';
          break;
        case 'bounce':
          status = 'invalid_email';
          break;
        case 'deferred':
          status = 'pending';
          break;
        case 'dropped':
          status = 'canceled';
          break;
        default:
          break;
      }

      if (status) {
        await Invite.query(trx).patchAndFetchById(invite.uid, { status });
      }
    }
    await trx.commit();
  } catch (err) {
    await trx.rollback();
    logger.error('unable to process tenant invite mail events', {
      events,
      err,
    });
  }
}
