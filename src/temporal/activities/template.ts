import { FgaService } from '@ss-libs/ss-component-auth';
import { TemplateField } from '@app/types/templateCustomField';
import { TestTemplate } from '@app/models/testTemplate';
import logger from '@app/config/logger';
import { setupOpenfga } from '@app/config/openfga';
import { serializeErr } from '@app/lib/error';
import { Tenant } from '@app/models/tenant';
import { tenantManager } from '@app/lib/tenants';
import { getSharedDb } from '@app/temporal/client';

export type DefaultTemplateData = {
  ownerType: string;
  ownerUid: string;
  projectId: number;
  userId: string;
};

export async function processTemplate(data: DefaultTemplateData) {
  try {
    const sharedDb = await getSharedDb();
    const tenant = await Tenant.query(sharedDb)
      .where('tenantUid', data.ownerUid)
      .where('setupStatus', 'completed')
      .withGraphFetched('dbServer')
      .first();

    if (!tenant) {
      logger.info(`skipping template setup for project ${data.projectId}`);
      return;
    }

    const conn = await tenantManager.loadTenant(tenant);
    const tenantDB = conn.db;

    const openFga = await setupOpenfga({
      storeId: tenant.openfgaStoreId,
      authModelId: tenant.openfgaAuthModelId,
    });
    const fgaService = new FgaService(openFga);

    const templates = [
      {
        name: 'Automated Tests',
        templateFields: [
          { name: 'repository', dataType: 'text' },
          { name: 'sha', dataType: 'text' },
        ] as TemplateField[],
      },
      {
        name: 'Simple',
        templateFields: [
          { name: 'Pre-condition', dataType: 'text' },
          { name: 'Steps', dataType: 'text' },
          { name: 'Expected Result', dataType: 'text' },
        ] as TemplateField[],
        isDefault: true,
      },
      {
        name: 'Exploratory',
        templateFields: [
          { name: 'Title', dataType: 'text' },
          { name: 'Charter', dataType: 'text' },
          { name: 'Time Limit', dataType: 'text' },
        ] as TemplateField[],
      },
    ];

    for (const template of templates) {
      const templateFromDb = await TestTemplate.query(tenantDB)
        .insert({
          createdBy: data.userId,
          customFields: { templateFields: template.templateFields },
          name: template.name,
          isDefault: template.isDefault || false,
          projectUid: data.projectId,
        })
        .returning('*');

      await fgaService.create({
        objectType: 'template',
        objectId: templateFromDb.uid,
        relation: 'owner',
        subjectType: data.ownerType,
        subjectId: data.ownerUid,
      });
    }

    logger.info(
      `successfully created default template for project "${data.projectId}"`,
    );
  } catch (err) {
    logger.error('Error processing template');
    logger.error(serializeErr(err));
    throw err;
  }
}
