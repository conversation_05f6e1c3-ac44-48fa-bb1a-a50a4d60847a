import 'module-alias/register';
import logger from '@app/config/logger';
import { IntegrationServiceFactory } from '@app/integrations/index';
import { storageProvider } from '@ss-libs/ss-component-media';
import axios from 'axios';
import { setupDB } from '@app/config/db';
import { initializeStorage } from '@app/utils/storage';
import { tenantModels } from '../../models';

// Moving the core functions from the worker directly to activities
export async function handleEntityUpdate(tenantUid: string, entityType: string, data: any): Promise<void> {
  const tenantDb = setupDB(tenantUid);

  const trx = await tenantDb.transaction();
  try {
    switch (entityType) {
      case 'defect':
        await handleSingleDefectUpdate(trx, data);
        break;
      case 'case':
        await updateCase(trx, data, tenantDb);
        break;
      case 'milestone':
        await updateMilestone(trx, data, tenantDb);
        break;
      case 'execution':
        await updateExecution(trx, data, tenantDb);
        break;
      case 'plan':
        await updatePlan(trx, data, tenantDb);
        break;
      case 'run':
        await updateRun(trx, data, tenantDb);
        break;
      default:
        logger.warn(`Unknown entity type: ${entityType}`);
    }
    await trx.commit();
  } catch (error) {
    await trx.rollback();
    throw error;
  } finally {
    await tenantDb.destroy();
  }
}

export async function handleEntityAttachmentsSync(tenantUid: string, entityType: string, data: any): Promise<void> {
  const tenantDb = setupDB(tenantUid);

  initializeStorage(tenantDb);
  try {
    switch (entityType) {
      case 'defect':
        await handleDefectAttachmentsSync(tenantDb, { ...data, ownerUid: tenantUid });
        break;
      default:
        logger.warn(`Unknown entity type: ${entityType}`);
    }
  } catch (error) {
    logger.error(`Error in handleEntityAttachmentsSync: ${error.message}`);
    throw error;
  } finally {
    await tenantDb.destroy();
  }
}

async function updateCase(trx: any, data: any, tenantDb: any) {
  if (data.source !== 'testrail') return;
  const url = data.link;
  const caseData:any = {
    name: data.name,
    description: data.description,
  };
  const externalEntity = await tenantModels.ExternalEntity.query(trx)
    .where({
      sourceId: String(data.priority),
      source: 'testrail',
      entityType: 'tag',
    })
    .first();
  if (externalEntity) {
    caseData.priority_id = Number(externalEntity.sourceId);
  }
  const integration = await tenantModels.Integration.query(trx)
    .findById(Number(data.customFields.integrationUid))
    .first();
  if (!integration) {
    logger.error(`Integration not found: ${data.customFields.integrationUid}`);
    return;
  }
  const integrationToken = await tenantModels.IntegrationToken.query(trx)
    .where('integrationUid', Number(integration.uid))
    .first();
  if (!integrationToken) {
    logger.error(`Auth token not found for integration: ${integration.uid}`);
    return;
  }
  const service = IntegrationServiceFactory.getService(integration.service);
  const authHeader = await service.prepareAuthHeader(integrationToken, tenantDb);
  const response = await service.updateEntity(url, authHeader, 'case', caseData);
  if (!response.success) {
    logger.error(`Failed to update case: ${response.error}`);
    return;
  }
  return response.data;
}
function dateToUnixTimestamp(dateString: string): number {
  return Math.floor(new Date(dateString).getTime() / 1000);
}

async function updateMilestone(trx: any, data: any, tenantDb: any) {
  if (data.source !== 'testrail') return;
  const url = data.customFields.link;
  const milestoneData = {
    name: data.name,
    description: data.description,
    is_completed: !!data.archivedAt,
    due_on: dateToUnixTimestamp(data.customFields.dueAt),
  };
  const integration = await tenantModels.Integration.query(trx)
    .findById(Number(data.customFields.integrationUid))
    .first();

  if (!integration) {
    logger.error(`Integration not found: ${data.customFields.integrationUid}`);
    return;
  }
  const integrationToken = await tenantModels.IntegrationToken.query(trx)
    .where('integrationUid', Number(data.customFields.integrationUid))
    .first();

  if (!integrationToken) {
    logger.error(`Auth token not found for integration: ${data.customFields.integrationUid}`);
    return;
  }
  const service = IntegrationServiceFactory.getService(integration.service);
  const authHeader = await service.prepareAuthHeader(integrationToken, tenantDb);
  const response = await service.updateEntity(url, authHeader, 'milestone', milestoneData);
  if (!response.success) {
    logger.error(`Failed to update milestone: ${response.error}`);
    return;
  }
  return response.data;
}

async function updateExecution(trx: any, data: any, tenantDb: any) {
  const testExecution = await tenantModels.TestExecution.query(trx)
    .findById(data.testExecutionUid)
    .first();

  if (!testExecution) {
    logger.error(`Test execution not found: ${data.testExecutionUid}`);
    return;
  }
  if (testExecution.source !== 'testrail') return;

  // Find status mapping from external entity table
  const externalEntity = await tenantModels.ExternalEntity.query(trx)
    .where({
      sourceId: String(data.status),
      source: 'testrail',
      entityType: 'tag',
    })
    .first();

  if (!externalEntity) {
    logger.error(`Status mapping not found for status: ${data.status}`);
    return;
  }

  const url = testExecution.link.replace('get_test', 'add_result');
  const resultData = {
    status_id: externalEntity.entityUid,
    comment: data.comment.replace(/<[^>]*>?/g, ''),
  };
  const integration = await tenantModels.Integration.query(trx)
    .findById(Number((testExecution.customFields as any)?.integrationUid))
    .first();

  if (!integration) {
    logger.error(`Integration not found: ${(testExecution.customFields as any)?.integrationUid}`);
    return;
  }
  const integrationToken = await tenantModels.IntegrationToken.query(trx)
    .where('integrationUid', Number(integration.uid))
    .first();

  if (!integrationToken) {
    logger.error(`Auth token not found for integration: ${integration.uid}`);
    return;
  }

  const service = IntegrationServiceFactory.getService(integration.service);
  const authHeader = await service.prepareAuthHeader(integrationToken, tenantDb);
  const response = await axios.post(url, resultData, authHeader);
  if (!response.status) {
    logger.error(`Failed to update execution: ${response.data}`);
    return;
  }
  return response.data;
}

async function updatePlan(trx: any, data: any, tenantDb: any) {
  if (data.source !== 'testrail') return;
  const url = data.customFields.link;
  const planData = {
    name: data.name,
    description: data.description,
  };
  const integration = await tenantModels.Integration.query(trx)
    .findById(Number(data.customFields.integrationUid))
    .first();

  if (!integration) {
    logger.error(`Integration not found: ${data.customFields.integrationUid}`);
    return;
  }
  const integrationToken = await tenantModels.IntegrationToken.query(trx)
    .where('integrationUid', Number(data.customFields.integrationUid))
    .first();

  if (!integrationToken) {
    logger.error(`Auth token not found for integration: ${data.customFields.integrationUid}`);
    return;
  }

  const service = IntegrationServiceFactory.getService(integration.service);
  const authHeader = await service.prepareAuthHeader(integrationToken, tenantDb);
  const response = await service.updateEntity(url, authHeader, 'plan', planData);
  if (!response.success) {
    logger.error(`Failed to update plan: ${response.error}`);
    return;
  }
  return response.data;
}

async function updateRun(trx: any, data: any, tenantDb: any) {
  if (data.source !== 'testrail') return;
  const url = data.link;
  const runData = {
    name: data.name,
    description: data.description,
    planId: data.customFields.raw.plan_id,
    externalId: data.externalId,
    entryId: data.customFields.raw.entry_id,
  };
  const integration = await tenantModels.Integration.query(trx)
    .findById(Number(data.customFields.integrationUid))
    .first();

  if (!integration) {
    logger.error(`Integration not found: ${data.customFields.integrationUid}`);
    return;
  }
  const integrationToken = await tenantModels.IntegrationToken.query(trx)
    .where('integrationUid', Number(data.customFields.integrationUid))
    .first();

  if (!integrationToken) {
    logger.error(`Auth token not found for integration: ${data.customFields.integrationUid}`);
    return;
  }
  const service = IntegrationServiceFactory.getService(integration.service);
  const authHeader = await service.prepareAuthHeader(integrationToken, tenantDb);
  const response = await service.updateEntity(url, authHeader, 'run', runData);
  if (!response.success) {
    logger.error(`Failed to update run: ${response.error}`);
    return;
  }
  return response.data;
}

async function handleDefectAttachmentsSync(tenantDb: any, data: any) {
  const trx = await tenantDb.transaction();
  const {
    defectUid, attachmentKey, fileName, fileType, ownerUid,
  } = data;

  try {
    const defect = await tenantModels.Defect.query(trx)
      .findById(defectUid)
      .first();

    if (!defect) {
      logger.error(`Defect not found: ${defectUid}`);
      return;
    }

    const integration = await tenantModels.Integration.query(trx)
      .findById(Number(defect.integrationSourceUid))
      .first();

    if (!integration) {
      logger.error(`Integration not found: ${defect.integrationSourceUid}`);
      return;
    }
    const Token = tenantModels.IntegrationToken.bindKnex(tenantDb);
    const integrationToken = await Token.query()
      .where('integrationUid', Number(defect.integrationSourceUid))
      .first();

    if (!integrationToken) {
      logger.error(`Auth token not found for integration: ${defect.integrationSourceUid}`);
      return;
    }

    const service = IntegrationServiceFactory.getService(integration.service);
    const authHeader = await service.prepareAuthHeader(integrationToken, tenantDb);

    const fileUrl = await storageProvider(ownerUid).fetch(attachmentKey);
    if (integration.service !== 'jira') {
      return;
    }

    const fileResponse = await axios.get(fileUrl, { responseType: 'arraybuffer' });

    if (!fileResponse.data) {
      logger.error(`Failed to download file: ${attachmentKey}`);
      return;
    }

    const formData = new FormData();
    formData.append('file', new Blob([fileResponse.data], { type: fileType }), fileName);

    const jiraApiUrl = `${defect.customFields.apiUrl}/attachments`;
    const response = await axios.post(jiraApiUrl, formData, {
      headers: {
        ...authHeader.headers,
        'Content-Type': 'multipart/form-data',
        'X-Atlassian-Token': 'no-check',
      },
    });

    logger.info(`File uploaded successfully to Jira: ${response.statusText}`, {
      defectUid,
      fileName,
    });
  } catch (error) {
    logger.error(`Failed to sync attachment with Jira: ${error.message}`, {
      defectUid,
      attachmentKey,
      fileName,
      fileType,
      error: error.stack,
    });
  }
}

async function handleSingleDefectUpdate(trx: any, data: any) {
  const { defectUid, defectData, projectUid } = data;
  try {
    const existingDefect = await tenantModels.Defect.query(trx)
      .where('uid', defectUid)
      .first();

    if (!existingDefect) {
      logger.warn(`Defect not found for uid: ${defectUid}`);
      await trx.rollback();
      return;
    }

    // Ensure projectUid exists in projectUids array
    const projectUids = existingDefect.projectUids.includes(projectUid)
      ? existingDefect.projectUids
      : [...existingDefect.projectUids, projectUid];

    await tenantModels.Defect.query(trx)
      .where('uid', defectUid)
      .patch({
        name: defectData.name,
        priority: defectData.priority,
        creator: defectData.creator,
        status: defectData.status,
        customFields: defectData.customFields,
        projectUids: trx.raw('?::integer[]', [projectUids]),
        updatedAt: new Date().toISOString(),
      });

    await trx.commit();
  } catch (error) {
    await trx.rollback();
    logger.error(`Error updating single entity ${defectUid}:`, error);
  }
}
