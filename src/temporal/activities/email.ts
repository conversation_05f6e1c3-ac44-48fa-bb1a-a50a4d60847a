import { sendMail } from '@ss-libs/ss-component-notifications';
import logger from '@app/config/logger';
import { serializeErr } from '@app/lib/error';
import env from '@app/config/env';

export type EmailNotification = {
  tenantUid?: string;
  to: string;
  subject: string;
  body: {
    template: string;
    params: Record<string, any>;
  };
  customArgs?: Record<string, string>;
};

const isNotUpstream = ['test'].includes(env.NODE_ENV);

export async function sendEmail(email: EmailNotification) {
  logger.info(`Email Body: "${JSON.stringify(email.body)}"`);

  if (isNotUpstream) {
    logger.info(
      `Skipped sending "${email.subject}" mail to ${email.to} in non-live environment`,
    );
    return;
  }

  try {
    await sendMail(
      email.to,
      email.subject,
      email.body.template,
      email.body.params,
      email.customArgs ? email.customArgs : undefined,
    );
  } catch (err) {
    logger.error('Error sending mail');
    logger.error(serializeErr(err));
    throw err;
  }
}
