import { FGARawWrite, FgaService } from '@ss-libs/ss-component-auth';
import { migrateTenantDB, setupDB, setupDBFromConfig } from '@app/config/db';
import { Attachment } from '@app/models/attachment';
import DEFAULT_PREFERENCE from '@app/constants/preference';
import { DEFAULT_TAGS } from '@app/constants/tags';
import { Dashboard } from '@app/models/dashboard';
import { Membership } from '@app/models/membership';
import { Org } from '@app/models/org';
import { Tag } from '@app/models/tag';
import { Tenant } from '@app/models/tenant';
import { User } from '@app/models/user';
import { charts } from '@app/constants/dashboard';
import fs from 'fs';
import logger from '@app/config/logger';
import path from 'path';
import { setupOpenfga } from '@app/config/openfga';
import { serializeErr } from '@app/lib/error';
import { getSharedDb } from '@app/temporal/client';
import { Knex } from 'knex';
import { OpenFgaClient } from '@openfga/sdk';
import { AppVersion } from '@app/models/appVersion';
import { defaultTenantConfigs } from '@app/constants/tenant';
import { setupRoles } from './role';

export type Account = {
  ownerType: 'org' | 'user';
  ownerUid: string;
  dbServerUid?: number;
  data?: {
    [key: string]: any;
  };
  orgName?: string;
};

async function setupTenantDatabase(acc: Account, sharedDb: Knex) {
  const storeName = acc.ownerUid;
  let tenant = await Tenant.query(sharedDb)
    .where({ tenantUid: acc.ownerUid, tenantType: acc.ownerType })
    .withGraphFetched('dbServer')
    .first();

  if (!tenant) {
    const versions = await AppVersion.query(sharedDb)
      .leftJoin('dbServers', 'dbServers.appNodeUid', 'appVersions.appNodeUid')
      .where((builder) => {
        if (acc.dbServerUid) builder.where('dbServers.uid', acc.dbServerUid);
      })
      .where('appVersions.isDefault', true)
      .where('appVersions.isActive', true);

    const frontendUid = versions.find((v) => v.component === 'frontend')?.uid;
    const backendUid = versions.find((v) => v.component === 'backend')?.uid;

    tenant = await Tenant.query(sharedDb)
      .insert({
        tenantType: acc.ownerType,
        tenantUid: acc.ownerUid,
        setupStatus: 'inProgress',
        dbServerUid: acc.dbServerUid,
        frontendVersionUid: frontendUid,
        backendVersionUid: backendUid,
        config: defaultTenantConfigs,
      })
      .returning('*')
      .withGraphFetched('dbServer');

    try {
      if (tenant.dbServer) {
        const dbServer = setupDBFromConfig(tenant.dbServer);
        await dbServer.raw('create database ??', [storeName]);
        await dbServer.destroy();
      } else {
        await sharedDb.raw('create database ??', [storeName]);
      }
    } catch (err) {
      if (err.code !== '42P04') throw err;
    }
  }

  logger.info('successfully setup tenant database for: ', tenant);
  return tenant;
}

async function setupAdmin(acc: Account, sharedDb: Knex, tenantDB: Knex): Promise<User> {
  let admin: User;
  if (acc.ownerType === 'user') {
    admin = (await User.query(sharedDb)
      .findById(acc.ownerUid)
      .patch({
        preferences: DEFAULT_PREFERENCE,
      })
      .returning('*')) as unknown as User;
  } else {
    admin = (await Org.relatedQuery('creator', sharedDb)
      .for(acc.ownerUid)
      .first()) as unknown as User;
    if (acc.data?.attachments) {
      await Attachment.query(tenantDB).insert([
        ...(acc.data?.attachments || []),
      ]);
    }

    await Org.query(sharedDb).findById(acc.ownerUid).patch({
      preferences: DEFAULT_PREFERENCE,
    });
  }
  return admin;
}

async function setupDashboard(acc: Account, tenantDB: Knex, admin: User) {
  const dashboardName = acc.ownerType === 'org'
    ? `${acc.orgName} Default Dashboard`
    : 'Default Dashboard';

  await Dashboard.query(tenantDB).insert({
    body: { charts },
    editable: true,
    systemDefault: true,
    createdBy: admin.uid,
    name: dashboardName,
  });
}

async function setupTags(acc: Account, tenantDB: Knex, client: OpenFgaClient) {
  const fga = new FgaService(client);

  const tagsFromDb = await Tag.query(tenantDB)
    .insert(DEFAULT_TAGS)
    .returning('*');

  const tagTuples: FGARawWrite[] = tagsFromDb.map((tag) => ({
    objectType: 'tag',
    objectId: tag.uid,
    relation: 'owner',
    subjectType: acc.ownerType,
    subjectId: acc.ownerUid,
  }));

  await fga.create(...tagTuples);
}

async function finalizeTenantSetup(
  tenant: Tenant,
  admin: User,
  acc: Account,
  client: OpenFgaClient,
  sharedDb: Knex,
) {
  await sharedDb.transaction(async (trx) => {
    await Promise.all([
      tenant.$query(trx).patchAndFetch({
        openfgaAuthModelId: client.authorizationModelId,
        openfgaStoreId: client.storeId,
        setupStatus: 'completed',
      }),
      Membership.query(trx)
        .insert({
          accountType: acc.ownerType,
          accountUid: acc.ownerUid,
          userUid: admin.uid,
        })
        .onConflict()
        .ignore(),
    ]);
  });
}

export async function setupAccount(acc: Account, db?: Knex) {
  let sharedDb = db;
  if (!db) {
    sharedDb = await getSharedDb();
  }

  try {
    const tenant = await setupTenantDatabase(acc, sharedDb);
    const storeName = acc.ownerUid;

    const tenantDB = setupDB(storeName, tenant.dbServer);
    await migrateTenantDB(tenantDB);
    logger.info('successfully migrated tenant database for', tenant);

    const migrationsPath = path.join(process.cwd(), 'dist/db/fgaMigrations');
    const files = fs
      .readdirSync(migrationsPath)
      .filter((element) => element.endsWith('js'));
    const latestModel = files[files.length - 1];
    const client = await setupOpenfga({ storeName });

    const admin = await setupAdmin(acc, sharedDb, tenantDB);
    await setupRoles(tenantDB, client, admin, acc);
    await setupDashboard(acc, tenantDB, admin);

    await tenantDB('fga_migrations').insert({
      name: latestModel,
    });

    await setupTags(acc, tenantDB, client);
    await finalizeTenantSetup(tenant, admin, acc, client, sharedDb);

    await tenantDB.destroy();
    logger.info('successfully setup openfga store for ', tenant);
  } catch (err) {
    logger.error('Error in account setup');
    logger.error(serializeErr(err));
    throw err;
  }
}
