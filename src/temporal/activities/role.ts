import { FGARawWrite, FgaService } from '@ss-libs/ss-component-auth';

import { Knex } from 'knex';
import { OpenFgaClient } from '@openfga/sdk';
import { User } from '@app/models/user';
import _ from 'lodash';
import { presetRoles, Role } from '@app/constants/auth';
import { v4 } from 'uuid';

export async function setupRoles(
  tenantDB: Knex,
  client: OpenFgaClient,
  admin: User | undefined,
  handle: { ownerType: string; ownerUid: string },
  customRoles?: Role[],
) {
  const fga = new FgaService(client);
  const roles = (customRoles ?? presetRoles).map((role) => ({ ...role, uid: v4() }));

  // insert the new roles
  await tenantDB('roles')
    .insert(
      roles.map((r) => ({
        uid: r.uid,
        name: r.name,
        slug: r.slug,
        description: r.description,
        system: r.system,
      })),
    )
    .onConflict()
    .ignore();

  const writes: FGARawWrite[] = [];
  const ownerRole = roles.find((r) => r.slug === 'owner');
  if (ownerRole && admin) {
    // add creator as an account owner
    writes.push(
      {
        subjectType: 'user',
        subjectId: admin.uid,
        relation: 'assignee',
        objectType: 'role',
        objectId: ownerRole.uid,
        condition: {
          name: 'default_role',
          context: {
            excluded: [],
          },
        },
      },
      // add user as a member of the org
      {
        subjectType: 'user',
        subjectId: admin.uid,
        relation: 'member',
        objectType: handle.ownerType,
        objectId: handle.ownerUid,
      },
    );
  }

  for (const role of roles) {
    writes.push(
      {
        subjectType: handle.ownerType,
        subjectId: handle.ownerUid,
        relation: 'owner',
        objectType: 'role',
        objectId: role.uid,
      },
      ...role.permissions.map((p) => ({
        objectType: handle.ownerType,
        objectId: handle.ownerUid,
        relation: p,
        subjectType: 'role',
        subjectId: `${role.uid}#assignee`,
      })),
    );
  }

  const creates: FGARawWrite[] = [];

  // we're allowed to this since checks have ~O(1)
  for (const tuple of writes) {
    const exists = await fga.check(
      `${tuple.subjectType}:${tuple.subjectId}`,
      `${tuple.objectType}:${tuple.objectId}`,
      tuple.relation,
      { current_project: '' },
    );
    if (!exists) creates.push(tuple);
  }
  await Promise.all(_.chunk(creates, 100).map((t) => fga.create(...t)));
}
