import { tenantManager } from '@app/lib/tenants';
import { setupDB } from '@app/config/db';
import { Knex } from 'knex';
import { getNextId } from '@app/lib/model';
import { attachParent } from '@app/controllers/case';
import { Org } from '@app/models/org';
import { User } from '@app/models/user';
import logger from '@app/config/logger';
import dayjs from 'dayjs';
import formatDate from '@app/utils/formatDate';
import { Tenant } from '@app/models/tenant';
import { getSharedDb } from '@app/temporal/client';

export type IngressData = {
  body: any;
  ownerUid: string;
  handlePreference: any;
  defaults: any;
  projectUid?: number;
  ownerType: 'user' | 'org';
};

type FieldType = 'statusColors' | 'priorityColors';
type EntitiesType = 'testCase' | 'testRun' | 'testPlan' | 'milestone';
export const entities = {
  runs: {
    table: 'tags',
    preferences: 'testRun',
    systemType: 'run',
    entityType: null,
    status: true,
    priority: true,
  },
  executions: {
    table: 'testExecutions',
    preferences: 'testCase',
    status: true,
    priority: true,
  },
  cases: {
    table: 'testCases',
    preferences: 'testCase',
    priority: true,
    status: false,
  },
  projects: {
    table: 'projects',
    status: false,
    priority: false,
  },
  milestones: {
    table: 'tags',
    systemType: 'milestone',
    preferences: 'milestone',
    entityType: null,
    status: true,
    priority: false,
  },
  folders: {
    table: 'tags',
    systemType: 'folder',
    entityType: '{cases}',
    status: false,
    priority: false,
  },
  plans: {
    table: 'tags',
    systemType: 'plan',
    entityType: '{}',
    preferences: 'testPlan',
    status: true,
    priority: true,
  },
};

const sortPayload = (payload: { [key: string]: any }) => {
  const orderedPayload = {
    ...(payload.folders ? { folders: payload.folders } : {}),
    ...(payload.cases ? { cases: payload.cases } : {}),
    ...(payload.runs ? { runs: payload.runs } : {}),
    ...(payload.executions ? { executions: payload.executions } : {}),
  };
  return orderedPayload;
};

const generateRandomHexColor = () => `#${Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')}`;

export async function handleIngress(data: IngressData) {
  const sharedDb = await getSharedDb();
  let isPreferencesUpdated = false;

  const {
    body, ownerUid, ownerType, projectUid, handlePreference, defaults,
  } = data;

  logger.info('Processing ingress data');
  logger.info('Body: %j', body);
  logger.info('Tenant UID: : %j', ownerUid); // CTODO - use org

  const tenant = await Tenant.query(sharedDb)
    .where('tenantUid', ownerUid)
    .withGraphFetched('dbServer')
    .first();

  if (!tenant) {
    logger.warn(`Tenant not found for tenantUid: ${ownerUid}`);
    throw new Error(`Tenant not found for tenantUid: ${ownerUid}`);
  }

  const { db } = await tenantManager.loadTenant(tenant);
  let currentRecord: { [key: string]: any };
  let parentFolder: number;

  async function findOrCreate(trx: Knex.Transaction, record: { [key: string]: any }, entityType: string, parentFolder?: number) {
    const isTestCase = entities[entityType].table === 'testCases';
    const tableName = entities[entityType].table;

    const existsEntity = await trx
      .from(tableName)
      .where((builder) => {
        if (isTestCase) {
          builder.where('externalId', record.externalId || record.caseRef);
        } else {
          builder.whereRaw(
            '("customFields"->>\'externalId\')::text = ?',
            [record.runRef],
          );
        }
      })
      .andWhere('projectUid', record.projectUid)
      .first();

    if (existsEntity) { return existsEntity.uid; }

    const uid = await getNextId(trx, <any>{ tableName });
    const fields = {
      externalId: isTestCase ? record.externalId || record.caseRef : record.runRef || '',
      priority: defaults[entities[entityType].preferences].priority,
      status: defaults[entities[entityType].preferences].status,
      source: record.source || '',
      ...(isTestCase ? {
        active: true, version: 1, uid, testCaseRef: uid,
      } : {}),
    };
    const payload = {
      ...(entities[entityType].table === 'tags' ? { systemType: entities[entityType].systemType } : {}),
      ...(isTestCase ? { parentUid: parentFolder } : {}),
      name: record.name || '',
      projectUid: record.projectUid,
      ...(isTestCase ? fields : {}),
      customFields: {
        ...(!isTestCase ? fields : {}),
      },
    };

    const [entity] = await trx.from(tableName)
      .insert(payload).returning('*');

    if (parentFolder && isTestCase) { await attachParent(uid, 1, parentFolder, trx); }

    return entity?.uid;
  }

  const preferencesMapping = {
    cases: 'testCase',
    runs: 'testRun',
  };

  const mapPreferences = (colors, entityType) => colors.reduce((acc: any, curr) => {
    if (curr.entityType === entityType) {
      acc[curr.name.toLowerCase()] = curr.id;
    }
    return acc;
  }, {});

  const preferences = Object.keys(entities).reduce((acc: any, curr) => {
    const entityType = preferencesMapping[curr];
    if (!acc[curr] && entityType) {
      acc[entityType] = {
        status: mapPreferences(handlePreference.statusColors, entityType),
        priority: mapPreferences(handlePreference.priorityColors, entityType),
      };
    }
    return acc;
  }, {
    totalStatuses: handlePreference.statusColors.length,
    totalPriorities: handlePreference.priorityColors.length,
  });

  const findFieldId = (handlePreference, entity: EntitiesType, name: string, field: FieldType) => handlePreference[field].find((element) => element.name?.toLowerCase() === name.toLowerCase() && element.entityType === entity)?.id;

  const updatePreferences = (entityType: EntitiesType, name: string, field: FieldType) => {
    if (!['statusColors', 'priorityColors'].includes(field)) {
      logger.info('Invalid field type detected. Expected either "statusColors" or "priorityColors".');
      return;
    }

    if (!['testCase', 'testRun', 'milestone', 'testPlan'].includes(entityType)) {
      logger.info('Invalid entity type detected.');
      return;
    }

    if (field === 'statusColors') { preferences.totalStatuses += 1; } else { preferences.totalPriorities += 1; }

    const id = field === 'statusColors' ? preferences.totalStatuses : preferences.totalPriorities;

    const newField = {
      color: generateRandomHexColor(),
      entityType,
      id,
      isDefault: false,
      ...(field === 'statusColors' ? {
        isCompleted: false,
        isSuccess: false,
        isFailure: false,
      } : {}),
      name,
    };

    handlePreference[field].push(newField);

    isPreferencesUpdated = true;

    return id;
  };
  const payload = sortPayload(body.entities);

  for (const entity of Object.keys(payload)) {
    const entityMapping = entities[entity];
    if (projectUid) {
      parentFolder = (await db.from('tags').where({
        systemType: 'folder',
        projectUid,
      }).first()).uid;
    }

    for (const entry of body.entities[entity].entries) {
      currentRecord = null;
      logger.log('info', ` Processing data for source [${entry.source}] and type [${entityMapping.systemType ?? entityMapping.table}]`);

      // Our hacky way to tell if this is a unix timestamp in miliseconds or seconds
      entry.externalCreatedAt = dayjs(entry.createdAt).year() < 1980
        ? formatDate(dayjs(entry.createdAt * 1000).toDate())
        : formatDate(dayjs().toDate());
      entry.externalUpdatedAt = dayjs(entry.updatedAt).year() < 1980
        ? formatDate(dayjs(entry.updatedAt * 1000).toDate())
        : formatDate(dayjs().toDate());
      delete entry.createdAt;
      delete entry.updatedAt;

      // Add preferences for new entities
      // If the incoming entry contains a status or priority property, check if a status/priority with the same name exists in preferences.
      // If it exists, retrieve its UID; otherwise, create a new status/priority with that name.
      // In case the entity requires a status/priority, but the incoming entry lacks this property, retrieve the default value for this entity.
      if (entityMapping.status) {
        if (entry.status) {
          const fieldId = findFieldId(handlePreference, entityMapping.preferences, entry.status, 'statusColors');
          if (fieldId) { entry.status = fieldId; } else { entry.status = updatePreferences(entityMapping.preferences, entry.status, 'statusColors'); }
        } else {
          entry.status = defaults[entityMapping.preferences].status;
        }
      }
      if (entityMapping.priority) {
        if (entry.priority) {
          const fieldId = findFieldId(handlePreference, entityMapping.preferences, entry.priority, 'priorityColors');
          if (fieldId) { entry.priority = fieldId; } else { entry.priority = updatePreferences(entityMapping.preferences, entry.priority, 'priorityColors'); }
        } else {
          entry.priority = defaults[entityMapping.preferences].priority;
        }
      }
      // First if testfiesta key exists

      if (Object.prototype.hasOwnProperty.call(entry, 'tfIdField')) {
        logger.log('info', ' Looking for matching record by tfIdField.');

        try {
          // eslint-disable-next-line no-await-in-loop
          currentRecord = await db
            .from(entityMapping.table)
            .where({
              uid: entry.tfIdField,
              ...(projectUid ? { projectUid } : {}),
            })
            .first();
        } catch (error) {
          logger.log('warn', `${error}`);
        }

        if (currentRecord) {
          logger.log('info', `Updating current record by tfIdField with uid [${currentRecord.uid}]`);
          // Update fields and continue
          // eslint-disable-next-line no-restricted-syntax
          for (const key of entry) {
            if (key !== 'customFields') {
              if (Object.prototype.hasOwnProperty.call(currentRecord, key)) {
                currentRecord[key] = entry[key];
              } else {
                currentRecord.customFields[key] = entry[key];
              }
            } else {
              // eslint-disable-next-line no-restricted-syntax
              for (const subKey of entry?.customFields ?? []) {
                currentRecord.customFields[subKey] = entry.customFields[subKey];
              }
            }
          }
          delete currentRecord.uid;
          db.from(entityMapping.table).where({
            uid: entry.tfIdField,
            ...(projectUid ? { projectUid } : {}),
          }).update(currentRecord);
          // eslint-disable-next-line no-continue
          continue;
        }
      } else {
        logger.log('info', ' testfiesta_id not found on entry.');
      }

      // Second if the externalId, type, and source combo match any in the DB
      logger.log('info', ' Looking for matching record by externalId.');

      if (Object.prototype.hasOwnProperty.call(entry, 'externalId')) {
        try {
          // eslint-disable-next-line no-await-in-loop
          currentRecord = await db
            .from(entityMapping.table)
            .where(function () {
              if (entityMapping.table !== 'tags') {
                // Tags include both externalID and source in customFields, but the other table does not.
                this.where({
                  source: entry.source,
                  externalId: entry.externalId,
                });
              } else {
                this.whereRaw(
                  '("customFields"->>\'source\')::text = ? AND ("customFields"->>\'externalId\')::text = ?',
                  [entry.source, entry.externalId],
                );
              }
            })
            .andWhere(function () {
              if (projectUid) {
                this.where({ projectUid });
              }
            })
            .first();
        } catch (error) {
          logger.log('warn', `${error}`);
          return;
        }
      }
      if (currentRecord) {
        logger.log('info', ` Updating current record externalId with id [${currentRecord.uid}]`);
        // Update fields and continue
        // eslint-disable-next-line no-restricted-syntax
        for (const key in entry) {
          if (key !== 'customFields') {
            if (Object.prototype.hasOwnProperty.call(currentRecord, key)) {
              currentRecord[key] = entry[key];
            } else {
              currentRecord.customFields[key] = entry[key];
            }
          } else {
            // eslint-disable-next-line no-restricted-syntax
            for (const [key, value] of Object.entries(entry.customFields)) {
              currentRecord.customFields[key] = value;
            }
          }
        }
        try {
          await db.from(entityMapping.table).where({
            uid: currentRecord.uid,
            ...(projectUid ? { projectUid } : {}),
          }).update({ ...currentRecord, uid: undefined });
          continue;
        } catch (error) {
          logger.log('warn', `${error}`);
          return;
        }
        // eslint-disable-next-line no-continue
      } else {
        logger.log(
          'info',
          ' Matching entry not found for externail_id.',
        );
      }
      logger.log('info', ' No records found.  Creating new record.');
      // Else, create the record

      // For new records, we should grab the columns of the table. If the entry keys match the table attributes, they will be inserted directly; otherwise, they will go into customFields attribute.
      const attributes = await db.from(entityMapping.table).columnInfo()
        .then((columns) => Object.keys(columns));

      if (entityMapping.table === 'tags') { // Entities in tags should include the systemType (e.g., plan, milestone, folder) and its entityType (e.g., {cases})
        entry.systemType = entityMapping.systemType;
        entry.entityTypes = entityMapping.entityType;
      }
      if (projectUid) {
        // If it's project scope, it should include projectUid
        entry.projectUid = projectUid;
      }

      if (entityMapping?.systemType && entityMapping.systemType === 'folder') {
        // New folders should be placed under the parent folder of the project
        entry.parentUid = parentFolder;
      }

      if (entityMapping.table === 'testCases' && entry.folderExternalId) {
        let ffUid = parentFolder;

        const folder = await db.from('tags').where({
          systemType: 'folder',
          projectUid: entry.projectUid,
        }).whereRaw(
          '("customFields"->>\'externalId\')::text = ?',
          [entry.folderExternalId],
        ).first();

        if (folder) ffUid = folder.uid;

        logger.log('info', ' New record created.');
        const trx = await db.transaction();
        await findOrCreate(trx, entry, 'cases', ffUid);
        await trx.commit();
        continue;
      }
      // Custom Scenario for entities

      if (entityMapping.table === 'testExecutions') {
        const trx = await db.transaction();
        const caseUid = await findOrCreate(trx, entry, 'cases', parentFolder);
        entry.testCaseUid = caseUid;
        entry.testCaseRef = caseUid;

        const runUid = await findOrCreate(trx, entry, 'runs');
        entry.testRunUid = runUid;
        await trx.commit();
      }

      const newRecord = Object.keys(entry).reduce((acc: Record<string, any>, curr) => {
        if (attributes.includes(curr)) {
          const val = entry[curr];
          if (val && typeof val === 'object') {
            acc[curr] = curr === 'customFields' ? val : JSON.stringify(val);
          } else {
            acc[curr] = val;
          }
        } else
          if (!acc.customFields) { acc.customFields = { [curr]: entry[curr] }; } else { acc.customFields[curr] = entry[curr]; }
        return acc;
      }, {});

      try {
        await db.from(entityMapping.table).insert(newRecord);
      } catch (error) {
        logger.log('warn', `${error}`);
      }
      logger.log('info', ' New record created.');
    }
  }

  if (isPreferencesUpdated) {
    const sharedDB = setupDB();
    const Model: any = ownerType === 'org' ? Org : User;

    await Model.query(sharedDB).findById(ownerUid).patch({
      preferences: handlePreference,
    });
    logger.info(`Preferences for ownerUid: '${ownerUid}' has been updated.`);
  }
}
