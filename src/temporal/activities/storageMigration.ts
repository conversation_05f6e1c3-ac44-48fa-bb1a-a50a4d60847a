import logger from '@app/config/logger';
import { serializeErr } from '@app/lib/error';
import { Knex } from 'knex';
import fs from 'fs';
import { storageProvider } from '@ss-libs/ss-component-media';
import { loadAllPrivateKeys } from '@app/config/keyManagerLoader';
import { AWSCredential } from '@ss-libs/ss-component-media/dist/providers/aws';
import { GCPCredential } from '@ss-libs/ss-component-media/dist/providers/gcp';
import { decryptProfileCredentials } from '@ss-libs/ss-component-media/dist/util/cryptography';
import { Attachment } from '@app/models/attachment';
import { initializeStorage } from '@app/utils/storage';
import { setupDB } from '@app/config/db';
import { getSharedDb } from '@app/temporal/client';
import { ScheduledTask } from '@app/models/scheduledTask';
import config from '@app/constants/config';
import env from '@app/config/env';

const BATCH_SIZE = 20;

type Credentials = AWSCredential | GCPCredential;

interface StorageMigrationJob {
  sourceProfileId?: string;
  destinationProfileId: string;
  tenantUid: string;
  taskUid: string;
}
async function updateTaskStatus(db: Knex, taskUid: string, data: Partial<ScheduledTask>) {
  await db('scheduledTasks')
    .where({ uid: taskUid })
    .update(data);
}
export async function handleStorageMigration(data: StorageMigrationJob) {
  const db = await getSharedDb();
  const {
    sourceProfileId, destinationProfileId, tenantUid, taskUid,
  } = data;
  await updateTaskStatus(db, taskUid, { status: 'running', message: null, percentage: 0 });
  logger.info('Starting storage migration job. Initializing storage...');
  initializeStorage(db);
  logger.info('Storage initialized successfully.');
  logger.info('Loading private keys...');
  await loadAllPrivateKeys(db);
  logger.info('Private keys loaded successfully.');
  try {
    logger.info('Fetching storage profiles for migration...');
    // orderby isdefault=true so we can get default profile first
    const destinationProfile = await db('storageProfiles').whereNull('deletedAt').where('uid', destinationProfileId).orderBy('isDefault', 'desc')
      .first();
    let sourceProfile;
    if (sourceProfileId) {
      sourceProfile = await db('storageProfiles').where('uid', sourceProfileId).orderBy('isDefault', 'desc').first();
    }
    if (!sourceProfile && env.GC_SERVICE_KEY_FILE && env.GCS_BUCKET_NAME) {
      sourceProfile = {
        provider: 'gcp',
        credentials: {
          bucket: env.GCS_BUCKET_NAME,
          ...JSON.parse(fs.readFileSync(env.GC_SERVICE_KEY_FILE).toString()),
        },
      };
    }
    if (!sourceProfile || !destinationProfile) {
      await updateTaskStatus(db, taskUid, {
        status: 'error',
        message: 'Source or destination profile not found',
        percentage: 0,
      });
      throw new Error('Source or destination profile not found');
    }

    const ownerUid = destinationProfile.ownerId;
    logger.info('Initializing storage managers...');
    // Initialize storage managers
    const sourceManager = storageProvider().getManager(sourceProfile);
    const destManager = storageProvider().getManager(destinationProfile);
    logger.info('Storage managers initialized successfully.');
    logger.info('Decrypting credentials...');
    // Decrypt credentials
    await updateTaskStatus(db, taskUid, { percentage: 30, message: 'Decrypting storage credentials' });
    const decryptSourceCredentials = sourceProfile.uid ? await decryptProfileCredentials({
      credentials: sourceProfile.credentials,
      provider: sourceProfile.provider,
      entityId: sourceProfile.uid,
    }) : sourceProfile.credentials as Credentials;
    const decryptDestCredentials = await decryptProfileCredentials({
      credentials: destinationProfile.credentials,
      provider: destinationProfile.provider,
      entityId: destinationProfile.uid,
    }) as unknown as Credentials;
    logger.info('Credentials decrypted successfully.');

    // Check if both credentials are identical (bucket, region, etc.)
    logger.info('Comparing source and destination credentials...');
    if (
      decryptSourceCredentials.bucket === decryptDestCredentials.bucket
      && sourceProfile.provider === destinationProfile.provider
    ) {
      const fieldsToCompare = sourceProfile.provider === 'aws'
        ? ['bucket', 'region']
        : ['bucket', 'project_id'];

      const isSame = fieldsToCompare.every(
        (field) => decryptSourceCredentials[field] === decryptDestCredentials[field],
      );

      if (isSame) {
        updateTaskStatus(db, taskUid, {
          status: 'completed',
          message: 'Source and destination credentials are identical, skipping migration',
          percentage: 100,
        });
        throw new Error('Source and destination credentials are same, skipping migration');
      }
    }
    logger.info('Credentials comparison completed.');
    const tenantDb = setupDB(tenantUid);
    // Get attachments keys for migration
    const attachments = await Attachment.query(tenantDb).where({ ownerUid });
    const attachmentKeys = attachments.map((att) => att.key);
    const totalSize = attachments.reduce((acc, att) => acc + att.size, 0);

    // Get keys from source under the owner's UID prefix
    logger.info(`Found ${attachmentKeys.length} keys with size ${totalSize}.`);
    if (sourceProfileId && totalSize > config.MAX_MIGRATION_BUCKET_SIZE * 1.1) {
      await updateTaskStatus(db, taskUid, {
        status: 'failed',
        message: 'Source bucket exceeds 50GB — migration not allowed.',
        percentage: 0,
      });
      throw new Error('Source bucket exceeds 50GB — migration not allowed.');
    }

    logger.info(`Starting migration from ${sourceProfile.provider}:${decryptSourceCredentials.bucket} to ${destinationProfile.provider}:${decryptDestCredentials.bucket}`);

    // Combine both public and attachment keys
    let currentPercentage = 10;
    await updateTaskStatus(db, taskUid, {
      message: 'Starting migration',
      percentage: currentPercentage,
    });
    const percentageIncrement = Math.floor((100 - currentPercentage) / Math.ceil(attachmentKeys.length / BATCH_SIZE));
    // Perform batched migration
    for (let i = 0; i < attachmentKeys.length; i += BATCH_SIZE) {
      const batch = attachmentKeys.slice(i, i + BATCH_SIZE);
      await Promise.all(
        batch.map(async (key) => {
          try {
            const stream = await sourceManager.getObjectStream(decryptSourceCredentials as Credentials, key);
            await destManager.putObjectStream(decryptDestCredentials as Credentials, key, stream);
          } catch (error) {
            if (error.code === 404) {
              logger.warn(`Skipping missing object: ${key}`);
            } else {
              logger.error(`Error processing key ${key}:`, error);
            }
          }
        }),
      );
      currentPercentage += percentageIncrement;
      await updateTaskStatus(db, taskUid, {
        message: `Migrating batch ${i / BATCH_SIZE + 1} of ${Math.ceil(attachmentKeys.length / BATCH_SIZE)}`,
        percentage: currentPercentage,
      });
    }
    await updateTaskStatus(db, taskUid, {
      status: 'completed',
      completedAt: db.fn.now() as unknown as string,
      message: `Migration completed successfully from ${sourceProfile.provider}:${decryptSourceCredentials.bucket} to ${destinationProfile.provider}:${decryptDestCredentials.bucket}`,
      percentage: 100,
    });

    logger.info('Migration completed successfully');
  } catch (err) {
    await updateTaskStatus(db, taskUid, {
      status: 'error',
      message: serializeErr(err),
      traceback: err.stack,
      percentage: 0,
    });
    logger.error('Storage migration failed');
    logger.error(serializeErr(err));
  }
}
