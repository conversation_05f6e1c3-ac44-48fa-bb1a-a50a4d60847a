import { SharedTestStep } from '@app/models/sharedTestStep';
import { TestCase } from '@app/models/testCase';
import { TestCaseStep } from '@app/models/testCaseStep';
import { TestExecution } from '@app/models/testExecution';
import { TestExecutionStep } from '@app/models/testExecutionStep';
import { Folder } from '@app/models/folder';
import { TestMilestone } from '@app/models/testMilestone';
import { TestMilestoneRun } from '@app/models/testMilestoneRun';
import { TestPlan } from '@app/models/testPlan';
import { TestRun } from '@app/models/testRun';
import { TestTemplate } from '@app/models/testTemplate';
import logger from '@app/config/logger';
import { serializeErr } from '@app/lib/error';
import { Tenant } from '@app/models/tenant';
import { tenantManager } from '@app/lib/tenants';
import { getSharedDb } from '@app/temporal/client';

export type ProjectData = {
  projectId: number;
  ownerUid: string;
};

export async function handleSoftDeleteProjectRelatedRecords(data: ProjectData) {
  try {
    const sharedDb = await getSharedDb();
    const tenant = await Tenant.query(sharedDb)
      .where('tenantUid', data.ownerUid)
      .withGraphFetched('dbServer')
      .first();

    const conn = await tenantManager.loadTenant(tenant);
    const tenantDB = conn.db;

    const trx = await tenantDB.transaction();

    try {
      await TestTemplate.query(trx)
        .where('projectUid', data.projectId)
        .patch({ deletedAt: tenantDB.fn.now() });

      await Folder.query(trx)
        .where('projectUid', data.projectId)
        .patch({ deletedAt: tenantDB.fn.now() });

      const deletedTestRuns = await TestRun.query(trx)
        .where('projectUid', data.projectId)
        .patch({ deletedAt: tenantDB.fn.now() })
        .returning('*');

      const deletedTestCases = await TestCase.query(trx)
        .where('projectUid', data.projectId)
        .patch({ deletedAt: tenantDB.fn.now() })
        .returning('*');

      const deletedTestMilestones = await TestMilestone.query(trx)
        .where('projectUid', data.projectId)
        .patch({ deletedAt: tenantDB.fn.now() })
        .returning('*');

      await TestPlan.query(trx)
        .where('projectUid', data.projectId)
        .patch({ deletedAt: tenantDB.fn.now() });

      await SharedTestStep.query(trx)
        .where('projectUid', data.projectId)
        .patch({ deletedAt: tenantDB.fn.now() });

      // related tables
      const deletedTestRunIds = deletedTestRuns.length > 0
        ? deletedTestRuns.map((testRun) => testRun.uid)
        : [];

      const deletedTestMilestoneIds = deletedTestMilestones.length > 0
        ? deletedTestMilestones.map((testMilestone) => testMilestone.uid)
        : [];

      const deletedCaseIds = deletedTestCases.length > 0
        ? deletedTestCases.map((testCase) => testCase.uid)
        : [];

      await TestCaseStep.query(trx)
        .whereIn('testCaseRef', deletedCaseIds)
        .patch({ deletedAt: tenantDB.fn.now() });

      const deletedExecutions = await TestExecution.query(trx)
        .whereIn('testCaseRef', deletedCaseIds)
        .patch({ deletedAt: tenantDB.fn.now() })
        .returning('*');

      const deletedExecutionIds = deletedExecutions.length > 0
        ? deletedExecutions.map((testCase) => testCase.uid)
        : [];

      await TestExecutionStep.query(trx)
        .whereIn('test_execution_uid', deletedExecutionIds)
        .patch({ deletedAt: tenantDB.fn.now() });

      await TestMilestoneRun.query(trx)
        .whereIn('milestone_uid', deletedTestMilestoneIds)
        .whereIn('run_uid', deletedTestRunIds)
        .patch({ deletedAt: tenantDB.fn.now() });

      await trx.commit();

      logger.info(
        `successfully soft deleted all project related records for project: "${data.projectId}"`,
      );
    } catch (err) {
      await trx.rollback();
      logger.error('Error in transaction');
      logger.error(serializeErr(err));
      throw err;
    }
  } catch (err) {
    logger.error('Error processing soft delete project related records');
    logger.error(serializeErr(err));
    throw err;
  }
}
