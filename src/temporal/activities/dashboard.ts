import logger from '@app/config/logger';
import { serializeErr } from '@app/lib/error';
import {
  planCharts, runCharts, testRunDetailViewCharts, milestoneDetailViewCharts, testPlanDetailViewCharts,
} from '@app/constants/dashboard';
import { Dashboard } from '@app/models/dashboard';
import { Tenant } from '@app/models/tenant';
import { tenantManager } from '@app/lib/tenants';
import { getSharedDb } from '@app/temporal/client';

export type DefaultDashboardData = {
  projectUid: number,
  editable: boolean,
  systemDefault: boolean,
  ownerUid: string,
  projectName: string,
};

export async function handleCreateDashboard(data: DefaultDashboardData) {
  try {
    const sharedDb = await getSharedDb();
    const tenant = await Tenant.query(sharedDb)
      .where('tenantUid', data.ownerUid)
      .where('setupStatus', 'completed')
      .withGraphFetched('dbServer')
      .first();
    if (!tenant) {
      logger.warn(`Tenant not found for tenantUid: ${data.ownerUid}, skipping dashboard setup for project ${data.projectUid}`);
      return;
    }

    const conn = await tenantManager.loadTenant(tenant);
    const tenantDB = conn.db;

    await Dashboard.query(tenantDB).insert([
      {
        body: { charts: runCharts },
        name: data.projectName,
        createdBy: data.ownerUid,
        projectUid: data.projectUid,
        editable: data.editable,
        systemDefault: data.systemDefault,
        entityType: 'testRun',
      },
      {
        body: { charts: planCharts },
        name: data.projectName,
        createdBy: data.ownerUid,
        projectUid: data.projectUid,
        editable: data.editable,
        systemDefault: data.systemDefault,
        entityType: 'testPlan',
      },
      {
        body: { charts: testRunDetailViewCharts },
        name: data.projectName,
        createdBy: data.ownerUid,
        projectUid: data.projectUid,
        editable: data.editable,
        systemDefault: data.systemDefault,
        entityType: 'testRunDetailView',
      },
      {
        body: { charts: testPlanDetailViewCharts },
        name: data.projectName,
        createdBy: data.ownerUid,
        projectUid: data.projectUid,
        editable: data.editable,
        systemDefault: data.systemDefault,
        entityType: 'testPlanDetailView',
      },
      {
        body: { charts: milestoneDetailViewCharts },
        name: data.projectName,
        createdBy: data.ownerUid,
        projectUid: data.projectUid,
        editable: data.editable,
        systemDefault: data.systemDefault,
        entityType: 'milestoneDetailView',
      },
    ]);

    logger.info(
      `successfully created default dashboard for project "${data.projectUid}"`,
    );
  } catch (err) {
    logger.error('Error processing dashboard');
    logger.error(serializeErr(err));
    throw err;
  }
}
