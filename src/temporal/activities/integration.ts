import 'module-alias/register';
import { IntegrationServiceFactory } from '@app/integrations/index';
import logger from '@app/config/logger';
import { kebabCase } from 'lodash';
import { getNextId } from '@app/lib/model';
import axios from 'axios';
import { SERVICE_CONFIGS } from '@app/constants/integration';
import { slugify } from '@app/utils/string';
import { Knex } from 'knex';
import { tenantManager } from '@app/lib/tenants';
import { FgaService } from '@ss-libs/ss-component-auth';
import { getSharedDb } from '@app/temporal/client';
import { transformError } from '@app/utils/transformIntegrationError';
import { tenantModels, sharedModels } from '../../models';
import { terminateWorkflow } from '../client';

type SyncContext = {
  shouldHalt: boolean;
  errorMessage?: string;
  status?: number;
};

export async function handleIntegrationError(tenantUid: string, integrationUid: string) {
  const sharedDb = await getSharedDb();
  const tenant = await sharedModels.Tenant.bindKnex(sharedDb).query()
    .where('tenantUid', tenantUid)
    .first();
  if (!tenant) {
    logger.warn(`Tenant not found for tenantUid: ${tenantUid}`);
    throw new Error(`Tenant not found for tenantUid: ${tenantUid}`);
  }

  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;
  const Integration = tenantModels.Integration.bindKnex(tenantDb);
  const integration = await Integration.query()
    .where('uid', integrationUid)
    .first();
  if (!integration) {
    logger.warn(`Integration not found for integrationUid: ${integrationUid}`);
    throw new Error(`Integration not found for integrationUid: ${integrationUid}`);
  }
  const count = (integration.errorCount || 0) + 1;
  if (count > 3) {
    await Integration.query()
      .where('uid', integrationUid)
      .patch({
        errorCount: count,
      });
    const handle = await sharedModels.Handle.bindKnex(sharedDb).query()
      .where('ownerUid', tenantUid)
      .first();
    if (!handle) {
      logger.warn(`Handle not found for tenantUid: ${tenantUid}`);
      throw new Error(`Handle not found for tenantUid: ${tenantUid}`);
    }
    await terminateWorkflow('integration-sync-queue', `${handle.name}:integration:sync:${integration.service}:${integration.uid}`);
  }
  await Integration.query()
    .where('uid', integrationUid)
    .patch({
      errorCount: count,
    });
}

const handleExternalIntegrationError = async (tenant: any, integrationUid: any, error: string, status: number) => {
  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;
  const Integration = tenantModels.Integration.bindKnex(tenantDb);
  const integration = await Integration.query()
    .where('uid', integrationUid)
    .first();
  if (!integration) {
    logger.warn(`Integration not found for integrationUid: ${integrationUid}`);
    throw new Error(`Integration not found for integrationUid: ${integrationUid}`);
  }
  // check if status is 401 then set integration to inactive
  if (Number(status) === 401) {
    await Integration.query()
      .where('uid', integration.uid)
      .patch({ status: 'inactive' });
    const sharedDb = await getSharedDb();
    const handle = await sharedModels.Handle.bindKnex(sharedDb).query()
      .where('ownerUid', tenant.tenantUid)
      .first();
    if (!handle) {
      logger.warn(`Handle not found for tenantUid: ${tenant.tenantUid}`);
      throw new Error(`Handle not found for tenantUid: ${tenant.tenantUid}`);
    }
    await terminateWorkflow('integration-sync-queue', `${handle.name}:integration:sync:${integration.service}:${integration.uid}`);
    return;
  }
  const externalErrors = [...(integration.externalErrors || []), {
    message: error,
    statusCode: status,
    timestamp: new Date().toISOString(),
    resolved: false,
  }];
  await Integration.query()
    .where('uid', integration.uid)
    .patch({
      status: 'error',
      externalErrors,
    });
  if (integration.externalErrors?.length > 3) {
    const sharedDb = await getSharedDb();
    const handle = await sharedModels.Handle.bindKnex(sharedDb).query()
      .where('ownerUid', tenant.tenantUid)
      .first();
    if (!handle) {
      logger.warn(`Handle not found for tenantUid: ${tenant.tenantUid}`);
      throw new Error(`Handle not found for tenantUid: ${tenant.tenantUid}`);
    }
    await terminateWorkflow('integration-sync-queue', `${handle.name}:integration:sync:${integration.service}:${integration.uid}`);
  }
};

export async function fetchAndUpdateUser(tokenUid: string, tenantUid: string) {
  const sharedDb = await getSharedDb();
  const tenant = await sharedModels.Tenant.bindKnex(sharedDb).query()
    .where('tenantUid', tenantUid)
    .withGraphFetched('dbServer')
    .first();
  if (!tenant) {
    logger.warn(`Tenant not found for tenantUid: ${tenantUid}`);
    throw new Error(`Tenant not found for tenantUid: ${tenantUid}`);
  }
  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;
  const trx = await tenantDb.transaction();
  logger.info(`Fetching and updating external user entity for tenant: ${tenantUid}`);
  try {
    // Use models object consistently with transaction
    const integrationToken = await tenantModels.IntegrationToken.bindKnex(tenantDb).query(trx)
      .findById(tokenUid);

    if (!integrationToken) {
      await trx.rollback();
      logger.warn(`Integration token not found for tokenUid: ${tokenUid}`);
      return;
    }

    const integration = await tenantModels.Integration.query(trx)
      .where('uid', integrationToken.integrationUid)
      .whereNull('deletedAt')
      .first();

    if (!integration) {
      await trx.rollback();
      logger.warn(`Integration not found for integrationUid: ${integrationToken.integrationUid}`);
      return;
    }

    const serviceName = integration.service;
    const service = IntegrationServiceFactory.getService(serviceName);
    if (!service) {
      await trx.rollback();
      logger.warn(`Invalid service for integrationUid: ${integrationToken.integrationUid}`);
      return;
    }

    const { url } = integrationToken;
    const header = await service.prepareAuthHeader(integrationToken, tenantDb);
    const response = await service.fetchUserData(header, url);

    if (!response.success) {
      // add errors to integration
      await tenantModels.Integration.query(trx)
        .where('uid', integration.uid)
        .patch({
          status: 'error',
          externalErrors: [...(integration.externalErrors || []), {
            message: response.error,
            statusCode: response.status,
            timestamp: new Date().toISOString(),
            resolved: false,
          }],
        });
      await trx.commit();
      return;
    }

    const userData = response.data;
    // Check if ExternalEntity exists
    const externalUserEntity = await tenantModels.ExternalEntity.query(trx)
      .where('source', serviceName)
      .where('entityType', 'org')
      .where('sourceId', userData.sourceId)
      .first();
    if (!externalUserEntity) {
      // Insert new ExternalEntity
      await tenantModels.ExternalEntity.query(trx).insert({
        ...userData,
        entityUid: tenantUid,
      });
    }

    await trx.commit();
    logger.info(`Successfully fetched and updated external user entity for tenant: ${tenantUid}`);
  } catch (error) {
    await trx.rollback();
    logger.error(
      `Error fetching and updating user for tenant: ${tenantUid}`,
      error,
    );
  }
}

export async function handleIntegrationDeletion(tenantUid: string, integrationUid: string, fullDelete: boolean) {
  const sharedDb = await getSharedDb();
  const tenant = await sharedModels.Tenant.bindKnex(sharedDb).query()
    .where('tenantUid', tenantUid)
    .first();
  if (!tenant) {
    logger.warn(`Tenant not found for tenantUid: ${tenantUid}`);
    throw new Error(`Tenant not found for tenantUid: ${tenantUid}`);
  }
  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;
  const trx = await tenantDb.transaction();
  const Defect = tenantModels.Defect.bindKnex(tenantDb);
  logger.info(`Deleting integration Data for integration: ${integrationUid} for tenant: ${tenantUid}`);
  try {
    if (!fullDelete) {
      // Soft delete: Update defects to use 'testfiesta' as source
      await Defect.query(trx)
        .where('integrationSourceUid', integrationUid)
        .patch({
          integrationSourceUid: null,
          updatedAt: new Date().toISOString(),
        });
    } else {
      // Hard delete: Delete defects and integration
      await Defect.query(trx)
        .where('integrationSourceUid', integrationUid)
        .patch({
          deletedAt: new Date().toISOString(),
        });
    }

    await trx.commit();
    logger.info(`Successfully ${fullDelete ? 'hard' : 'soft'} deleted integration data for integration: ${integrationUid} for tenant: ${tenantUid}`);
  } catch (error) {
    await trx.rollback();
    logger.error(`Error deleting integration ${integrationUid}:`, error);
  }
}

export async function fetchAndProcessEntities(
  integrationUid: string,
  tenantUid: string,
  syncFlow: boolean = false,
) {
  // db connections
  const sharedDb = await getSharedDb();
  const tenant = await sharedModels.Tenant.bindKnex(sharedDb).query()
    .where('tenantUid', tenantUid)
    .first();
  if (!tenant) {
    logger.warn(`Tenant not found for tenantUid: ${tenantUid}`);
    throw new Error(`Tenant not found for tenantUid: ${tenantUid}`);
  }
  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;

  // models
  const Integration = tenantModels.Integration.bindKnex(tenantDb);
  const IntegrationToken = tenantModels.IntegrationToken.bindKnex(tenantDb);

  // variable to track sync start time when sync begins
  let timeStart = null;
  // get integration from db
  const integration = await Integration.query()
    .findById(integrationUid)
    .whereNull('deletedAt')
    .whereNull('archivedAt');

  if (!integration) {
    logger.warn(`Integration not found or archived for integrationUid: ${integrationUid}`);
    throw new Error(`Integration not found or archived for integrationUid: ${integrationUid}`);
  }

  // get integration token from db
  const integrationToken = await IntegrationToken.query()
    .where('integrationUid', integration.uid)
    .where('ownerUid', tenantUid)
    .first();

  if (!integrationToken) {
    logger.warn(`Auth token not found for integrationUid: ${integration.uid}`);
    throw new Error(`Auth token not found for integrationUid: ${integration.uid}`);
  }
  const syncContext: SyncContext = {
    shouldHalt: false,
  };
  try {
    logger.info(`Fetching and processing entities for integration: ${integrationUid} for tenant: ${tenantUid}`);

    // get service from factory
    const service = IntegrationServiceFactory.getService(integration.service);

    // get max api limit for the service
    const maxApiLimit = SERVICE_CONFIGS[integration.service].apiLimit;

    // prepare auth header
    const authHeader = await service.prepareAuthHeader(integrationToken, tenantDb);

    // get integration configuration
    const { projectConfigurations, syncDuration, url } = integration.configuration;

    // First create the configuration mapping
    const configurationMapping = {};
    if (!projectConfigurations) {
      logger.warn(`No project configurations found for integration: ${integration.uid}`);
      return;
    }

    // create configuration mapping  to map external project id to testfiesta projects list
    projectConfigurations.forEach((config) => {
      Object.entries(config.projects).forEach(([key, projectList]: [string, any[]]) => {
        projectList.forEach((project) => {
          const { projectId, projectName } = project;
          if (!configurationMapping[projectId]) {
            configurationMapping[projectId] = {
              resourceId: config.resourceId,
              url,
              projectList: [],
              projectName,
            };
          }
          const numericKey = parseInt(key);
          if (!configurationMapping[projectId].projectList.includes(numericKey)) {
            configurationMapping[projectId].projectList.push(numericKey);
          }
        });
      });
    });

    // Now iterate over the mapping structure
    for (const [projectId, config] of Object.entries(configurationMapping)) {
      const params: any = {
        projectId,
        service: integration.service,
        projectName: (config as { projectName: string }).projectName,
        uid: integration.uid,
        url: (config as { url: string }).url,
        resourceId: (config as { resourceId: string }).resourceId,
        projectList: (config as { projectList: number[] }).projectList,
        tenantUid,
        ownerType: 'org',
        creatorUid: integrationToken.creatorUid,
        syncDuration,
      };

      // calling Utility function to fetch entity Web URL for navigation in UI
      const urlData = await fetchEntityUrl(params, authHeader);
      if (!urlData.success) {
        handleExternalIntegrationError(tenant, integration.uid, urlData.error, urlData.status);
        logger.error(`Failed to fetch entity url for integration ${integration.uid}:`, urlData.error);
        return;
      }
      params.webUrl = urlData.data;
      if (syncFlow) {
        params.syncedAt = integration.syncedAt;
      }
      // create a global map to save id mappings from external entitites to testfiesta entitities
      const idMappings: any = {};

      // fetch tags from the service
      const tagData = await service.fetchTags(params, authHeader);
      if (!tagData.success) {
        handleExternalIntegrationError(tenant, integration.uid, tagData.error, tagData.status);
        logger.error(`Failed to fetch tags for integration ${integration.uid}:`, tagData.error);
        return;
      }

      params.tags = tagData.data;
      // save tags to db and get their corrosponding testfiesta ids
      const tagMappings = await proccessTags(tenant, sharedDb, tagData.data, integration.service, tenantUid);
      idMappings.tagDataMap = tagMappings;

      // fetch case fields from the service
      const fields = await fetchEntityFields(params, authHeader);
      if (!fields.success) {
        handleExternalIntegrationError(tenant, integration.uid, fields.error, fields.status);
        logger.error(`Failed to fetch case fields for integration ${integration.uid}:`, fields.error);
        return;
      }
      params.caseFields = fields.data;
      timeStart = new Date().toISOString();

      // Loop over entities to fetch all relevant data
      for (const entityType of integration.entityTypes) {
        // initialize pagination
        const pagination = {
          startAt: 0,
          maxResults: maxApiLimit,
          hasMoreData: true,
          page: 1,
        };

        // loop until there is more data to fetch for the entity
        while (pagination.hasMoreData) {
          // fetch entities from the service
          const response = await service.fetchEntities(
            entityType,
            authHeader,
            params,
            pagination,
            tagMappings,
          );
            // if failed to fetch entities, set pagination to false and save error to integration
          if (!response.success) {
            pagination.hasMoreData = false;
            handleExternalIntegrationError(tenant, integration.uid, response.error, response.status);
            logger.error(`Failed to fetch ${entityType} for integration ${integration.uid} service ${integration.service} for project ${params.projectId} for tenant ${tenantUid}`, response.error);
            return;
          }
          params.hasMoreData = response.pagination.hasMoreData;

          // handle entities via switch
          switch (entityType) {
            case 'projects':
            case 'repositories':
              await processProject(tenant, response.data, params);
              break;
            case 'defects':
              await processDefects(tenant, response.data, params);
              break;
            case 'milestones':
              await processMilestones(tenant, response.data, params, idMappings);
              break;
            case 'plans':
              await processPlans(tenant, response.data, params, idMappings, authHeader, syncContext);
              break;
            case 'runs':
              await processRuns(tenant, response.data, params, idMappings, authHeader, syncContext);
              break;
            case 'templates':
              await processTemplates(tenant, params, response.data, tenantUid, idMappings);
              break;
            case 'customFields':
              await processCustomFields(tenant, params, response.data);
              break;
            case 'folders':
              await processFolders(tenant, params, response.data, idMappings, authHeader, tagMappings, syncContext);
              break;
            case 'sharedSteps':
              await processSharedSteps(tenant, params, response.data, tagMappings);
              break;
            case 'configurations':
              await processConfigurations(tenant, response.data, params, idMappings);
              break;
            default:
              logger.warn(`Unknown entity type: ${entityType}`);
          }
          if (syncContext.shouldHalt) {
            handleExternalIntegrationError(tenant, integration.uid, syncContext.errorMessage, syncContext.status);
            return;
          }
          pagination.startAt = response.pagination.startAt;
          pagination.hasMoreData = response.pagination.hasMoreData;
          pagination.page = response.pagination.page;
        }
      }
      if (syncContext.shouldHalt) {
        handleExternalIntegrationError(tenant, integration.uid, syncContext.errorMessage, syncContext.status);
        return;
      }
      if (idMappings.testRuns) {
        await calculateProgress(tenant, params, idMappings);
      }
    }
    await Integration.query()
      .where('uid', integration.uid)
      .patch({
        syncedAt: timeStart,
      });
    if (syncFlow) {
      logger.info(`Successfully synced integration ${integration.uid} for tenant: ${tenantUid}`);
    } else {
      logger.info(`Successfully fetched and processed entities for integration: ${integration.uid} for tenant: ${tenantUid}`);
    }
  } catch (error) {
    logger.error(`Error in integration service: ${error.message}`);
    throw error;
  }
}
async function fetchEntityFields(params: any, authHeader: any) {
  if (params.service !== 'testrail') {
    return {
      success: true,
      data: [],
    };
  }
  const testrailService = IntegrationServiceFactory.getService(params.service);
  const caseFields = await testrailService.fetchEntities('caseFields', authHeader, params, {
    startAt: 0,
    maxResults: SERVICE_CONFIGS[params.service].apiLimit,
    hasMoreData: true,
    page: 1,
  });
  return caseFields;
}

async function processSharedSteps(tenant: any, params: any, sharedSteps: any, tagMappings: any) {
  if (sharedSteps.length === 0) {
    return;
  }
  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;
  const fga = new FgaService(conn.fga);
  const externalIds = sharedSteps.map((step: any) => step.externalId);
  const SharedTestStep = tenantModels.SharedTestStep.bindKnex(tenantDb);
  const stepMap: { [key: string]: { [key: string]: number } } = {};
  try {
    for (const project of params.projectList) {
      // Get all existing steps in one query
      const existingSteps = await SharedTestStep.query()
        .whereIn('externalId', externalIds)
        .where({
          projectUid: project,
          source: 'testrail',
          active: true,
        })
        .whereRaw('("customFields"->>\'integrationUid\')::text = ?', [params.uid]);

      const existingStepsMap = existingSteps.reduce((acc, step) => {
        acc[step.externalId] = step;
        return acc;
      }, {});

      // Prepare data for bulk operations
      const stepsToCreate = [];
      const stepsToDeactivate = new Set();
      const trx = await tenantDb.transaction();
      for (const step of sharedSteps) {
        const existingStep = existingStepsMap[step.externalId];
        if (!existingStep) {
          const nextId = await getNextId(trx, SharedTestStep);
          stepsToCreate.push({
            ...step,
            uid: nextId,
            version: 1,
            projectUid: project,
            active: true,
            sharedTestStepRef: nextId,
            createdBy: params.tenantUid,
            customFields: {
              ...step.customFields,
              tags: [],
              integrationUid: params.uid,
            },
          });
        } else if (new Date(existingStep.externalUpdatedAt) < new Date(step.externalUpdatedAt)) {
          const nextId = await getNextId(trx, SharedTestStep);
          stepsToDeactivate.add(existingStep.uid);
          stepsToCreate.push({
            ...step,
            uid: nextId,
            version: existingStep.version + 1,
            projectUid: project,
            active: true,
            sharedTestStepRef: existingStep.sharedTestStepRef,
            createdBy: params.tenantUid,
            customFields: {
              ...step.customFields,
              tags: existingStep.customFields?.tags || [],
              integrationUid: params.uid,
            },
          });
        } else {
          // Add to mapping even if skipping update
          if (!stepMap[step.externalId]) {
            stepMap[step.externalId] = {};
          }
          stepMap[step.externalId][project] = existingStep.uid;
        }
      }
      // deactivate old versions
      if (stepsToDeactivate.size > 0) {
        await SharedTestStep.query(trx)
          .whereIn('uid', Array.from(stepsToDeactivate) as number[])
          .patch({ active: false });
      }

      // Bulk create new steps
      if (stepsToCreate.length > 0) {
        const createdSteps = await SharedTestStep.query(trx)
          .insert(stepsToCreate)
          .returning('*');

        createdSteps.forEach((step: any) => {
          if (!stepMap[step.externalId]) {
            stepMap[step.externalId] = {};
          }
          stepMap[step.externalId][project] = step.uid;
        });

        // Bulk create FGA permissions
        await Promise.all(createdSteps.map((step: any) => fga.create({
          objectType: 'step',
          objectId: step.uid,
          relation: 'owner',
          subjectType: params.ownerType,
          subjectId: params.tenantUid,
        })));
      }
      await trx.commit();
    }
    if (tagMappings) {
      tagMappings.sharedStepsMap = stepMap;
    }
    return stepMap;
  } catch (error) {
    logger.error(`Error processing shared steps for integration ${params.uid} service ${params.service} for tenant ${tenant.uid} for project ${params.projectId}:`, error);
    throw error;
  }
}

async function processCustomFields(tenant: any, params: any, customFields: any) {
  if (customFields.length === 0) {
    return;
  }
  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;
  const fga = new FgaService(conn.fga);
  const CustomField = tenantModels.CustomField.bindKnex(tenantDb);
  try {
    for (const project of params.projectList) {
      for (const field of customFields) {
        const existingField = await CustomField.query()
          .where({
            name: field.name,
            projectUid: project,
          })
          .first();

        // skip if field is already created from testrail
        if (!existingField || existingField.source !== 'testrail') {
          if (existingField && existingField.name === field.name) {
            field.name = `${field.name} (Testrail)`;
          }
          const newField = await CustomField.query().insert({
            name: field.name,
            type: field.dataType,
            options: field.options,
            source: 'testrail',
            externalId: String(field.externalId),
            projectUid: project,
            slug: kebabCase(`${field.name}-${project}`),
          });

          // Add owner permissions for custom field
          await fga.create({
            objectType: 'custom_field',
            objectId: newField.uid,
            relation: 'owner',
            subjectType: params.ownerType,
            subjectId: params.tenantUid,
          });
        }
      }
    }
  } catch (error) {
    logger.error(`Error processing custom fields for integration ${params.uid} service ${params.service} for tenant ${tenant.uid} for project ${params.projectId}:`, error);
    throw error;
  }
}

async function processConfigurations(tenant: any, configurations: any, params: any, idMappings: any) {
  if (configurations.length === 0) {
    return;
  }
  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;
  const externalIds = configurations.map((config: any) => config.externalId);
  const Tag = tenantModels.Tag.bindKnex(tenantDb);
  try {
    for (const project of params.projectList) {
      const trx = await tenantDb.transaction();
      // Get all existing configs in one query
      const existingConfigs = await Tag.query(trx)
        .where({
          projectUid: project,
          systemType: 'config',
          source: params.service,
          integrationUid: params.uid,
        })
        .whereIn('externalId', externalIds);

      const existingConfigsMap = existingConfigs.reduce((acc: any, config: any) => {
        acc[config.externalId] = config;
        return acc;
      }, {});

      // Prepare batch arrays
      const configsToCreate = [];
      const configsToUpdate = [];
      const configOptionsToProcess = [];

      // Sort configurations into create/update batches
      for (const config of configurations) {
        const existingConfig = existingConfigsMap[config.externalId];

        if (existingConfig) {
          configsToUpdate.push({
            name: config.name,
            uid: existingConfig.uid,
          });
          configOptionsToProcess.push({
            tagUid: existingConfig.uid,
            options: config.options,
          });
        } else {
          const newConfig = {
            name: config.name,
            projectUid: project,
            systemType: 'config',
            source: config.source,
            externalId: config.externalId,
            integrationUid: params.uid,
          };
          configsToCreate.push(newConfig);
        }
      }

      // Batch create new configs
      if (configsToCreate.length > 0) {
        const createdConfigs = await Tag.query(trx)
          .insert(configsToCreate)
          .returning(['uid', 'externalId']);

        // Add newly created configs to options processing queue
        createdConfigs.forEach((config: any) => {
          const originalConfig = configurations.find(
            (c: any) => c.externalId === config.externalId,
          );
          if (originalConfig) {
            configOptionsToProcess.push({
              tagUid: config.uid,
              options: originalConfig.options,
            });
          }
        });
      }

      // Batch update existing configs
      if (configsToUpdate.length > 0) {
        await Promise.all(
          configsToUpdate.map((config) => Tag.query(trx)
            .where('uid', config.uid)
            .patch({
              name: config.name,
            })),
        );
      }

      // Batch process config options
      if (configOptionsToProcess.length > 0) {
        const optionsMap = await Promise.all(
          configOptionsToProcess.map((item) => processConfigOptions(tenant, item.tagUid, item.options, params, trx, project)),
        );

        const configMap = optionsMap.reduce((acc: any, optionMap: any) => {
          if (!acc[project]) {
            acc[project] = {};
          }
          acc[project] = { ...acc[project], ...optionMap };

          return acc;
        }, {});
        if (!idMappings.configMap) {
          idMappings.configMap = configMap;
        } else {
          idMappings.configMap = { ...idMappings.configMap, ...configMap };
        }
      }

      await trx.commit();
    }
  } catch (error) {
    logger.error(`Error processing configurations for integration ${params.uid} service ${params.service} for tenant ${tenant.uid} for project ${params.projectId}:`, error);
    throw error;
  }
}

// Update processConfigOptions to accept transaction
async function processConfigOptions(tenant: any, configUid: any, options: any, params: any, trx: any, project: any) {
  if (options.length === 0) {
    return;
  }
  const configMap: any = {};
  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;
  const Tag = tenantModels.Tag.bindKnex(tenantDb);

  try {
    // Get all existing options in one query
    const existingOptions = await Tag.query(trx)
      .where({
        projectUid: project,
        parentUid: configUid,
        systemType: 'config.option',
        source: params.service,
        integrationUid: params.uid,
      });
    const existingOptionsMap = existingOptions.reduce((acc: any, option: any) => {
      acc[option.externalId] = option;
      return acc;
    }, {});

    const optionsToCreate = [];
    const optionsToUpdate = [];

    // Sort options into create/update batches
    options.forEach((option: any) => {
      const existingOption = existingOptionsMap[option.externalId];

      if (existingOption) {
        optionsToUpdate.push({
          uid: existingOption.uid,
          name: option.name,
        });
        configMap[option.externalId] = `${configUid}::${existingOption.uid}`;
      } else {
        optionsToCreate.push({
          name: option.name,
          projectUid: project,
          parentUid: configUid,
          systemType: 'config.option',
          source: params.service,
          externalId: option.externalId,
          integrationUid: params.uid,
        });
      }
    });

    // Batch create new options
    if (optionsToCreate.length > 0) {
      const createdOptions = await Tag.query(trx).insert(optionsToCreate);
      createdOptions.forEach((option: any) => {
        configMap[option.externalId] = `${configUid}::${option.uid}`;
      });
    }

    // Batch update existing options
    if (optionsToUpdate.length > 0) {
      await Promise.all(
        optionsToUpdate.map((option) => Tag.query(trx)
          .where('uid', option.uid)
          .patch({ name: option.name })),
      );
    }
    return configMap;
  } catch (error) {
    logger.error(`Error processing config options for integration ${params.uid} service ${params.service} for tenant ${tenant.uid} for project ${params.projectId}:`, error);
    throw error;
  }
}

// fetch entity url from the service
async function fetchEntityUrl(params: any, authHeader: any) {
  const { service } = params;
  if (service === 'jira') {
    try {
      const serverResponse = await axios.get(
        `https://api.atlassian.com/ex/jira/${params.resourceId}/rest/api/3/serverInfo`,
        authHeader,
      );
      return {
        success: true,
        data: serverResponse.data.baseUrl,
      };
    } catch (error) {
      logger.error(`Error fetching entity url for integration ${params.uid}:`, error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }
  return {
    success: true,
    data: '',
  };
}

async function processMilestones(tenant: any, milestones: any, params: any, idMappings: any) {
  if (milestones.length === 0) {
    return;
  }
  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;
  const TestMilestone = tenantModels.TestMilestone.bindKnex(tenantDb);
  const milestoneMap: { [key: string]: { [key: string]: number } } = {};
  const externalIds = milestones.map((milestone: any) => milestone.externalId);

  try {
    for (const project of params.projectList) {
      const trx = await tenantDb.transaction();
      // Get all existing milestones in one query
      const existingMilestones = await TestMilestone.query(trx)
        .where({
          systemType: 'milestone',
          projectUid: project,
          source: params.service,
          integrationUid: params.uid,
        })
        .whereIn('externalId', externalIds);

      const existingMilestonesMap = existingMilestones.reduce((acc, milestone) => {
        acc[milestone.externalId] = milestone;
        return acc;
      }, {});

      // Prepare batch arrays
      const milestonesToCreate = [];
      const milestonesToUpdate = [];
      const parentRelationsToProcess = [];

      // Sort milestones into create/update batches
      for (const milestone of milestones) {
        const existingMilestone = existingMilestonesMap[milestone.externalId];
        const { parentId } = milestone.customFields;
        const parentUid = parentId ? milestoneMap[parentId]?.[project] : null;

        // Prepare customFields
        const customFields = {
          ...(existingMilestone?.customFields || {}),
          status: existingMilestone?.customFields?.status,
          dueAt: milestone.customFields.dueAt,
          startDate: milestone.customFields.startDate,
          progress: existingMilestone?.customFields?.progress || 0,
          tagUids: existingMilestone?.customFields?.tagUids || [],
          link: milestone.customFields.link || `https://${params.url}/index.php?api/v2/get_milestone/${milestone.id}`,
          syncedAt: new Date().toISOString(),
        };

        if (existingMilestone) {
          milestonesToUpdate.push({
            uid: existingMilestone.uid,
            name: milestone.name,
            description: milestone.description,
            customFields,
            parentUid,
            archivedAt: milestone.archivedAt,
          });

          // Add to milestone mapping
          if (!milestoneMap[milestone.externalId]) {
            milestoneMap[milestone.externalId] = {};
          }
          milestoneMap[milestone.externalId][project] = existingMilestone.uid;
        } else {
          milestonesToCreate.push({
            name: milestone.name,
            description: milestone.description,
            projectUid: project,
            systemType: 'milestone',
            source: 'testrail',
            externalId: milestone.externalId,
            integrationUid: params.uid,
            customFields,
            parentUid,
            archivedAt: milestone.archivedAt,
          });
        }

        // Track parent-child relationships to process
        if (milestone.parentId) {
          parentRelationsToProcess.push({
            externalId: milestone.externalId,
            parentId: milestone.parentId,
          });
        }
      }

      // Batch create new milestones
      if (milestonesToCreate.length > 0) {
        const createdMilestones = await TestMilestone.query(trx)
          .insert(milestonesToCreate)
          .returning('*');

        // Update milestone mapping with newly created milestones
        createdMilestones.forEach((milestone: any) => {
          if (!milestoneMap[milestone.externalId]) {
            milestoneMap[milestone.externalId] = {};
          }
          milestoneMap[milestone.externalId][project] = milestone.uid;
        });
      }

      // Batch update existing milestones
      if (milestonesToUpdate.length > 0) {
        await Promise.all(
          milestonesToUpdate.map((milestone) => TestMilestone.query(trx)
            .where('uid', milestone.uid)
            .patch(milestone)),
        );
      }

      // Process parent-child relationships after all milestones are created/updated
      if (parentRelationsToProcess.length > 0) {
        await Promise.all(
          parentRelationsToProcess.map((relation) => {
            if (milestoneMap[relation.parentId]?.[project]) {
              return TestMilestone.query(trx)
                .where('uid', milestoneMap[relation.externalId][project])
                .patch({
                  parentUid: milestoneMap[relation.parentId][project],
                });
            }
            return Promise.resolve();
          }),
        );
      }

      await trx.commit();
    }
    idMappings.milestones = milestoneMap;
    return milestoneMap;
  } catch (error) {
    logger.error(`Error processing milestones for integration ${params.uid} service ${params.service} for tenant ${tenant.uid} for project ${params.projectId}:`, error);
    throw error;
  }
}

async function processPlans(tenant: any, plans: any, params: any, idMappings: any, authHeader: any, syncContext: SyncContext) {
  if (plans.length === 0) {
    return;
  }
  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;
  const TestPlan = tenantModels.TestPlan.bindKnex(tenantDb);

  try {
    for (const project of params.projectList) {
      const trx = await tenantDb.transaction();
      // Get all existing plans in one query
      const existingPlans = await TestPlan.query(trx)
        .select('*')
        .where({
          systemType: 'plan',
          projectUid: project,
          source: params.service,
          integrationUid: params.uid,
        })
        .whereIn('externalId', plans.map((p) => p.externalId));

      const existingPlansMap = existingPlans.reduce((acc, plan) => {
        acc[plan.externalId] = plan;
        return acc;
      }, {});

      // Prepare batch arrays
      const plansToCreate = [];
      const plansToUpdate = [];
      const milestoneRelationsToProcess = [];
      const runsToProcess = [];

      // Sort plans into create/update batches
      for (const plan of plans) {
        const existingPlan = existingPlansMap[plan.externalId];

        // Prepare customFields
        const customFields = {
          ...(existingPlan?.customFields || {}),
          status: existingPlan?.customFields?.status,
          priority: existingPlan?.customFields?.priority,
          progress: existingPlan?.customFields?.progress || 0,
          frequency: existingPlan?.customFields?.frequency || {},
          tagUids: existingPlan?.customFields?.tagUids || [],
          link: `https://${params.url}/index.php?api/v2/get_plan/${plan.id}`,
          syncedAt: new Date().toISOString(),
        };

        if (existingPlan) {
          plansToUpdate.push({
            uid: existingPlan.uid,
            name: plan.name,
            description: plan.description,
            customFields,
          });

          // Track milestone relations and runs for existing plans
          if (plan.customFields?.milestoneId && idMappings.milestones[plan.customFields.milestoneId]?.[project]) {
            milestoneRelationsToProcess.push({
              planUid: existingPlan.uid,
              milestoneUid: idMappings.milestones[plan.customFields.milestoneId][project],
            });
          }
          runsToProcess.push({
            planUid: existingPlan.uid,
            planExternalId: plan.externalId,
          });
        } else {
          const newPlan = {
            name: plan.name,
            description: plan.description,
            projectUid: project,
            systemType: 'plan',
            entityTypes: [],
            slug: slugify(`${plan.name}-${Date.now()}`),
            source: 'testrail',
            externalId: plan.externalId,
            integrationUid: params.uid,
            customFields,
          };
          plansToCreate.push(newPlan);

          // Track milestone relations and runs for new plans (will be processed after creation)
          if (plan.customFields?.milestoneId && idMappings.milestones[plan.customFields.milestoneId]?.[project]) {
            milestoneRelationsToProcess.push({
              planExternalId: plan.externalId, // Store externalId temporarily
              milestoneUid: idMappings.milestones[plan.customFields.milestoneId][project],
            });
          }
          runsToProcess.push({
            planExternalId: plan.externalId,
          });
        }
      }

      // Batch update existing plans
      if (plansToUpdate.length > 0) {
        await Promise.all(
          plansToUpdate.map((plan) => TestPlan.query(trx)
            .where('uid', plan.uid)
            .patch(plan)),
        );
      }

      // Batch create new plans
      let createdPlans = [];
      if (plansToCreate.length > 0) {
        createdPlans = await TestPlan.query(trx)
          .insert(plansToCreate)
          .returning('*');

        // Update milestone relations with actual UIDs
        createdPlans.forEach((plan) => {
          const relationsToUpdate = milestoneRelationsToProcess.filter(
            (rel) => rel.planExternalId === plan.externalId,
          );
          relationsToUpdate.forEach((rel) => {
            rel.planUid = plan.uid;
            delete rel.planExternalId;
          });

          // Update runs processing list with actual UIDs
          runsToProcess.forEach((run) => {
            if (run.planExternalId === plan.externalId) {
              run.planUid = plan.uid;
            }
          });
        });
      }

      // Batch process milestone relations
      if (milestoneRelationsToProcess.length > 0) {
        await Promise.all(
          milestoneRelationsToProcess.map(async (relation) => {
            if (!relation.planUid) return; // Skip if no planUid (shouldn't happen)

            const existingRelation = await tenantModels.TestMilestonePlan.query(trx)
              .where({
                milestoneUid: relation.milestoneUid,
                planUid: relation.planUid,
                deletedAt: null,
              })
              .first();

            if (!existingRelation) {
              await tenantModels.TestMilestonePlan.query(trx).insert({
                milestoneUid: relation.milestoneUid,
                planUid: relation.planUid,
              });
            }
          }),
        );
      }

      await trx.commit();

      // Process runs for all plans
      const service = IntegrationServiceFactory.getService(params.service);
      const pagination = {
        startAt: 0,
        maxResults: SERVICE_CONFIGS.testrail.apiLimit,
        page: 1,
        hasMoreData: true,
      };

      // Process runs sequentially to maintain rate limiting
      for (const run of runsToProcess) {
        if (!run.planUid) continue;
        const planParams = { ...params, planId: run.planExternalId };
        const runs = await service.fetchEntities('planEntries', authHeader, planParams, pagination);
        if (!runs.success) {
          logger.error(`Error fetching plan entries for integration ${params.uid} service ${params.service} for tenant ${tenant.uid} for project ${params.projectId}:`, runs.message);
          syncContext.errorMessage = runs.message;
          syncContext.status = runs.status;
          syncContext.shouldHalt = true;
          return;
        }
        await processRuns(tenant, runs.data, planParams, idMappings, authHeader, syncContext, project, run.planUid);
        if (syncContext.shouldHalt) {
          return;
        }
      }
    }
  } catch (error) {
    logger.error(`Error processing plans for integration ${params.uid} service ${params.service} for tenant ${tenant.uid} for project ${params.projectId}:`, error);
    throw error;
  }
}

async function processRuns(tenant: any, runs: any, params: any, idMappings: any, authHeader: any, syncContext: SyncContext, project: any = null, planUid: any = null) {
  if (runs.length === 0) {
    return;
  }
  if (project) {
    await processRun(tenant, runs, params, idMappings, authHeader, syncContext, project, planUid);
  } else {
    for (const project of params.projectList) {
      await processRun(tenant, runs, params, idMappings, authHeader, syncContext, project, planUid);
      if (syncContext.shouldHalt) {
        return;
      }
    }
  }
}

async function processRun(tenant: any, runs: any, params: any, idMappings: any, authHeader: any, syncContext: SyncContext, project: any, planUid: any = null) {
  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;
  const TestRun = tenantModels.TestRun.bindKnex(tenantDb);
  const TestMilestoneRun = tenantModels.TestMilestoneRun.bindKnex(tenantDb);
  const TestPlanRun = tenantModels.TestPlanRun.bindKnex(tenantDb);

  const trx = await tenantDb.transaction();
  try {
    // Get all existing runs in one query
    const externalIds = runs.map((run) => run.externalId);
    const existingTestRuns = await TestRun.query(trx)
      .where({
        projectUid: project,
        systemType: 'run',
        source: params.service,
        integrationUid: params.uid,
      })
      .whereIn('externalId', externalIds);

    // Create a map for faster lookups
    const existingRunsMap = existingTestRuns.reduce((acc, run) => {
      acc[run.externalId] = run;
      return acc;
    }, {});

    // Prepare batch arrays
    const runsToUpdate = [];
    const runsToCreate = [];
    const milestoneRelationsToProcess = [];
    const planRelationsToProcess = []; // Add array for plan relations
    const executionsToProcess = [];

    // Process each run
    for (const run of runs) {
      const existingTestRun = existingRunsMap[run.externalId];

      // Skip processing if run hasn't been updated
      if (existingTestRun && (existingTestRun.externalUpdatedAt && new Date(existingTestRun.externalUpdatedAt) >= new Date(run.externalUpdatedAt))) {
        if (!idMappings.testRuns) {
          idMappings.testRuns = [];
        }
        idMappings.testRuns.push(existingTestRun.uid);
        executionsToProcess.push({
          runUid: existingTestRun.uid,
          externalId: run.externalId,
        });
        continue;
      }
      const configs = run.customFields.configs.map((config: any) => idMappings.configMap[project][config]);
      // Prepare custom fields
      const customFields = {
        ...(existingTestRun?.customFields || {}),
        ...run.customFields,
        progress: existingTestRun?.customFields?.progress || 0,
        frequency: existingTestRun?.customFields?.frequency || {},
        tags: existingTestRun?.customFields?.tags || [],
        configs,
      };

      const commonData = {
        name: run.name,
        externalCreatedAt: run.externalCreatedAt,
        externalUpdatedAt: run.externalUpdatedAt,
        source: run.source,
        externalId: run.externalId,
        integrationUid: params.uid,
        projectUid: project,
        customFields,
        systemType: 'run',
      };

      if (existingTestRun) {
        runsToUpdate.push({
          uid: existingTestRun.uid,
          ...commonData,
          updatedAt: new Date().toISOString(),
        });
        executionsToProcess.push({
          runUid: existingTestRun.uid,
          externalId: run.externalId,
        });

        // Track milestone relation if needed
        if (run.customFields?.externalMilestoneId
          && idMappings.milestones[run.customFields.externalMilestoneId]?.[project]) {
          milestoneRelationsToProcess.push({
            runUid: existingTestRun.uid,
            milestoneUid: idMappings.milestones[run.customFields.externalMilestoneId][project],
          });
        }

        // Track plan relation if planUid is provided
        if (planUid) {
          planRelationsToProcess.push({
            runUid: existingTestRun.uid,
            planUid,
          });
        }
      } else {
        runsToCreate.push({
          ...commonData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });
      }
    }

    // Batch update existing runs
    if (runsToUpdate.length > 0) {
      await Promise.all(
        runsToUpdate.map((run) => TestRun.query(trx)
          .where('uid', run.uid)
          .patch(run)),
      );
    }

    // Batch create new runs
    let createdRuns = [];
    if (runsToCreate.length > 0) {
      createdRuns = await TestRun.query(trx)
        .insert(runsToCreate)
        .returning('*');

      createdRuns.forEach((run) => {
        executionsToProcess.push({
          runUid: run.uid,
          externalId: run.externalId,
        });

        // Track milestone relation if needed
        const originalRun = runs.find((r) => r.externalId === run.externalId);
        if (originalRun?.customFields?.externalMilestoneId
          && idMappings.milestones[originalRun.customFields.externalMilestoneId]?.[project]) {
          milestoneRelationsToProcess.push({
            runUid: run.uid,
            milestoneUid: idMappings.milestones[originalRun.customFields.externalMilestoneId][project],
          });
        }

        // Track plan relation if planUid is provided
        if (planUid) {
          planRelationsToProcess.push({
            runUid: run.uid,
            planUid,
          });
        }
      });
    }

    // Process milestone relations
    if (milestoneRelationsToProcess.length > 0) {
      // Get existing relations in one query
      const existingRelations = await TestMilestoneRun.query(trx)
        .whereIn('runUid', milestoneRelationsToProcess.map((rel) => rel.runUid))
        .whereNull('deletedAt');

      const existingRelationsMap = existingRelations.reduce((acc, rel) => {
        acc[`${rel.runUid}-${rel.milestoneUid}`] = true;
        return acc;
      }, {});

      // Filter out existing relations and create new ones
      const relationsToCreate = milestoneRelationsToProcess.filter(
        (rel) => !existingRelationsMap[`${rel.runUid}-${rel.milestoneUid}`],
      );

      if (relationsToCreate.length > 0) {
        await TestMilestoneRun.query(trx).insert(relationsToCreate);
      }
    }

    // Process plan relations
    if (planRelationsToProcess.length > 0) {
      // Get existing relations in one query
      const existingRelations = await TestPlanRun.query(trx)
        .whereIn('runUid', planRelationsToProcess.map((rel) => rel.runUid))
        .whereNull('deletedAt');

      const existingRelationsMap = existingRelations.reduce((acc, rel) => {
        acc[`${rel.runUid}-${rel.planUid}`] = true;
        return acc;
      }, {});

      // Filter out existing relations and create new ones
      const relationsToCreate = planRelationsToProcess.filter(
        (rel) => !existingRelationsMap[`${rel.runUid}-${rel.planUid}`],
      );

      if (relationsToCreate.length > 0) {
        await TestPlanRun.query(trx).insert(relationsToCreate);
      }
    }

    await trx.commit();

    // Initialize testRuns array in idMappings if needed
    if (!idMappings.testRuns) {
      idMappings.testRuns = [];
    }

    // Add all processed run UIDs to idMappings
    idMappings.testRuns.push(...executionsToProcess.map((exec) => exec.runUid));

    for (const run of executionsToProcess) {
      const executionParams = { ...params, runId: run.externalId };
      const executionMap = await processExecutions(tenant, run.runUid, project, executionParams, idMappings, authHeader, syncContext);
      if (syncContext.shouldHalt) {
        return;
      }
      await processTestResults(tenant, run.runUid, project, executionMap, executionParams, authHeader, idMappings.tagDataMap, syncContext);
      if (syncContext.shouldHalt) {
        return;
      }
    }
  } catch (error) {
    await trx.rollback();
    logger.error(`Error batch processing runs for TF project ${project} for integration ${params.uid} service ${params.service} for tenant ${tenant.uid} for project ${params.projectId}:`, error);
    throw error;
  }
}

async function processExecutions(tenant: any, testRunUid: any, project: any, params: any, idMappings: any, authHeader: any, syncContext: SyncContext) {
  const pagination = {
    startAt: 0,
    maxResults: SERVICE_CONFIGS[params.service].apiLimit,
    page: 1,
    hasMoreData: true,
  };
  const service = IntegrationServiceFactory.getService(params.service);
  const executionMap: { [key: string]: { [key: string]: number } } = {};
  try {
    while (pagination.hasMoreData) {
      const response = await service.fetchEntities(
        'testExecutions',
        authHeader,
        params,
        pagination,
        idMappings.tagDataMap,
      );

      if (!response.success) {
        logger.error(`Error fetching executions for run ${testRunUid}:`, response.message);
        syncContext.errorMessage = response.message;
        syncContext.status = response.status;
        syncContext.shouldHalt = true;
        return;
      }

      const executions = response.data;
      const externalIds = executions.map((execution: any) => execution.externalId);
      if (externalIds.length === 0) {
        return executionMap;
      }
      const conn = await tenantManager.loadTenant(tenant);
      const tenantDb = conn.db;
      const TestExecution = tenantModels.TestExecution.bindKnex(tenantDb);
      const TestCase = tenantModels.TestCase.bindKnex(tenantDb);
      const trx = await tenantDb.transaction();
      try {
        // Get all existing executions in one query
        const existingExecutions = await TestExecution.query(trx)
          .whereIn('externalId', externalIds)
          .where({
            source: params.service,
            testRunUid,
            projectUid: project,
          });

        const existingExecutionsMap = existingExecutions.reduce((acc, execution) => {
          acc[execution.externalId] = execution;
          return acc;
        }, {});

        // Prepare data for bulk operations
        const executionsToCreate = [];
        const executionsToUpdate = [];
        for (const execution of executions) {
          const { caseId, ...executionData } = execution;
          let testCase = idMappings.testCases[caseId]?.[project];

          if (!testCase) {
            // find the case in the tenant
            testCase = await TestCase.query(trx).where({
              externalId: caseId,
              projectUid: project,
              source: params.service,
              active: true,
            }).first();
            if (!testCase) {
              logger.warn(`Test case not found for caseId: ${caseId} in project: ${project}`);
              continue;
            }
            testCase = {
              uid: testCase.uid,
              ref: testCase.testCaseRef,
            };
          }

          const existingExecution = existingExecutionsMap[execution.externalId];
          const existingCustomFields = existingExecution?.customFields || {};

          const customFields = {
            ...existingCustomFields,
            tags: existingCustomFields.tags || [],
            integrationUid: params.uid,
          };

          const commonData = {
            ...executionData,
            testCaseUid: testCase.uid,
            testCaseRef: testCase.ref,
            testRunUid,
            projectUid: project,
            syncedAt: new Date().toISOString(),
            customFields,
            status: existingExecution?.status || executionData.status,
            priority: existingExecution?.priority || executionData.priority,
            assignedTo: existingExecution?.assignedTo || executionData.assignedTo,
            lastAssignedAt: existingExecution?.lastAssignedAt || executionData.lastAssignedAt,
          };

          if (existingExecution) {
            executionsToUpdate.push({
              uid: existingExecution.uid,
              ...commonData,
              updatedAt: new Date().toISOString(),
            });
            if (!executionMap[project]) {
              executionMap[project] = {};
            }
            executionMap[project][execution.externalId] = existingExecution.uid;
          } else {
            executionsToCreate.push({
              ...commonData,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            });
          }
        }

        // Bulk update existing executions
        if (executionsToUpdate.length > 0) {
          await Promise.all(
            executionsToUpdate.map((execution) => TestExecution.query(trx)
              .where('uid', execution.uid)
              .patch(execution)),
          );
        }

        // Bulk create new executions
        if (executionsToCreate.length > 0) {
          const createdExecutions = await TestExecution.query(trx)
            .insertGraph(executionsToCreate)
            .returning('*');
          createdExecutions.forEach((e) => {
            if (!executionMap[project]) {
              executionMap[project] = {};
            }
            executionMap[project][e.externalId] = e.uid;
          });
        }
        await trx.commit();
        return executionMap;
      } catch (error) {
        await trx.rollback();
        logger.error(`Error bulk processing executions for test run ${testRunUid}:`, error);
      }

      pagination.startAt = response.pagination.startAt;
      pagination.hasMoreData = response.pagination.hasMoreData;
      pagination.page = response.pagination.page;
    }
  } catch (error) {
    logger.error(`Error processing executions for test run ${testRunUid}:`, error);
    throw error;
  }
}

async function processTestResults(tenant: any, runUid: any, project: any, executionMap: any, params: any, authHeader: any, tagMappings: any, syncContext: SyncContext) {
  const pagination = {
    startAt: 0,
    maxResults: SERVICE_CONFIGS[params.service].apiLimit,
    page: 1,
    hasMoreData: true,
  };
  try {
    const service = IntegrationServiceFactory.getService(params.service);
    while (pagination.hasMoreData) {
      // Fetch results using service
      const response = await service.fetchEntities(
        'testResultsByRun',
        authHeader,
        params,
        pagination,
        tagMappings,
      );

      if (!response.success) {
        logger.error(`Error fetching results for execution ${runUid}:`, response.message);
        syncContext.errorMessage = response.message;
        syncContext.status = response.status;
        syncContext.shouldHalt = true;
        return;
      }

      const results = response.data;
      if (results.length === 0) {
        return;
      }
      const conn = await tenantManager.loadTenant(tenant);
      const tenantDb = conn.db;
      const TestResult = tenantModels.TestResult.bindKnex(tenantDb);
      const trx = await tenantDb.transaction();
      try {
        // Get all existing results in one query
        const existingResults = await TestResult.query(trx)
          .select('testResults.*')
          .join('testExecutions', 'testResults.testExecutionUid', '=', 'testExecutions.uid')
          .where({
            'testResults.source': params.service,
            'testExecutions.testRunUid': runUid,
          })
          .whereIn('testResults.externalId', results.map((r) => r.externalId));

        // Create a map for faster lookups
        const existingResultsMap = existingResults.reduce((acc, result) => {
          acc[result.externalId] = result;
          return acc;
        }, {});

        // Prepare batch arrays
        const resultsToCreate = [];

        // Sort results into create/update batches
        for (const result of results) {
          const existingResult = existingResultsMap[result.externalId];
          const runData = {
            ...result,
            testExecutionUid: executionMap[project][result.customFields.testId],
            syncedAt: new Date().toISOString(),
          };

          if (!existingResult) {
            resultsToCreate.push({
              ...runData,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            });
          }
        }

        // Batch create new results
        if (resultsToCreate.length > 0) {
          await TestResult.query(trx).insert(resultsToCreate);
        }
        await trx.commit();
      } catch (error) {
        await trx.rollback();
        logger.error(`Error batch processing test results for run ${runUid}:${params.runId}`, error);
      }

      pagination.startAt = response.pagination.startAt;
      pagination.hasMoreData = response.pagination.hasMoreData;
      pagination.page = response.pagination.page;
    }
  } catch (error) {
    logger.error(`Error processing test results for run ${runUid}:${params.runId}`, error);
    throw error;
  }
}

async function processFolders(tenant: any, params: any, suites: any, idMappings: any, authHeader: any, tagMappings: any, syncContext: SyncContext) {
  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;
  const entityTypes: string[] = ['cases'];
  const Tag = tenantModels.Tag.bindKnex(tenantDb);
  try {
    for (const project of params.projectList) {
      for (const suite of suites) {
        const trx = await tenantDb.transaction();
        const parentFolder = await Tag.query(trx)
          .where({
            projectUid: project,
            systemType: 'folder',
          })
          .whereNull('parentUid')
          .orderBy('uid', 'asc')
          .first();

        const existingFolder = await tenantModels.Tag.query(trx)
          .where({
            projectUid: project,
            parentUid: parentFolder.uid,
            systemType: 'folder',
            externalId: suite.externalId,
            source: params.service,
            integrationUid: params.uid,
          })
          .first();
        let folderUid;
        if (existingFolder) {
          await Tag.query(trx)
            .where('uid', existingFolder.uid)
            .patch({
              ...suite,
              projectUid: project,
              parentUid: parentFolder.uid,
              entityTypes: trx.raw('?::text[]', [entityTypes]),
            });
          folderUid = existingFolder.uid;
        } else {
          const newFolder = await Tag.query(trx).insert({
            ...suite,
            projectUid: project,
            parentUid: parentFolder.uid,
            entityTypes: trx.raw('?::text[]', [entityTypes]),
          });
          folderUid = newFolder.uid;
        }
        await trx.commit();
        const sectionFolderMap = await processSubFolders(tenant, params, suite.externalId, folderUid, project, authHeader, syncContext);
        if (syncContext.shouldHalt) {
          return;
        }
        await processCases(tenant, suite.externalId, folderUid, project, sectionFolderMap, params, idMappings, authHeader, tagMappings, syncContext);
        if (syncContext.shouldHalt) {
          return;
        }
      }
    }
  } catch (error) {
    logger.error(`Error processing suites for integration ${params.uid} service ${params.service} for tenant ${tenant.uid} for project ${params.projectId}:`, error);
    throw error;
  }
}

async function processSubFolders(tenant: any, params: any, suiteId: any, folderUid: any, project: any, authHeader: any, syncContext: SyncContext) {
  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;
  const sectionFolderMap: { [key: string]: { [key: number]: number } } = {};
  const pagination = {
    startAt: 0,
    maxResults: SERVICE_CONFIGS.testrail.apiLimit,
    page: 1,
    hasMoreData: true,
  };
  const Tag = tenantModels.Tag.bindKnex(tenantDb);
  const entityTypes = ['cases'];
  params.suiteId = suiteId;
  const testrailService = IntegrationServiceFactory.getService(params.service);
  try {
    while (pagination.hasMoreData) {
      const response = await testrailService.fetchEntities(
        'subFolders',
        authHeader,
        params,
        pagination,
      );
      if (!response.success) {
        logger.error(`Error fetching subfolders for integration ${params.uid} service ${params.service} for tenant ${tenant.uid} for project ${params.projectId}:`, response.message);
        syncContext.errorMessage = response.message;
        syncContext.status = response.status;
        syncContext.shouldHalt = true;
        return;
      }
      const sections = response.data;
      for (const section of sections) {
        const trx = await tenantDb.transaction();
        const parentFolder = sectionFolderMap[section.customFields.parentId]?.[project] || folderUid;
        const sectionFolder = await Tag.query(trx)
          .where({
            projectUid: project,
            parentUid: parentFolder,
            systemType: 'folder',
            externalId: section.externalId,
            source: params.service,
            integrationUid: params.uid,
          })
          .first();
        if (sectionFolder) {
          await Tag.query(trx)
            .where('uid', sectionFolder.uid)
            .patch({
              ...section,
              projectUid: project,
              parentUid: parentFolder,
              entityTypes: trx.raw('?::text[]', [entityTypes]),
            });

          // Create nested structure: suiteId -> projectUid -> folderId
          if (!sectionFolderMap[section.externalId]) {
            sectionFolderMap[section.externalId] = {};
          }
          sectionFolderMap[section.externalId][project] = sectionFolder.uid;
        } else {
          const newFolder = await Tag.query(trx).insert({
            ...section,
            projectUid: project,
            parentUid: parentFolder,
            entityTypes: trx.raw('?::text[]', [entityTypes]),
          });
            // Create nested structure for new folders
          if (!sectionFolderMap[section.externalId]) {
            sectionFolderMap[section.externalId] = {};
          }
          sectionFolderMap[section.externalId][project] = newFolder.uid;
        }
        await trx.commit();
      }
      pagination.startAt = response.pagination.startAt;
      pagination.hasMoreData = response.pagination.hasMoreData;
      pagination.page = response.pagination.page;
    }
    return sectionFolderMap;
  } catch (error) {
    logger.error(`Error processing sections for integration ${params.uid} service ${params.service} for tenant ${tenant.uid} for project ${params.projectId}:`, error);
    throw error;
  }
}

async function processCases(tenant: any, suiteId: any, folderUid: any, project: any, subfolderMap: any, params: any, idMappings: any, authHeader: any, tagMappings: any, syncContext: SyncContext) {
  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;
  const pagination = {
    startAt: 0,
    maxResults: SERVICE_CONFIGS.testrail.apiLimit,
    page: 1,
    hasMoreData: true,
  };
  params.suiteId = suiteId;

  if (!idMappings.testCases) {
    idMappings.testCases = {};
  }

  const testrailService = IntegrationServiceFactory.getService(params.service);
  const TestCase = tenantModels.TestCase.bindKnex(tenantDb);

  try {
    while (pagination.hasMoreData) {
      const response = await testrailService.fetchEntities(
        'cases',
        authHeader,
        params,
        pagination,
        tagMappings,
      );

      if (!response.success) {
        logger.error(`Error fetching cases for integration ${params.uid} service ${params.service} for tenant ${tenant.uid} for project ${params.projectId}:`, response.message);
        syncContext.errorMessage = response.message;
        syncContext.status = response.status;
        syncContext.shouldHalt = true;
        return;
      }

      const cases = response.data;
      const trx = await tenantDb.transaction();
      // Get all existing cases in one query
      const externalIds = cases.map((c) => c.externalId);
      const existingTestCases = await TestCase.query(trx)
        .where({
          projectUid: project,
          source: params.service,
          active: true,
        })
        .whereIn('externalId', externalIds)
        .whereRaw('("customFields"->>\'integrationUid\')::text = ?', [params.uid])
        .whereNull('deletedAt');

      // Create lookup map
      const existingCasesMap = existingTestCases.reduce((acc, testCase) => {
        acc[testCase.externalId] = testCase;
        return acc;
      }, {});

      // Prepare batch arrays
      const casesToCreate = [];
      const casesToUpdate = [];
      const tagsToProcess = [];
      const folderTagsToProcess = [];
      // Process each case
      for (const testCase of cases) {
        const existingTestCase = existingCasesMap[testCase.externalId];
        let caseUid;
        let caseRef;
        let version = 1;
        // Track folder assignment
        const sectionFolder = subfolderMap[String(testCase.customFields.raw.section_id)]?.[project];
        const folderTagUid = sectionFolder || folderUid;
        if (existingTestCase) {
          // Skip if no update needed
          if (new Date(existingTestCase.externalUpdatedAt) >= new Date(testCase.externalUpdatedAt)) {
            if (!idMappings.testCases[testCase.externalId]) {
              idMappings.testCases[testCase.externalId] = {};
            }
            idMappings.testCases[testCase.externalId][project] = {
              uid: existingTestCase.uid,
              ref: existingTestCase.testCaseRef,
            };
            continue;
          }

          // Mark previous version as inactive
          casesToUpdate.push({
            testCaseUid: existingTestCase.uid,
            active: false,
          });

          caseUid = await getNextId(trx, tenantModels.TestCase);
          caseRef = existingTestCase.testCaseRef;
          version = existingTestCase.version + 1;
        } else {
          caseUid = await getNextId(trx, tenantModels.TestCase);
          caseRef = caseUid;
        }
        const steps = testCase.steps.map((step:any) => {
          if (step.sharedStepUid) {
            return {
              ...step,
              sharedStepUid: tagMappings.sharedStepsMap[step.sharedStepUid][project],
            };
          }
          return step;
        });
        // Prepare new case version
        casesToCreate.push({
          ...testCase,
          steps,
          uid: caseUid,
          testCaseRef: caseRef,
          version,
          projectUid: project,
          testTemplateUid: idMappings.templateMappings[testCase.testTemplateUid]?.[project],
          active: true,
          parentUid: folderTagUid,
          customFields: {
            ...testCase.customFields,
            integrationUid: params.uid,
          },
          syncedAt: new Date().toISOString(),
        });

        // Track tags to process
        if (testCase.customFields.tags?.length > 0) {
          tagsToProcess.push({
            caseRef,
            version,
            tags: testCase.customFields.tags,
          });
        }

        if (folderTagUid) {
          folderTagsToProcess.push({
            caseRef,
            version,
            folderTagUid,
          });
        }

        // Update mappings
        if (!idMappings.testCases[testCase.externalId]) {
          idMappings.testCases[testCase.externalId] = {};
        }
        idMappings.testCases[testCase.externalId][project] = {
          uid: caseUid,
          ref: caseRef,
        };
      }

      // Batch update existing cases (mark inactive)
      if (casesToUpdate.length > 0) {
        await Promise.all(
          casesToUpdate.map((caseToUpdate) => TestCase.query(trx)
            .where('uid', caseToUpdate.testCaseUid)
            .patch({ active: false })),
        );
      }

      // Batch create new cases
      if (casesToCreate.length > 0) {
        await TestCase.query(trx).insert(casesToCreate);
      }

      // Process tags in batch
      if (tagsToProcess.length > 0) {
        await Promise.all(
          tagsToProcess.map(async ({ caseRef, version, tags }) => {
            const existingTags = await tenantModels.TestCaseTag.query(trx)
              .where({
                testCaseRef: caseRef,
                deletedAt: null,
              })
              .select('tagUid', 'testCaseAddedVersion');

            const existingTagIds = new Set(existingTags.map((t) => t.tagUid));
            const tagsToCreate = [];
            const tagsToUpdate = [];

            for (const tagId of tags) {
              if (!existingTagIds.has(tagId)) {
                tagsToCreate.push({
                  tagUid: tagId,
                  testCaseRef: caseRef,
                  testCaseAddedVersion: trx.raw('?::integer[]', [[version]]),
                });
              } else {
                const existingTag = existingTags.find((t) => t.tagUid === tagId);
                if (!existingTag.testCaseAddedVersion.includes(version)) {
                  tagsToUpdate.push({
                    tagUid: tagId,
                    testCaseRef: caseRef,
                    versions: [...existingTag.testCaseAddedVersion, version],
                  });
                }
              }
            }

            if (tagsToCreate.length > 0) {
              await tenantModels.TestCaseTag.query(trx).insert(tagsToCreate);
            }

            if (tagsToUpdate.length > 0) {
              await Promise.all(
                tagsToUpdate.map((tag) => tenantModels.TestCaseTag.query(trx)
                  .where({ tagUid: tag.tagUid, testCaseRef: tag.testCaseRef })
                  .patch({
                    testCaseAddedVersion: trx.raw('?::integer[]', [tag.versions]),
                  })),
              );
            }
          }),
        );
      }

      // Process folder tags in batch
      if (folderTagsToProcess.length > 0) {
        const existingFolderTags = await tenantModels.TestCaseTag.query(trx)
          .whereIn('testCaseRef', folderTagsToProcess.map((f) => f.caseRef))
          .whereIn('tagUid', folderTagsToProcess.map((f) => f.folderTagUid))
          .whereNull('deletedAt');

        const existingFolderTagsMap = existingFolderTags.reduce((acc, tag) => {
          acc[`${tag.testCaseRef}-${tag.tagUid}`] = tag;
          return acc;
        }, {});

        const folderTagsToCreate = [];
        const folderTagsToUpdate = [];

        folderTagsToProcess.forEach(({ caseRef, version, folderTagUid }) => {
          const existingTag = existingFolderTagsMap[`${caseRef}-${folderTagUid}`];

          if (!existingTag) {
            folderTagsToCreate.push({
              tagUid: folderTagUid,
              testCaseRef: caseRef,
              testCaseAddedVersion: trx.raw('?::integer[]', [[version]]),
            });
          } else if (!existingTag.testCaseAddedVersion.includes(version)) {
            folderTagsToUpdate.push({
              uid: existingTag.uid,
              versions: [...existingTag.testCaseAddedVersion, version],
            });
          }
        });

        if (folderTagsToCreate.length > 0) {
          await tenantModels.TestCaseTag.query(trx).insert(folderTagsToCreate);
        }

        if (folderTagsToUpdate.length > 0) {
          await Promise.all(
            folderTagsToUpdate.map((tag) => tenantModels.TestCaseTag.query(trx)
              .where('uid', tag.uid)
              .patch({
                testCaseAddedVersion: trx.raw('?::integer[]', [tag.versions]),
              })),
          );
        }
      }

      await trx.commit();
      pagination.startAt = response.pagination.startAt;
      pagination.hasMoreData = response.pagination.hasMoreData;
      pagination.page = response.pagination.page;
    }
  } catch (error) {
    logger.error(`Error handling test cases for integration ${params.uid} service ${params.service} for tenant ${tenant.uid} for project ${params.projectId}:`, error);
    throw error;
  }
}

async function processTemplates(tenant: any, params: any, templates: any, tenantUid: string, idMappings: any) {
  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;
  const fga = new FgaService(conn.fga);
  const templateMap: { [key: string]: { [key: string]: number } } = {};
  const TestTemplate = tenantModels.TestTemplate.bindKnex(tenantDb);
  try {
    for (const project of params.projectList) {
      for (const template of templates) {
        // Check if template exists by name for this project
        const trx = await tenantDb.transaction();
        const existingTemplate = await TestTemplate.query(trx)
          .where({
            projectUid: project,
            name: template.name,
          })
          .first();

        // Simplify customFields to be a direct array
        const templateFields = template.customFields
          .filter((field: any) => field.dataType !== undefined)
          .map((field: any) => ({
            id: field.id,
            name: field.name,
            dataType: field.dataType,
            options: field.options || [],
            defaultValue: field.defaultValue || '',
            ...(field.dataType === 'date' && { default_date: field.default_date }),
          }));

        if (existingTemplate) {
          await tenantModels.TestTemplate.query(trx)
            .where('uid', existingTemplate.uid)
            .patch({
              customFields: {
                ...existingTemplate.customFields,
                templateFields,
              },
              createdBy: params.creatorUid,
              updatedAt: new Date().toISOString(),
            });

          if (!templateMap[template.id]) {
            templateMap[template.id] = {};
          }
          templateMap[template.id][project] = existingTemplate.uid;
        } else {
          const newTemplate = await tenantModels.TestTemplate.query(trx).insert({
            name: template.name,
            customFields: {
              templateFields,
            },
            projectUid: project,
            createdBy: params.creatorUid,
          });

          // Add owner permissions for template
          await fga.create({
            objectType: 'template',
            objectId: newTemplate.uid,
            relation: 'owner',
            subjectType: params.ownerType,
            subjectId: params.tenantUid,
          });

          if (!templateMap[template.id]) {
            templateMap[template.id] = {};
          }
          templateMap[template.id][project] = newTemplate.uid;
        }
        await trx.commit();
      }
    }
    idMappings.templateMappings = templateMap;
  } catch (error) {
    logger.error(`Error processing templates for integration ${params.uid} service ${params.service} for tenant ${tenant.uid} for project ${params.projectId}:`, error);
    throw error;
  }
}

async function proccessTags(tenant: any, sharedDb: Knex, tags: any, service: any, tenantUid: any) {
  switch (service) {
    case 'testrail':
      return mapPreferences(tenant, sharedDb, tenantUid, tags);
    default:
      return syncTags(tenant, tags, service);
  }
}

async function processProject(tenant: any, project: any, params: any) {
  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;
  const ExternalEntity = tenantModels.ExternalEntity.bindKnex(tenantDb);
  try {
    const existingProject = await ExternalEntity.query()
      .where({
        entityUid: params.uid as string,
        entityType: 'integration',
        sourceId: project.sourceId as string,
      })
      .whereRaw('("customFields"->>\'sourceType\')::text = ?', ['project'])
      .whereNull('deletedAt')
      .first();

    if (existingProject) {
      await ExternalEntity.query()
        .where('uid', existingProject.uid)
        .patch({
          ...project,
          entityUid: String(params.uid),
          updatedAt: new Date().toISOString(),
        });
    } else {
      await ExternalEntity.query().insert({
        ...project,
        entityType: 'integration',
        entityUid: String(params.uid),
        source: String(params.service),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
    }
  } catch (error) {
    logger.error(`Error processing project ${params.projectId} for integration ${params.uid} service ${params.service} for tenant ${tenant.uid}:`, error);
    throw error;
  }
}

async function processDefects(tenant: any, defects: any, params: any) {
  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;
  const Defect = tenantModels.Defect.bindKnex(tenantDb);
  if (!params.defectIds) {
    params.defectIds = [];
  }
  try {
    for (const defect of defects) {
      if (!params.defectIds.includes(defect.externalId)) {
        params.defectIds.push(defect.externalId);
      }
      const existingDefect = await Defect.query()
        .where({
          integrationSourceUid: String(params.uid),
          externalId: defect.externalId,
        })
        .whereNull('deletedAt')
        .first();
      if (existingDefect) {
        await Defect.query()
          .where('uid', existingDefect.uid)
          .patch({
            ...defect,
            updatedAt: new Date().toISOString(),
            projectUids: tenantDb.raw('?::integer[]', [params.projectList]),
          });
      } else {
        await Defect.query().insert({
          ...defect,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          projectUids: tenantDb.raw('?::integer[]', [params.projectList]),
        });
      }
    }
  } catch (error) {
    logger.error(`Error processing defects for integration ${params.uid} service ${params.service} for tenant ${tenant.uid} project ${params.projectId}:`, error);
    throw error;
  }
}

async function syncTags(tenant: any, items: any[], source: string) {
  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;
  const ExternalEntity = tenantModels.ExternalEntity.bindKnex(tenantDb);
  const Tag = tenantModels.Tag.bindKnex(tenantDb);
  const existingMappings = await ExternalEntity.query()
    .where({
      source,
      entityType: 'tag',
      deletedAt: null,
    })
    .select(['sourceId', 'entityUid', 'customFields', 'source']);

  // Create organized mapping object by name
  const tagMappings: any = {}; // First process existing mappings
  existingMappings.forEach((mapping) => {
    const { type } = (mapping.customFields as any);
    if (!tagMappings[type]) {
      tagMappings[type] = {};
    }
    tagMappings[type][mapping.sourceId] = {
      tagUid: mapping.entityUid,
      name: (mapping.customFields as any).name,
      color: (mapping.customFields as any).color,
    };
  });

  for (const item of items) {
    // Check if tag exists by name, system type and entity type
    const existingTag = await Tag.query()
      .where({
        systemType: item.type,
        deletedAt: null,
      })
      .whereRaw('LOWER(name) = LOWER(?)', [item.name])
      .first();

    let tagUid: number;

    if (!existingTag) {
      const tag = await Tag.query()
        .insert({
          name: item.name,
          description: item.description,
          slug: kebabCase(item.name),
          systemType: item.type,
          entityTypes: ['defects'],
          source: item.sourceService,
          externalId: item.id,
          customFields: {
            ...item.customFields,
            color: item.color,
          },
        })
        .returning('*');

      tagUid = tag.uid;
      // Add to mapping object by name
      if (!tagMappings[item.type]) {
        tagMappings[item.type] = {};
      }
      tagMappings[item.type][item.id] = {
        tagUid: tag.uid,
        name: item.name,
        color: item.color,
      };
    } else {
      if (!existingTag.entityTypes.includes('defects')) {
        await Tag.query()
          .where('uid', existingTag.uid)
          .patch({
            entityTypes: Tag.knex().raw('array_append("entityTypes", ?)', ['defects']),
            source: item.sourceService,
            externalId: item.id,
            customFields: {
              ...existingTag.customFields,
              color: item.color,
              ...item.customFields,
            },
          });
      } else {
        await Tag.query()
          .where('uid', existingTag.uid)
          .patch({
            source: item.sourceService,
            externalId: item.id,
            customFields: {
              ...existingTag.customFields,
              color: item.color,
              ...item.customFields,
            },
          });
      }
      if (!tagMappings[item.type]) {
        tagMappings[item.type] = {};
      }
      tagMappings[item.type][item.id] = {
        tagUid: existingTag.uid,
        name: existingTag.name,
        color: existingTag.customFields ? (existingTag.customFields as any).color : '#000000',
      };
      tagUid = existingTag.uid;
    }

    // Check if external entity mapping exists
    const existingEE = await ExternalEntity.query()
      .where({
        entityUid: String(tagUid),
        source,
        sourceId: item.id,
        entityType: 'tag',
      })
      .first();

    if (!existingEE) {
      await ExternalEntity.query()
        .insert({
          source,
          sourceId: item.id,
          entityType: 'tag',
          entityUid: String(tagUid),
          customFields: {
            ...item.customFields,
            type: item.type,
            name: item.name,
            color: item.color,
          },
        });
    } else {
      await ExternalEntity.query()
        .where('uid', existingEE.uid)
        .patch({
          customFields: {
            ...existingEE.customFields,
            ...item.customFields,
          },
        });
    }
  }
  return tagMappings;
}

async function mapPreferences(tenant: any, sharedDb: Knex, tenantUid: string, tagData: any) {
  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;
  const Org = sharedModels.Org.bindKnex(sharedDb);
  const ExternalEntity = tenantModels.ExternalEntity.bindKnex(tenantDb);
  const Tag = tenantModels.Tag.bindKnex(tenantDb);
  try {
    // Get tenant preferences
    const tenant = await Org.query()
      .where('uid', tenantUid)
      .select(['preferences'])
      .first();

    if (!tenant) {
      logger.error(`Tenant not found for tenantUid: ${tenantUid}`);
      throw new Error('Tenant not found');
    }

    const { preferences } = tenant;
    const statusMappings = {};
    const priorityMappings = {};
    const tagMappings = { case: {} };

    // Process Status Mappings
    for (const status of tagData.status) {
      const normalizedName = status.name.toLowerCase().replace(/\s+/g, '');

      // Find matching status in preferences
      const matchingStatus = preferences.statusColors.find((s) => s.name.toLowerCase().replace(/\s+/g, '') === normalizedName
        && (s.entityType === 'testCase' || s.entityType === 'testRun'));

      if (matchingStatus) {
        statusMappings[status.id] = matchingStatus.id;
      } else {
        // Create new status in preferences
        const newStatusId = Math.max(...preferences.statusColors.map((s) => s.id)) + 1;
        const newStatus = {
          id: newStatusId,
          color: status.color,
          entityType: 'testCase', // Default to testCase
          name: status.name,
          isDefault: false,
          isCompleted: false,
          isSuccess: false,
          isFailure: false,
        };

        preferences.statusColors.push(newStatus);
        statusMappings[status.id] = newStatusId;
      }
    }

    // Process Priority Mappings
    for (const priority of tagData.priority) {
      const normalizedName = priority.name.toLowerCase().replace(/\s+/g, '');

      // Find matching priority in preferences
      const matchingPriority = preferences.priorityColors.find((p) => p.name.toLowerCase().replace(/\s+/g, '') === normalizedName
        && (p.entityType === 'testCase' || p.entityType === 'testRun'));

      if (matchingPriority) {
        priorityMappings[priority.id] = matchingPriority.id;
      } else {
        // Create new priority in preferences
        const newPriorityId = Math.max(...preferences.priorityColors.map((p) => p.id)) + 1;
        const newPriority = {
          id: newPriorityId,
          color: priority.color,
          entityType: 'testCase', // Default to testCase
          name: priority.name,
          isDefault: false,
        };

        preferences.priorityColors.push(newPriority);
        priorityMappings[priority.id] = newPriorityId;
      }
    }

    // Process Case Tags
    for (const tag of tagData.tags) {
      // Check if tag exists by name and entity type
      const existingTag = await Tag.query()
        .where({
          systemType: 'tag',
          deletedAt: null,
        })
        .whereRaw('LOWER(name) = LOWER(?)', [tag.name])
        .whereRaw('\'cases\' = ANY("entityTypes")')
        .first();

      let tagUid: number;

      if (!existingTag) {
        // Create new tag
        const newTag = await Tag.query()
          .insert({
            name: tag.name,
            slug: kebabCase(tag.name),
            systemType: 'tag',
            entityTypes: Tag.knex().raw('?::text[]', [['cases']]),
            source: 'testrail',
            externalId: tag.id,
          })
          .returning('*');

        tagUid = newTag.uid;
      } else {
        await Tag.query();
        tagUid = existingTag.uid;
      }

      // Create external entity mapping if it doesn't exist
      const existingEE = await ExternalEntity.query()
        .where({
          entityUid: String(tagUid),
          source: 'testrail',
          sourceId: String(tag.id),
          entityType: 'tag',
        })
        .first();

      if (!existingEE) {
        await ExternalEntity.query()
          .insert({
            source: 'testrail',
            sourceId: String(tag.id),
            entityType: 'tag',
            entityUid: String(tagUid),
            customFields: {
              type: 'tag',
              name: tag.name,
            },
          });
      }

      // Add to tag mappings
      tagMappings.case[tag.id] = {
        tagUid,
        name: tag.name,
      };
    }

    // Update tenant preferences
    await Org.query()
      .where('uid', tenantUid)
      .patch({ preferences });

    return { statusMappings, priorityMappings, tagMappings };
  } catch (error) {
    logger.error(`Error mapping preferences for tenant ${tenantUid} service testrail: ${error}`);
    throw error;
  }
}

async function calculateProgress(tenant: any, params: any, idMappings: any) {
  const conn = await tenantManager.loadTenant(tenant);
  const tenantDb = conn.db;
  const TestRun = tenantModels.TestRun.bindKnex(tenantDb);
  const TestPlan = tenantModels.TestPlan.bindKnex(tenantDb);
  const TestMilestone = tenantModels.TestMilestone.bindKnex(tenantDb);
  const TestExecution = tenantModels.TestExecution.bindKnex(tenantDb);
  if (idMappings.testRuns.length === 0) {
    return;
  }
  try {
    // Get all runs that were created/updated during import with proper relationship loading
    const runs = await TestRun.query()
      .whereIn('uid', idMappings.testRuns)
      .withGraphFetched({
        testExecutions: true,
        testMilestones: true,
        testPlans: true,
      })
      .modifiers({
        activeOnly(builder) {
          builder.whereNull('deletedAt');
        },
      })
      .whereNull('deletedAt');

    // Track plans and milestones that need updating
    const planUids = new Set<number>();
    const milestoneUids = new Set<number>();

    // Process each run
    for (const run of runs) {
      const trx = await tenantDb.transaction();
      // Get all executions for this run
      const executions = await TestExecution.query(trx)
        .where('testRunUid', run.uid)
        .whereNull('deletedAt');

      // Calculate frequency, progress and case count
      const frequency: any = {};
      let completedExecutions = 0;
      const caseCount = executions.length;

      for (const exec of executions) {
        if (frequency[exec.status]) {
          frequency[exec.status]++;
        } else {
          frequency[exec.status] = 1;
        }

        const mappedStatus = params.tags.status.find(
          (status: any) => status.id === exec.status,
        );
        if (mappedStatus?.isFinal) {
          completedExecutions++;
        }
      }

      // Update run with progress, frequency and case count
      await TestRun.query(trx)
        .where('uid', run.uid)
        .patch({
          customFields: {
            ...run.customFields,
            frequency,
            caseCount,
            progress: caseCount > 0 ? Math.round((completedExecutions / caseCount) * 100) : 0,
          },
        });

      // Track associated plan and milestones
      if (run.testPlans) {
        run.testPlans.forEach((plan) => planUids.add(plan.uid));
      }
      if (run.testMilestones) {
        run.testMilestones.forEach((milestone) => milestoneUids.add(milestone.uid));
      }

      await trx.commit();
    }

    // Update plans
    for (const planUid of planUids) {
      const plan = await TestPlan.query()
        .findById(planUid)
        .withGraphFetched({
          runs: true,
        })
        .whereNull('deletedAt');

      if (plan) {
        const trx = await tenantDb.transaction();
        // Get all runs for this plan using the proper relation
        const planRuns = plan.runs;
        const planFrequency: any = {};
        let planCompletedExecutions = 0;
        let planTotalCases = 0;

        // Aggregate run data
        for (const planRun of planRuns) {
          planTotalCases += planRun.customFields.caseCount || 0;

          // Add frequencies
          Object.entries(planRun.customFields.frequency || {}).forEach(
            ([status, count]) => {
              planFrequency[status] = (planFrequency[status] || 0) + Number(count);
            },
          );

          // Add completed executions based on progress
          planCompletedExecutions += Math.round(
            ((planRun.customFields.progress || 0) / 100)
              * (planRun.customFields.caseCount || 0),
          );
        }

        // Update plan
        await TestPlan.query(trx)
          .where('uid', planUid)
          .patch({
            customFields: {
              ...plan.customFields,
              frequency: planFrequency,
              progress: planTotalCases
                ? Math.round((planCompletedExecutions / planTotalCases) * 100) : 0,
            },
          });

        await trx.commit();
      }
    }

    // Update milestones
    for (const milestoneUid of milestoneUids) {
      const milestone = await TestMilestone.query()
        .findById(milestoneUid)
        .withGraphFetched({
          testRuns: true,
          testPlans: {
            runs: true,
          },
        })
        .whereNull('deletedAt');

      if (milestone) {
        const trx = await tenantDb.transaction();
        // Get direct runs for this milestone using the proper relation
        const milestoneRuns = milestone.testRuns;
        // Get plans for this milestone
        const milestonePlans = milestone.testPlans;

        const milestoneFrequency: any = {};
        let milestoneCompletedExecutions = 0;
        let milestoneTotalCases = 0;
        let hasCompleteRuns = false;

        // Process direct runs
        for (const run of milestoneRuns) {
          milestoneTotalCases += run.customFields.caseCount || 0;

          // Add frequencies
          Object.entries(run.customFields.frequency || {}).forEach(
            ([status, count]) => {
              milestoneFrequency[status] = (milestoneFrequency[status] || 0) + Number(count);
            },
          );

          // Check if run is marked as complete
          const mappedStatus = params.tags.status.find(
            (status: any) => status.id === run.customFields.status,
          );
          if (mappedStatus?.isFinal) {
            hasCompleteRuns = true;
            milestoneCompletedExecutions += run.customFields.caseCount || 0;
          } else {
            milestoneCompletedExecutions += Math.round(
              ((run.customFields.progress || 0) / 100)
                * (run.customFields.caseCount || 0),
            );
          }
        }

        // Process runs from plans
        for (const plan of milestonePlans) {
          // Get runs for this plan using the proper relation
          const planRuns = plan.runs;

          for (const run of planRuns) {
            milestoneTotalCases += run.customFields.caseCount || 0;

            // Add frequencies
            Object.entries(run.customFields.frequency || {}).forEach(
              ([status, count]) => {
                milestoneFrequency[status] = (milestoneFrequency[status] || 0) + Number(count);
              },
            );

            // Check if run is marked as complete
            const mappedStatus = params.tags.status.find(
              (status: any) => status.id === run.customFields.status,
            );
            if (mappedStatus?.isFinal) {
              hasCompleteRuns = true;
              milestoneCompletedExecutions += run.customFields.caseCount || 0;
            } else {
              milestoneCompletedExecutions += Math.round(
                ((run.customFields.progress || 0) / 100)
                  * (run.customFields.caseCount || 0),
              );
            }
          }
        }

        // Update milestone
        await TestMilestone.query(trx)
          .where('uid', milestoneUid)
          .patch({
            customFields: {
              ...milestone.customFields,
              progress: calculateMilestoneProgress(milestoneTotalCases, milestoneCompletedExecutions, hasCompleteRuns),
            },
          });

        await trx.commit();
      }
    }
  } catch (error) {
    logger.error('Error in calculateProgress:', error);
    throw error;
  }
}

function calculateMilestoneProgress(totalCases: number, completedExecutions: number, hasCompleteRuns: boolean): number {
  if (totalCases === 0) {
    return 0;
  }
  const progress = Math.round((completedExecutions / totalCases) * 100);
  return hasCompleteRuns ? 100 : progress;
}
