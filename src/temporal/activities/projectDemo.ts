import logger from '@app/config/logger';
import { Tenant } from '@app/models/tenant';
import { tenantManager } from '@app/lib/tenants';
import { setupOpenfga } from '@app/config/openfga';
import { FGARawWrite, FgaService } from '@ss-libs/ss-component-auth';
import projectDemo from '@app/constants/projectDemo';
import { TestCase } from '@app/models/testCase';
import { CreateCaseDTO } from '@app/types/case';
import { Tag } from '@app/models/tag';
import preferenceService from '@app/models/preferences';
import _ from 'lodash';
import { TestRun } from '@app/models/testRun';
import { TestMilestone } from '@app/models/testMilestone';
import { Knex } from 'knex';
import { Project } from '@app/models/project';
import { getSharedDb } from '../client';
import { updateMilestoneState } from './milestone';
import { updateRunState } from './run';

export interface DemoProjectData {
  ownerType: 'user' | 'org';
  ownerUid: string;
  projectId: number;
  userId: string;
  rootFolderUid: number;
}

export async function setupProjectDemo(params: DemoProjectData) {
  const sharedDb = await getSharedDb();
  const tenant = await Tenant.query(sharedDb)
    .where('tenantUid', params.ownerUid)
    .where('setupStatus', 'completed')
    .withGraphFetched('dbServer')
    .first();

  if (!tenant) {
    logger.info(`skipping demo setup for project ${params.projectId}`);
    return;
  }

  const conn = await tenantManager.loadTenant(tenant);
  const tenantDB = conn.db;

  const openFga = await setupOpenfga({
    storeId: tenant.openfgaStoreId,
    authModelId: tenant.openfgaAuthModelId,
  });
  const fgaService = new FgaService(openFga);
  const trx = await tenantDB.transaction();

  try {
    const [tags, prefs] = await Promise.all([
      Tag.query(trx)
        .where({
          systemType: 'tag',
          deletedAt: null,
        })
        .limit(5),
      preferenceService.getDefaults(
        sharedDb,
        params.ownerType,
        params.ownerUid,
      ),
    ]);

    const tagUids = tags.map((t) => t.uid);
    const caseUids = await createTestCases(
      trx,
      tagUids,
      prefs,
      fgaService,
      params,
    );

    const milestone = await TestMilestone.create(trx, params.projectId, {
      dueAt: projectDemo.milestone.dueAt().toISOString(),
      name: projectDemo.milestone.name,
      startDate: new Date().toISOString(),
      status: prefs.milestone?.status,
      tagUids,
    });

    const { testRun, executions } = await TestRun.newRun(
      trx,
      {
        name: projectDemo.run.name,
        priority: prefs.testRun.priority,
        status: prefs.testRun.status,
        projectUid: params.projectId,
        dueAt: projectDemo.run.dueAt(),
        tagUids,
      },
      {
        caseUids,
        execPriority: prefs.testCase?.priority,
        execStatus: prefs.testCase?.status,
        milestoneUids: [milestone.uid], // attach milestone to newly created run
      },
    );
    const fgaWrites: FGARawWrite[] = [
      {
        objectType: 'run',
        objectId: testRun.uid,
        relation: 'owner',
        subjectType: params.ownerType,
        subjectId: params.ownerUid,
      },
      ...executions.map((e) => ({
        objectType: 'execution',
        objectId: e.uid,
        relation: 'owner',
        subjectType: params.ownerType,
        subjectId: params.ownerUid,
      })),
    ];

    fgaWrites.push({
      objectType: 'milestone',
      objectId: milestone.uid,
      relation: 'owner',
      subjectType: params.ownerType,
      subjectId: params.ownerUid,
    });

    await fgaService.create(...fgaWrites);

    await trx.commit();
    logger.info(
      `successfully created demo project, with test cases=[${caseUids}], run=[${testRun.uid}], milestone=[${milestone.uid}]`,
    );

    await Project.query(tenantDB)
      .where('uid', params.projectId)
      .patch({ status: 'active' });

    await updateMilestoneState({
      milestoneUids: [milestone.uid],
      ownerType: params.ownerType,
      ownerUid: params.ownerUid,
    });
    await updateRunState({
      runUids: [testRun.uid],
      ownerType: params.ownerType,
      ownerUid: params.ownerUid,
    });
  } catch (err) {
    await trx.rollback();
    logger.error(err);
  }
}

async function createTestCases(
  trx: Knex.Transaction,
  tagUids: number[],
  prefs: any,
  fgaService: FgaService,
  params: DemoProjectData,
) {
  const dto: CreateCaseDTO[] = projectDemo.cases.map((d) => ({
    name: d.name,
    steps: d.steps,
    tagIds: tagUids,
    customFields: d.customFields,
    parentId: params.rootFolderUid,
    priority: prefs.testCase?.priority,
    status: prefs.testCase?.status,
    projectId: params.projectId,
    createdBy: params.userId,
  }));

  const { cases, fgaWrites } = await TestCase.newCases(
    dto,
    prefs,
    params.projectId,
    params.ownerUid,
    params.ownerType,
    trx,
  );
  await Promise.all(
    _.chunk(fgaWrites, 100).map((group) => fgaService.create(...group)),
  );
  return cases.map((c) => c.uid);
}
