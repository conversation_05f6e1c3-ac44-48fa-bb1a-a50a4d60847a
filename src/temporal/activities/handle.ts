import { Handle } from '@app/models/handle';
import dayjs from 'dayjs';
import logger from '@app/config/logger';
import { serializeErr } from '@app/lib/error';
import { getSharedDb, startWorkflow, terminateWorkflow } from '@app/temporal/client';
import { Knex } from 'knex';

const RETENTION_PERIOD_MONTHS = 6;
const FREE_HANDLE_CRON_PATTERN = '0 23 * * *';

export async function freeHandles(db?: Knex) {
  let sharedDb = db;
  if (!db) {
    sharedDb = await getSharedDb();
  }

  const trx = await sharedDb.transaction();

  try {
    const cutoffDate = dayjs()
      .subtract(RETENTION_PERIOD_MONTHS, 'month')
      .toDate();

    const deletedUserHandles = await Handle.query(trx)
      .join('users', 'handles.ownerUid', 'users.uid')
      .where('handles.ownerType', 'user')
      .whereNotNull('users.deletedAt')
      .where('users.deletedAt', '<=', cutoffDate)
      .select('handles.*');

    const deletedOrgHandles = await Handle.query(trx)
      .join('orgs', 'handles.ownerUid', 'orgs.uid')
      .where('handles.ownerType', 'org')
      .whereNotNull('orgs.deletedAt')
      .where('orgs.deletedAt', '<=', cutoffDate)
      .select('handles.*');

    const handlesToFree = [...deletedUserHandles, ...deletedOrgHandles];

    if (handlesToFree && handlesToFree.length > 0) {
      await Handle.query(trx)
        .delete()
        .whereIn(
          'uid',
          handlesToFree.map((handle) => handle.uid),
        );
    }

    await trx.commit();
    logger.info(
      `Freed ${handlesToFree.length} handles at ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`,
    );
  } catch (err) {
    await trx.rollback();
    logger.error('Error processing free handles');
    logger.error(serializeErr(err));
    throw err;
  }
}

// Instead of using node-cron, use Temporal's native scheduling
export async function freeHandlesCron() {
  await terminateWorkflow('free-handles-queue', 'free-handles');

  await startWorkflow('freeHandlesWorkflow', {
    taskQueue: 'free-handles-queue',
    workflowId: 'free-handles',
    args: [{}],
    cronSchedule: FREE_HANDLE_CRON_PATTERN,
  });
}
