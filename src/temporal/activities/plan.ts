import { Tenant } from '@app/models/tenant';
import { TestPlan } from '@app/models/testPlan';
import logger from '@app/config/logger';
import preferences from '@app/models/preferences';
import { serializeErr } from '@app/lib/error';
import { tenantManager } from '@app/lib/tenants';
import { startWorkflow, getSharedDb } from '@app/temporal/client';
import { UpdateMilestoneData } from '@app/temporal/activities/milestone';

export type UpdatePlanStateDTO = {
  ownerUid: Tenant['tenantUid'];
  ownerType: Tenant['tenantType'];
  planUids: number[];
};

export async function updatePlanState(dto: UpdatePlanStateDTO) {
  try {
    const sharedDb = await getSharedDb();
    const tenant = await Tenant.query(sharedDb)
      .where({ tenantType: dto.ownerType, tenantUid: dto.ownerUid })
      .withGraphFetched('dbServer')
      .first();
    if (!tenant) {
      log(`tenant ${dto.ownerType}:${dto.ownerUid}`);
      return;
    }

    const pref = await preferences.getCompleted(sharedDb, dto.ownerType, dto.ownerUid);
    const { db: tdb } = await tenantManager.loadTenant(tenant);
    const milestoneUids = new Set<number>();

    const plans = await TestPlan.query(tdb)
      .whereIn('uid', dto.planUids)
      .where('systemType', 'plan')
      .withGraphFetched('[milestones]');

    for (const plan of plans) {
      await TestPlan.updateProgress(tdb, plan, pref);
      for (const m of plan.milestones) milestoneUids.add(m.uid);
    }

    const param: UpdateMilestoneData = {
      ownerType: dto.ownerType,
      ownerUid: dto.ownerUid,
      milestoneUids: [...milestoneUids],
    };
    await startWorkflow('updateMilestoneWorkflow', {
      taskQueue: 'update-milestone-queue',
      workflowId: `update.milestone.${dto.ownerUid}.${Date.now()}`,
      args: [param],
    });
  } catch (err) {
    log(`error syncing changes: ${serializeErr(err)}`, 'error');
    throw err;
  }
}

function log(msg: string, level: 'info' | 'error' = 'info') {
  logger[level](`[updatePlanState]: ${msg}`);
}
