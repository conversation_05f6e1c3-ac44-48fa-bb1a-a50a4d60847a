import { Tenant } from '@app/models/tenant';
import { TestMilestone } from '@app/models/testMilestone';
import logger from '@app/config/logger';
import preferences from '@app/models/preferences';
import { serializeErr } from '@app/lib/error';
import { tenantManager } from '@app/lib/tenants';
import { getSharedDb } from '@app/temporal/client';
import { Knex } from 'knex';

export type UpdateMilestoneData = {
  ownerUid: Tenant['tenantUid'];
  ownerType: Tenant['tenantType'];
  milestoneUids: number[];
};

function log(msg: string, level: 'info' | 'error' = 'info') {
  logger[level](`[updateMilestoneState]: ${msg}`);
}

export async function updateMilestoneState(dto: UpdateMilestoneData, db?: Knex) {
  let sharedDb = db;
  if (!db) {
    sharedDb = await getSharedDb();
  }

  const tenant = await Tenant.query(sharedDb)
    .where({ tenantType: dto.ownerType, tenantUid: dto.ownerUid })
    .withGraphFetched('dbServer')
    .first();
  if (!tenant) {
    log(`tenant ${dto.ownerType}:${dto.ownerUid}`);
    return;
  }

  const pref = await preferences.getCompleted(sharedDb, dto.ownerType, dto.ownerUid);
  const { db: tdb } = await tenantManager.loadTenant(tenant);

  try {
    await tdb.transaction(async (trx) => {
      const milestoneUpdates = (
        await TestMilestone.query(tdb)
          .whereIn('uid', dto.milestoneUids)
          .where('systemType', 'milestone')
      ).map((m) => TestMilestone.updateProgress(trx, m, pref));
      await Promise.all(milestoneUpdates);
    });
  } catch (err) {
    log(`error syncing changes: ${serializeErr(err)}`, 'error');
    throw err;
  }
}
