import 'module-alias/register';

import http from 'http';
import { buildApp } from './app';
import env from './config/env';
import { internalRoutes } from './routes/internal';
import { loadAllPrivateKeys } from './config/keyManagerLoader';
import logger from './config/logger';
import { setupDB } from './config/db';
import { tenantManager } from './lib/tenants';

async function main() {
  const db = setupDB();
  const app = buildApp(db, internalRoutes, true);
  await loadAllPrivateKeys(db);
  const httpServer = http.createServer(app);
  const PORT = `${env.PORT}`;

  httpServer.on('listening', () => logger.info(`App is running on port ${PORT}`));
  httpServer.listen(PORT);

  process.on('SIGTERM', async () => {
    logger.info('Exiting app...');
    httpServer.close();
    await db.destroy();
    await tenantManager.shutdown();
  });
}
main();
