import { Handle } from '@ss-libs/ss-component-auth';
import env from '@app/config/env';
import { setupDB } from '@app/config/db';
import { DBServer } from '@app/models/dbServer';
import { EmailNotification } from '@app/temporal/activities';
import { startWorkflow } from '@app/temporal/client';
import { Account } from '../temporal/activities/account';

const sharedDB = setupDB();
/**
 * sets up an account by
 * @param type
 * @param uid
 */
export async function onCreateUser(handle: Handle) {
  const dbServer = await DBServer.query(sharedDB)
    .findOne({
      'dbServers.isDefault': true,
      'appNode.isDefault': true,
    })
    .joinRelated('appNode');

  const param: Account = {
    ownerType: 'user',
    ownerUid: handle.ownerUid,
    dbServerUid: dbServer?.uid,
  };
  await startWorkflow('setupAccountWorkflow', {
    taskQueue: 'setup-account-queue',
    workflowId: `setup.account.${handle.ownerUid}.${Date.now()}`,
    args: [param],
  });
}

export async function sendResetPasswordMail(user, token: string) {
  const resetLink = `${env.FRONTEND_URL}/resetPassword?token=${token}`;

  const param: EmailNotification = {
    body: { params: { resetLink }, template: 'reset_password' },
    to: user.email,
    subject: 'Reset password link',
  };

  startWorkflow('emailWorkflow', {
    taskQueue: 'email-queue',
    workflowId: `email:${Date.now()}`,
    args: [param],
  });
}
