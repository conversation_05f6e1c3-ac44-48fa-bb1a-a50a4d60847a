import {
  WebhookHandler,
  WebhookStatus,
} from '@ss-libs/ss-component-notifications';

import { startWorkflow } from '@app/temporal/client';

/**
 * bounce = finally not sent
 * deferred = email not found
 * dropped = email found, but client has decided to stop receiving these emails,
 * delivered = success
 */
const relevantStatuses: WebhookStatus[] = [
  'bounce',
  'deferred',
  'delivered',
  'dropped',
];

export const onNotificationWebhook: WebhookHandler = async (events) => {
  const valid = events.filter((e) => relevantStatuses.includes(e.status));
  await startWorkflow('processEmailEventsWorkflow', {
    taskQueue: 'email-events-queue',
    workflowId: `email.webhook.${Date.now()}`,
    args: [{ events: valid }],
  });
};
