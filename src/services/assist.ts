import { composeInitialCaseDetails, isAssistComplete } from '@app/constants/aiAssist';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatOpenAI } from '@langchain/openai';
import { z } from 'zod';
import { OutputFixingParser } from 'langchain/output_parsers';
import { ApplicationError } from '@app/lib/http';
import errorConstants from '@app/constants/errors';
import { StatusCodes } from 'http-status-codes';

// This should be dynamic .. based on inputs
const scoreAttributes = (attrs: string[]) => z.object(
  attrs.reduce((acc, attr) => {
    acc[attr] = z.number().lte(10).gte(1);
    return acc;
  }, {} as Record<string, any>),
);

// Scoring Template
const scoringTemplate = ChatPromptTemplate.fromMessages([
  ['system', `Instructions: 
    Purpose and Context:
      You are a **test case evaluation assistant**. Your primary function is to assess the quality of a test case by assigning scores for each of the following attributes:
    Scoring Attributes:
      {attributes}

    Scoring Guidelines:
      - **Do not assign identical scores** unless the test cases are almost identical. 
        - If the cases seem similar, their scores should still reflect small differences (e.g., using decimal points) to capture subtle variations in quality.
      - Scores should be given as whole numbers or decimals between 1 and 10, with decimal values helping to differentiate between cases that are very similar but not identical.  
  
    Relevance Enforcement:
      - If the provided test case is incomplete or lacks sufficient information for evaluation, respond with:
        **"Insufficient information provided to score the test case."**
      - Do not include content unrelated to the scoring or its justification.\n{format_instructions}
  `],
  ['user', `The following test cases need to be scored for quality. Please evaluate them based on the attributes provided and return the scores in the specified format.

  {cases}`],
]);

export const scoreCases = async (cases: any[], attributes: string[]) => {
  try {
    const model = new ChatOpenAI({
      apiKey: process.env.OPENAI_API_KEY,
      model: 'gpt-4o-mini',
      temperature: 0.5,
    });

    // Format cases to be send for Assist

    const formattedCases = cases.map((c) => {
      const {
        caseTitle, casePriority, steps, inputFields,
      } = c;

      return composeInitialCaseDetails(caseTitle, casePriority, steps, inputFields, true);
    });

    const outputSchema = z.object({
      scores: z.array(
        z.object({
          ...scoreAttributes(attributes).shape,
        }),
      ).length(cases.length),
    });

    const parser = StructuredOutputParser.fromZodSchema(outputSchema);
    const chain = scoringTemplate.pipe(model);

    const response = await chain.invoke({
      format_instructions: parser.getFormatInstructions(),
      attributes: attributes.join(', '),
      cases: formattedCases,
    });

    if (isAssistComplete(response.content as string)) {
      try {
        return await parser.parse(response.content as string);
      } catch (err:any) {
        try {
          const res = OutputFixingParser.fromLLM(model, parser);
          return await res.parse(err);
        } catch {
          throw new ApplicationError(
            StatusCodes.BAD_REQUEST,
            errorConstants.NO_RESPONSE_FOUND,
          );
        }
      }
    } else {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        errorConstants.NO_RESPONSE_FOUND,
      );
    }
  } catch (error) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errorConstants.NO_RESPONSE_FOUND,
    );
  }
};
