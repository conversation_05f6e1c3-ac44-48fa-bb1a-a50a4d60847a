import { ApplicationError, httpH<PERSON><PERSON> } from '@app/lib/http';
import {
  BulkUpdateRunDTO,
  DuplicateRunsDTO,
  GetRunRelationsDTO,
  ListRunsDTO,
  UpdateRunCasesDTO,
} from '@app/types/run';
import { PaginatedQuery, paginated } from '@app/lib/model';

import { FGARawWrite } from '@ss-libs/ss-component-auth';
import { INTEGRATION_IMAGES } from '@app/constants/integration';
import { Request } from 'express';
import { StatusCodes } from 'http-status-codes';
import { TestExecution } from '@app/models/testExecution';
import { TestRun } from '@app/models/testRun';
import _ from 'lodash';
import env from '@app/config/env';
import errors from '@app/constants/errors';
import { getConfigOptions } from '@app/models/testPlan';
import logger from '@app/config/logger';
import preferencesService from '@app/models/preferences';
import { startWorkflow } from '../temporal/client';
import { UpdateRunStateDTO } from '../temporal/activities/run';

/**
 * create a test runs
 * POST
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.body.externalId
 * @param {String} req.body.source
 * @param {String} req.body.name
 * @param {Object} req.body.customFields
 */

const createTestRun = async (req: Request) => {
  const dto = req.body;

  const trx = await req.knexDB.transaction();
  try {
    const { ownerType, ownerUid } = req.locals.handle;

    const defaults = await preferencesService.getDefaults(
      req.sharedKnexDB,
      ownerType,
      ownerUid,
    );

    const { testRun, executions } = await req.models.TestRun.newRun(
      trx,
      {
        priority: dto.priority ?? defaults.testRun?.priority,
        status: dto.status ?? defaults.testRun?.status,
        name: dto.name,
        projectUid: req.locals.project.uid,
        description: dto.description,
        dueAt: dto.dueAt,
        tagUids: dto.tagUids,
      },
      {
        caseUids: dto.caseUids ?? [],
        milestoneUids: dto.milestoneUids ?? [],
        execPriority: defaults.testCase?.priority,
        execStatus: defaults.testCase?.status,
      },
    );
    const fgaWrites: FGARawWrite[] = [
      {
        objectType: 'run',
        objectId: testRun.uid,
        relation: 'owner',
        subjectType: ownerType,
        subjectId: ownerUid,
      },
      ...executions.map((e) => ({
        objectType: 'execution',
        objectId: e.uid,
        relation: 'owner',
        subjectType: ownerType,
        subjectId: ownerUid,
      })),
    ];
    await req.fga.create(...fgaWrites);
    await trx.commit();

    const param: UpdateRunStateDTO = {
      runUids: [testRun.uid],
      ownerType,
      ownerUid,
    };
    await startWorkflow('updateRunWorkflow', {
      taskQueue: 'update-run-queue',
      workflowId: `update.run.${testRun.uid}.${Date.now()}`,
      args: [param],
    });
    return testRun;
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

/**
 * Duplicate test-run with all it's execution with support for config, just like plans
 */
const duplicateTestRun = async (req: Request) => {
  const dto: DuplicateRunsDTO = req.body;
  const trx = await req.knexDB.transaction();
  const { ownerType, ownerUid } = req.locals.handle;
  try {
    const defaults = await preferencesService.getDefaults(
      req.sharedKnexDB,
      ownerType,
      ownerUid,
    );

    const testRuns: TestRun[] = [];
    const executions: TestExecution[] = [];
    const fgaWrites: FGARawWrite[] = [];

    for (const run of dto.testRuns) {
      const sourceRun = await req.models.TestRun.findOne(trx, { uid: run.uid });
      if (!sourceRun) continue;

      const configs = getConfigOptions(dto.configuration, run.configuration);

      const baseRun = {
        name: sourceRun.name ?? '',
        priority:
          sourceRun.customFields?.priority ?? defaults.testRun?.priority,
        status: defaults.testRun?.status,
        projectUid: req.locals.project.uid,
        source: sourceRun.source,
        externalId: sourceRun.externalId,
        link: sourceRun.customFields?.link,
        description: sourceRun.description ?? '',
      };
      const opts = {
        execPriority: defaults.testCase?.priority,
        execStatus: defaults.testCase?.status,
        sourceRunUid: sourceRun.uid,
      };

      if (!configs.length) {
        const result = await req.models.TestRun.duplicate(
          trx,
          { ...baseRun },
          opts,
        );
        testRuns.push(result.testRun);
        executions.push(...result.executions);
      } else {
        for (const config of configs) {
          const result = await req.models.TestRun.duplicate(
            trx,
            {
              ...baseRun,
              configs: config,
            },
            opts,
          );
          testRuns.push(result.testRun);
          executions.push(...result.executions);
        }
      }
    }

    fgaWrites.push(
      ...testRuns.map((r) => ({
        objectType: 'run',
        objectId: r.uid,
        relation: 'owner',
        subjectType: ownerType,
        subjectId: ownerUid,
      })),
      ...executions.map((e) => ({
        objectType: 'execution',
        objectId: e.uid,
        relation: 'owner',
        subjectType: ownerType,
        subjectId: ownerUid,
      })),
    );

    if (fgaWrites.length > 0) await req.fga.create(...fgaWrites);

    await trx.commit();
    const param: UpdateRunStateDTO = {
      runUids: testRuns.map((r) => r.uid),
      ownerType,
      ownerUid,
    };
    await startWorkflow('updateRunWorkflow', {
      taskQueue: 'update-run-queue',
      workflowId: `update.run.${testRuns[0].uid}.${Date.now()}`,
      args: [param],
    });
    return testRuns;
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

/**
 * update a test runs
 */
const updateTestRun = async (req: Request) => {
  const { id } = req.params;

  const trx = await req.knexDB.transaction();
  try {
    const run = await req.models.TestRun.updateOne(trx, +id, req.body);
    if (!run) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, errors.RUN_NOT_FOUND);
    }

    await trx.commit();
    const param: UpdateRunStateDTO = {
      runUids: [run.uid],
      ownerType: req.locals.handle.ownerType,
      ownerUid: req.locals.handle.ownerUid,
    };
    await startWorkflow('updateRunWorkflow', {
      taskQueue: 'update-run-queue',
      workflowId: `update.run.${run.uid}.${Date.now()}`,
      args: [param],
    });
    try {
      if (run.source === 'testrail') {
        startWorkflow('integrationEntitiesWorkflow', {
          taskQueue: 'integration-entities-queue',
          workflowId: `${req.locals.handle.ownerUid}:run:${Date.now()}`,
          args: [
            {
              tenantUid: req.locals.handle.ownerUid,
              task: 'updateEntity',
              entityType: 'run',
              data: run,
            },
          ],
        });
      }
    } catch (error) {
      logger.error(`Error updating single entity ${run.uid}:`, error);
    }
    return run;
  } catch (err) {
    logger.info('Failed to update test run');
    await trx.rollback();
    throw err;
  }
};

/**
 * update multiple runs using their runIds
 * PUT
 * @param {Object} req
 */
const updateTestRuns = async (req: Request) => {
  const dto: BulkUpdateRunDTO = req.body;
  const trx = await req.knexDB.transaction();

  try {
    const validRuns: TestRun[] = await req.models.TestRun.findByIds(
      trx,
      dto.uids,
    );
    if (validRuns.length === 0) {
      await trx.rollback();
      return [];
    }

    const uids = validRuns.map((r) => r.uid);
    if (dto.action === 'addMilestones') {
      await req.models.TestRun.addMilestones(trx, uids, dto.milestoneUids);
    }
    if (dto.action === 'addPlans') {
      await req.models.TestRun.addPlans(trx, uids, dto.planUids);
    }
    if (dto.action === 'removePlans') {
      await req.models.TestRun.removePlans(trx, uids, dto.planUids);
    }
    if (dto.action === 'updateDueDate') {
      await req.models.TestRun.updateDueAt(trx, uids, dto.dueAt);
    }
    if (dto.action === 'removeMilestones') {
      await req.models.TestRun.removeMilestones(trx, uids, dto.milestoneUids);
    }
    if (dto.action === 'archive') {
      await req.models.TestRun.updateArchiveStatus(trx, uids, true);
    }
    if (dto.action === 'unarchive') {
      await req.models.TestRun.updateArchiveStatus(trx, uids, false);
    }
    const testRuns = await req.models.TestRun.findByIds(trx, uids);

    await trx.commit();
    const param: UpdateRunStateDTO = {
      runUids: uids,
      ownerType: req.locals.handle.ownerType,
      ownerUid: req.locals.handle.ownerUid,
    };
    await startWorkflow('updateRunWorkflow', {
      taskQueue: 'update-run-queue',
      workflowId: `update.run.${uids}.${Date.now()}`,
      args: [param],
    });
    return testRuns;
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

/**
 * get a list of test runs
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.orgId
 * @param {Number} req.query.per_page
 * @param {Number} req.query.current_page
 */
const getRuns = async (req: Request) => {
  const q: ListRunsDTO = req.query as any;
  const trx = await req.knexDB.transaction();
  try {
    const page = await req.models.TestRun.findAll(trx, {
      ...q,
      projectUid: req.locals.project.uid,
    });

    await trx.commit();
    return page;
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const getRunRelations = async (req: Request) => {
  const dto: GetRunRelationsDTO = req.query as any;
  switch (dto.relation) {
    case 'config':
      return req.models.TestRun.getConfigs(req.knexDB, dto.runUids);
    case 'milestone':
      return req.models.TestRun.getMilestones(req.knexDB, dto.runUids);
    case 'tag':
      return req.models.TestRun.getTags(req.knexDB, dto.runUids);
    default:
      throw new ApplicationError(StatusCodes.CONFLICT, '');
  }
};

const getRun = async (req: Request) => {
  const run = await req.models.TestRun.query()
    .withGraphFetched('[testMilestones]')
    .modifyGraph('testMilestones', (q) => {
      q.whereNull('tags.deletedAt')
        .whereNull('testMilestoneRuns.deletedAt')
        .select('tags.uid', 'name');
    })
    .where({
      uid: req.params.id,
      projectUid: req.locals.project.uid,
      systemType: 'run',
      deletedAt: null,
    })
    .first();

  if (!run) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errors.RUN_NOT_FOUND);
  }

  const dto = TestRun.toDTO(run);
  await req.models.TestRun.populateConfig(req.knexDB, dto);
  await req.models.TestRun.populateTags(req.knexDB, dto);
  return dto;
};

const getRunFolders = async (req: Request) => {
  const folders = await TestRun.getFolders(req.knexDB, req.params.id as any);
  return { folders };
};

/**
 * Delete test runs by their runId
 * DELETE
 * @param {Object} req
 * @param {Object} res
 */
const deleteTestRuns = async (req: Request) => {
  const testRuns = await req.models.TestRun.deleteByIds(
    req.knexDB,
    req.body.runUids,
  );

  const param: UpdateRunStateDTO = {
    runUids: testRuns,
    ownerType: req.locals.handle.ownerType,
    ownerUid: req.locals.handle.ownerUid,
  };
  await startWorkflow('updateRunWorkflow', {
    taskQueue: 'update-run-queue',
    workflowId: `update.run.${testRuns}.${Date.now()}`,
    args: [param],
  });

  return {
    message: 'Test runs have been deleted successfully',
  };
};
/**
 * get a list of test executions releated to a test run
 */
const getRunExecutions = async (req: Request) => {
  const { id } = req.params;

  const run = await req.models.TestRun.query().findById(id);
  if (!run) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errors.RUN_NOT_FOUND);
  }

  const executions: any[] = await req.models.TestExecution.query()
    .withGraphFetched('[tags(isCaseTag), attachments, defects.[integration]]')
    .whereNull('testExecutions.deletedAt')
    .innerJoin('testCases', 'testCases.uid', 'testExecutions.testCaseUid')
    .select(
      'testExecutions.*',
      'testCases.name',
      'testCases.steps',
      'testCases.externalId',
      'testCases.testCaseRef',
      'testCases.parentUid as parentUid',
      'testCases.active',
      'testCases.customFields as caseFields',
    )
    .where('testRunUid', id)
    .where('testCases.projectUid', req.locals.project.uid);

  const userUids: any = new Set();
  executions.map((execution) => userUids.add(execution.assignedTo));

  const orgId = req.locals.handle.ownerUid;
  const tuples = await req.fga.query('', `org:${orgId}`, 'member');
  const membersUids = tuples.map((t) => {
    userUids.add(t.key.user.split(':')[1]);
    return t.key.user.split(':')[1];
  });

  const users = await req.models.User.query()
    .join('handles', 'users.uid', '=', 'handles.ownerUid')
    .whereIn('users.uid', [...userUids])
    .where('handles.current', true)
    .where('handles.ownerType', 'user')
    .select({
      uid: 'users.uid',
      firstName: 'users.firstName',
      lastName: 'users.lastName',
      email: 'users.email',
      handle: 'handles.name',
    });

  const result = users.reduce((acc, curr) => {
    acc[curr.uid] = curr;
    return acc;
  }, {});

  const baseURL = `${env.BACKEND_URL}${env.API_INTERNAL_ROUTE}/${req.params.handle}`;
  const assignees = users.filter((user) => membersUids.includes(user.uid));

  for (const item of executions) {
    if (item.attachments?.length) {
      const attachments = item.attachments.map((element: any) => ({
        previewUrl: `${baseURL}/executions/attachments/${element.uid}/object`,
        name: element.name,
        type: element.fileType,
        uid: element.uid,
      }));

      item.attachments = attachments;
    }
    for (const defect of item.defects) {
      if (defect.integration) {
        defect.integration.image = INTEGRATION_IMAGES[defect.integration.service];
      }
    }
    item.tags = item.customFields?.tags?.map((i) => i.uid) ?? [];

    item.tags = await req.models.Tag.findByIds(req.knexDB, item.tags);

    if (!_.isEmpty(item.customFields?.case) && item.customFields.case?.name) {
      item.steps = item.customFields.case?.steps ?? [];
      item.name = item.customFields.case.name;
      item.caseFields = {
        templateFields: item.customFields.case?.templateFields ?? [],
      };
    }
    item.assignedTo = result[item.assignedTo];
  }

  return {
    executions,
    assignees,
    progress: run.customFields.progress,
    frequency: run.customFields.frequency,
    run: {
      name: run.name,
      description: run.description,
    },
  };
};

const getTestCases = async (req: Request) => {
  const { limit, offset } = req.query as any as PaginatedQuery;
  const sql = req.models.TestExecution.query()
    .where({
      testRunUid: req.params.id,
      deletedAt: null,
    })
    .withGraphFetched('[tags(isCaseTag), attachments, testCase]')
    .toKnexQuery();

  return paginated(sql, limit, offset, req.knexDB);
};

const updateTestRunCases = async (req: Request) => {
  const body = req.body as UpdateRunCasesDTO;
  const runUid = +req.params.id;
  const trx = await req.knexDB.transaction();

  let added = 0;
  let removed = 0;
  try {
    if (body.addCaseUids?.length > 0) {
      const defaults = await preferencesService.getDefaults(
        req.sharedKnexDB,
        req.locals.handle.ownerType,
        req.locals.handle.ownerUid,
      );
      const cases = await req.models.TestRun.addCases(
        trx,
        runUid,
        body.addCaseUids,
        defaults.testCase?.status,
        defaults.testCase?.priority,
      );
      added = cases.length;
    }
    if (body.removeExecUids?.length > 0) {
      const execs = await req.models.TestRun.removeExecs(
        trx,
        runUid,
        body.removeExecUids,
      );
      removed = execs.length;
    }

    await trx.commit();
    const param: UpdateRunStateDTO = {
      runUids: [runUid],
      ownerType: req.locals.handle.ownerType,
      ownerUid: req.locals.handle.ownerUid,
    };
    await startWorkflow('updateRunWorkflow', {
      taskQueue: 'update-run-queue',
      workflowId: `update.run.${runUid}.${Date.now()}`,
      args: [param],
    });
    return { added, removed };
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

/**
 * Get count of test runs for a specific project
 * GET
 * @param {Object} req
 */
const getRunsCountByProject = async (req: Request) => {
  const count = await req.models.Project.countRuns(
    req.knexDB,
    req.locals.project.uid,
  );
  return { count };
};

const getCasesCountByRun = async (req: Request) => {
  const count = await req.models.TestRun.countCases(req.knexDB, req.params.id);
  return { count };
};

const countRuns = async (req: Request) => {
  const [active, archived] = await Promise.all([
    req.models.TestRun.countRuns(req.knexDB, 'active'),
    req.models.TestRun.countRuns(req.knexDB, 'archived'),
  ]);

  return { active, archived };
};

export default {
  createTestRun: httpHandler(createTestRun),
  duplicateTestRun: httpHandler(duplicateTestRun),
  updateTestRuns: httpHandler(updateTestRuns),
  updateTestRun: httpHandler(updateTestRun),
  getRuns: httpHandler(getRuns),
  getRun: httpHandler(getRun),
  deleteTestRuns: httpHandler(deleteTestRuns),
  getRunExecutions: httpHandler(getRunExecutions),
  getRunCases: httpHandler(getTestCases),
  updateTestRunCases: httpHandler(updateTestRunCases),
  getRunsCountByProject: httpHandler(getRunsCountByProject),
  getCasesCountByRun: httpHandler(getCasesCountByRun),
  countRuns: httpHandler(countRuns),
  getRunRelations: httpHandler(getRunRelations),
  getRunFolders: httpHandler(getRunFolders),
};
