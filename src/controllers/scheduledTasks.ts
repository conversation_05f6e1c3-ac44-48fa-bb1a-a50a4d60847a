import { Request } from 'express';
import { ApplicationError, httpHandler } from '@app/lib/http';
import { StatusCodes } from 'http-status-codes';
import errorConstants from '@app/constants/errors';
import { startWorkflow, WorkflowName } from '@app/temporal/client';
import { TaskQueue } from '@app/constants/temporal';

const getSchduledTask = async (req: Request) => {
  const { id } = req.params;
  const { ownerUid } = req.locals.handle;
  const task = await req.models.ScheduledTask.query()
    .where({ uid: id, ownerUid })
    .select('uid', 'status', 'message', 'percentage', 'createdAt', 'completedAt')
    .first();
  if (!task) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.SCHEDULED_TASK_NOT_FOUND,
    );
  }
  return task;
};

const runScheduledTask = async (req: Request) => {
  const { id } = req.params;
  const { ownerUid } = req.locals.handle;
  const task = await req.models.ScheduledTask.query().where({ uid: id, ownerUid }).first();
  if (!task) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.SCHEDULED_TASK_NOT_FOUND,
    );
  }
  if (task.status !== 'not_started') {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errorConstants.SCHEDULED_TASK_ALREADY_RUNNING,
    );
  }

  await startWorkflow(task.name as WorkflowName, {
    taskQueue: task.queue as TaskQueue,
    workflowId: `${task.uid}.${task.name}.${Date.now()}`,
    args: [task.taskData],
  });
  return task;
};

export default {
  getSchduledTask: httpHandler(getSchduledTask),
  runScheduledTask: httpHandler(runScheduledTask),
};
