import { ApplicationError, httpHandler } from '@app/lib/http';

import { AccessToken } from '@app/models/accessToken';
import { FgaService } from '@ss-libs/ss-component-auth';
import { Request } from 'express';
import { StatusCodes } from 'http-status-codes';
import { WriteInput } from '@ss-libs/ss-component-auth/dist/openfga';
import config from '@app/constants/config';
import errorConstants from '@app/constants/errors';

const newAccessToken = async (req: Request) => {
  const { ownerType, ownerUid } = req.locals.handle;
  const { body } = req;
  const { projects } : { projects: any } = body;
  const trx = await req.sharedKnexDB.transaction();
  try {
    const accessToken = await req.models.AccessToken.createOne(trx, {
      name: body.name,
      days: body.days,
      ownerType,
      ownerUid,
      prefix: config.DEFAULT_ACCESS_TOKEN_PREFIX,
      createdBy: req.locals.user.uid,
    });

    const permissions: WriteInput[] = [
      {
        relation: 'member',
        objectType: ownerType,
        objectId: ownerUid,
        subjectType: 'accessToken',
        subjectId: accessToken.uid,
      },
    ];

    const perms: string[] = [...body.permissions];

    // Check permissions
    if (projects && projects.length) {
      const findProjects = await req.models.Project.query().whereIn('uid', projects).select(['name', 'uid']);
      for (const project of projects) {
        const projectPermissions = perms.map((perm) => ({
          user: `user:${req.locals.user.uid}`,
          object: `project:${project}`,
          relation: perm,
          context: {
            current_project: `project:${project}`,
          },
        }));

        const grantAccess = await req.fga.batchCheck(projectPermissions);
        const permissionDenied = grantAccess.find((p) => !p.allowed);
        if (permissionDenied) {
          const projectName = findProjects.find((p) => p.uid === project)?.name;
          const message = `Project ${projectName} does not have permission ${permissionDenied._request.relation}`;
          throw new ApplicationError(StatusCodes.FORBIDDEN, message);
        }

        for (const p of perms) {
          permissions.push({
            relation: p,
            objectType: 'project',
            objectId: project,
            subjectType: 'accessToken',
            subjectId: accessToken.uid,
          });
        }
      }
    } else if (!projects || !projects.length) {
      const orgPermissions = perms.map((perm) => ({
        user: `user:${req.locals.user.uid}`,
        object: `${ownerType}:${ownerUid}`,
        relation: perm,
        context: {
          current_project: '',
        },
      }));

      const grantAccess = await req.fga.batchCheck(orgPermissions);

      const permissionDenied = grantAccess.find((p) => !p.allowed);

      if (permissionDenied) {
        const message = `does not have permission ${permissionDenied._request.relation}`;
        throw new ApplicationError(StatusCodes.FORBIDDEN, message);
      }

      for (const p of perms) {
        permissions.push({
          relation: p,
          objectType: ownerType,
          objectId: ownerUid,
          subjectType: 'accessToken',
          subjectId: accessToken.uid,
        });
      }
    }
    await req.fga.create(...Object.values(permissions));
    await trx.commit();
    return accessToken;
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const accessTokenIndex = async (req: Request) => {
  const { ownerType, ownerUid } = req.locals.handle;

  const accessTokens = await req.models.AccessToken.findAll(req.sharedKnexDB, {
    ownerType,
    ownerUid,
  });

  for (const t of accessTokens) {
    await attachPermissions(t, ownerType, ownerUid, req.fga);
  }

  return accessTokens;
};

const updateAccessToken = async (req: Request) => {
  const { name } = req.body;
  const { ownerType, ownerUid } = req.locals.handle;

  const [accessToken] = await req.models.AccessToken.query()
    .where({
      uid: req.params.id,
      ownerType,
      ownerUid,
      deletedAt: null,
    })
    .patch({
      name,
      updatedAt: req.sharedKnexDB.fn.now(),
    })
    .returning('*');

  if (!accessToken) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.ACCESS_TOKEN_NOT_FOUND,
    );
  }

  return { success: true };
};

const deleteAccessToken = async (req: Request) => {
  const { ownerType, ownerUid } = req.locals.handle;
  const count = await req.models.AccessToken.delete(req.sharedKnexDB, {
    ownerType,
    ownerUid,
    uid: req.params.id,
  });

  const orgScopePermissions = await req.fga.query(`accessToken:${req.params.id}`, 'org:', '');
  const projectScopePermissions = await req.fga.query(`accessToken:${req.params.id}`, 'project:', '');

  const permissions = [...orgScopePermissions, ...projectScopePermissions].map((t) => {
    const object = t.key.object?.split(':');
    return {
      relation: t.key.relation,
      objectType: object[0],
      objectId: object[1],
      subjectType: 'accessToken',
      subjectId: req.params.id,
    };
  });

  if (permissions.length) { await req.fga.delete(...permissions); }

  return { affectedRows: count };
};

async function attachPermissions(
  token: AccessToken,
  ownerType: string,
  ownerUid: string,
  fga: FgaService,
) {
  const tuples = await fga.query(
    `accessToken:${token.uid}`,
    `${ownerType}:${ownerUid}`,
    '',
  );
  token.permissions = tuples.map((t) => t.key.relation);
}

export default {
  newAccessToken: httpHandler(newAccessToken),
  accessTokenIndex: httpHandler(accessTokenIndex),
  updateAccessToken: httpHandler(updateAccessToken),
  deleteAccessToken: httpHandler(deleteAccessToken),
};
