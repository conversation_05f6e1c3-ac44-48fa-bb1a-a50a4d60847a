import { Attachment } from '@app/models/attachment';
import { Request, Response } from 'express';
import { TestResult } from '@app/models/testResult';
import env from '@app/config/env';
import { httpHandler } from '@app/lib/http';
import i18n from 'i18n';
import { isEmpty } from 'lodash';
import logger from '@app/config/logger';
import { CreateResultDTO } from '@app/types/result';
import { startWorkflow } from '../temporal/client';
import { AttachmentData } from '../temporal/activities/attachment';
/**
 * POST: Create a new test result
 * @param {string} req.params.runUID - UID of the test execution
 * */
const createResult = async (req: Request) => {
  const dto = req.body as CreateResultDTO;
  const execUid: number = req.params.id as any;

  // Start a transaction
  const trx = await req.knexDB.transaction();
  try {
    // Inserting the test result and returning its UID
    const result = await TestResult.create(trx, {
      executionUid: execUid,
      reporterUid: req.locals.user.uid,
      status: dto.status,
      comment: dto.comment,
      stepUid: dto.stepUid,
      tagUids: dto.tagUids,
    });

    trx.commit();
    try {
      const testExecution = result.testExecution!;
      if (testExecution && testExecution.source === 'testrail') {
        startWorkflow('integrationEntitiesWorkflow', {
          taskQueue: 'integration-entities-queue',
          workflowId: `${req.locals.handle.ownerUid}:execution:${Date.now()}`,
          args: [
            {
              tenantUid: req.locals.handle.ownerUid,
              task: 'updateEntity',
              entityType: 'execution',
              data: result,
            },
          ],
        });
      }
    } catch (error) {
      logger.error(`Error updating single entity ${result.uid}:`, error);
    }
    return result;
  } catch (error) {
    // If there's an error, rollback the transaction
    await trx.rollback();
    throw error;
  }
};

/**
 *  GET: Retrieve results for a test execution
 * @param {string} req.params.executionUid - UID of the test execution
 * */
const getResultsByExecution = async (req: Request) => {
  const { executionUid } = req.params;

  const results = await req.models.TestResult.query()
    .where({ testExecutionUid: executionUid })
    .orderBy('createdAt', 'desc')
    .whereNull('testResults.deletedAt')
    .select([
      'testResults.uid as resultUid ',
      'testResults.reporterUid',
      'status',
      'comment',
      'testResults.createdAt as resultCreatedAt',
      'testResults.customFields',
    ])
    .withGraphFetched('attachments')
    .modifyGraph('attachments', (builder) => {
      builder.whereNull('attachments.deletedAt');
    });

  const userIds = [];
  const tagIds = [];
  for (const result of results) {
    userIds.push(result.reporterUid);
    tagIds.push(...(result.customFields?.tagUids ?? []));
  }

  const usersQuery = await req.models.User.query()
    .whereIn('uid', userIds)
    .select(['firstName', 'lastName', 'uid', 'avatar']);

  const users = usersQuery.reduce((acc, user) => {
    acc[user.uid] = {
      firstName: user.firstName,
      lastName: user.lastName,
      avatar: user.avatar,
    };
    return acc;
  }, {});

  const tags = {};
  if (tagIds.length > 0) {
    (
      await req.models.Tag.query()
        .where({ deletedAt: null, systemType: 'tag' })
        .whereIn('uid', tagIds)
        .select('uid', 'name')
    ).forEach((t) => {
      tags[t.uid] = t;
    });
  }

  const baseURL = `${env.BACKEND_URL}${env.API_INTERNAL_ROUTE}/${req.params.handle}`;

  for (const result of results) {
    result.attachments = result.attachments.map(
      (element: any) => ({
        url: `${baseURL}/results/attachments/${element.uid}/object`,
        name: element.name,
        fileType: element.fileType,
        uid: element.uid,
      }) as any,
    );
    result.reporter = users[result.reporterUid];
    result.tags = (result.customFields?.tagUids ?? []).map((t) => tags[t]);
  }
  return results;
};

/**
 * Get: Retrieve a test results related to test case
 * @param {string} req.params.caseUid - UID of the test case
 * @param {number} req.query.pageSize - Number of results per page
 * @param {number} req.query.page - Page number
 */
const getResultsByCase = async (req: Request) => {
  const { caseUid } = req.params;
  const pageSize = parseInt(req.query.pageSize as string) || 10; // Default page size is 10
  const page = parseInt(req.query.page as string) || 1; // Default page number is 1
  const offset = (page - 1) * pageSize;

  const results = await req
    .knexDB('testResults')
    .join(
      '"testExecutions"',
      '"testResults"."executionUid"',
      '=',
      '"testExecutions".uid',
    )
    .join(
      '"testCases"',
      '"testExecutions"."testCaseRef"',
      '=',
      '"testCases".uid',
    )
    .where('"testCases".uid', caseUid)
    .select('"testResults".*')
    .orderBy('"testResults".createdAt', 'desc')
    .limit(pageSize)
    .offset(offset);

  return results;
};

/**
 * PUT: Update a test result
 * @param {string} req.param.resultUid - UID of the test result
 * */
const updateResult = async (req: Request) => {
  const { id: resultUid } = req.params;
  const { status, comment } = req.body;

  const update: Partial<TestResult> = {};
  if (status) update.status = status;
  if (comment) update.comment = comment;

  let result: TestResult;

  const trx = await req.knexDB.transaction();
  try {
    const query = req.models.TestResult.query(trx).findById(resultUid);
    if (isEmpty(update)) {
      result = await query;
    } else {
      result = (await query
        .patch(update)
        .returning('*')) as unknown as TestResult;
    }

    await req.models.TestExecution.query(trx)
      .where({ uid: result.testExecutionUid })
      .patch({ updatedAt: req.knexDB.fn.now() });

    trx.commit();
    return result;
  } catch (err) {
    trx.rollback();
    throw err;
  }
};

const uploadAttachment = async (req: Request) => {
  const { fileType, size, fileName } = req.body;
  const { id: resultUid } = req.params;
  const { ownerUid } = req.locals.handle;
  const creatorUid = req.locals.user?.uid || null;

  const newAttachment = {
    name: fileName,
    ownerUid,
    size,
    fileType,
    mediaType: 'attachment',
    creatorUid,
  };

  const trx = await req.knexDB.transaction();

  try {
    const {
      key,
      signed_url: signedUrl,
      object_url: objectUrl,
      client_headers: clientHeaders,
      uid,
    } = await Attachment.createAttachment(newAttachment, trx);

    await trx('resultAttachments').insert({
      attachmentUid: uid,
      resultUid,
    });

    await trx.commit();

    return {
      uid,
      objectKey: key,
      signedUrl,
      objectUrl,
      clientHeaders,
    };
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const deleteAttachment = async (req: Request, res: Response) => {
  const trx = await req.knexDB.transaction();
  const attachmentId = <string>req.params.id;

  try {
    const attachment = await req.models.Attachment.query(trx).findOne({
      uid: attachmentId,
    });

    if (!attachment) return res.status(404).send();

    const { key } = attachment;
    const { ownerUid } = req.locals.handle;
    const param: AttachmentData = {
      type: 'delete',
      key,
      ownerUid,
    };

    await Promise.all([
      startWorkflow('attachmentWorkflow', {
        taskQueue: 'attachment-queue',
        workflowId: `${ownerUid}:attachment:delete:${Date.now()}`,
        args: [param],
      }),
      Attachment.deleteAttachment(trx, attachmentId, 'resultAttachments'),
    ]);
    await trx.commit();
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

/**
 * DELETE: Delete a test result
 * @param {string} req.params.resultUid - UID of the test result
 * */
const deleteResult = async (req: Request) => {
  const { id: resultUid } = req.params;

  const trx = await req.knexDB.transaction();
  try {
    const testResult = await req.models.TestResult.query(trx)
      .findById(resultUid)
      .patchAndFetchById(resultUid, { deletedAt: req.knexDB.fn.now() });

    const resultAttachments = await Attachment.query(trx)
      .patch({ deletedAt: req.knexDB.fn.now() })
      .whereExists(
        Attachment.relatedQuery('testResults').where(
          'testResults.uid',
          resultUid,
        ),
      )
      .returning('*');

    const { ownerUid } = req.locals.handle;
    resultAttachments.forEach(async (element) => {
      const { key } = element;
      logger.log('info', `Pushing data to worker: Deleting key ${key}`);

      const param: AttachmentData = {
        type: 'delete',
        key,
        ownerUid,
      };

      await startWorkflow('attachmentWorkflow', {
        taskQueue: 'attachment-queue',
        workflowId: `${ownerUid}:attachment:delete:${Date.now()}`,
        args: [param],
      });
    });

    await req.models.TestExecution.query(trx)
      .where({ uid: testResult.testExecutionUid })
      .patch({ updatedAt: req.knexDB.fn.now() });

    await trx.commit();
    return { message: i18n.__('resultDeleted') };
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

export default {
  createResult: httpHandler(createResult),
  getResultsByExecution: httpHandler(getResultsByExecution),
  updateResult: httpHandler(updateResult),
  deleteResult: httpHandler(deleteResult),
  getResultsByCase: httpHandler(getResultsByCase),
  uploadAttachment: httpHandler(uploadAttachment),
  deleteAttachment: httpHandler(deleteAttachment),
};
