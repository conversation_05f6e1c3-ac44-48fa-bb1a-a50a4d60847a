import { ApplicationError, httpHandler } from '@app/lib/http';

import DEFAULT_PREFERENCE from '@app/constants/preference';
import { Knex } from 'knex';
import { Org } from '@app/models/org';
import { Request } from 'express';
import { StatusCodes } from 'http-status-codes';
import { Tenant } from '@app/models/tenant';
import { UniqueViolationError } from 'objection';
import { User } from '@app/models/user';
import _ from 'lodash';
import billing from '@ss-libs/ss-component-payments';
import errors from '@app/constants/errors';
import logger from '@app/config/logger';

/**
 * update handle proporties including name current and owner type
 * @param {Object} req
 * @param {Object} res
 * @return {Object} res
 */
const updateHandle = async (req: Request) => {
  const trx = await req.sharedKnexDB.transaction();
  try {
    const oldName = req.locals.handle.name;
    const owner = req.locals.handle.ownerUid;

    const [handle] = await req.models.Handle.query(trx)
      .where({ name: oldName, ownerUid: owner })
      .patch({ name: req.body.name })
      .returning('*');

    if (!handle) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errors.HANDLE_NOT_FOUND,
      );
    }
    const { ownerType } = req.locals.handle;

    const account = ownerType === 'org'
      ? await req.models.Org.query(trx).findById(owner)
      : await req.models.User.query(trx).findById(owner);

    if (account.stripeId) {
      await billing
        .updateCustomer({
          stripeCustomerId: account.stripeId,
          accountType: ownerType,
          handle: handle.name,
          uid: owner,
        })
        .then(() => {
          logger.info(
            `successfully updated stripe details for "${handle.name}"`,
          );
        });
    }

    await trx.commit();
    return { handle };
  } catch (err) {
    await trx.rollback();
    if (err instanceof UniqueViolationError) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        errors.HANDLE_DUPLICATED,
      );
    }
    throw err;
  }
};
/**
 * search users using user name and email
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {String} request.query.name
 * TODO should be moved to Elastic Search as it is resource intensive
 */
const handleInUse = async (req: Request) => {
  const { handle: name } = req.params;

  const handle = await req.models.Handle.query()
    .select('uid', 'name', 'ownerUid', 'ownerType', 'current')
    .where('name', '=', name);
  if (handle.length && handle[0].current) {
    throw new ApplicationError(StatusCodes.CONFLICT, errors.HANDLE_FOUND);
  }
  return { message: 'Handle is not in use', handleInUse: false };
};

const getHandleStatus = async (req: Request) => {
  const { handle } = req.locals;
  const tenant: Tenant = (await req.models.Handle.relatedQuery('tenant')
    .for(handle.uid)
    .first()) as any;
  return { handle, setupStatus: tenant.setupStatus };
};

const getPreferences = async (req: Request) => {
  const account = await getAccount(req);
  const preferences = _.isEmpty(account.preferences)
    ? DEFAULT_PREFERENCE
    : account.preferences;
  return preferences;
};

const updatePreference = async (req: Request) => {
  const trx = await req.sharedKnexDB.transaction();
  try {
    let account = await getAccount(req, trx);
    account = await account.$query(trx).patchAndFetch(req.body);
    await trx.commit();
    return account.preferences;
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

async function getAccount(req: Request, trx?: Knex.Transaction) {
  const { ownerType, ownerUid } = req.locals.handle;

  let account: User | Org;

  if (ownerType === 'user') {
    account = await req.models.User.query(trx).findById(ownerUid);
  } else if (ownerType === 'org') {
    account = await req.models.Org.query(trx).findById(ownerUid);
  } else {
    throw new ApplicationError(StatusCodes.UNPROCESSABLE_ENTITY, null);
  }

  return account;
}

export default {
  updateHandle: httpHandler(updateHandle),
  handleInUse: httpHandler(handleInUse),
  getHandleStatus: httpHandler(getHandleStatus),
  getPreferences: httpHandler(getPreferences),
  updatePreference: httpHandler(updatePreference),
};
