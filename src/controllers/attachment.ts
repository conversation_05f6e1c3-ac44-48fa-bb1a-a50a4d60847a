import config from '@app/constants/config';
import { Request, Response } from 'express';
import errors from '@app/constants/errors';
import { storageProvider } from '@ss-libs/ss-component-media';
import { ApplicationError, httpHandler } from '@app/lib/http';
import { StatusCodes } from 'http-status-codes';
import { Attachment } from '@app/models/attachment';
import { GetAttachmentDTO } from '@app/types/attachment';

const getAttachment = async (req: Request, res: Response) => {
  const attachmentId = req.params.id;
  const { ownerUid } = req.locals.handle;

  const dto = req.query as GetAttachmentDTO;

  let attachment = await req.models.Attachment.query().where((builder) => {
    if (dto.external && dto.source) {
      builder.where('externalId', attachmentId);
      builder.where('source', dto.source);
    } else {
      builder.where('uid', attachmentId);
    }
  }).first();

  if (!attachment) {
    attachment = await req.models.Attachment.query()
      .where('externalId', attachmentId)
      .where('source', 'pinata')
      .first();
  }

  if (!attachment) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errors.ATTACHMENT_NOT_FOUND,
    );
  }

  const { key } = attachment;

  const url = await storageProvider(ownerUid).fetch(key);

  if (dto.download) {
    return url;
  }

  res.header('Cache-Control', 'max-age=3600');

  return res.redirect(302, url);
};

const checkPotentialMigrationSize = async (req: Request, res: Response) => {
  try {
    const trx = await req.knexDB.transaction();
    const totalSize = await Attachment.getTotalSize(trx);
    await trx.commit();
    return res.status(StatusCodes.OK).json({
      totalSize,
      overSized: totalSize > config.MAX_MIGRATION_BUCKET_SIZE,
    });
  } catch (error) {
    throw new ApplicationError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      errors.INTERNAL_SERVER_ERROR,
    );
  }
};

const cleanupFailedUpload = async (req: Request) => {
  const trx = await req.knexDB.transaction();
  const { id, type } = req.params;
  const tuplesMapping = {
    cases: 'case',
    projects: 'project',
    results: 'result',
  };

  const tableName = `${tuplesMapping[type]}Attachments`;

  await Attachment.deleteAttachment(trx, id, tableName);
};

export default {
  getAttachment: httpHandler(getAttachment),
  cleanupFailedUpload: httpHandler(cleanupFailedUpload),
  checkPotentialMigrationSize: httpHandler(checkPotentialMigrationSize),
};
