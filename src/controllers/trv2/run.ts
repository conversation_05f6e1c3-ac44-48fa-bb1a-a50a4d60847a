import { ApplicationError, httpHandler } from '@app/lib/http';

import { FGARawWrite } from '@ss-libs/ss-component-auth';
import { Request } from 'express';
import { StatusCodes } from 'http-status-codes';
import errors from '@app/constants/errors';
import logger from '@app/config/logger';
import preferencesService from '@app/models/preferences';
import env from '@app/config/env';
import { formatCaseCount, formatConfigs } from '@app/utils/formatRunsFields';
import { buildLink } from '@app/utils/buildTestrailLinks';
import { startWorkflow } from '@app/temporal/client';
import { UpdateRunStateDTO } from '@app/temporal/activities/run';

const getTestRuns = async (req: Request) => {
  const projectUid = req.query._id as string;
  if (!projectUid) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: project_id is a required field');
  }
  const project = await req.models.Project.query()
    .where({
      uid: projectUid,
      deletedAt: null,
    })
    .first();
  if (!project) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, 'Field: project_id is invalid');
  }
  const baseURL = `${env.FRONTEND_URL}/${req.locals.handle.name}`;
  const suite = await req.models.Tag.query()
    .where({
      projectUid,
      systemType: 'folder',
      parentUid: null,
      deletedAt: null,
    })
    .first();
  if (!suite) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, 'Field: project_id is invalid');
  }
  const filters = req.query._filters as any;
  const {
    created_after: createdAfter,
    created_before: createdBefore,
    is_completed: isCompleted,
    limit = 250,
    offset = 0,
    milestone_id: milestoneId,
  } = filters;

  const runsQuery = req.models.TestRun.query()
    .where({
      projectUid,
      systemType: 'run',
      deletedAt: null,
    })
    .withGraphFetched('testMilestones')
    .withGraphFetched('testPlans');

  const countQuery = req.models.TestRun.query()
    .where({
      projectUid,
      systemType: 'run',
      deletedAt: null,
    }).count('uid as count');

  if (createdAfter) {
    runsQuery.where('createdAt', '>=', new Date(createdAfter).toISOString());
    countQuery.where('createdAt', '>=', new Date(createdAfter).toISOString());
  }
  if (createdBefore) {
    runsQuery.where('createdAt', '<=', new Date(createdBefore).toISOString());
    countQuery.where('createdAt', '<=', new Date(createdBefore).toISOString());
  }
  if (isCompleted) {
    if (parseInt(isCompleted) === 1) {
      runsQuery.whereNotNull('archivedAt');
      countQuery.whereNotNull('archivedAt');
    } else {
      runsQuery.whereNull('archivedAt');
      countQuery.whereNull('archivedAt');
    }
  }
  if (milestoneId) {
    const milestoneRuns = await req.models.TestMilestoneRun.query()
      .where({
        milestoneUid: milestoneId,
        deletedAt: null,
      });
    runsQuery.whereIn('uid', milestoneRuns.map((mr) => mr.runUid));
    countQuery.whereIn('uid', milestoneRuns.map((mr) => mr.runUid));
  }
  const rawData = await runsQuery.clone();
  const planEntries = await req.models.TestPlanRun.query()
    .whereIn('runUid', rawData.map((r) => r.uid));
  runsQuery.whereNotIn('uid', planEntries.map((pe) => pe.runUid));
  const runs = await runsQuery.limit(limit).offset(offset);
  countQuery.whereNotIn('uid', planEntries.map((pe) => pe.runUid));
  const totalCount = await countQuery.first();
  for (const run of runs) {
    await req.models.TestRun.populateConfig(req.knexDB, run);
  }
  // fetch user preferences
  const { statusColors } = await preferencesService.findOne(
    req.sharedKnexDB,
    req.locals.handle.ownerType,
    req.locals.handle.ownerUid,
  );
  const runsData = runs.map((r) => ({
    id: r.uid,
    suite_id: suite.uid,
    name: r.name,
    description: r.description,
    milestone_id: r.testMilestones.length > 0 ? r.testMilestones[0].uid : null,
    assignedto_id: null,
    include_all: false,
    is_completed: !!r.archivedAt,
    completed_on: r.archivedAt ? new Date(r.archivedAt).getTime() : null,
    ...formatConfigs(r.customFields.configs, r.configs),
    ...formatCaseCount(r.customFields.frequency, statusColors),
    project_id: projectUid,
    plan_id: r.testPlans.length > 0 ? r.testPlans[0].uid : null,
    updated_on: new Date(r.updatedAt).getTime(),
    refs: null,
    dataset_id: null,
    created_by: null,
    url: `${baseURL}/${project.key}/runs/${r.uid}`,
  }));
  const total = parseInt((totalCount as any).count);
  const links = {
    next: Number(offset) + Number(limit) >= total ? null : buildLink('api/v2/get_runs', Number(offset) + Number(limit), Number(limit), projectUid, filters),
    prev: Number(offset) <= 0 ? null : buildLink('api/v2/get_runs', Math.max(0, Number(offset) - Number(limit)), Number(limit), projectUid, filters),
  };
  const pagination: any = {
    offset: parseInt(offset),
    limit: parseInt(limit),
    size: runsData.length,
    _links: links,
    runs: runsData,
  };
  return pagination;
};

const getTestRun = async (req: Request) => {
  const runId = req.query._id as string;
  if (!runId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: run_id is a required field');
  }
  const run = await req.models.TestRun.query()
    .where({
      uid: runId,
      systemType: 'run',
      deletedAt: null,
    })
    .withGraphFetched('testMilestones')
    .withGraphFetched('testPlans')
    .first();

  if (!run) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: run_id is invalid');
  }
  // populate configs
  await req.models.TestRun.populateConfig(req.knexDB, run);
  const baseURL = `${env.FRONTEND_URL}/${req.locals.handle.name}`;
  const suite = await req.models.Tag.query()
    .where({
      projectUid: run.projectUid,
      systemType: 'folder',
      parentUid: null,
      deletedAt: null,
    })
    .first();
  const { statusColors } = await preferencesService.findOne(
    req.sharedKnexDB,
    req.locals.handle.ownerType,
    req.locals.handle.ownerUid,
  );
  const project = await req.models.Project.query()
    .where({
      uid: run.projectUid,
      deletedAt: null,
    })
    .first();

  return {
    id: run.uid,
    suite_id: suite.uid,
    name: run.name,
    description: run.description,
    milestone_id: run.testMilestones.length > 0 ? run.testMilestones[0].uid : null,
    assignedto_id: null,
    include_all: false,
    is_completed: !!run.archivedAt,
    completed_on: run.archivedAt ? new Date(run.archivedAt).getTime() : null,
    ...formatConfigs(run.customFields.configs, run.configs),
    ...formatCaseCount(run.customFields.frequency, statusColors),
    project_id: run.projectUid,
    plan_id: run.testPlans.length > 0 ? run.testPlans[0].uid : null,
    updated_on: new Date(run.updatedAt).getTime(),
    refs: null,
    dataset_id: null,
    created_by: null,
    url: `${baseURL}/${project.key}/runs/${run.uid}`,
  };
};

const createTestRun = async (req: Request) => {
  const projectUid = req.query._id as string;
  if (!projectUid) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: project_id is a required field');
  }
  const project = await req.models.Project.query()
    .where({
      uid: projectUid,
      deletedAt: null,
    })
    .first();
  if (!project) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: project_id is invalid');
  }
  const {
    name = 'Master',
    description,
    include_all: includeAll = true, // default to true
    case_ids: caseIds,
    milestone_id: milestoneId,
  } = req.body;
  const trx = await req.knexDB.transaction();
  try {
    const { ownerType, ownerUid } = req.locals.handle;
    const caseUids = [];
    if (includeAll) {
      const cases = await req.models.TestCase.query()
        .where({
          projectUid,
          active: true,
          deletedAt: null,
        });
      caseUids.push(...cases.map((c) => c.uid));
    } else {
      caseUids.push(...caseIds);
    }
    const defaults = await preferencesService.getDefaults(
      req.sharedKnexDB,
      ownerType,
      ownerUid,
    );

    const { testRun, executions } = await req.models.TestRun.newRun(
      trx,
      {
        priority: defaults.testRun?.priority,
        status: defaults.testRun?.status,
        name,
        projectUid: Number(projectUid),
        description,
        dueAt: null,
        tagUids: [],
      },
      {
        caseUids,
        milestoneUids: milestoneId ? [milestoneId] : [],
        execPriority: defaults.testCase?.priority,
        execStatus: defaults.testCase?.status,
      },
    );
    const fgaWrites: FGARawWrite[] = [
      {
        objectType: 'run',
        objectId: testRun.uid,
        relation: 'owner',
        subjectType: ownerType,
        subjectId: ownerUid,
      },
      ...executions.map((e) => ({
        objectType: 'execution',
        objectId: e.uid,
        relation: 'owner',
        subjectType: ownerType,
        subjectId: ownerUid,
      })),
    ];
    await req.fga.create(...fgaWrites);
    await trx.commit();
    await req.models.TestRun.populateConfig(req.knexDB, testRun);
    const param: UpdateRunStateDTO = {
      runUids: [testRun.uid],
      ownerType,
      ownerUid,
    };
    await startWorkflow('updateRunWorkflow', {
      taskQueue: 'update-run-queue',
      workflowId: `update.run.${testRun.uid}.${Date.now()}`,
      args: [param],
    });

    const baseURL = `${env.FRONTEND_URL}/${req.locals.handle.name}`;
    const suite = await req.models.Tag.query()
      .where({
        projectUid,
        systemType: 'folder',
        parentUid: null,
        deletedAt: null,
      })
      .first();
    if (!suite) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Field: project_id is invalid');
    }
    // fetch user preferences
    const { statusColors } = await preferencesService.findOne(
      req.sharedKnexDB,
      req.locals.handle.ownerType,
      req.locals.handle.ownerUid,
    );
    return {
      id: testRun.uid,
      suite_id: suite.uid,
      name: testRun.name,
      description: testRun.description,
      milestone_id: milestoneId,
      assignedto_id: null,
      include_all: false,
      is_completed: !!testRun.archivedAt,
      completed_on: testRun.archivedAt ? new Date(testRun.archivedAt).getTime() : null,
      ...formatConfigs(testRun.customFields.configs, testRun.configs),
      ...formatCaseCount(testRun.customFields.frequency, statusColors),
      project_id: projectUid,
      plan_id: null,
      updated_on: new Date(testRun.updatedAt).getTime(),
      refs: null,
      dataset_id: null,
      created_by: null,
      url: `${baseURL}/${project.key}/runs/${testRun.uid}`,
    };
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const updateTestRun = async (req: Request) => {
  const runId = req.query._id as string;
  if (!runId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: run_id is a required field');
  }
  const run = await req.models.TestRun.query()
    .where({
      uid: runId,
      systemType: 'run',
      deletedAt: null,
    })
    .first();
  if (!run) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: run_id is invalid');
  }
  const {
    name,
    description,
    include_all: includeAll,
    case_ids: caseIds,
    milestone_id: milestoneId,
  } = req.body;
  const caseUids = [];
  if (includeAll) {
    const cases = await req.models.TestCase.query()
      .where({
        projectUid: run.projectUid,
        active: true,
        deletedAt: null,
      });
    caseUids.push(...cases.map((c) => c.uid));
  } else if (caseIds?.length > 0) {
    caseUids.push(...caseIds);
  }
  // TODO: decide what to update for caseUids
  const trx = await req.knexDB.transaction();
  try {
    const run = await req.models.TestRun.updateOne(trx, +runId, {
      name,
      description,
      addMilestoneUids: milestoneId ? [milestoneId] : [],
    });
    if (!run) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, errors.RUN_NOT_FOUND);
    }
    if (caseUids.length > 0) {
      const runExecutions = await req.models.TestExecution.query()
        .where({
          testRunUid: run.uid,
          deletedAt: null,
        });
      const caseUidsToRemove = runExecutions.map((e) => e.testCaseUid);
      const defaults = await preferencesService.getDefaults(
        req.sharedKnexDB,
        req.locals.handle.ownerType,
        req.locals.handle.ownerUid,
      );
      await req.models.TestRun.addCases(
        trx,
        run.uid,
        caseIds,
        defaults.testCase?.status,
        defaults.testCase?.priority,
      );
      if (caseUidsToRemove?.length > 0) {
        await req.models.TestRun.removeExecs(
          trx,
          run.uid,
          caseUidsToRemove,
        );
      }
    }
    await trx.commit();
    const updatedRun = await req.models.TestRun.query()
      .where({
        uid: runId,
        systemType: 'run',
        deletedAt: null,
      })
      .withGraphFetched('testMilestones')
      .withGraphFetched('testPlans')
      .first();
    await req.models.TestRun.populateConfig(req.knexDB, updatedRun);
    const param: UpdateRunStateDTO = {
      runUids: [run.uid],
      ownerType: req.locals.handle.ownerType,
      ownerUid: req.locals.handle.ownerUid,
    };
    await startWorkflow('updateRunWorkflow', {
      taskQueue: 'update-run-queue',
      workflowId: `update.run.${run.uid}.${Date.now()}`,
      args: [param],
    });
    const baseURL = `${env.FRONTEND_URL}/${req.locals.handle.name}`;
    const suite = await req.models.Tag.query()
      .where({
        projectUid: run.projectUid,
        systemType: 'folder',
        parentUid: null,
        deletedAt: null,
      })
      .first();
    const { statusColors } = await preferencesService.findOne(
      req.sharedKnexDB,
      req.locals.handle.ownerType,
      req.locals.handle.ownerUid,
    );
    const project = await req.models.Project.query()
      .where({
        uid: run.projectUid,
        deletedAt: null,
      })
      .first();
    return {
      id: updatedRun.uid,
      suite_id: suite.uid,
      name: updatedRun.name,
      description: updatedRun.description,
      milestone_id: updatedRun.testMilestones.length > 0 ? updatedRun.testMilestones[0].uid : null,
      assignedto_id: null,
      ...formatConfigs(updatedRun.customFields.configs, updatedRun.configs),
      ...formatCaseCount(updatedRun.customFields.frequency, statusColors),
      project_id: run.projectUid,
      plan_id: updatedRun.testPlans.length > 0 ? updatedRun.testPlans[0].uid : null,
      updated_on: new Date(updatedRun.updatedAt).getTime(),
      refs: null,
      dataset_id: null,
      created_by: null,
      url: `${baseURL}/${project.key}/runs/${updatedRun.uid}`,
    };
  } catch (err) {
    logger.info('Failed to update test run');
    await trx.rollback();
    throw err;
  }
};

const closeTestRun = async (req: Request) => {
  const runId = req.query._id as string;
  if (!runId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: run_id is a required field');
  }
  const run = await req.models.TestRun.updateOne(req.knexDB, +runId, {
    archive: true,
  });
  const baseURL = `${env.FRONTEND_URL}/${req.locals.handle.name}`;
  const suite = await req.models.Tag.query()
    .where({
      projectUid: run.projectUid,
      systemType: 'folder',
      parentUid: null,
      deletedAt: null,
    })
    .first();
  const { statusColors } = await preferencesService.findOne(
    req.sharedKnexDB,
    req.locals.handle.ownerType,
    req.locals.handle.ownerUid,
  );
  const project = await req.models.Project.query()
    .where({
      uid: run.projectUid,
      deletedAt: null,
    })
    .first();
  const updatedRun = await req.models.TestRun.query()
    .where({
      uid: runId,
      systemType: 'run',
      deletedAt: null,
    })
    .withGraphFetched('testMilestones')
    .withGraphFetched('testPlans')
    .first();
  await req.models.TestRun.populateConfig(req.knexDB, updatedRun);
  return {
    id: updatedRun.uid,
    suite_id: suite.uid,
    name: run.name,
    description: run.description,
    milestone_id: run.testMilestones.length > 0 ? run.testMilestones[0].uid : null,
    assignedto_id: null,
    ...formatConfigs(run.customFields.configs, run.configs),
    ...formatCaseCount(run.customFields.frequency, statusColors),
    project_id: run.projectUid,
    plan_id: run.testPlans.length > 0 ? run.testPlans[0].uid : null,
    updated_on: new Date(run.updatedAt).getTime(),
    refs: null,
    dataset_id: null,
    created_by: null,
    url: `${baseURL}/${project.key}/runs/${run.uid}`,
  };
};

const deleteTestRun = async (req: Request) => {
  const runid = req.query._id as string;
  if (!runid) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: run_id is a required field');
  }
  const run = await req.models.TestRun.query()
    .where({
      uid: runid,
      systemType: 'run',
      deletedAt: null,
    })
    .first();
  if (!run) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: run_id is invalid');
  }
  const runUids = [run.uid];
  const { soft } = req.body;
  if (soft) {
    const executions = await req.models.TestExecution.query()
      .where({
        runUid: run.uid,
        deletedAt: null,
      });
    const results = await req.models.TestResult.query()
      .where({
        testExecutionUid: executions.map((e) => e.uid),
        deletedAt: null,
      });
    return {
      tests: executions.length,
      results: results.length,
    };
  }
  const testRuns = await req.models.TestRun.deleteByIds(
    req.knexDB,
    runUids,
  );
  const param: UpdateRunStateDTO = {
    runUids: testRuns,
    ownerType: req.locals.handle.ownerType,
    ownerUid: req.locals.handle.ownerUid,
  };
  await startWorkflow('updateRunWorkflow', {
    taskQueue: 'update-run-queue',
    workflowId: `update.run.${testRuns}.${Date.now()}`,
    args: [param],
  });
  return {
    message: 'Test runs have been deleted successfully',
  };
};

export default {
  getTestRuns: httpHandler(getTestRuns),
  getTestRun: httpHandler(getTestRun),
  createTestRun: httpHandler(createTestRun),
  updateTestRun: httpHandler(updateTestRun),
  closeTestRun: httpHandler(closeTestRun),
  deleteTestRun: httpHandler(deleteTestRun),
};
