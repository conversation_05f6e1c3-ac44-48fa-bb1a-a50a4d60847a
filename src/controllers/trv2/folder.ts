import { ApplicationError, httpHandler } from '@app/lib/http';
import { Request } from 'express';
import { StatusCodes } from 'http-status-codes';
import env from '@app/config/env';
import { kebabCase } from 'lodash';
import { buildLink } from '@app/utils/buildTestrailLinks';

const getProjectSuites = async (req: Request) => {
  const projectId = req.query._id;
  if (!projectId) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field :project_id is a required field.',
    );
  }

  const foldersQuery = req.models.Tag.query()
    .where({
      systemType: 'folder',
      'tags.deletedAt': null,
      'tags.projectUid': projectId,
      'tags.parentUid': null,
    }).orderBy('name', 'ASC');

  const folders = await foldersQuery;
  const foldersResponse = folders.map((folder) => ({
    id: folder.uid,
    name: folder.name,
    description: folder.description,
    project_id: folder.projectUid,
    is_master: true,
    is_baseline: false,
    is_completed: false,
    completed_on: null,
    url: `${env.FRONTEND_URL}/${req.locals.handle.name}/${folder.projectUid}/cases`,
  }));
  return foldersResponse;
};

const getSuite = async (req: Request) => {
  const id = req.query._id;
  if (!id) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field :id is a required field.',
    );
  }

  const folder = await req.models.Tag.query()
    .where({
      systemType: 'folder',
      parentUid: null,
      uid: id,
      deletedAt: null,
    })
    .first();

  if (!folder) throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :suite_id is not a valid test suite');

  return {
    id: folder.uid,
    name: folder.name,
    description: folder.description,
    project_id: folder.projectUid,
    is_master: folder.parentUid === null,
    is_baseline: false,
    is_completed: false,
    completed_on: null,
    url: `${env.FRONTEND_URL}/${req.locals.handle.name}/${folder.projectUid}/cases`,
  };
};

const addSuite = async () => {
  throw new ApplicationError(StatusCodes.FORBIDDEN, 'This operation is not permitted because this project only supports a single test suite.');
};

const updateSuite = async (req: Request) => {
  const id = req.query._id as string;
  if (!id) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :id is a required field.');
  }

  const folder = await req.models.Tag.query()
    .where({
      systemType: 'folder',
      parentUid: null,
      uid: id,
      deletedAt: null,
    })
    .first();

  if (!folder) throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :suite_id is not a valid test suite');
  const { name, description } = req.body;
  const updateData:any = {
    name,
    description,
  };
  if (name) {
    updateData.slug = kebabCase(name);
    const existing = await req.models.Tag.query()
      .where({
        slug: updateData.slug,
        systemType: 'folder',
        parentUid: folder.parentUid,
      })
      .whereNot('uid', id)
      .first();
    if (existing) {
      throw new ApplicationError(StatusCodes.CONFLICT, 'A folder with this name already exists in this directory.');
    }
  }

  await folder.$query()
    .patch(updateData)
    .where({
      uid: id,
      deletedAt: null,
    })
    .first();

  return {
    id: folder.uid,
    name: folder.name,
    description: folder.description,
    project_id: folder.projectUid,
    is_master: folder.parentUid === null,
    is_baseline: false,
    is_completed: false,
    completed_on: null,
    url: `${env.FRONTEND_URL}/${req.locals.handle.name}/${folder.projectUid}/cases`,
  };
};

const deleteSuite = async () => {
  throw new ApplicationError(StatusCodes.FORBIDDEN, 'This operation is not permitted because this project only supports a single test suite.');
};

const getSections = async (req: Request) => {
  const id = req.query._id as string;
  if (!id) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :project_id is a required field.');
  }

  const suite = await req.models.Tag.query()
    .where({
      systemType: 'folder',
      deletedAt: null,
      projectUid: id,
      parentUid: null,
    })
    .first();

  if (!suite) throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :project_id is not a valid test suite');

  const filters = req.query._filters as any;
  const {
    limit = 250,
    offset = 0,
  } = filters;

  const folders = await req.models.Tag.query()
    .where({
      systemType: 'folder',
      deletedAt: null,
      projectUid: id,
    })
    .whereNotNull('parentUid')
    .orderBy('parentUid', 'ASC')
    .orderBy('name', 'ASC')
    .offset(Number(offset))
    .limit(Number(limit));

  const total = await req.models.Tag.query()
    .where({
      systemType: 'folder',
      deletedAt: null,
      projectUid: id,
    })
    .whereNotNull('parentUid')
    .count()
    .first();

  const totalCount = Number((total as any).count);

  const sections: any[] = [];
  const seenFolders: Set<number> = new Set(); // Set to track folders we've already added

  let currentDisplayOrder = 0;

  const assignDepthAndOrder = (parentUid: number | null, parentDepth: number): void => {
    const children = folders.filter((folder) => folder.parentUid === parentUid);

    const parentFolder = folders.find((folder) => folder.uid === parentUid);
    if (parentFolder && !seenFolders.has(parentFolder.uid)) { // Only add if not already added
      sections.push({
        id: parentFolder.uid,
        suite_id: parentFolder.projectUid,
        name: parentFolder.name,
        description: parentFolder.description,
        parent_id: parentFolder.parentUid === suite.uid ? null : parentFolder.parentUid,
        display_order: currentDisplayOrder++,
        depth: parentDepth,
      });
      seenFolders.add(parentFolder.uid); // Mark as seen
    }

    for (const child of children) {
      assignDepthAndOrder(child.uid, parentDepth + 1);
    }
  };

  const rootFolders = folders.sort((a, b) => {
    if (a.parentUid === b.parentUid) {
      return a.name.localeCompare(b.name);
    }
    return a.parentUid - b.parentUid;
  });

  for (const root of rootFolders) {
    assignDepthAndOrder(root.uid, 0);
  }

  const links = {
    next: Number(offset) + Number(limit) >= totalCount ? null : buildLink('api/v2/get_sections', Number(offset) + Number(limit), Number(limit), id, filters),
    prev: Number(offset) <= 0 ? null : buildLink('api/v2/get_sections', Math.max(0, Number(offset) - Number(limit)), Number(limit), id, filters),
  };

  return {
    offset: Number(offset),
    limit: Number(limit),
    size: sections.length,
    _links: links,
    sections,
  };
};

const getSection = async (req: Request) => {
  const id = req.query._id as string;
  if (!id) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :section_id is a required field.');
  }

  const folder = await req.models.Tag.query()
    .where({
      systemType: 'folder',
      deletedAt: null,
      uid: id,
    })
    .whereNotNull('parentUid')
    .first();

  if (!folder) throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :section_id is not a valid test section');

  const parent = await req.models.Tag.query()
    .where({
      systemType: 'folder',
      deletedAt: null,
      projectUid: folder.projectUid,
    })
    .whereNull('parentUid')
    .first();

  return {
    id: folder.uid,
    name: folder.name,
    description: folder.description,
    suite_id: parent?.uid,
    parent_id: folder.parentUid === parent?.uid ? null : folder.parentUid,
    display_order: null,
    depth: null,
  };
};

const addSection = async (req: Request) => {
  const { name, parent_id: parentId, description } = req.body;
  const projectId = req.query._id as string;
  if (!projectId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :project_id is a required field.');
  }
  if (!name) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :name is a required field.');
  }
  const suite = await req.models.Tag.query()
    .where({
      systemType: 'folder',
      deletedAt: null,
      projectUid: projectId,
      parentUid: null,
    })
    .first();
  if (!suite) throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :project_id is not a valid test suite');

  if (parentId) {
    const parent = await req.models.Tag.query()
      .where({
        systemType: 'folder',
        deletedAt: null,
        uid: parentId,
      })
      .whereNotNull('parentUid')
      .first();
    if (!parent) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'Field :parent_id is not a valid section.',
      );
    }
    if (parent.projectUid !== suite.projectUid) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'Field :parent_id must be of the same project.',
      );
    }
  }

  const parentUid = parentId || suite.uid;
  const sectionData:any = {
    name,
    description,
    slug: kebabCase(name),
    parentUid,
    projectUid: projectId,
    entityTypes: ['cases'],
    systemType: 'folder',
  };
  try {
    const newSection = await req.models.Tag.query().insert(sectionData);
    return {
      id: newSection.uid,
      name: newSection.name,
      description: newSection.description,
      suite_id: suite.uid,
      parent_id: parentUid === suite.uid ? null : parentUid,
      display_order: null,
      depth: null,
    };
  } catch (error) {
    throw new ApplicationError(StatusCodes.INTERNAL_SERVER_ERROR, 'Failed to create section.');
  }
};
const moveSection = async (req: Request) => {
  const { parent_id: parentId } = req.body;

  const sectionId = req.query._id as string;
  if (!sectionId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :section_id is a required field.');
  }
  const section = await req.models.Tag.query()
    .where({
      systemType: 'folder',
      deletedAt: null,
      uid: sectionId,
    })
    .whereNotNull('parentUid')
    .first();
  if (!section) throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :section_id is not a valid test section');
  const suite = await req.models.Tag.query()
    .where({
      systemType: 'folder',
      deletedAt: null,
      parentUid: null,
      projectUid: section.projectUid,
    })
    .first();
  if (!suite) throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :section_id is not a valid test section');
  if (parentId) {
    const parent = await req.models.Tag.query()
      .where({
        systemType: 'folder',
        deletedAt: null,
        uid: parentId,
      })
      .whereNotNull('parentUid')
      .first();
    if (!parent) throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :parent_id is not a valid test section');
    if (parent.projectUid !== section.projectUid) throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :parent_id must be of the same project');

    // validate the parentid so it is not same or any nested child of the section with recursive check
    const validateParent = async (childId: number, parentId: number): Promise<boolean> => {
      const child = await req.models.Tag.query()
        .where({
          systemType: 'folder',
          deletedAt: null,
          uid: childId,
        })
        .first();

      if (!child) return false; // If child doesn't exist, it's invalid

      if (child.parentUid === parentId) return false; // Parent cannot be the same as the parentId

      if (child.parentUid === null) return true; // If we reached the root (null parent), the parent is valid

      // Recurse to check the parent of the current child
      return validateParent(child.parentUid, parentId);
    };
    const isValid = section.uid !== parent.uid && await validateParent(parent.uid, section.uid);
    if (!isValid) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'Invalid parent_id or after_id. All section IDs must exist and be in the same project and suite. The parent_id may not be a child section of the section being moved.',
      );
    }
    await section.$query()
      .patch({
        parentUid: parent.uid,
      })
      .where({
        uid: sectionId,
      });
    return {
      id: section.uid,
      name: section.name,
      suite_id: suite.uid,
      description: section.description,
      parent_id: parent.uid,
      display_order: null,
      depth: null,
    };
  }

  await section.$query()
    .patch({
      parentUid: suite.uid,
    })
    .where({
      uid: sectionId,
    });
  return {
    id: section.uid,
    suite_id: suite.uid,
    description: section.description,
    parent_id: null,
    display_order: null,
    depth: null,
  };
};

const updateSection = async (req: Request) => {
  const { name, description } = req.body;
  const sectionId = req.query._id as string;
  if (!sectionId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :section_id is a required field.');
  }
  const section = await req.models.Tag.query()
    .where({
      systemType: 'folder',
      deletedAt: null,
      uid: sectionId,
    })
    .whereNotNull('parentUid')
    .first();
  if (!section) throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :section_id is not a valid test section');
  const updateData:any = {};
  if (name) {
    updateData.name = name;
    updateData.slug = kebabCase(name);
  }
  if (description) {
    updateData.description = description;
  }
  await section.$query()
    .patch(updateData)
    .where({
      uid: sectionId,
    });
  const updatedSection = await req.models.Tag.query()
    .where({
      uid: sectionId,
    })
    .first();
  const suite = await req.models.Tag.query()
    .where({
      systemType: 'folder',
      deletedAt: null,
      parentUid: null,
      projectUid: updatedSection.projectUid,
    })
    .first();
  return {
    id: updatedSection.uid,
    name: updatedSection.name,
    description: updatedSection.description,
    suite_id: suite.uid,
    parent_id: updatedSection.parentUid === suite.uid ? null : updatedSection.parentUid,
    display_order: null,
    depth: null,
  };
};

const deleteSection = async (req: Request) => {
  const sectionId = req.query._id as string;
  const { soft } = req.body;
  const section = await req.models.Tag.query()
    .where({
      systemType: 'folder',
      deletedAt: null,
      uid: sectionId,
    })
    .whereNotNull('parentUid')
    .first();
  if (!section) throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :section_id is not a valid test section');
  if (soft && parseInt(soft) === 1) {
    const caseCount = await req.models.TestCase.query()
      .where({
        parentUid: sectionId,
        deletedAt: null,
      })
      .count()
      .first();
    return parseInt((caseCount as any).count);
  }
  await section.$query()
    .patch({
      deletedAt: req.knexDB.fn.now(),
      slug: null,
    })
    .where({
      uid: sectionId,
    });
};

export default {
  getProjectSuites: httpHandler(getProjectSuites),
  getSuite: httpHandler(getSuite),
  addSuite: httpHandler(addSuite),
  updateSuite: httpHandler(updateSuite),
  deleteSuite: httpHandler(deleteSuite),
  getSections: httpHandler(getSections),
  getSection: httpHandler(getSection),
  addSection: httpHandler(addSection),
  updateSection: httpHandler(updateSection),
  deleteSection: httpHandler(deleteSection),
  moveSection: httpHandler(moveSection),
};
