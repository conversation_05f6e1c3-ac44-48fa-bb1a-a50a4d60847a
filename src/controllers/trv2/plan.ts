/* eslint-disable @typescript-eslint/quotes */
import { ApplicationError, httpHandler } from '@app/lib/http';

import { Request } from 'express';
import { StatusCodes } from 'http-status-codes';
import { getConfigOptions, TestPlan } from '@app/models/testPlan';
import errorConstants from '@app/constants/errors';
import { ConfigurationDTO } from '@app/types/plan';
import { FGARawWrite } from '@ss-libs/ss-component-auth';
import preferencesService from '@app/models/preferences';
import { formatCaseCount } from '@app/utils/formatRunsFields';
import env from '@app/config/env';
import { buildLink } from '@app/utils/buildTestrailLinks';
import { generateEntryId, transformRunEnteries } from '@app/utils/formatplanEnteries';
import { startWorkflow } from '@app/temporal/client';
import { UpdatePlanStateDTO } from '@app/temporal/activities/plan';
import { UpdateRunStateDTO } from '@app/temporal/activities/run';

const getPlans = async (req: Request) => {
  const projectId = req.query._id as string;
  if (!projectId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: project_id is a required field');
  }
  const project = await req.models.Project.query()
    .where({
      uid: projectId,
      deletedAt: null,
    })
    .first();
  if (!project) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, 'Fields: project_id is a required field');
  }
  const filters = req.query._filters as any;
  const {
    created_after: createdAfter,
    created_before: createdBefore,
    is_completed: isCompleted,
    milestone_id: milestoneId,
    limit = 250,
    offset = 0,
  } = filters;

  const plansQuery = req.models.TestPlan.query()
    .where({
      'tags.deletedAt': null,
      systemType: 'plan',
      'tags.projectUid': project.uid,
    })
    .withGraphFetched('milestones')
    .where((w) => {
      if (createdAfter) w.where('tags.createdAt', '>=', new Date(createdAfter).toISOString());
      if (createdBefore) w.where('tags.createdAt', '<=', new Date(createdBefore).toISOString());
      if (isCompleted) w.whereNull('tags.archivedAt');

      return w;
    });
  const countQuery = req.models.TestPlan.query()
    .count('uid as count')
    .where({
      'tags.deletedAt': null,
      systemType: 'plan',
      'tags.projectUid': project.uid,
    })
    .where((w) => {
      if (createdAfter) w.where('tags.createdAt', '>=', new Date(createdAfter).toISOString());
      if (createdBefore) w.where('tags.createdAt', '<=', new Date(createdBefore).toISOString());
      if (isCompleted) w.whereNull('tags.archivedAt');
      return w;
    });
  if (milestoneId) {
    plansQuery.whereExists(
      TestPlan.relatedQuery('milestones')
        .whereNull('testMilestonePlans.deletedAt')
        .where({
          'milestones.systemType': 'milestone',
          'milestones.deletedAt': null,
        })
        .whereIn('uid', milestoneId),
    );
    countQuery.whereExists(
      TestPlan.relatedQuery('milestones')
        .whereNull('testMilestonePlans.deletedAt')
        .where({
          'milestones.systemType': 'milestone',
          'milestones.deletedAt': null,
        }),
    );
  }
  const { statusColors } = await preferencesService.findOne(
    req.sharedKnexDB,
    req.locals.handle.ownerType,
    req.locals.handle.ownerUid,
  );
  const baseURL = `${env.FRONTEND_URL}/${req.locals.handle.name}`;
  const plans: any = await plansQuery.offset(offset).limit(limit);
  const count = await countQuery.first();
  const total = parseInt((count as any).count ?? '0', 10);
  const plansData = plans.map((p: any) => ({
    id: p.uid,
    name: p.name,
    refs: null, // just to keep it consistent with the TestRail response
    description: p.description,
    milestone_id: p.milestones.length > 0 ? p.milestones[0].uid : null,
    assignedto_id: null,
    is_completed: !!p.archivedAt,
    completed_on: p.archivedAt ? new Date(p.archivedAt).getTime() : null,
    ...formatCaseCount(p.customFields.frequency, statusColors),
    project_id: p.projectUid,
    created_on: new Date(p.createdAt).getTime(),
    created_by: null, // just to keep it consistent with the TestRail response
    url: `${baseURL}/${project.key}/plans/${p.uid}/rerun`,
  }));
  const links = {
    next: Number(offset) + Number(limit) >= total ? null : buildLink('api/v2/get_plans', Number(offset) + Number(limit), Number(limit), projectId, filters),
    prev: Number(offset) <= 0 ? null : buildLink('api/v2/get_plans', Math.max(0, Number(offset) - Number(limit)), Number(limit), projectId, filters),
  };
  const pagination: any = {
    offset: parseInt(offset),
    limit: parseInt(limit),
    size: plansData.length,
    _links: links,
    plans: plansData,
  };
  return pagination;
};

const getPlan = async (req: Request) => {
  const planId = req.query._id as string;
  if (!planId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_id is a required field');
  }
  const plan = await req.models.TestPlan.query()
    .where({
      uid: planId,
      systemType: 'plan',
      deletedAt: null,
    })
    .withGraphFetched('milestones')
    .withGraphFetched({ runs: { testMilestones: true } })
    .first();

  if (!plan) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.PLAN_NOT_FOUND,
    );
  }

  for (const r of plan.runs ?? []) {
    await req.models.TestRun.populateConfig(req.knexDB, r);
  }
  // fetch user preferences
  const { statusColors } = await preferencesService.findOne(
    req.sharedKnexDB,
    req.locals.handle.ownerType,
    req.locals.handle.ownerUid,
  );
  const project = await req.models.Project.query()
    .where({
      uid: plan.projectUid,
      deletedAt: null,
    })
    .first();
  if (!project) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, 'Field: project_id is invalid');
  }
  const baseURL = `${env.FRONTEND_URL}/${req.locals.handle.name}`;
  const suite = await req.models.Tag.query()
    .where({
      projectUid: plan.projectUid,
      systemType: 'folder',
      parentUid: null,
      deletedAt: null,
    })
    .first();
  if (!suite) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, 'Field: project_id is invalid');
  }
  return {
    id: plan.uid,
    name: plan.name,
    refs: null,
    description: plan.description,
    milestone_id: plan.milestones.length > 0 ? plan.milestones[0].uid : null,
    assignedto_id: null,
    is_completed: plan.archivedAt !== null,
    completed_on: plan.archivedAt ? new Date(plan.archivedAt).getTime() : null,
    ...formatCaseCount(plan.customFields.frequency, statusColors),
    project_id: plan.projectUid,
    created_on: new Date(plan.createdAt).getTime(),
    created_by: null,
    url: `${baseURL}/${project.key}/plans/${plan.uid}/rerun`,
    entries: transformRunEnteries(plan.name, plan.runs, plan.projectUid, plan.uid, suite.uid, statusColors, baseURL),
  };
};

const createPlan = async (req: Request) => {
  const { defaults } = await preferencesService.getDefaultsAndCompleted(
    req.sharedKnexDB,
    req.locals.handle.ownerType,
    req.locals.handle.ownerUid,
  );
  const projjectId = req.query._id as string;
  if (!projjectId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: project_id is a required field');
  }
  const project = await req.models.Project.query()
    .where({
      uid: projjectId,
      deletedAt: null,
    })
    .first();
  if (!project) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, 'Field: project_id is invalid');
  }
  const {
    name,
    description,
    milestone_id: milestoneId,
    entries,
  } = req.body;
  if (!name) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: name is a required field');
  }
  if (milestoneId) {
    const milestone = await req.models.TestMilestone.query()
      .where({
        uid: milestoneId,
        projectUid: project.uid,
        systemType: 'milestone',
        deletedAt: null,
      })
      .first();
    if (!milestone) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Field: milestone_id is invalid');
    }
  }
  const trx = await req.knexDB.transaction();
  try {
    const { plan } = await req.models.TestPlan.newPlan(
      {
        name,
        description,
        testRuns: [],
      },
      project.uid,
      defaults,
      trx,
    );
    const planRuns = [];
    const planExecutions = [];
    if (entries && entries.length > 0) {
      for (const entry of entries) {
        const {
          suite_id: suiteUid,
          name = 'Master',
          include_all: includeAll = true,
          case_ids: caseIds,
          config_ids: configIds,
          runs,
        } = entry;
        if (!suiteUid) {
          throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: suite_id is a required field');
        }
        const suite = await req.models.Tag.query()
          .where({
            uid: suiteUid,
            projectUid: project.uid,
            systemType: 'folder',
            parentUid: null,
            deletedAt: null,
          })
          .first();
        if (!suite) {
          throw new ApplicationError(StatusCodes.NOT_FOUND, 'Field: suite_id is invalid');
        }
        const caseUids = [];
        if (includeAll) {
          const cases = await req.models.TestCase.query()
            .where({
              projectUid: project.uid,
              active: true,
              deletedAt: null,
            });
          caseUids.push(...cases.map((c) => c.uid));
        } else {
          if (!caseIds || caseIds.length === 0) {
            throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: case_ids is a required field');
          }
          caseUids.push(...caseIds);
        }
        if (configIds && configIds.length > 0) {
          const configs = await req.models.Tag.query()
            .whereIn('uid', configIds)
            .where({
              projectUid: project.uid,
              systemType: 'config.option',
              deletedAt: null,
            })
            .orderBy('uid', 'asc');
          if (configs.length !== configIds.length) {
            throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: config_ids is invalid');
          }
          if (!runs || runs.length === 0) {
            throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :entries has configurations but no test runs.');
          }
          for (const run of runs) {
            const {
              include_all: runIncludeAll = null,
              case_ids: runCaseIds,
              config_ids: runConfigIds,
            } = run;
            const runCaseUids = [];
            if (runIncludeAll === null) {
              // use enteries case ids
              runCaseUids.push(...caseUids);
            } else if (includeAll === false) {
              if (!runCaseIds || runCaseIds.length === 0) {
                throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: case_ids is a required field');
              }
              runCaseUids.push(...runCaseIds);
            } else {
              // use all cases
              const cases = await req.models.TestCase.query()
                .where({
                  projectUid: project.uid,
                  active: true,
                  deletedAt: null,
                });
              runCaseUids.push(...cases.map((c) => c.uid));
            }

            if (!runConfigIds || runConfigIds.length === 0) {
              throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :entries.runs uses an invalid combination of configurations (use one from each group).');
            }
            const runConfigs = await req.models.Tag.query()
              .whereIn('uid', runConfigIds)
              .where({
                projectUid: project.uid,
                systemType: 'config.option',
                deletedAt: null,
              });
            if (runConfigs.length !== runConfigIds.length) {
              throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :entries.runs uses an invalid combination of configurations (use one from each group).');
            }
            // make sure all configs are subset of parent configIds
            const isValidConfig = runConfigs.every((c) => configIds.includes(c.uid));
            if (!isValidConfig) {
              throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :entries.runs uses an invalid combination of configurations (use one from each group).');
            }
            const configurations:ConfigurationDTO = {
              type: 'simple',
              sets: runConfigs.map((c) => [`${c.parentUid}::${c.uid}`]),
            };
            const emptyConfig:ConfigurationDTO = { sets: [], type: 'matrix' };

            const configs = getConfigOptions(emptyConfig, configurations);
            for (const config of configs) {
              const { testRun, executions } = await req.models.TestRun.newRun(
                trx,
                {
                  priority: defaults.testRun?.priority,
                  status: defaults.testRun?.status,
                  name,
                  projectUid: project.uid,
                  description,
                  configs: config,
                  dueAt: null,
                  tagUids: [],
                },
                {
                  caseUids: runCaseUids,
                  execPriority: defaults.testCase?.priority,
                  execStatus: defaults.testCase?.status,
                },
              );
              await req.models.TestRun.populateConfig(req.knexDB, testRun);
              planRuns.push(testRun);
              planExecutions.push(...executions);
              await req.models.TestRun.addPlans(trx, [testRun.uid], [plan.uid]);
            }
          }
        } else {
          const { testRun, executions } = await req.models.TestRun.newRun(
            trx,
            {
              priority: defaults.testRun?.priority,
              status: defaults.testRun?.status,
              name,
              projectUid: project.uid,
              description,
              dueAt: null,
              tagUids: [],
            },
            {
              caseUids,
              execPriority: defaults.testCase?.priority,
              execStatus: defaults.testCase?.status,
            },
          );
          await req.models.TestRun.populateConfig(req.knexDB, testRun);
          planRuns.push(testRun);
          planExecutions.push(...executions);
          await req.models.TestRun.addPlans(trx, [testRun.uid], [plan.uid]);
        }
      }
    }
    const { ownerType, ownerUid } = req.locals.handle;
    // create runs and executions and plan and link them all
    const fgaWrites: FGARawWrite[] = [
      {
        objectType: 'plan',
        objectId: plan.uid,
        subjectType: ownerType,
        subjectId: ownerUid,
        relation: 'owner',
      },
      ...planRuns.map((r) => ({
        objectType: 'run',
        objectId: r.uid,
        relation: 'owner',
        subjectType: ownerType,
        subjectId: ownerUid,
      })),
      ...planExecutions.map((e) => ({
        objectType: 'execution',
        objectId: e.uid,
        relation: 'owner',
        subjectType: ownerType,
        subjectId: ownerUid,
      })),
    ];
    await req.fga.create(...fgaWrites);
    if (milestoneId) {
      await req.models.TestPlan.addMilestones(trx, [plan.uid], [milestoneId]);
    }
    await trx.commit();

    const param: UpdatePlanStateDTO = {
      planUids: [plan.uid],
      ownerType,
      ownerUid,
    };
    await startWorkflow('updatePlanWorkflow', {
      taskQueue: 'update-plan-queue',
      workflowId: `update.plan.${plan.uid}.${Date.now()}`,
      args: [param],
    });

    const { statusColors } = await preferencesService.findOne(
      req.sharedKnexDB,
      req.locals.handle.ownerType,
      req.locals.handle.ownerUid,
    );
    const baseURL = `${env.FRONTEND_URL}/${req.locals.handle.name}`;
    for (const run of planRuns) {
      await req.models.TestRun.populateConfig(req.knexDB, run);
    }
    return {
      id: plan.uid,
      name: plan.name,
      refs: null,
      description: plan.description,
      milestone_id: milestoneId,
      assignedto_id: null,
      is_completed: plan.archivedAt !== null,
      completed_on: plan.archivedAt ? new Date(plan.archivedAt).getTime() : null,
      ...formatCaseCount(plan.customFields.frequency, statusColors),
      project_id: plan.projectUid,
      created_on: new Date(plan.createdAt).getTime(),
      created_by: null,
      url: `${baseURL}/${project.key}/plans/${plan.uid}/rerun`,
      entries: transformRunEnteries(plan.name, planRuns, plan.projectUid, plan.uid, 11, statusColors, baseURL),
    };
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const updatePlan = async (req: Request) => {
  const planId = req.query._id as unknown as number;
  if (!planId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_id is a required field');
  }
  const plan = await req.models.TestPlan.query()
    .where({
      uid: planId,
      deletedAt: null,
    })
    .first();
  if (!plan) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errorConstants.PLAN_NOT_FOUND);
  }
  const trx = await req.knexDB.transaction();
  const {
    name,
    description,
    milestone_id: milestoneId,
  } = req.body;
  if (milestoneId) {
    const milestone = await req.models.TestMilestone.query()
      .where({
        uid: milestoneId,
        projectUid: plan.projectUid,
        systemType: 'milestone',
        deletedAt: null,
      })
      .first();
    if (!milestone) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Field: milestone_id is invalid');
    }
  }
  await req.models.TestPlan.query(trx)
    .patch({
      name,
      description,
    })
    .where({
      uid: planId,
    });
  if (milestoneId) {
    await req.models.TestPlan.addMilestones(trx, [plan.uid], [milestoneId]);
  }
  await trx.commit();
  const { statusColors } = await preferencesService.findOne(
    req.sharedKnexDB,
    req.locals.handle.ownerType,
    req.locals.handle.ownerUid,
  );
  const baseURL = `${env.FRONTEND_URL}/${req.locals.handle.name}`;
  const updatedPlan = await req.models.TestPlan.query()
    .where({
      uid: planId,
      deletedAt: null,
    })
    .withGraphFetched('milestones')
    .withGraphFetched({ runs: { testMilestones: true } })
    .first();
  for (const run of updatedPlan.runs) {
    await req.models.TestRun.populateConfig(req.knexDB, run);
  }
  const project = await req.models.Project.query()
    .where({
      uid: updatedPlan.projectUid,
      deletedAt: null,
    })
    .first();
  const response = {
    id: updatedPlan.uid,
    name: updatedPlan.name,
    refs: null,
    description: updatedPlan.description,
    milestone_id: updatedPlan.milestones.length > 0 ? updatedPlan.milestones[0].uid : null,
    assignedto_id: null,
    is_completed: updatedPlan.archivedAt !== null,
    completed_on: updatedPlan.archivedAt ? new Date(updatedPlan.archivedAt).getTime() : null,
    ...formatCaseCount(updatedPlan.customFields.frequency, statusColors),
    project_id: updatedPlan.projectUid,
    created_on: new Date(updatedPlan.createdAt).getTime(),
    created_by: null,
    url: `${baseURL}/${project.key}/plans/${updatedPlan.uid}/rerun`,
    entries: transformRunEnteries(updatedPlan.name, updatedPlan.runs, updatedPlan.projectUid, updatedPlan.uid, 11, statusColors, baseURL),
  };
  return response;
};

const addPlanEntry = async (req: Request) => {
  const { defaults } = await preferencesService.getDefaultsAndCompleted(
    req.sharedKnexDB,
    req.locals.handle.ownerType,
    req.locals.handle.ownerUid,
  );
  const trx = await req.knexDB.transaction();
  const planId = req.query._id as unknown as number;
  if (!planId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_id is a required field');
  }
  const plan = await req.models.TestPlan.query()
    .where({
      uid: planId,
      systemType: 'plan',
      deletedAt: null,
    })
    .first();
  if (!plan) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errorConstants.PLAN_NOT_FOUND);
  }
  const planRuns = [];
  const planExecutions = [];
  const {
    suite_id: suiteUid,
    name = 'Master',
    include_all: includeAll = true,
    case_ids: caseIds,
    config_ids: configIds,
    runs,
  } = req.body;
  if (!suiteUid) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: suite_id is a required field');
  }
  const suite = await req.models.Tag.query()
    .where({
      uid: suiteUid,
      projectUid: plan.projectUid,
      systemType: 'folder',
      parentUid: null,
      deletedAt: null,
    })
    .first();
  if (!suite) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, 'Field: suite_id is invalid');
  }
  const caseUids = [];
  if (includeAll) {
    const cases = await req.models.TestCase.query()
      .where({
        projectUid: plan.projectUid,
        active: true,
        deletedAt: null,
      });
    caseUids.push(...cases.map((c) => c.uid));
  } else {
    if (!caseIds || caseIds.length === 0) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: case_ids is a required field');
    }
    caseUids.push(...caseIds);
  }
  if (configIds && configIds.length > 0) {
    const configs = await req.models.Tag.query()
      .whereIn('uid', configIds)
      .where({
        projectUid: plan.projectUid,
        systemType: 'config.option',
        deletedAt: null,
      })
      .orderBy('uid', 'asc');
    if (configs.length !== configIds.length) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: config_ids is invalid');
    }
    if (!runs || runs.length === 0) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :entries has configurations but no test runs.');
    }
    for (const run of runs) {
      const {
        include_all: runIncludeAll = null,
        case_ids: runCaseIds,
        config_ids: runConfigIds,
      } = run;
      const runCaseUids = [];
      if (runIncludeAll === null) {
        // use enteries case ids
        runCaseUids.push(...caseUids);
      } else if (includeAll === false) {
        if (!runCaseIds || runCaseIds.length === 0) {
          throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: case_ids is a required field');
        }
        runCaseUids.push(...runCaseIds);
      } else {
        // use all cases
        const cases = await req.models.TestCase.query()
          .where({
            projectUid: plan.projectUid,
            active: true,
            deletedAt: null,
          });
        runCaseUids.push(...cases.map((c) => c.uid));
      }

      if (!runConfigIds || runConfigIds.length === 0) {
        throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :entries.runs uses an invalid combination of configurations (use one from each group).');
      }
      const runConfigs = await req.models.Tag.query()
        .whereIn('uid', runConfigIds)
        .where({
          projectUid: plan.projectUid,
          systemType: 'config.option',
          deletedAt: null,
        });
      if (runConfigs.length !== runConfigIds.length) {
        throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :entries.runs uses an invalid combination of configurations (use one from each group).');
      }
      // make sure all configs are subset of parent configIds
      const isValidConfig = runConfigs.every((c) => configIds.includes(c.uid));
      if (!isValidConfig) {
        throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :entries.runs uses an invalid combination of configurations (use one from each group).');
      }
      const configurations:ConfigurationDTO = {
        type: 'simple',
        sets: runConfigs.map((c) => [`${c.parentUid}::${c.uid}`]),
      };
      const emptyConfig:ConfigurationDTO = { sets: [], type: 'matrix' };

      const configs = getConfigOptions(emptyConfig, configurations);
      for (const config of configs) {
        const { testRun, executions } = await req.models.TestRun.newRun(
          trx,
          {
            priority: defaults.testRun?.priority,
            status: defaults.testRun?.status,
            name,
            projectUid: plan.projectUid,
            configs: config,
            dueAt: null,
            tagUids: [],
          },
          {
            caseUids: runCaseUids,
            execPriority: defaults.testCase?.priority,
            execStatus: defaults.testCase?.status,
          },
        );
        planRuns.push(testRun);
        planExecutions.push(...executions);
        await req.models.TestRun.addPlans(trx, [testRun.uid], [plan.uid]);
      }
    }
  } else {
    const { testRun, executions } = await req.models.TestRun.newRun(
      trx,
      {
        priority: defaults.testRun?.priority,
        status: defaults.testRun?.status,
        name,
        projectUid: plan.projectUid,
        dueAt: null,
        tagUids: [],
      },
      {
        caseUids,
        execPriority: defaults.testCase?.priority,
        execStatus: defaults.testCase?.status,
      },
    );
    planRuns.push(testRun);
    planExecutions.push(...executions);
    await req.models.TestRun.addPlans(trx, [testRun.uid], [plan.uid]);
  }
  const { ownerType, ownerUid } = req.locals.handle;
  const fgaWrites: FGARawWrite[] = [
    ...planRuns.map((r) => ({
      objectType: 'run',
      objectId: r.uid,
      relation: 'owner',
      subjectType: ownerType,
      subjectId: ownerUid,
    })),
    ...planExecutions.map((e) => ({
      objectType: 'execution',
      objectId: e.uid,
      relation: 'owner',
      subjectType: ownerType,
      subjectId: ownerUid,
    })),
  ];
  await req.fga.create(...fgaWrites);
  await trx.commit();
  for (const run of planRuns) {
    await req.models.TestRun.populateConfig(req.knexDB, run);
  }
  const { statusColors } = await preferencesService.findOne(
    req.sharedKnexDB,
    req.locals.handle.ownerType,
    req.locals.handle.ownerUid,
  );
  const baseURL = `${env.FRONTEND_URL}/${req.locals.handle.name}`;
  return transformRunEnteries(plan.name, planRuns, plan.projectUid, plan.uid, 11, statusColors, baseURL)[0];
};

const addRunToPlanEntry = async (req: Request) => {
  const planId = req.query._id as unknown as number;
  const planEntryId = req.query._id2 as unknown as string;
  if (!planId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_id is a required field');
  }
  if (!planEntryId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_entry_id is a required field');
  }
  const plan = await req.models.TestPlan.query()
    .where({
      uid: planId,
      deletedAt: null,
      systemType: 'plan',
    })
    .first();
  if (!plan) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_id is invalid');
  }
  const planEntryWithConfig = generateEntryId(Number(planId), plan.projectUid, '-withConfig');
  const planEntryWithoutConfig = generateEntryId(Number(planId), plan.projectUid, '-noConfig');
  if (planEntryId !== planEntryWithConfig && planEntryId !== planEntryWithoutConfig) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_entry_id is invalid');
  }
  const { defaults } = await preferencesService.getDefaultsAndCompleted(
    req.sharedKnexDB,
    req.locals.handle.ownerType,
    req.locals.handle.ownerUid,
  );
  const trx = await req.knexDB.transaction();
  const planRuns = [];
  const planExecutions = [];
  const {
    include_all: includeAll = null,
    case_ids: caseIds,
    config_ids: configIds,
    name = 'Master',
  } = req.body;
  const caseUids = [];
  if (includeAll) {
    const cases = await req.models.TestCase.query()
      .where({
        projectUid: plan.projectUid,
        active: true,
        deletedAt: null,
      });
    caseUids.push(...cases.map((c) => c.uid));
  } else {
    if (!caseIds || caseIds.length === 0) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: case_ids is a required field');
    }
    caseUids.push(...caseIds);
  }
  if (!configIds || configIds.length === 0) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: config_ids is a required field');
  }
  const configs = await req.models.Tag.query()
    .whereIn('uid', configIds)
    .where({
      projectUid: plan.projectUid,
      systemType: 'config.option',
      deletedAt: null,
    })
    .orderBy('uid', 'asc');
  if (configs.length !== configIds.length) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: config_ids is invalid');
  }
  const configurations:ConfigurationDTO = {
    type: 'simple',
    sets: configs.map((c) => [`${c.parentUid}::${c.uid}`]),
  };
  const emptyConfig:ConfigurationDTO = { sets: [], type: 'matrix' };
  const runConfigs = getConfigOptions(emptyConfig, configurations);
  for (const config of runConfigs) {
    const { testRun, executions } = await req.models.TestRun.newRun(
      trx,
      {
        priority: defaults.testRun?.priority,
        status: defaults.testRun?.status,
        name,
        projectUid: plan.projectUid,
        configs: config,
        dueAt: null,
        tagUids: [],
      },
      {
        caseUids,
        execPriority: defaults.testCase?.priority,
        execStatus: defaults.testCase?.status,
      },
    );
    planRuns.push(testRun);
    planExecutions.push(...executions);
    await req.models.TestRun.addPlans(trx, [testRun.uid], [plan.uid]);
  }
  const { ownerType, ownerUid } = req.locals.handle;
  const fgaWrites: FGARawWrite[] = [
    ...planRuns.map((r) => ({
      objectType: 'run',
      objectId: r.uid,
      relation: 'owner',
      subjectType: ownerType,
      subjectId: ownerUid,
    })),
    ...planExecutions.map((e) => ({
      objectType: 'execution',
      objectId: e.uid,
      relation: 'owner',
      subjectType: ownerType,
      subjectId: ownerUid,
    })),
  ];
  await req.fga.create(...fgaWrites);
  await trx.commit();
  const { statusColors } = await preferencesService.findOne(
    req.sharedKnexDB,
    req.locals.handle.ownerType,
    req.locals.handle.ownerUid,
  );
  const baseURL = `${env.FRONTEND_URL}/${req.locals.handle.name}`;
  const updatedPlan = await req.models.TestPlan.query()
    .where({
      uid: planId,
      deletedAt: null,
      systemType: 'plan',
    })
    .withGraphFetched('milestones')
    .withGraphFetched({ runs: { testMilestones: true } })
    .first();
  for (const run of updatedPlan.runs) {
    await req.models.TestRun.populateConfig(req.knexDB, run);
  }
  const project = await req.models.Project.query()
    .where({
      uid: updatedPlan.projectUid,
      deletedAt: null,
    })
    .first();
  const response = {
    id: updatedPlan.uid,
    name: updatedPlan.name,
    refs: null,
    description: updatedPlan.description,
    milestone_id: updatedPlan.milestones.length > 0 ? updatedPlan.milestones[0].uid : null,
    assignedto_id: null,
    is_completed: updatedPlan.archivedAt !== null,
    completed_on: updatedPlan.archivedAt ? new Date(updatedPlan.archivedAt).getTime() : null,
    ...formatCaseCount(updatedPlan.customFields.frequency, statusColors),
    project_id: updatedPlan.projectUid,
    created_on: new Date(updatedPlan.createdAt).getTime(),
    created_by: null,
    url: `${baseURL}/${project.key}/plans/${updatedPlan.uid}/rerun`,
    entries: transformRunEnteries(updatedPlan.name, updatedPlan.runs, updatedPlan.projectUid, updatedPlan.uid, 11, statusColors, baseURL),
  };
  return response;
};

const updatePlanEntry = async (req: Request) => {
  const planId = req.query._id as unknown as number;
  const planEntryId = req.query._id2 as unknown as string;
  if (!planId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_id is a required field');
  }
  const plan = await req.models.TestPlan.query()
    .where({
      uid: planId,
      deletedAt: null,
      systemType: 'plan',
    })
    .withGraphFetched('runs')
    .first();
  if (!plan) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errorConstants.PLAN_NOT_FOUND);
  }
  const planEntryWithConfig = generateEntryId(Number(planId), plan.projectUid, '-withConfig');
  const planEntryWithoutConfig = generateEntryId(Number(planId), plan.projectUid, '-noConfig');
  if (planEntryId !== planEntryWithConfig && planEntryId !== planEntryWithoutConfig) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_entry_id is invalid');
  }
  const withConfig = planEntryId === planEntryWithConfig;

  // filter out all runs that belong to the plan entry
  const runs = plan.runs.filter((r) => {
    const configsCount = r.customFields.configs?.length;
    if (withConfig) {
      return configsCount > 0;
    }
    return configsCount === 0;
  });
  const { statusColors } = await preferencesService.findOne(
    req.sharedKnexDB,
    req.locals.handle.ownerType,
    req.locals.handle.ownerUid,
  );
  const baseURL = `${env.FRONTEND_URL}/${req.locals.handle.name}`;
  if (runs.length === 0) {
    return transformRunEnteries(plan.name, plan.runs, plan.projectUid, plan.uid, 11, statusColors, baseURL)[0];
  }
  const {
    name,
    description,
  } = req.body;
  const trx = await req.knexDB.transaction();
  for (const run of runs) {
    await req.models.TestRun.updateOne(trx, run.uid, { name, description });
  }
  await trx.commit();
  for (const run of runs) {
    await req.models.TestRun.populateConfig(req.knexDB, run);
  }
  return transformRunEnteries(plan.name, runs, plan.projectUid, plan.uid, 11, statusColors, baseURL)[0];
};

const updateRunInPlanEntry = async (req: Request) => {
  const runId = req.query._id as unknown as number;
  if (!runId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: run_id is a required field');
  }
  const run = await req.models.TestRun.query()
    .where({
      uid: runId,
      deletedAt: null,
      systemType: 'run',
    })
    .withGraphFetched('testPlans')
    .first();
  if (!run) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: run_id is invalid');
  }
  if (run.testPlans.length === 0) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: run_id is invalid');
  }
  const {
    description,
    include_all: includeAll = null,
    case_ids: caseIds,
  } = req.body;
  const trx = await req.knexDB.transaction();
  if (description) {
    await req.models.TestRun.updateOne(trx, runId, { description });
  }
  if (includeAll !== null) {
    const caseUids = [];
    if (includeAll) {
      const cases = await req.models.TestCase.query()
        .where({
          projectUid: run.projectUid,
          active: true,
          deletedAt: null,
        });
      caseUids.push(...cases.map((c) => c.uid));
    } else {
      if (!caseIds || caseIds.length === 0) {
        throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: case_ids is a required field');
      }
      caseUids.push(...caseIds);
    }
    // find current case uids in the run
    const currentExecutions = await req.models.TestExecution.query()
      .where({
        testRunUid: runId,
        deletedAt: null,
      });
    const caseUidsToRemove = currentExecutions.map((e) => e.testCaseUid);
    const defaults = await preferencesService.getDefaults(
      req.sharedKnexDB,
      req.locals.handle.ownerType,
      req.locals.handle.ownerUid,
    );
    if (caseUids?.length > 0) {
      await req.models.TestRun.addCases(
        trx,
        runId,
        caseUids,
        defaults.testCase?.status,
        defaults.testCase?.priority,
      );
    }
    if (caseUidsToRemove?.length > 0) {
      await req.models.TestRun.removeExecs(
        trx,
        runId,
        caseUidsToRemove,
      );
    }
  }
  await trx.commit();
  const param: UpdateRunStateDTO = {
    runUids: [runId],
    ownerType: req.locals.handle.ownerType,
    ownerUid: req.locals.handle.ownerUid,
  };
  await startWorkflow('updateRunWorkflow', {
    taskQueue: 'update-run-queue',
    workflowId: `update.run.${runId}.${Date.now()}`,
    args: [param],
  });
  const planUid = run.testPlans[0].uid;
  const plan = await req.models.TestPlan.query()
    .where({
      uid: planUid,
      deletedAt: null,
      systemType: 'plan',
    })
    .withGraphFetched('milestones')
    .withGraphFetched({ runs: { testMilestones: true } })
    .first();
  for (const run of plan.runs) {
    await req.models.TestRun.populateConfig(req.knexDB, run);
  }
  const project = await req.models.Project.query()
    .where({
      uid: plan.projectUid,
      deletedAt: null,
    })
    .first();
  const { statusColors } = await preferencesService.findOne(
    req.sharedKnexDB,
    req.locals.handle.ownerType,
    req.locals.handle.ownerUid,
  );
  const baseURL = `${env.FRONTEND_URL}/${req.locals.handle.name}`;
  const response = {
    id: plan.uid,
    name: plan.name,
    refs: null,
    description: plan.description,
    milestone_id: plan.milestones.length > 0 ? plan.milestones[0].uid : null,
    assignedto_id: null,
    is_completed: plan.archivedAt !== null,
    completed_on: plan.archivedAt ? new Date(plan.archivedAt).getTime() : null,
    ...formatCaseCount(plan.customFields.frequency, statusColors),
    project_id: plan.projectUid,
    created_on: new Date(plan.createdAt).getTime(),
    created_by: null,
    url: `${baseURL}/${project.key}/plans/${plan.uid}/rerun`,
    entries: transformRunEnteries(plan.name, plan.runs, plan.projectUid, plan.uid, 11, statusColors, baseURL),
  };
  return response;
};

const closePlan = async (req: Request) => {
  const planId = req.query._id as unknown as number;
  if (!planId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_id is a required field');
  }
  const plan = await req.models.TestPlan.query()
    .where({
      uid: planId,
      deletedAt: null,
      systemType: 'plan',
    })
    .first();
  if (!plan) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errorConstants.PLAN_NOT_FOUND);
  }
  const trx = await req.knexDB.transaction();
  await req.models.TestPlan.query(trx)
    .patch({
      archivedAt: new Date(),
    })
    .where({
      uid: planId,
    });
  await trx.commit();
  const { statusColors } = await preferencesService.findOne(
    req.sharedKnexDB,
    req.locals.handle.ownerType,
    req.locals.handle.ownerUid,
  );
  const baseURL = `${env.FRONTEND_URL}/${req.locals.handle.name}`;
  const updatedPlan = await req.models.TestPlan.findOne(trx, { uid: planId }, true);
  const project = await req.models.Project.query()
    .where({
      uid: updatedPlan.projectUid,
      deletedAt: null,
    })
    .first();
  const response = {
    id: updatedPlan.uid,
    name: updatedPlan.name,
    refs: null,
    description: updatedPlan.description,
    milestone_id: null,
    assignedto_id: null,
    is_completed: updatedPlan.archivedAt !== null,
    completed_on: updatedPlan.archivedAt ? new Date(updatedPlan.archivedAt).getTime() : null,
    ...formatCaseCount(updatedPlan.customFields.frequency, statusColors),
    project_id: updatedPlan.projectUid,
    created_on: new Date(updatedPlan.createdAt).getTime(),
    created_by: null,
    url: `${baseURL}/${project.key}/plans/${updatedPlan.uid}/rerun`,
    entries: transformRunEnteries(updatedPlan.name, updatedPlan.runs, updatedPlan.projectUid, updatedPlan.uid, 11, statusColors, baseURL),
  };
  return response;
};

const deletePlan = async (req: Request) => {
  const planId = req.query._id as unknown as number;
  if (!planId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_id is a required field');
  }
  const plan = await req.models.TestPlan.query()
    .where({
      uid: planId,
      deletedAt: null,
      systemType: 'plan',
    })
    .first();
  if (!plan) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errorConstants.PLAN_NOT_FOUND);
  }
  const trx = await req.knexDB.transaction();
  await req.models.TestPlan.query(trx)
    .patch({
      deletedAt: new Date(),
    })
    .where({
      uid: planId,
    });
  await trx.commit();
  return plan;
};

const deletePlanEntry = async (req: Request) => {
  const planId = req.query._id as unknown as number;
  const planEntryId = req.query._id2 as unknown as string;
  if (!planId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_id is a required field');
  }
  if (!planEntryId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_entry_id is a required field');
  }
  const plan = await req.models.TestPlan.query()
    .where({
      uid: planId,
      deletedAt: null,
      systemType: 'plan',
    })
    .withGraphFetched('runs')
    .first();
  if (!plan) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errorConstants.PLAN_NOT_FOUND);
  }
  const planEntryWithConfig = generateEntryId(Number(planId), plan.projectUid, '-withConfig');
  const planEntryWithoutConfig = generateEntryId(Number(planId), plan.projectUid, '-noConfig');
  if (planEntryId !== planEntryWithConfig && planEntryId !== planEntryWithoutConfig) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_entry_id is invalid');
  }
  const withConfig = planEntryId === planEntryWithConfig;
  const runs = plan.runs.filter((r) => {
    const configsCount = r.customFields.configs?.length;
    if (withConfig) {
      return configsCount > 0;
    }
    return configsCount === 0;
  });
  const runUids = runs.map((r) => r.uid);
  const testRuns = await req.models.TestRun.deleteByIds(
    req.knexDB,
    runUids,
  );

  const param: UpdateRunStateDTO = {
    runUids: testRuns,
    ownerType: req.locals.handle.ownerType,
    ownerUid: req.locals.handle.ownerUid,
  };
  for (const runUid of testRuns) {
    await startWorkflow('updateRunWorkflow', {
      taskQueue: 'update-run-queue',
      workflowId: `update.run.${runUid}.${Date.now()}`,
      args: [param],
    });
  }
};

const deleteRunFromPlanEntry = async (req: Request) => {
  const runId = req.query._id as unknown as number;
  if (!runId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: run_id is a required field');
  }
  const run = await req.models.TestRun.query()
    .where({
      uid: runId,
      deletedAt: null,
      systemType: 'run',
    })
    .withGraphFetched('testPlans')
    .first();
  if (!run) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errorConstants.RUN_NOT_FOUND);
  }
  if (run.testPlans.length === 0) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: run_id is invalid');
  }
  const testRuns = await req.models.TestRun.deleteByIds(
    req.knexDB,
    [runId],
  );

  const param: UpdateRunStateDTO = {
    runUids: testRuns,
    ownerType: req.locals.handle.ownerType,
    ownerUid: req.locals.handle.ownerUid,
  };
  await startWorkflow('updateRunWorkflow', {
    taskQueue: 'update-run-queue',
    workflowId: `update.run.${runId}.${Date.now()}`,
    args: [param],
  });
};

export default {
  getPlans: httpHandler(getPlans),
  getPlan: httpHandler(getPlan),
  createPlan: httpHandler(createPlan),
  updatePlan: httpHandler(updatePlan),
  addPlanEntry: httpHandler(addPlanEntry),
  addRunToPlanEntry: httpHandler(addRunToPlanEntry),
  updatePlanEntry: httpHandler(updatePlanEntry),
  updateRunInPlanEntry: httpHandler(updateRunInPlanEntry),
  closePlan: httpHandler(closePlan),
  deletePlan: httpHandler(deletePlan),
  deletePlanEntry: httpHandler(deletePlanEntry),
  deleteRunFromPlanEntry: httpHandler(deleteRunFromPlanEntry),
};
