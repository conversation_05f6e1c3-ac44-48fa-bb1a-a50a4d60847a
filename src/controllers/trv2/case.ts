import { ApplicationError, httpHandler } from '@app/lib/http';
import { buildLink } from '@app/utils/buildTestrailLinks';
import {
  getCaseCustomFields, getCaseSteps, transformCustomSteps, transformTemplateFields,
} from '@app/utils/formatCaseFields';
import { Request } from 'express';

import { FGARawWrite } from '@ss-libs/ss-component-auth';
import { Knex } from 'knex';
import { StatusCodes } from 'http-status-codes';
import { TestCaseStep } from '@app/models/testCaseStep';
import { TestCaseTag } from '@app/models/testCaseTag';
import errorConstants from '@app/constants/errors';
import { getNextId } from '@app/lib/model';
import logger from '@app/config/logger';
import preferencesService from '@app/models/preferences';
import { Tag } from '@app/models/tag';

const getCases = async (req: Request) => {
  const projectId = req.query._id as string;
  if (!projectId) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field :project_id is a required field.',
    );
  }

  const project = await req.models.Project.query()
    .where('uid', projectId)
    .whereNull('deletedAt')
    .first();

  if (!project) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      'Field :project_id is not a valid or accessible project.',
    );
  }

  const filters = req.query._filters as any;
  const {
    offset = 0,
    limit = 250,
    suite_id: suiteId,
    created_after: createdAfter,
    created_before: createdBefore,
    created_by: createdBy,
    filter,
    priority_id: priorityId,
    section_id: sectionId,
    template_id: templateId,
    updated_after: updatedAfter,
    updated_before: updatedBefore,
  } = filters;
  const parentSuite = await req.models.Tag.query()
    .where({
      systemType: 'folder',
      parentUid: null,
      deletedAt: null,
      projectUid: projectId,
    })
    .first();
  const casesQuery = req.models.TestCase.query()
    .orderBy('uid', 'desc')
    .withGraphFetched('tags(isCaseTag)')
    .where('projectUid', projectId)
    .whereNull('deletedAt')
    .where('active', true);

  const countQuery = req.models.TestCase.query()
    .where('projectUid', projectId)
    .whereNull('deletedAt')
    .where('active', true)
    .count('uid as count');
  if (suiteId) {
    const suite = await req.models.Tag.query()
      .where({
        systemType: 'folder',
        deletedAt: null,
        projectUid: projectId,
        uid: suiteId,
      })
      .first();
    if (!suite) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Field :suite_id is not a valid test suite.');
    }
    const subFolders = await req.models.Tag.query()
      .where({
        systemType: 'folder',
        deletedAt: null,
        projectUid: projectId,
        parentUid: suiteId,
      })
      .orderBy('uid', 'asc');
    const folderUids = [suiteId, ...subFolders.map((folder) => folder.uid)];
    casesQuery.whereIn('parentUid', folderUids);
    countQuery.whereIn('parentUid', folderUids);
  }
  if (createdAfter) {
    casesQuery.where('createdAt', '>=', new Date(createdAfter).toISOString());
    countQuery.where('createdAt', '>=', new Date(createdAfter).toISOString());
  }
  if (createdBefore) {
    casesQuery.where('createdAt', '<=', new Date(createdBefore).toISOString());
    countQuery.where('createdAt', '<=', new Date(createdBefore).toISOString());
  }
  if (createdBy) {
    const creators = createdBy.split(',');
    casesQuery.whereIn('createdBy', creators);
    countQuery.whereIn('createdBy', creators);
  }
  if (filter) {
    casesQuery.whereRaw(`name LIKE '%${filter}%'`);
    countQuery.whereRaw(`name LIKE '%${filter}%'`);
  }
  if (sectionId) {
    casesQuery.where('parentUid', sectionId);
    countQuery.where('parentUid', sectionId);
  }
  if (templateId) {
    casesQuery.where('testTemplateUid', templateId);
    countQuery.where('testTemplateUid', templateId);
  }
  if (updatedAfter) {
    casesQuery.where('updatedAt', '>=', new Date(updatedAfter).toISOString());
    countQuery.where('updatedAt', '>=', new Date(updatedAfter).toISOString());
  }
  if (updatedBefore) {
    casesQuery.where('updatedAt', '<=', new Date(updatedBefore).toISOString());
    countQuery.where('updatedAt', '<=', new Date(updatedBefore).toISOString());
  }
  if (priorityId) {
    casesQuery.where('priority', priorityId);
    countQuery.where('priority', priorityId);
  }

  const total = await countQuery.first();
  const totalCount = Number((total as any).count);
  casesQuery.limit(limit).offset(offset);

  const cases = await casesQuery;
  const caseData = cases.map((item: any) => ({
    id: item.testCaseRef,
    title: item.name,
    section_id: item.parentUid,
    template_id: item.testTemplateUid,
    type_id: item.tags.length > 0 ? item.tags[0].uid : null,
    priority_id: item.priority,
    created_by: item.createdBy,
    created_on: new Date(item.createdAt).getTime(),
    updated_by: item.createdBy,
    updated_on: new Date(item.updatedAt).getTime(),
    suite_id: suiteId || parentSuite?.uid,
    is_deleted: 0,
    custom_steps_separated: transformCustomSteps(item.steps),
    ...transformTemplateFields(item.customFields.templateFields),
    comments: [],
  }));

  const links = {
    next: Number(offset) + Number(limit) >= totalCount ? null : buildLink('api/v2/get_cases', Number(offset) + Number(limit), Number(limit), projectId, filters),
    prev: Number(offset) <= 0 ? null : buildLink('api/v2/get_cases', Math.max(0, Number(offset) - Number(limit)), Number(limit), projectId, filters),
  };

  return {
    offset: Number(offset),
    limit: Number(limit),
    size: caseData.length,
    _links: links,
    cases: caseData,
  };
};

const getCase = async (req: Request) => {
  const caseId = req.query._id as string;

  if (!caseId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :case_id is a required field.');
  }

  // Fetch the test case from the database
  const testCase: any = await req.models.TestCase.query()
    .where('testCaseRef', caseId)
    .where('active', true)
    .whereNull('deletedAt')
    .withGraphFetched('tags(isCaseTag)')
    .first();

  // Handle case not found
  if (!testCase) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, 'Case not found.');
  }

  const suite = await req.models.Tag.query()
    .where({
      systemType: 'folder',
      parentUid: null,
      deletedAt: null,
      projectUid: testCase.projectUid,
    })
    .first();

  return {
    id: testCase.testCaseRef,
    title: testCase.name,
    section_id: testCase.parentUid,
    template_id: testCase.testTemplateUid,
    type_id: testCase.tags.length > 0 ? testCase.tags[0].uid : null,
    priority_id: testCase.priority,
    created_by: testCase.createdBy,
    created_on: new Date(testCase.createdAt).getTime(),
    updated_by: testCase.createdBy,
    updated_on: new Date(testCase.updatedAt).getTime(),
    suite_id: suite?.uid,
    is_deleted: 0,
    custom_steps_separated: transformCustomSteps(testCase.steps),
    ...transformTemplateFields(testCase.customFields.templateFields),
    comments: [],
  };
};

// TODO: get case history from old versions of test case
const getCaseHistory = async (req: Request) => {
  const caseId = req.query._id as string;

  const history = await req.models.TestCase.query()
    .where('testCaseRef', caseId)
    .orderBy('createdAt', 'desc');
  // TODO: make history of changes done on the case
  return history; // Dummy response
};

const createCase = async (req: Request) => {
  const {
    title,
    template_id: templateId,
    priority_id: priorityId,
    custom_steps_separated: customStepsSeparated,
    type_id: tagId,
  } = req.body;

  const trx = await req.knexDB.transaction();
  const owner = req.locals.handle.ownerUid;
  const { ownerType } = req.locals.handle;
  const caseSteps: Array<Partial<TestCaseStep>> = [];
  const fgaWrites: FGARawWrite[] = [];
  const caseTags: Array<Partial<TestCaseTag>> = [];
  const { defaults } = await preferencesService.getDefaultsAndCompleted(
    req.sharedKnexDB,
    ownerType,
    req.locals.handle.ownerUid,
  );
  const sectionId = req.query._id as string;
  if (!sectionId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :section_id is required.');
  }

  const section = await req.models.Tag.query()
    .where('uid', sectionId)
    .where('systemType', 'folder')
    .whereNull('deletedAt')
    .first();
  if (!section) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :section_id is not a valid section.');
  }
  let testTemplate;
  if (templateId) {
    testTemplate = await req.models.TestTemplate.query()
      .where('uid', templateId)
      .first();
    if (!testTemplate) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :template_id is not a valid template.');
    }
  } else {
    testTemplate = await req.models.TestTemplate.query()
      .where('projectUid', section.projectUid)
      .where('isDefault', true)
      .first();
  }

  const { templateFields } = testTemplate.customFields;
  const customFields = getCaseCustomFields(templateFields, [], req.body);
  const sharedSteps = {};
  const sharedStepIds = new Set<number>();
  if (customStepsSeparated) {
    for (const step of customStepsSeparated) {
      if (step.shared_step_id) {
        const sharedStep = await req.models.SharedTestStep.query()
          .where('uid', step.shared_step_id)
          .first();
        if (sharedStep) {
          sharedSteps[step.shared_step_id] = sharedStep;
          sharedStepIds.add(step.shared_step_id);
        } else {
          throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :shared_step_id is not a valid shared step.');
        }
      }
    }
  }
  const steps = getCaseSteps(customStepsSeparated, sharedSteps);
  const uid = await getNextId(trx, req.models.TestCase);

  const testCase = {
    externalId: '',
    source: '',
    name: title,
    customFields,
    projectUid: section.projectUid,
    parentUid: parseInt(sectionId, 10),
    priority: priorityId || defaults?.testCase.priority,
    testTemplateUid: templateId || testTemplate.uid,
    active: true,
    version: 1,
    createdBy: req.locals.accessToken.createdBy,
    uid,
    testCaseRef: uid,
    steps,
  };

  const sharedStepsArray = [...sharedStepIds];

  if (sharedStepsArray?.length > 0) {
    caseSteps.push(
      ...sharedStepsArray.map((sharedStepId: number) => ({
        testCaseRef: uid,
        sharedTestStepRef: sharedStepId,
        testCaseAddedVersion: [1],
      })),
    );
  }
  if (tagId) {
    caseTags.push({ tagUid: tagId, testCaseRef: uid });
  }

  fgaWrites.push({
    objectType: 'case',
    objectId: uid,
    relation: 'owner',
    subjectType: ownerType, // type of entity
    subjectId: owner, // entity id
  });

  const caseQuery: any = await req.models.TestCase.query(trx)
    .insert(testCase)
    .returning('*');

  if (sectionId) {
    await attachParent(caseQuery.testCaseRef, 1, parseInt(sectionId, 10), trx);
  }

  if (caseSteps.length > 0) {
    await req.models.TestCaseStep.query(trx).insert(caseSteps);
  }

  if (caseTags.length > 0) {
    await req.models.TestCaseTag.query(trx).insert(caseTags);
  }
  await caseQuery.$fetchGraph('tags(isCaseTag)', { transaction: trx });

  await req.fga.create(...fgaWrites);

  await trx.commit();

  const suite = await req.models.Tag.query()
    .where({
      systemType: 'folder',
      parentUid: null,
      deletedAt: null,
      projectUid: section.projectUid,
    })
    .first();
  return {
    id: caseQuery.uid,
    title: caseQuery.name,
    section_id: caseQuery.parentUid,
    template_id: caseQuery.testTemplateUid,
    type_id: caseQuery.tags.length > 0 ? caseQuery.tags[0].uid : null,
    priority_id: caseQuery.priority,
    created_by: caseQuery.createdBy,
    created_on: new Date(caseQuery.createdAt).getTime(),
    updated_by: caseQuery.createdBy,
    updated_on: new Date(caseQuery.updatedAt).getTime(),
    suite_id: suite?.uid,
    is_deleted: 0,
    custom_steps_separated: transformCustomSteps(caseQuery.steps),
    ...transformTemplateFields(caseQuery.customFields.templateFields),
    comments: [], // TODO: decide whether to include this or not
  };
};

const updateTestCase = async (req: Request) => {
  const caseId = req.query._id as string;
  const {
    title: name,
    template_id: templateId,
    priority_id: priorityId,
    type_id: tagId,
    custom_steps_separated: customStepsSeparated,
    section_id: parentId,
  } = req.body;
  const trx = await req.knexDB.transaction();

  try {
    const testCase = await req.models.TestCase.query()
      .where('testCaseRef', caseId)
      .where('active', true)
      .whereNull('deletedAt')
      .first();
    if (!testCase) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :case_id is not a valid case.');
    }

    const sharedStepIds = [];
    const sharedSteps = {};
    if (customStepsSeparated) {
      for (const step of customStepsSeparated) {
        if (step.shared_step_id) {
          const sharedStep = await req.models.SharedTestStep.query()
            .where('uid', step.shared_step_id)
            .first();
          if (sharedStep) {
            sharedSteps[step.shared_step_id] = sharedStep;
            sharedStepIds.push(step.shared_step_id);
          } else {
            throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :shared_step_id is not a valid shared step.');
          }
        }
      }
    }

    const caseSteps = getCaseSteps(customStepsSeparated, sharedSteps);
    let testTemplate;
    if (templateId) {
      testTemplate = await req.models.TestTemplate.query()
        .where('uid', templateId)
        .first();
      if (!testTemplate) {
        throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :template_id is not a valid template.');
      }
    } else {
      testTemplate = await req.models.TestTemplate.query()
        .where('uid', testCase.testTemplateUid)
        .first();
    }

    const { templateFields } = testTemplate.customFields;
    const customFields = getCaseCustomFields(templateFields, testCase.customFields.templateFields, req.body);

    const incomingUpdate: any = {
      ...(name && { name }),
      ...(priorityId && { priority: priorityId }),
      ...(customFields && { customFields }),
      parentUid: parentId || testCase.parentUid,
      testTemplateUid: templateId || testTemplate.uid,
    };
    if (caseSteps && caseSteps.length > 0) {
      incomingUpdate.steps = caseSteps;
    }

    // get the latest version
    // mark all versions as inactive
    // create new version
    const [latestCase] = await Promise.all([
      req.models.TestCase.query(trx)
        .where({ testCaseRef: caseId })
        .orderBy('version', 'DESC')
        .first(),
      req.models.TestCase.query(trx)
        .where({ testCaseRef: caseId })
        .patch({ active: false }),
    ]);

    incomingUpdate.customFields = {
      ...latestCase.customFields,
      ...incomingUpdate.customFields,
    };

    const newFields = {
      ...incomingUpdate,
      version: latestCase.version + 1,
      createdBy: latestCase.createdBy,
      active: true,
    };

    Object.keys(latestCase).forEach((key) => {
      if (
        key !== 'uid'
        && key.indexOf('At') < 0
        // eslint-disable-next-line no-prototype-builtins
        && !newFields.hasOwnProperty(key)
        && latestCase[key]
      ) {
        newFields[key] = latestCase[key];
      }
    });
    const newCase: any = await req.models.TestCase.query(trx)
      .insert(newFields)
      .returning('*');
    if (parentId) {
      await attachParent(
        latestCase.testCaseRef,
        newFields.version,
        parentId,
        trx,
      );
    } else {
      await attachParent(
        latestCase.testCaseRef,
        newFields.version,
        latestCase.parentUid,
        trx,
      );
    }

    const currentTestCaseSteps = await req.models.TestCaseStep.query(trx)
      .whereNull('deletedAt')
      .where('testCaseRef', caseId)
      .select('sharedTestStepRef', 'testCaseAddedVersion');

    const currentSharedStepIds = currentTestCaseSteps.map(
      (step) => step.sharedTestStepRef,
    );

    const removedSharedStepIds = currentSharedStepIds.filter(
      (id) => !sharedStepIds.includes(id),
    );

    if (removedSharedStepIds.length > 0) {
      await req.models.TestCaseStep.query(trx)
        .where('testCaseRef', caseId)
        .whereIn('sharedTestStepRef', removedSharedStepIds)
        .patch({
          deletedAt: req.knexDB.fn.now(),
        });
    }

    if (sharedStepIds && sharedStepIds.length > 0) {
      const testCaseSteps = sharedStepIds.map((sharedStepId: number) => {
        const existingStep = currentTestCaseSteps.find(
          (step) => step.sharedTestStepRef === sharedStepId,
        );
        return {
          testCaseRef: newCase.uid,
          sharedTestStepRef: sharedStepId,
          testCaseAddedVersion: existingStep
            ? [...existingStep.testCaseAddedVersion, newFields.version]
            : [newFields.version],
        };
      });
      await req.models.TestCaseStep.query(trx).insert(testCaseSteps);
    }

    if (tagId) {
      await updateTags(
        trx,
        newCase.testCaseRef,
        newCase.version,
        [tagId],
      );
    }
    await newCase.$fetchGraph('tags(isCaseTag)', { transaction: trx });

    if (parentId) await attachParent(newCase.testCaseRef, newCase.version, parentId, trx);

    await trx.commit();
    const suite = await req.models.Tag.query()
      .where({
        projectUid: newCase.projectUid,
        parentUid: null,
        deletedAt: null,
        systemType: 'folder',
      })
      .first();
    return {
      id: newCase.testCaseRef,
      title: newCase.name,
      section_id: newCase.parentUid,
      template_id: newCase.testTemplateUid,
      type_id: newCase.tags.length > 0 ? newCase.tags[0].uid : null,
      priority_id: newCase.priority,
      created_by: newCase.createdBy,
      created_on: new Date(newCase.createdAt).getTime(),
      updated_by: newCase.createdBy,
      updated_on: new Date(newCase.updatedAt).getTime(),
      suite_id: suite?.uid,
      is_deleted: 0,
      custom_steps_separated: transformCustomSteps(newCase.steps),
      ...transformTemplateFields(newCase.customFields.templateFields),
      comments: [],
    };
  } catch (error) {
    await trx.rollback();
    logger.error(`test case cannot be updated testCaseUId: ${caseId}`, error);
    throw error;
  }
};

export async function attachParent(
  caseRef: number,
  version: number,
  parentId: number,
  trx: Knex,
) {
  if (!parentId) return;

  // clear all existing attachments
  await trx.raw(
    `update "testCaseTags" as tc set "deletedAt"=current_timestamp, "testCaseRemovedVersion" = array_append("testCaseRemovedVersion",:version)
    from tags where "tagUid"=tags.uid and tags."systemType"='folder' and "testCaseRef"=:caseRef and tc."deletedAt" is null
    and "tagUid" <> :parentId
    `,
    { caseRef, version, parentId },
  );

  const attachment = await TestCaseTag.query(trx)
    .where({
      testCaseRef: caseRef,
      tagUid: parentId,
    })
    .first();

  if (!attachment) {
    const folder = await Tag.query(trx)
      .where({
        systemType: 'folder',
        deletedAt: null,
        uid: parentId,
      })
      .first();
    if (!folder) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errorConstants.FOLDER_NOT_FOUND,
      );
    }
    return;
  }

  if (attachment.deletedAt) {
    await TestCaseTag.query(trx)
      .findById(attachment.uid)
      .patch({
        deletedAt: trx.raw('null'),
        testCaseAddedVersion: trx.raw(
          'array_append("testCaseAddedVersion",?)',
          [version],
        ),
      });
  }
}

const updateTestCases = async (req: Request) => {
  const suiteId = req.query._id as string;
  if (!suiteId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :suite_id is required.');
  }

  const caseIds = req.body.case_ids as string[];
  if (!caseIds) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :case_ids is required.');
  }

  const cases = await req.models.TestCase.query()
    .whereIn('testCaseRef', caseIds)
    .where('active', true)
    .whereNull('deletedAt');

  const suite = await req.models.Tag.query()
    .where({
      uid: suiteId,
      parentUid: null,
      deletedAt: null,
      systemType: 'folder',
    })
    .first();
  if (!suite) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :suite_id is not a valid suite.');
  }
  if (cases.length !== caseIds.length) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :case_ids are not valid.');
  }
  const updatedCases = [];
  const trx = await req.knexDB.transaction();
  const {
    title: name,
    template_id: templateId,
    priority_id: priorityId,
    type_id: tagId,
    custom_steps_separated: customStepsSeparated,
    section_id: parentId,
  } = req.body;
  for (const testCase of cases) {
    if (testCase.projectUid !== suite.projectUid) {
      throw new ApplicationError(StatusCodes.FORBIDDEN, 'Invalid data');
    }

    const sharedStepUids = new Set<number>();
    const sharedSteps = {};
    if (customStepsSeparated) {
      for (const step of customStepsSeparated) {
        if (step.shared_step_id) {
          const sharedStep = await req.models.SharedTestStep.query()
            .where('uid', step.shared_step_id)
            .first();
          if (sharedStep) {
            sharedSteps[step.shared_step_id] = sharedStep;
            sharedStepUids.add(step.shared_step_id);
          } else {
            throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :shared_step_id is not a valid shared step.');
          }
        }
      }
    }
    const steps = getCaseSteps(customStepsSeparated, sharedSteps);
    const sharedStepIds = [...sharedStepUids];
    let testTemplate;
    if (templateId) {
      testTemplate = await req.models.TestTemplate.query()
        .where('uid', templateId)
        .first();
      if (!testTemplate) {
        throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :template_id is not a valid template.');
      }
    } else {
      testTemplate = await req.models.TestTemplate.query()
        .where('uid', testCase.testTemplateUid)
        .first();
    }

    const { templateFields } = testTemplate.customFields;
    const customFields = getCaseCustomFields(templateFields, testCase.customFields.templateFields, req.body);
    if (parentId) {
      const section = await req.models.Tag.query()
        .where({
          uid: parentId,
          projectUid: suite.projectUid,
          deletedAt: null,
          systemType: 'folder',
        })
        .first();
      if (!section) {
        throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :section_id is not a valid section.');
      }
    }
    const incomingUpdate: any = {
      ...(name && { name }),
      ...(priorityId && { priority: priorityId }),
      ...(customFields && { customFields }),
      parentUid: parentId || testCase.parentUid,
      testTemplateUid: templateId || testTemplate.uid,
    };
    if (steps && steps.length > 0) {
      incomingUpdate.steps = steps;
    }
    Object.keys(testCase).forEach((key) => {
      if (
        key !== 'uid'
        && key.indexOf('At') < 0
        // eslint-disable-next-line no-prototype-builtins
        && !incomingUpdate.hasOwnProperty(key)
        && testCase[key]
      ) {
        incomingUpdate[key] = testCase[key];
      }
    });

    // get the latest version
    // mark all versions as inactive
    // create new version
    const [latestCase] = await Promise.all([
      req.models.TestCase.query(trx)
        .where({ testCaseRef: testCase.testCaseRef })
        .orderBy('version', 'DESC')
        .first(),
      req.models.TestCase.query(trx)
        .where({ testCaseRef: testCase.testCaseRef })
        .patch({ active: false }),
    ]);

    incomingUpdate.customFields = {
      ...latestCase.customFields,
      ...incomingUpdate.customFields,
    };

    const version = latestCase.version + 1;

    const newCase = await req.models.TestCase.query(trx).insert({
      ...incomingUpdate,
      createdBy: latestCase.createdBy,
      projectUid: suite.projectUid,
      testCaseRef: testCase.testCaseRef,
      version,
      active: true,
    });

    if (parentId) {
      await attachParent(
        latestCase.testCaseRef,
        newCase.version,
        parentId,
        trx,
      );
    } else {
      await attachParent(
        latestCase.testCaseRef,
        newCase.version,
        latestCase.parentUid,
        trx,
      );
    }

    const currentTestCaseSteps = await req.models.TestCaseStep.query(trx)
      .whereNull('deletedAt')
      .where('testCaseRef', latestCase.testCaseRef)
      .select('sharedTestStepRef', 'testCaseAddedVersion');

    const currentSharedStepIds = currentTestCaseSteps.map(
      (step) => step.sharedTestStepRef,
    );

    const removedSharedStepIds = currentSharedStepIds.filter(
      (id) => !sharedStepIds.includes(id),
    );

    if (removedSharedStepIds.length > 0) {
      await req.models.TestCaseStep.query(trx)
        .where('testCaseRef', latestCase.testCaseRef)
        .whereIn('sharedTestStepRef', removedSharedStepIds)
        .patch({
          deletedAt: req.knexDB.fn.now(),
        });
    }

    if (sharedStepIds && sharedStepIds.length > 0) {
      const testCaseSteps = sharedStepIds.map((sharedStepId: number) => {
        const existingStep = currentTestCaseSteps.find(
          (step) => step.sharedTestStepRef === sharedStepId,
        );
        return {
          testCaseRef: newCase.uid,
          sharedTestStepRef: sharedStepId,
          testCaseAddedVersion: existingStep
            ? [...existingStep.testCaseAddedVersion, newCase.version]
            : [newCase.version],
        };
      });
      await req.models.TestCaseStep.query(trx).insert(testCaseSteps);
    }

    if (tagId) {
      await updateTags(
        trx,
        newCase.testCaseRef,
        newCase.version,
        [tagId],
      );
    }
    await newCase.$fetchGraph('tags(isCaseTag)', { transaction: trx });
    // Commit the transaction
    updatedCases.push({
      id: newCase.testCaseRef,
      title: newCase.name,
      section_id: newCase.parentUid,
      template_id: newCase.testTemplateUid,
      type_id: (newCase as any).tags.length > 0 ? (newCase as any).tags[0].uid : null,
      priority_id: newCase.priority,
      created_by: newCase.createdBy,
      created_on: new Date(newCase.createdAt).getTime(),
      updated_by: newCase.createdBy,
      updated_on: new Date(newCase.updatedAt).getTime(),
      suite_id: suite?.uid,
      is_deleted: 0,
      custom_steps_separated: transformCustomSteps(newCase.steps),
      ...transformTemplateFields(newCase.customFields.templateFields),
      comments: [],
    });
  }
  await trx.commit();
  return {
    updated_cases: updatedCases,
  };
};

const copyCasesToSection = async (req: Request) => {
  const sectionId = req.query._id as string;
  if (!sectionId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :section_id is required.');
  }
  const existingSection = await req.models.Tag.query()
    .where({
      uid: sectionId,
      deletedAt: null,
      systemType: 'folder',
    })
    .first();
  if (!existingSection) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :section_id is not a valid section.');
  }
  const caseIds = req.body.case_ids as string[];
  if (!caseIds) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :case_ids is required.');
  }
  const cases: any = await req.models.TestCase.query()
    .whereIn('testCaseRef', caseIds)
    .where('projectUid', existingSection.projectUid)
    .whereNull('deletedAt')
    .where('active', true)
    .withGraphFetched('tags(isCaseTag)');

  if (cases.length !== caseIds.length) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :case_ids are not valid.');
  }
  const trx = await req.knexDB.transaction();
  for (const testCase of cases) {
    const owner = req.locals.handle.ownerUid;
    const { ownerType } = req.locals.handle;
    const caseSteps: Array<Partial<TestCaseStep>> = [];
    const fgaWrites: FGARawWrite[] = [];
    const caseTags: Array<Partial<TestCaseTag>> = [];
    const { steps } = testCase;
    const sharedStepIds = new Set<number>();
    if (steps && steps.length > 0) {
      for (const step of steps) {
        if (step.sharedStepUid) {
          sharedStepIds.add(step.sharedStepUid);
        }
      }
    }
    const uid = await getNextId(trx, req.models.TestCase);
    const testCaseData = {
      externalId: testCase.externalId,
      source: testCase.source,
      name: testCase.name,
      customFields: testCase.customFields,
      priority: testCase.priority,
      testTemplateUid: testCase.testTemplateUid,
      projectUid: existingSection.projectUid,
      parentUid: parseInt(sectionId, 10),
      active: true,
      version: 1,
      createdBy: req.locals.accessToken.createdBy,
      uid,
      testCaseRef: uid,
      steps,
    };

    const sharedStepsArray = [...sharedStepIds];

    if (sharedStepsArray?.length > 0) {
      caseSteps.push(
        ...sharedStepsArray.map((sharedStepId: number) => ({
          testCaseRef: uid,
          sharedTestStepRef: sharedStepId,
          testCaseAddedVersion: [1],
        })),
      );
    }

    if (testCase.tags.length > 0) {
      for (const tag of testCase.tags) {
        caseTags.push({ tagUid: tag.uid, testCaseRef: uid });
      }
    }

    fgaWrites.push({
      objectType: 'case',
      objectId: uid,
      relation: 'owner',
      subjectType: ownerType, // type of entity
      subjectId: owner, // entity id
    });

    const caseQuery = await req.models.TestCase.query(trx)
      .insert(testCaseData);

    if (sectionId) {
      await attachParent(caseQuery.testCaseRef, 1, parseInt(sectionId, 10), trx);
    }

    if (caseSteps.length > 0) {
      await req.models.TestCaseStep.query(trx).insert(caseSteps);
    }

    if (caseTags.length > 0) {
      await req.models.TestCaseTag.query(trx).insert(caseTags);
    }

    await req.fga.create(...fgaWrites);
  }
  await trx.commit();
};

const moveCasesToSection = async (req: Request) => {
  const sectionId = req.query._id as string;
  if (!sectionId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :section_id is required.');
  }
  const existingSection = await req.models.Tag.query()
    .where({
      uid: sectionId,
      deletedAt: null,
      systemType: 'folder',
    })
    .first();
  if (!existingSection) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :section_id is not a valid section.');
  }
  const cases = await req.models.TestCase.query()
    .whereIn('testCaseRef', req.body.case_ids as string[])
    .where('projectUid', existingSection.projectUid)
    .whereNull('deletedAt')
    .where('active', true);
  if (cases.length !== req.body.case_ids.length) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :case_ids are not valid.');
  }
  for (const testCase of cases) {
    await attachParent(testCase.testCaseRef, 1, parseInt(sectionId, 10), req.knexDB);
  }
};

const deleteCase = async (req: Request) => {
  const caseId = req.query._id as string;
  if (!caseId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :case_id is required.');
  }
  const { soft } = req.body;
  const testCase = await req.models.TestCase.query()
    .where('testCaseRef', caseId)
    .whereNull('deletedAt')
    .where('active', true)
    .first();
  if (!testCase) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, 'Field :case_id is not a valid case.');
  }
  if (soft && parseInt(soft as string) === 1) {
    const executions = await req.models.TestExecution.query()
      .where('testCaseRef', testCase.testCaseRef)
      .whereNull('deletedAt');

    const executionsIds = executions.map((execution) => execution.uid);
    const results = await req.models.TestResult.query()
      .whereIn('testExecutionUid', executionsIds)
      .whereNull('deletedAt');

    return {
      tests: executions.length,
      results: results.length,
    };
  }
  await req.models.TestCase.query()
    .where('testCaseRef', caseId)
    .patch({ deletedAt: req.knexDB.fn.now() });

  await req.models.TestCaseTag.query()
    .where('testCaseRef', caseId)
    .patch({ deletedAt: req.knexDB.fn.now() });
};

const deleteTestCases = async (req: Request) => {
  const suiteId = req.query._id as string;
  if (!suiteId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :suite_id is required.');
  }
  const caseIds = req.body.case_ids as string[];
  if (!caseIds) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :case_ids is required.');
  }
  const suite = await req.models.Tag.query()
    .where({
      uid: suiteId,
      parentUid: null,
      deletedAt: null,
      systemType: 'folder',
    })
    .first();
  if (!suite) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :suite_id is not a valid suite.');
  }
  const cases = await req.models.TestCase.query()
    .whereIn('testCaseRef', caseIds)
    .where('projectUid', suite.projectUid)
    .where('active', true)
    .whereNull('deletedAt');
  if (cases.length !== caseIds.length) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Some test cases no longer exist or are from different test suites.');
  }
  const soft = req.body.soft as number;
  if (soft && soft === 1) {
    const executions = await req.models.TestExecution.query()
      .whereIn('testCaseRef', cases.map((c) => c.testCaseRef))
      .whereNull('deletedAt');
    const results = await req.models.TestResult.query()
      .whereIn('testExecutionUid', executions.map((e) => e.uid))
      .whereNull('deletedAt');
    return {
      tests: executions.length,
      results: results.length,
    };
  }
  await req.knexDB.transaction(async (trx) => {
    const testCases = await req.models.TestCase.query(trx)
      .whereIn('uid', caseIds)
      .patch({ deletedAt: req.knexDB.fn.now() })
      .returning('*');

    await req.models.TestCaseTag.query(trx)
      .whereIn(
        'testCaseRef',
        testCases.map((t) => t.testCaseRef),
      )
      .patch({ deletedAt: req.knexDB.fn.now() });
  });
};

async function updateTags(
  trx: Knex.Transaction,
  caseRef: number,
  version: number,
  tagUids: number[],
) {
  const existingTags = await TestCaseTag.query(trx)
    .where({
      testCaseRef: caseRef,
      systemType: 'tag',
      'testCaseTags.deletedAt': null,
    })
    .innerJoin('tags', 'tags.uid', 'tagUid')
    .select('testCaseTags.*');
  const existingTagIds = existingTags.map((t) => t.tagUid);
  const finalTagIds = new Set(existingTagIds);

  if (tagUids?.length > 0) {
    tagUids.forEach((tagUid: number) => finalTagIds.add(tagUid));
  }

  const tagList = Array.from(finalTagIds);
  if (tagList.length === 0) return;

  for (const tagUid of tagList) {
    const insert = (await TestCaseTag.query(trx)
      .toKnexQuery()
      .insert({
        tagUid,
        testCaseRef: caseRef,
        testCaseAddedVersion: [version],
      })
      .onConflict(['testCaseRef', 'tagUid'])
      .ignore()) as any;

    if (insert.rowCount > 0) continue;

    await TestCaseTag.query(trx)
      .where({ tagUid, testCaseRef: caseRef })
      .patch({
        deletedAt: trx.raw('null'),
        testCaseRemovedVersion: trx.raw(
          'array_append("testCaseRemovedVersion",?)',
          [version],
        ),
      });
  }
}

export default {
  getCases: httpHandler(getCases),
  getCase: httpHandler(getCase),
  getCaseHistory: httpHandler(getCaseHistory),
  createCase: httpHandler(createCase),
  updateTestCase: httpHandler(updateTestCase),
  updateTestCases: httpHandler(updateTestCases),
  copyCasesToSection: httpHandler(copyCasesToSection),
  moveCasesToSection: httpHandler(moveCasesToSection),
  deleteCase: httpHandler(deleteCase),
  deleteCases: httpHandler(deleteTestCases),
};
