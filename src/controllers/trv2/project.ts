import { ApplicationError, httpHandler } from '@app/lib/http';
import { Request } from 'express';
import env from '@app/config/env';
import { StatusCodes } from 'http-status-codes';
import { generateKey } from '@app/utils/projectKey';
import { UniqueViolationError } from 'objection';
import errorConstants from '@app/constants/errors';
import { kebabCase } from 'lodash';
import { buildLink } from '@app/utils/buildTestrailLinks';
import { startWorkflow } from '@app/temporal/client';
import { DefaultDashboardData } from '@app/temporal/activities/dashboard';
import { DefaultTemplateData } from '@app/temporal/activities/template';
import { ProjectData } from '@app/temporal/activities/softDeleteProjectRelatedRecords';

const getProjects = async (req: Request) => {
  const owner = req.locals.handle.ownerUid;
  const { ownerType } = req.locals.handle;

  const filters = req.query._filters as any;
  const {
    limit = 250,
    offset = 0,
    is_completed: isCompleted,
  } = filters;

  const relatedProjects = (
    await req.fga.query(`${ownerType}:${owner}`, 'project:', 'owner')
  ).map((tuple: any) => tuple.key.object.split(':')[1]);

  const totalProjectQuery = req.models.Project.query()
    .whereNull('deletedAt')
    .whereIn('uid', relatedProjects)
    .count('uid as total')
    .first();

  const sql = req.models.Project.query()
    .select(['projects.*'])
    .whereIn('projects.uid', relatedProjects)
    .limit(limit)
    .offset(offset);
  if (isCompleted) {
    const isActive = parseInt(isCompleted) === 0;
    if (isActive) {
      totalProjectQuery.whereNull('archivedAt');
      sql.whereNull('archivedAt');
    } else {
      totalProjectQuery.whereNotNull('archivedAt');
      sql.whereNotNull('archivedAt');
    }
  }
  const totalProjects = await totalProjectQuery;
  const baseURL = `${env.FRONTEND_URL}/${req.locals.handle.name}`;
  const projectData = await sql;
  const userSelectables = [
    'users.uid',
  ];
  const orgMembers = (await req.models.Membership.query()
    .where({
      accountUid: owner,
      accountType: ownerType,
      'memberships.deletedAt': null,
    })
    .innerJoin('users', 'userUid', 'users.uid')
    .select(userSelectables)) as any[];

  const projects = projectData.map((project: any) => ({
    id: project.uid,
    announcement: project.customFields?.description,
    completed_on: project.archivedAt ? new Date(project.archivedAt).getTime() : null,
    default_role_id: null,
    default_role: null,
    is_completed: !!project.archivedAt,
    name: project.name,
    show_announcement: !!project.customFields?.description,
    suite_mode: 1, // represents single suite mode followed in testfiesta
    url: `${baseURL}/${project.key}/cases`,
    users: orgMembers.map((user: any) => user.uid),
    groups: [],
  }));
  const total = parseInt((totalProjects as any).total);
  const links = {
    next: Number(offset) + Number(limit) >= total ? null : buildLink('api/v2/get_projects', Number(offset) + Number(limit), Number(limit), '', filters),
    prev: Number(offset) <= 0 ? null : buildLink('api/v2/get_projects', Math.max(0, Number(offset) - Number(limit)), Number(limit), '', filters),
  };
  const pagination: any = {
    offset: parseInt(offset),
    limit: parseInt(limit),
    size: projects.length,
    _links: links,
    results: projects,
  };
  return pagination;
};

const getProject = async (req: Request) => {
  const id = req.query._id as string;
  if (!id) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field :project_id is a required field.',
    );
  }

  const project = await req.models.Project.query()
    .findById(id)
    .whereNull('deletedAt');

  if (!project) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field :project_id is not a valid or accessible project.',
    );
  }
  const baseURL = `${env.FRONTEND_URL}/${req.locals.handle.name}`;
  return {
    id: project.uid,
    announcement: project.customFields?.description,
    completed_on: project.archivedAt ? new Date(project.archivedAt).getTime() : null,
    default_role_id: null,
    default_role: null,
    is_completed: !!project.archivedAt,
    name: project.name,
    show_announcement: !!project.customFields?.description,
    suite_mode: 1, // represents single suite mode followed in testfiesta
    url: `${baseURL}/${project.key}/cases`,
    users: [],
    groups: [],
  };
};

const addProject = async (req: Request) => {
  const trx = await req.knexDB.transaction();
  try {
    const { body } = req;
    const owner = req.locals.handle.ownerUid;
    const { ownerType } = req.locals.handle;
    const customFields = {
      status: 'active',
      description: body.announcement,
      star: false,
    };

    const project = await req.models.Project.query(trx).insert({
      name: body.name,
      key: generateKey(body.name),
      customFields,
    });

    await req.models.Tag.query(trx).insert({
      name: body.name,
      slug: kebabCase(body.name),
      projectUid: project.uid,
      customFields: { source: 'testfiesta' },
      entityTypes: ['cases'],
      systemType: 'folder',
    });

    await trx.commit();

    await req.fga.create(
      {
        objectType: 'project',
        objectId: project.uid,
        relation: 'owner',
        subjectType: ownerType,
        subjectId: owner,
      },
      {
        objectType: 'project',
        objectId: project.uid,
        relation: 'parent',
        subjectType: ownerType,
        subjectId: owner,
      },
    );

    const param: DefaultTemplateData = {
      ownerType,
      ownerUid: owner,
      userId: req.locals.accessToken.createdBy,
      projectId: project.uid,
    };
    await startWorkflow('templateWorkflow', {
      taskQueue: 'template-queue',
      workflowId: `${owner}:template:${Date.now()}`,
      args: [param],
    });

    const param2: DefaultDashboardData = {
      projectUid: project.uid,
      ownerUid: owner,
      projectName: `${project.name} Default Dashboard`,
      systemDefault: false,
      editable: true,
    };

    await startWorkflow('dashboardWorkflow', {
      taskQueue: 'dashboard-queue',
      workflowId: `${owner}:dashboard:${Date.now()}`,
      args: [param2],
    });

    return {
      id: project.uid,
      name: project.name,
      announcement: project.customFields?.description,
      show_announcement: !!project.customFields?.description,
      is_completed: false,
      completed_on: null,
      suite_mode: 1,
      default_role_id: null,
      case_statuses_enabled: false,
      url: `${env.FRONTEND_URL}/${req.locals.handle.name}/${project.key}/cases`,
      users: [],
      groups: [],
    };
  } catch (error) {
    await trx.rollback();
    if (error instanceof UniqueViolationError) {
      throw new ApplicationError(
        StatusCodes.UNPROCESSABLE_ENTITY,
        errorConstants.PROJECT_KEY_EXISTS,
      );
    }
    throw error;
  }
};

const updateProject = async (req: Request) => {
  const projectId = req.query._id as string;
  if (!projectId) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field :project_id is a required field.',
    );
  }

  const project = await req.models.Project.query().findById(projectId);

  if (!project) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field :project_id is not a valid or accessible project.',
    );
  }
  const {
    name,
    announcement,
    is_completed: isCompleted,
  } = req.body;

  if (project.archivedAt && isCompleted !== false) {
    throw new ApplicationError(
      StatusCodes.UNPROCESSABLE_ENTITY,
      errorConstants.ARCHIVED_PROJECT_CANNOT_BE_UPDATED,
    );
  }

  const incomingUpdate :any = {
    name,
    customFields: {
      ...project.customFields,
      description: announcement || project.customFields.description,
    },
  };

  if (isCompleted) {
    incomingUpdate.archivedAt = req.knexDB.fn.now();
    incomingUpdate.customFields.status = 'archived';
  } else if (isCompleted === false) {
    incomingUpdate.archivedAt = null;
    incomingUpdate.customFields.status = 'active';
  }

  const updatedProject = await req.models.Project.query()
    .patch(incomingUpdate)
    .where('uid', project.uid)
    .returning('*');

  return {
    id: updatedProject[0].uid,
    name: updatedProject[0].name,
    announcement: updatedProject[0].customFields?.description,
    show_announcement: !!updatedProject[0].customFields?.description,
    is_completed: !!updatedProject[0].archivedAt,
    completed_on: updatedProject[0].archivedAt ? new Date(updatedProject[0].archivedAt).getTime() : null,
    suite_mode: 1,
    default_role_id: null,
    case_statuses_enabled: false,
    url: `${env.FRONTEND_URL}/${req.locals.handle.name}/${updatedProject[0].key}/cases`,
    users: [],
    groups: [],
  };
};

const deleteProject = async (req: Request) => {
  const projectId = req.query._id as string;
  if (!projectId) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field :project_id is a required field.',
    );
  }

  const project = await req.models.Project.query()
    .findById(projectId)
    .whereNull('deletedAt');

  if (!project) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field :project_id is not a valid or accessible project.',
    );
  }

  await req.models.Project.query()
    .findById(projectId)
    .patch({ deletedAt: req.knexDB.fn.now() });

  const param: ProjectData = {
    projectId: req.locals.project.uid,
    ownerUid: req.locals.handle.ownerUid,
  };

  await startWorkflow('softDeleteProjectRelatedRecordsWorkflow', {
    taskQueue: 'soft-delete-project-records-queue',
    workflowId: `${req.locals.handle.ownerUid}:soft-delete:${Date.now()}`,
    args: [param],
  });
};

export default {
  getProjects: httpHandler(getProjects),
  getProject: httpHandler(getProject),
  addProject: httpHandler(addProject),
  updateProject: httpHandler(updateProject),
  deleteProject: httpHandler(deleteProject),
};
