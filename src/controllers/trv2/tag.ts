import { Request } from 'express';
import { httpHand<PERSON> } from '@app/lib/http';

const getCaseTypes = async (req: Request) => {
  const tags = await req.models.Tag.query()
    .where({ deletedAt: null, systemType: 'tag' })
    .whereRaw('"entityTypes" @> ?', [['cases']])
    .select('*');

  return tags.map((tag) => ({
    id: tag.uid,
    name: tag.name,
    is_default: false,
  }));
};
export default {
  getCaseTypes: httpHandler(getCaseTypes),
};
