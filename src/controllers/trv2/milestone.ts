import { ApplicationError, httpHandler } from '@app/lib/http';
import { CreateMilestoneDTO, UpdateMilestoneDTO } from '@app/types/milestone';

import { Request } from 'express';
import { StatusCodes } from 'http-status-codes';
import preferencesService from '@app/models/preferences';
import { buildLink } from '@app/utils/buildTestrailLinks';
import env from '@app/config/env';
import { startWorkflow } from '@app/temporal/client';
import { UpdateMilestoneData } from '@app/temporal/activities/milestone';

const getMilestones = async (req: Request) => {
  const projectId = req.query._id as string;
  if (!projectId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: project_id is required field');
  }
  const project = await req.models.Project.query()
    .where('uid', projectId)
    .first();
  if (!project) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: project_id is not valid');
  }
  const filters = req.query._filters as any;
  const {
    is_completed: isCompleted,
    limit = 250,
    offset = 0,
  } = filters;
  const milestonesQuery = req.models.TestMilestone.query()
    .where({
      projectUid: project.uid,
      systemType: 'milestone',
      deletedAt: null,
    });
  const totalQuery = req.models.TestMilestone.query()
    .where({
      projectUid: project.uid,
      systemType: 'milestone',
      deletedAt: null,
    });
  if (isCompleted) {
    milestonesQuery.whereNotNull('archivedAt');
    totalQuery.whereNotNull('archivedAt');
  }
  const total = await totalQuery.count('uid as total').first();
  milestonesQuery.limit(limit);
  milestonesQuery.offset(offset);
  const milestones = await milestonesQuery;
  const links = {
    next: Number(offset) + Number(limit) >= (total as any).total ? null : buildLink('api/v2/get_milestones', Number(offset) + Number(limit), Number(limit), projectId, filters),
    prev: Number(offset) <= 0 ? null : buildLink('api/v2/get_milestones', Math.max(0, Number(offset) - Number(limit)), Number(limit), projectId, filters),
  };
  const baseURL = `${env.FRONTEND_URL}/${req.locals.handle.name}`;
  const milestoneData = milestones.map((milestone: any) => ({
    id: milestone.uid,
    name: milestone.name,
    description: milestone.description,
    start_on: new Date(milestone.customFields.startDate).getTime(),
    started_on: new Date(milestone.customFields.startDate).getTime(),
    is_started: new Date(milestone.customFields.startDate) < new Date(),
    due_on: milestone.customFields.dueAt ? new Date(milestone.customFields.dueAt).getTime() : null,
    is_completed: !!milestone.archivedAt,
    completed_on: milestone.archivedAt ? new Date(milestone.archivedAt).getTime() : null,
    project_id: project.uid,
    parent_id: milestone.parentUid,
    refs: null,
    url: `${baseURL}/${project.key}/milestones/${milestone.uid}`,
    milestones: [],
  }));
  const pagination: any = {
    offset: parseInt(offset),
    limit: parseInt(limit),
    size: milestones.length,
    _links: links,
    milestones: milestoneData,
  };
  return pagination;
};

const getMilestone = async (req: Request) => {
  const milestoneId = req.query._id as string;
  if (!milestoneId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: milestone_id is required field');
  }
  const milestone = await req.models.TestMilestone.query()
    .where({
      uid: milestoneId,
      systemType: 'milestone',
      deletedAt: null,
    })
    .first();
  if (!milestone) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field: milestone_id is not valid',
    );
  }
  const project = await req.models.Project.query()
    .where('uid', milestone.projectUid)
    .first();
  if (!project) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: project_id is not valid');
  }
  const baseURL = `${env.FRONTEND_URL}/${req.locals.handle.name}`;
  const milestoneData = {
    id: milestone.uid,
    name: milestone.name,
    description: milestone.description,
    start_on: new Date(milestone.customFields.startDate).getTime(),
    started_on: new Date(milestone.customFields.startDate).getTime(),
    is_started: new Date(milestone.customFields.startDate) < new Date(),
    due_on: milestone.customFields.dueAt ? new Date(milestone.customFields.dueAt).getTime() : null,
    is_completed: !!milestone.archivedAt,
    completed_on: milestone.archivedAt ? new Date(milestone.archivedAt).getTime() : null,
    project_id: project.uid,
    parent_id: milestone.parentUid,
    refs: null,
    url: `${baseURL}/${project.key}/milestones/${milestone.uid}`,
    milestones: [],
  };
  return milestoneData;
};

const createMilestone = async (req: Request) => {
  const projectId = req.query._id as string;
  if (!projectId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: project_id is required field');
  }
  const project = await req.models.Project.query()
    .where('uid', projectId)
    .first();
  if (!project) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: project_id is not valid');
  }
  const {
    name,
    description,
    due_on: dueAt,
    start_on: startDate = new Date(),
  } = req.body;
  const { defaults } = await preferencesService.getDefaultsAndCompleted(
    req.sharedKnexDB,
    req.locals.handle.ownerType,
    req.locals.handle.ownerUid,
  );

  const trx = await req.knexDB.transaction();
  const dto: CreateMilestoneDTO = {
    name,
    description,
    dueAt: dueAt ? new Date(dueAt).toISOString() : null,
    startDate: startDate ? new Date(startDate).toISOString() : null,
    status: defaults.milestone.status,
  };
  try {
    const milestone = await req.models.TestMilestone.create(trx, project.uid, {
      ...dto,
    });

    const owner = req.locals.handle.ownerUid;
    const { ownerType } = req.locals.handle;

    await req.fga.create({
      objectType: 'milestone',
      objectId: milestone.uid,
      relation: 'owner',
      subjectType: ownerType,
      subjectId: owner,
    });

    await trx.commit();

    const param: UpdateMilestoneData = {
      ownerType,
      ownerUid: owner,
      milestoneUids: [milestone.uid],
    };
    await startWorkflow('updateMilestoneWorkflow', {
      taskQueue: 'update-milestone-queue',
      workflowId: `update.milestone.${milestone.uid}.${Date.now()}`,
      args: [param],
    });

    const baseURL = `${env.FRONTEND_URL}/${req.locals.handle.name}`;

    return {
      id: milestone.uid,
      name: milestone.name,
      description: milestone.description,
      start_on: new Date(milestone.customFields.startDate).getTime(),
      started_on: new Date(milestone.customFields.startDate).getTime(),
      is_started: new Date(milestone.customFields.startDate) < new Date(),
      due_on: milestone.customFields.dueAt ? new Date(milestone.customFields.dueAt).getTime() : null,
      is_completed: !!milestone.archivedAt,
      completed_on: milestone.archivedAt ? new Date(milestone.archivedAt).getTime() : null,
      project_id: project.uid,
      parent_id: milestone.parentUid,
      refs: null,
      url: `${baseURL}/${project.key}/milestones/${milestone.uid}`,
      milestones: [],
    };
  } catch (error) {
    await trx.rollback();
    throw error;
  }
};
const deleteMilestone = async (req: Request) => {
  const id = req.query._id as any;
  if (!id) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: milestone_id is required field');
  }
  const milestone = await req.models.TestMilestone.query()
    .where({
      uid: id,
      systemType: 'milestone',
      deletedAt: null,
    })
    .first();
  if (!milestone) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: milestone_id is not valid');
  }
  const trx = await req.knexDB.transaction();
  const [mstone] = await req.models.TestMilestone.deleteByIds(trx, [
    +id,
  ]);

  if (mstone) {
    const param : UpdateMilestoneData = {
      milestoneUids: [mstone.uid],
      ownerType: req.locals.handle.ownerType,
      ownerUid: req.locals.handle.ownerUid,
    };
    await startWorkflow('updateMilestoneWorkflow', {
      taskQueue: 'update-milestone-queue',
      workflowId: `update.milestone.${mstone.uid}.${Date.now()}`,
      args: [param],
    });
  }
  await trx.commit();
  return {
    id: mstone.uid,
    name: mstone.name,
    description: mstone.description,
  };
};

const updateMilestone = async (req: Request) => {
  const id = req.query._id as any;
  if (!id) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: milestone_id is required field');
  }
  const milestone = await req.models.TestMilestone.query()
    .where({
      uid: id,
      systemType: 'milestone',
      deletedAt: null,
    })
    .first();
  if (!milestone) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: milestone_id is not valid');
  }

  const trx = await req.knexDB.transaction();
  const {
    name,
    description,
    due_on: dueAt,
    start_on: startDate,
    is_completed: archived,
  } = req.body;
  const dto: UpdateMilestoneDTO = {
    name,
    description,
    dueAt: dueAt ? new Date(dueAt).toISOString() : undefined,
    startDate: startDate ? new Date(startDate).toISOString() : undefined,
    archived,
  };
  try {
    const milestone = await req.models.TestMilestone.updateOne(trx, id, dto);
    await trx.commit();
    const param: UpdateMilestoneData = {
      milestoneUids: [milestone.uid],
      ownerType: req.locals.handle.ownerType,
      ownerUid: req.locals.handle.ownerUid,
    };
    await startWorkflow('updateMilestoneWorkflow', {
      taskQueue: 'update-milestone-queue',
      workflowId: `update.milestone.${milestone.uid}.${Date.now()}`,
      args: [param],
    });
    const baseURL = `${env.FRONTEND_URL}/${req.locals.handle.name}`;
    const project = await req.models.Project.query()
      .where('uid', milestone.projectUid)
      .first();
    if (!project) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field: project_id is not valid');
    }
    return {
      id: milestone.uid,
      name: milestone.name,
      description: milestone.description,
      start_on: new Date(milestone.customFields.startDate).getTime(),
      started_on: new Date(milestone.customFields.startDate).getTime(),
      is_started: new Date(milestone.customFields.startDate) < new Date(),
      due_on: milestone.customFields.dueAt ? new Date(milestone.customFields.dueAt).getTime() : null,
      is_completed: !!milestone.archivedAt,
      completed_on: milestone.archivedAt ? new Date(milestone.archivedAt).getTime() : null,
      project_id: milestone.projectUid,
      parent_id: milestone.parentUid,
      refs: null,
      url: `${baseURL}/${project.key}/milestones/${milestone.uid}`,
      milestones: [],
    };
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

export default {
  getMilestones: httpHandler(getMilestones),
  getMilestone: httpHandler(getMilestone),
  createMilestone: httpHandler(createMilestone),
  updateMilestone: httpHandler(updateMilestone),
  deleteMilestone: httpHandler(deleteMilestone),
};
