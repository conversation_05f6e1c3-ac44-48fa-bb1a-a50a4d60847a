import { ApplicationError, httpH<PERSON>ler } from '@app/lib/http';
import { Request } from 'express';
import { StatusCodes } from 'http-status-codes';
import { UniqueViolationError } from 'objection';
import errorConstants from '@app/constants/errors';

const getConfigurations = async (req: Request) => {
  const projectId = req.query._id;
  if (!projectId) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field project_id is a required field',
    );
  }
  const project = await req.models.Project.query().where('uid', projectId as string).first();
  if (!project) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field project_id is not valid id',
    );
  }

  const configurations = await req.models.Configuration.query()
    .whereNull('deletedAt')
    .where('projectUid', projectId as string)
    .where('systemType', 'config')
    .withGraphFetched('[options(isConfigOption)]');

  return configurations.map((configuration: any) => ({
    id: configuration.uid,
    name: configuration.name,
    project_id: configuration.projectUid,
    configs: configuration.options.map((option: any) => ({
      id: option.uid,
      name: option.name,
      group_id: option.parentUid,
    })),
  }));
};

const addConfigGroup = async (req: Request) => {
  const projectId = req.query._id;
  if (!projectId) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field project_id is a required field',
    );
  }
  const project = await req.models.Project.query().where('uid', projectId as string).first();
  if (!project) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field project_id is not valid id',
    );
  }
  const trx = await req.knexDB.transaction();
  try {
    const { name } = req.body;
    const config = await req.models.Configuration.create(trx, {
      name,
      projectUid: projectId as any,
      options: [],
    });
    await trx.commit();
    return {
      id: config.uid,
      name: config.name,
      project_id: config.projectUid,
      configs: [],
    };
  } catch (err) {
    await trx.rollback();
    if (err instanceof UniqueViolationError) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        errorConstants.DUPLICATE_CONFIG,
      );
    }
    throw err;
  }
};

const addConfigOption = async (req: Request) => {
  const configGroupId = req.query._id;
  if (!configGroupId) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field config_group_id is a required field',
    );
  }
  const configGroup = await req.models.Configuration.query().where(
    { uid: configGroupId as string, systemType: 'config' },
  ).first();
  if (!configGroup) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field config_group_id is not valid id',
    );
  }
  const trx = await req.knexDB.transaction();
  try {
    const { name } = req.body;
    const configOption = await req.models.Tag.query(trx).insert({
      name,
      parentUid: configGroupId as any,
      systemType: 'config.option',
      projectUid: configGroup.projectUid,
    });
    await trx.commit();
    return {
      id: configOption.uid,
      name: configOption.name,
      group_id: configOption.parentUid,
    };
  } catch (err) {
    await trx.rollback();
    if (err instanceof UniqueViolationError) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        errorConstants.DUPLICATE_CONFIG,
      );
    }
    throw err;
  }
};

const updateConfigGroup = async (req: Request) => {
  const configGroupId = req.query._id;
  if (!configGroupId) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field config_group_id is a required field',
    );
  }
  const configGroup = await req.models.Configuration.query().where(
    { uid: configGroupId as string, systemType: 'config' },
  ).withGraphFetched('[options(isConfigOption)]').first();
  if (!configGroup) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field config_group_id is not valid id',
    );
  }
  const trx = await req.knexDB.transaction();
  try {
    const { name } = req.body;
    if (name) {
      const config = await req.models.Configuration.query(trx).where('uid', configGroupId as any).patch({
        name: name || configGroup.name,
      }).returning('*')
        .withGraphFetched('[options(isConfigOption)]')
        .first();
      await trx.commit();
      return {
        id: config.uid,
        name: config.name,
        project_id: config.projectUid,
        configs: config.options.map((option: any) => ({
          id: option.uid,
          name: option.name,
          group_id: option.parentUid,
        })),
      };
    }
    return {
      id: configGroup.uid,
      name: configGroup.name,
      project_id: configGroup.projectUid,
      configs: configGroup.options.map((option: any) => ({
        id: option.uid,
        name: option.name,
        group_id: option.parentUid,
      })),
    };
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const updateConfigOption = async (req: Request) => {
  const configOptionId = req.query._id;
  if (!configOptionId) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field config_option_id is a required field',
    );
  }
  const configOption = await req.models.Tag.query().where(
    { uid: configOptionId as string, systemType: 'config.option' },
  ).first();
  if (!configOption) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field config_option_id is not valid id',
    );
  }
  const trx = await req.knexDB.transaction();
  try {
    const { name } = req.body;
    const config = await req.models.Tag.query(trx).where('uid', configOptionId as any).patch({
      name: name || configOption.name,
    }).returning('*')
      .first();
    await trx.commit();
    return {
      id: config.uid,
      name: config.name,
      group_id: config.parentUid,
    };
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const deleteConfigGroup = async (req: Request) => {
  const configGroupId = req.query._id;
  if (!configGroupId) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field config_group_id is a required field',
    );
  }
  const configGroup = await req.models.Configuration.query().where(
    { uid: configGroupId as string, systemType: 'config' },
  ).first();
  if (!configGroup) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field config_group_id is not valid id',
    );
  }
  const trx = await req.knexDB.transaction();
  await req.models.Configuration.query(trx).where('uid', configGroupId as any).patch({ deletedAt: req.knexDB.fn.now() });
  await trx.commit();
};

const deleteConfigOption = async (req: Request) => {
  const configOptionId = req.query._id;
  if (!configOptionId) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field config_option_id is a required field',
    );
  }
  const configOption = await req.models.Tag.query().where(
    { uid: configOptionId as string, systemType: 'config.option' },
  ).first();
  if (!configOption) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field config_option_id is not valid id',
    );
  }
  const trx = await req.knexDB.transaction();
  await req.models.Tag.query(trx).where('uid', configOptionId as any).patch({ deletedAt: req.knexDB.fn.now() });
  await trx.commit();
};
export default {
  getConfigurations: httpHandler(getConfigurations),
  addConfigGroup: httpHandler(addConfigGroup),
  addConfigOption: httpHandler(addConfigOption),
  updateConfigGroup: httpHandler(updateConfigGroup),
  updateConfigOption: httpHandler(updateConfigOption),
  deleteConfigGroup: httpHandler(deleteConfigGroup),
  deleteConfigOption: httpHandler(deleteConfigOption),
};
