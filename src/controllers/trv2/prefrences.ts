import { httpHandler } from '@app/lib/http';
import { Request } from 'express';

const getStatuses = async (req: Request) => {
  const org = await req.models.Org.query()
    .where({
      uid: req.locals.handle.ownerUid,
    })
    .select('*')
    .first();
  const statuses = org?.preferences?.statusColors;
  return statuses
    .filter((status) => status.entityType === 'testCase')
    .map((status) => ({
      id: status.id,
      color_bright: status.color,
      color_dark: status.color,
      color_medium: status.color,
      is_final: status.isCompleted,
      is_system: false,
      is_untested: status.isDefault,
      label: status.name,
      name: status.name,
    }));
};

const getPriorities = async (req: Request) => {
  const org = await req.models.Org.query()
    .where({
      uid: req.locals.handle.ownerUid,
    })
    .select('*')
    .first();

  const priorities = org?.preferences?.priorityColors;
  return priorities
    .filter((priority) => priority.entityType === 'testCase')
    .map((priority) => ({
      id: priority.id,
      is_default: priority.isDefault,
      name: priority.name,
      priority: priority.id,
      short_name: priority.name,
    }));
};
export default {
  getStatuses: httpHandler(getStatuses),
  getPriorities: httpHandler(getPriorities),
};
