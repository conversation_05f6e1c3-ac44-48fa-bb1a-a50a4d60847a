import { ApplicationError, httpHandler } from '@app/lib/http';
import { Attachment } from '@app/models/attachment';
import { AttachmentData } from '@app/temporal/activities/attachment';
import { startWorkflow } from '@app/temporal/client';
import { buildLink } from '@app/utils/buildTestrailLinks';
import { generateEntryId } from '@app/utils/formatplanEnteries';
import axios from 'axios';
import { Request } from 'express';
import { StatusCodes } from 'http-status-codes';

const getAttachmentsForCase = async (req: Request) => {
  const caseId = req.query._id as string;

  if (!caseId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field :case_id is a required field.');
  }

  // Fetch the test case from the database
  const testCase: any = await req.models.TestCase.query()
    .where('testCaseRef', caseId)
    .where('active', true)
    .whereNull('deletedAt')
    .first();

  if (!testCase) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field case_id is not a valid test case id');
  }

  const filters = req.query._filters as any;
  const {
    offset = 0,
    limit = 250,
  } = filters;

  const attachments = await req.models.Attachment.query().join('caseAttachments', 'attachments.uid', 'caseAttachments.attachmentUid').where('caseRef', caseId).offset(offset)
    .limit(limit);
  const count:any = await req.models.Attachment.query().join('caseAttachments', 'attachments.uid', 'caseAttachments.attachmentUid').where('caseRef', caseId).count('attachments.uid as count')
    .first();
  const totalCount = count.count;

  const links = {
    next: Number(offset) + Number(limit) >= totalCount ? null : buildLink('api/v2/get_attachments_for_case', Number(offset) + Number(limit), Number(limit), caseId, filters),
    prev: Number(offset) <= 0 ? null : buildLink('api/v2/get_attachments_for_case', Math.max(0, Number(offset) - Number(limit)), Number(limit), caseId, filters),
  };

  return {
    offset: Number(offset),
    limit: Number(limit),
    size: attachments.length,
    _links: links,
    attachments: attachments.map((attachment:any) => ({
      id: attachment.uid,
      name: attachment.name,
      file_name: attachment.fileName,
      file_type: attachment.fileType,
      casandra_file_id: null,
      size: attachment.size,
      created_on: attachment.createdAt,
      project_id: testCase.projectUid,
      user_id: attachment.createdBy,
      data_id: attachment.uid,
      is_image: false,
      icon: null,
    })),
  };
};

const getAttachmentsForPlan = async (req: Request) => {
  const planId = req.query._id as string;
  if (!planId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_id is a required field');
  }
  const plan = await req.models.TestPlan.query()
    .where({
      uid: planId,
      systemType: 'plan',
      deletedAt: null,
    })
    .first();

  if (!plan) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Field plan_id is not a valid plan id');
  }
  const filters = req.query._filters as any;
  const {
    offset = 0,
    limit = 250,
  } = filters;

  const attachments = await req.models.Attachment.query().join('planAttachments', 'attachments.uid', 'planAttachments.attachmentUid').where('planUid', planId).offset(offset)
    .limit(limit);
  const count:any = await req.models.Attachment.query().join('planAttachments', 'attachments.uid', 'planAttachments.attachmentUid').where('planUid', planId).count('attachments.uid as count')
    .first();
  const totalCount = count.count;

  const links = {
    next: Number(offset) + Number(limit) >= totalCount ? null : buildLink('api/v2/get_attachments_for_plan', Number(offset) + Number(limit), Number(limit), planId, filters),
    prev: Number(offset) <= 0 ? null : buildLink('api/v2/get_attachments_for_plan', Math.max(0, Number(offset) - Number(limit)), Number(limit), planId, filters),
  };

  return {
    offset: Number(offset),
    limit: Number(limit),
    size: attachments.length,
    _links: links,
    attachments: attachments.map((attachment:any) => ({
      id: attachment.uid,
      name: attachment.name,
      file_name: attachment.fileName,
      file_type: attachment.fileType,
      casandra_file_id: null,
      size: attachment.size,
      created_on: attachment.createdAt,
      project_id: plan.projectUid,
      user_id: attachment.createdBy,
      data_id: attachment.uid,
      is_image: false,
      icon: null,
    })),
  };
};

const getAttachmentsForPlanEntry = async (req: Request) => {
  const planId = req.query._id as unknown as number;
  const planEntryId = req.query._id2 as unknown as string;
  if (!planId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_id is a required field');
  }
  if (!planEntryId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_entry_id is a required field');
  }
  const plan = await req.models.TestPlan.query()
    .where({
      uid: planId,
      deletedAt: null,
      systemType: 'plan',
    })
    .withGraphFetched({
      runs: {
        attachments: true,
      },
    })
    .first();
  if (!plan) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_id is invalid');
  }
  const planEntryWithConfig = generateEntryId(Number(planId), plan.projectUid, '-withConfig');
  const planEntryWithoutConfig = generateEntryId(Number(planId), plan.projectUid, '-noConfig');
  if (planEntryId !== planEntryWithConfig && planEntryId !== planEntryWithoutConfig) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_entry_id is invalid');
  }
  let attachments;
  if (planEntryId === planEntryWithConfig) {
    attachments = plan.runs.filter((run:any) => run.customFields?.configs?.length > 0).map((run:any) => run.attachments).flat();
  } else {
    attachments = plan.runs.filter((run:any) => run.customFields?.configs?.length === 0).map((run:any) => run.attachments).flat();
  }
  return attachments.map((attachment:any) => ({
    id: attachment.uid,
    name: attachment.name,
    file_name: attachment.fileName,
    file_type: attachment.fileType,
    casandra_file_id: null,
    size: attachment.size,
    created_on: attachment.createdAt,
    project_id: plan.projectUid,
    user_id: attachment.createdBy,
    data_id: attachment.uid,
    is_image: false,
    icon: null,
  }));
};

const getAttachmentsForRun = async (req: Request) => {
  const runId = req.query._id as unknown as number;
  if (!runId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: run_id is a required field');
  }
  const run = await req.models.TestRun.query()
    .where('uid', runId)
    .first();
  if (!run) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: run_id is invalid');
  }
  const filters = req.query._filters as any;
  const {
    offset = 0,
    limit = 250,
  } = filters;

  const attachments = await req.models.Attachment.query().join('runAttachments', 'attachments.uid', 'runAttachments.attachmentUid').where('runUid', runId).offset(offset)
    .limit(limit);
  const count:any = await req.models.Attachment.query().join('runAttachments', 'attachments.uid', 'runAttachments.attachmentUid').where('runUid', runId).count('attachments.uid as count')
    .first();
  const totalCount = count.count;

  const links = {
    next: Number(offset) + Number(limit) >= totalCount ? null : buildLink('api/v2/get_attachments_for_run', Number(offset) + Number(limit), Number(limit), String(runId), filters),
    prev: Number(offset) <= 0 ? null : buildLink('api/v2/get_attachments_for_run', Math.max(0, Number(offset) - Number(limit)), Number(limit), String(runId), filters),
  };

  return {
    offset: Number(offset),
    limit: Number(limit),
    size: attachments.length,
    _links: links,
    attachments: attachments.map((attachment:any) => ({
      id: attachment.uid,
      name: attachment.name,
      file_name: attachment.fileName,
      file_type: attachment.fileType,
      casandra_file_id: null,
      size: attachment.size,
      created_on: attachment.createdAt,
      project_id: run.projectUid,
      user_id: attachment.createdBy,
      data_id: attachment.uid,
      is_image: false,
      icon: null,
    })),
  };
};

const getAttachmentsForTest = async (req: Request) => {
  const testId = req.query._id as unknown as number;
  if (!testId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: test_id is a required field');
  }
  const test = await req.models.TestExecution.query()
    .where('uid', testId)
    .first();
  if (!test) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: test_id is invalid');
  }
  const filters = req.query._filters as any;
  const {
    offset = 0,
    limit = 250,
  } = filters;

  const attachments = await req.models.Attachment.query().join('executionAttachments', 'attachments.uid', 'executionAttachments.attachmentUid').where('executionUid', testId).offset(offset)
    .limit(limit);
  const count:any = await req.models.Attachment.query().join('executionAttachments', 'attachments.uid', 'executionAttachments.attachmentUid').where('executionUid', testId).count('attachments.uid as count')
    .first();
  const totalCount = count.count;

  const links = {
    next: Number(offset) + Number(limit) >= totalCount ? null : buildLink('api/v2/get_attachments_for_test', Number(offset) + Number(limit), Number(limit), String(testId), filters),
    prev: Number(offset) <= 0 ? null : buildLink('api/v2/get_attachments_for_test', Math.max(0, Number(offset) - Number(limit)), Number(limit), String(testId), filters),
  };

  return {
    offset: Number(offset),
    limit: Number(limit),
    size: attachments.length,
    _links: links,
    attachments: attachments.map((attachment:any) => ({
      id: attachment.uid,
      name: attachment.name,
      file_name: attachment.fileName,
      file_type: attachment.fileType,
      casandra_file_id: null,
      size: attachment.size,
      created_on: attachment.createdAt,
      project_id: test.projectUid,
      user_id: attachment.createdBy,
      data_id: attachment.uid,
      is_image: false,
      icon: null,
    })),
  };
};

const getAttachment = async (req:Request) => {
  const attachmentId = req.query._id as unknown as string;
  if (!attachmentId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: attachment_id is a required field');
  }
  const attachment = await req.models.Attachment.query().where('uid', attachmentId).first();
  if (!attachment) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: attachment_id is invalid');
  }
  return {
    id: attachment.uid,
    name: attachment.name,
    file_name: attachment.name,
    file_type: attachment.fileType,
    casandra_file_id: null,
    size: attachment.size,
    created_on: attachment.createdAt,
    project_id: null,
    user_id: null,
    data_id: attachment.uid,
    is_image: false,
    icon: null,
  };
};

const deleteAttachment = async (req:Request) => {
  const attachmentId = req.query._id as unknown as string;
  if (!attachmentId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: attachment_id is a required field');
  }
  const { ownerUid } = req.locals.handle;
  const attachment = await req.models.Attachment.query().findOne({
    uid: attachmentId,
  });
  if (!attachment) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: attachment_id is invalid');
  }
  const { key } = attachment;

  const param: AttachmentData = {
    type: 'delete',
    key,
    ownerUid,
  };
  await Promise.all([
    startWorkflow('attachmentWorkflow', {
      taskQueue: 'attachment-queue',
      workflowId: `${ownerUid}:attachment:delete:${Date.now()}`,
      args: [param],
    }),
    req.models.Attachment.query().where('uid', attachmentId).patch({
      deletedAt: new Date(),
    }),
  ]);
};

const addCaseAttachment = async (req: Request) => {
  const caseId = req.query._id as unknown as string;
  if (!caseId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: case_id is a required field');
  }
  const testCase = await req.models.TestCase.query().where({ testCaseRef: caseId, active: true, deletedAt: null }).first();
  if (!testCase) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: case_id is invalid');
  }
  const files = req.files || {} as any;
  if (!files.attachment) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'No attachment or file uploaded');
  }
  let attachmentId:string;
  const trx = await req.knexDB.transaction();
  try {
    const {
      name,
      mimetype: fileType,
      size,
      data,
    } = files.attachment;
    const newAttachment = {
      name,
      creatorUid: req.locals.accessToken?.createdBy,
      size,
      fileType,
      mediaType: 'attachment',
      ownerUid: req.locals.handle.ownerUid,
    };
    const {
      signed_url: signedUrl,
      client_headers: clientHeaders,
      uid,
    } = await Attachment.createAttachment(newAttachment, trx);
    attachmentId = uid;
    await trx('caseAttachments').insert({
      attachmentUid: uid,
      caseRef: caseId,
    });
    const buffer = Buffer.from(data);
    await axios.put(signedUrl, buffer, {
      headers: {
        ...clientHeaders,
      },
    });
    await trx.commit();
  } catch (err) {
    await trx.rollback();
    throw err;
  }
  return {
    attachment_id: attachmentId,
  };
};

const addPlanAttachment = async (req:Request) => {
  const planId = req.query._id as unknown as string;
  if (!planId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_id is a required field');
  }
  const plan = await req.models.TestPlan.query().where({ uid: planId, systemType: 'plan', deletedAt: null }).first();
  if (!plan) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_id is invalid');
  }
  const files = req.files || {} as any;
  if (!files.attachment) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'No attachment or file uploaded');
  }
  let attachmentId:string;
  const trx = await req.knexDB.transaction();
  try {
    const {
      name,
      mimetype: fileType,
      size,
      data,
    } = files.attachment;
    const newAttachment = {
      name,
      creatorUid: req.locals.accessToken?.createdBy,
      size,
      fileType,
      mediaType: 'attachment',
      ownerUid: req.locals.handle.ownerUid,
    };
    const {
      signed_url: signedUrl,
      client_headers: clientHeaders,
      uid,
    } = await Attachment.createAttachment(newAttachment, trx);
    attachmentId = uid;
    await trx('planAttachments').insert({
      attachmentUid: uid,
      planUid: planId,
    });
    const buffer = Buffer.from(data);
    await axios.put(signedUrl, buffer, {
      headers: {
        ...clientHeaders,
      },
    });
    await trx.commit();
  } catch (err) {
    await trx.rollback();
    throw err;
  }
  return {
    attachment_id: attachmentId,
  };
};

const addPlanEntryAttachment = async (req:Request) => {
  const planId = req.query._id as unknown as string;
  if (!planId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_id is a required field');
  }
  const planEntryId = req.query._id2 as unknown as string;
  if (!planEntryId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_entry_id is a required field');
  }
  const plan = await req.models.TestPlan.query().where({ uid: planId, systemType: 'plan', deletedAt: null }).withGraphFetched({
    runs: true,
  }).first();
  if (!plan) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_id is invalid');
  }
  const planEntryWithConfig = generateEntryId(Number(planId), plan.projectUid, '-withConfig');
  const planEntryWithoutConfig = generateEntryId(Number(planId), plan.projectUid, '-noConfig');
  if (planEntryId !== planEntryWithConfig && planEntryId !== planEntryWithoutConfig) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: plan_entry_id is invalid');
  }
  const files = req.files || {} as any;
  if (!files.attachment) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'No attachment or file uploaded');
  }
  let runs;
  if (planEntryId === planEntryWithConfig) {
    runs = plan.runs.filter((run:any) => run.customFields?.configs?.length > 0);
  } else {
    runs = plan.runs.filter((run:any) => run.customFields?.configs?.length === 0);
  }
  let attachmentId:string;
  const trx = await req.knexDB.transaction();
  try {
    const {
      name,
      mimetype: fileType,
      size,
      data,
    } = files.attachment;
    const newAttachment = {
      name,
      creatorUid: req.locals.accessToken?.createdBy,
      size,
      fileType,
      mediaType: 'attachment',
      ownerUid: req.locals.handle.ownerUid,
    };
    const {
      signed_url: signedUrl,
      client_headers: clientHeaders,
      uid,
    } = await Attachment.createAttachment(newAttachment, trx);
    attachmentId = uid;
    await Promise.all(runs.map(async (run:any) => {
      await trx('runAttachments').insert({
        attachmentUid: uid,
        runUid: run.uid,
      });
    }));
    const buffer = Buffer.from(data);
    await axios.put(signedUrl, buffer, {
      headers: {
        ...clientHeaders,
      },
    });
    await trx.commit();
  } catch (err) {
    await trx.rollback();
    throw err;
  }
  return {
    attachment_id: attachmentId,
  };
};

const addRunAttachment = async (req:Request) => {
  const runId = req.query._id as unknown as string;
  if (!runId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: run_id is a required field');
  }
  const run = await req.models.TestRun.query().where({ uid: runId, systemType: 'run', deletedAt: null }).first();
  if (!run) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: run_id is invalid');
  }
  const files = req.files || {} as any;
  if (!files.attachment) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'No attachment or file uploaded');
  }
  let attachmentId:string;
  const trx = await req.knexDB.transaction();
  try {
    const {
      name,
      mimetype: fileType,
      size,
      data,
    } = files.attachment;
    const newAttachment = {
      name,
      creatorUid: req.locals.accessToken?.createdBy,
      size,
      fileType,
      mediaType: 'attachment',
      ownerUid: req.locals.handle.ownerUid,
    };
    const {
      signed_url: signedUrl,
      client_headers: clientHeaders,
      uid,
    } = await Attachment.createAttachment(newAttachment, trx);
    attachmentId = uid;
    await trx('runAttachments').insert({
      attachmentUid: uid,
      runUid: runId,
    });
    const buffer = Buffer.from(data);
    await axios.put(signedUrl, buffer, {
      headers: {
        ...clientHeaders,
      },
    });
    await trx.commit();
  } catch (err) {
    await trx.rollback();
    throw err;
  }
  return {
    attachment_id: attachmentId,
  };
};

const addResultAttachment = async (req:Request) => {
  const resultId = req.query._id as unknown as string;
  if (!resultId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: result_id is a required field');
  }
  const result = await req.models.TestResult.query().where({ uid: resultId, deletedAt: null }).first();
  if (!result) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Fields: result_id is invalid');
  }
  const files = req.files || {} as any;
  if (!files.attachment) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'No attachment or file uploaded');
  }
  let attachmentId:string;
  const trx = await req.knexDB.transaction();
  try {
    const {
      name,
      mimetype: fileType,
      size,
      data,
    } = files.attachment;
    const newAttachment = {
      name,
      creatorUid: req.locals.accessToken?.createdBy,
      size,
      fileType,
      mediaType: 'attachment',
      ownerUid: req.locals.handle.ownerUid,
    };
    const {
      signed_url: signedUrl,
      client_headers: clientHeaders,
      uid,
    } = await Attachment.createAttachment(newAttachment, trx);
    attachmentId = uid;
    await trx('resultAttachments').insert({
      attachmentUid: uid,
      resultUid: resultId,
    });
    const buffer = Buffer.from(data);
    await axios.put(signedUrl, buffer, {
      headers: {
        ...clientHeaders,
      },
    });
    await trx.commit();
  } catch (err) {
    await trx.rollback();
    throw err;
  }
  return {
    attachment_id: attachmentId,
  };
};

export default {
  getAttachmentsForCase: httpHandler(getAttachmentsForCase),
  getAttachmentsForPlan: httpHandler(getAttachmentsForPlan),
  getAttachmentsForPlanEntry: httpHandler(getAttachmentsForPlanEntry),
  getAttachmentsForRun: httpHandler(getAttachmentsForRun),
  getAttachmentsForTest: httpHandler(getAttachmentsForTest),
  getAttachment: httpHandler(getAttachment),
  deleteAttachment: httpHandler(deleteAttachment),
  addCaseAttachment: httpHandler(addCaseAttachment),
  addPlanAttachment: httpHandler(addPlanAttachment),
  addPlanEntryAttachment: httpHandler(addPlanEntryAttachment),
  addRunAttachment: httpHandler(addRunAttachment),
  addResultAttachment: httpHandler(addResultAttachment),
};
