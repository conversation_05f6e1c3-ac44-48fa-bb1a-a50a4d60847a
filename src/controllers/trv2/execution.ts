import { Request } from 'express';
import { ApplicationError, httpHandler } from '@app/lib/http';
import { StatusCodes } from 'http-status-codes';
import { transformCustomSteps, transformTemplateFields } from '@app/utils/formatCaseFields';
import { buildLink } from '@app/utils/buildTestrailLinks';

const getExecutions = async (req: Request) => {
  const runId = req.query._id as string;
  if (!runId) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field run_id is a required field',
    );
  }
  const testRun = await req.models.TestRun.query().findById(runId);
  if (!testRun) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      'Test run not found',
    );
  }
  const filters = req.query._filters as any;
  const {
    limit = 250,
    offset = 0,
    status_id: statusIds,
  } = filters;
  const executionsQuery = req.models.TestExecution.query()
    .where({
      testRunUid: runId,
    });
  const totalQuery = req.models.TestExecution.query()
    .where({
      testRunUid: runId,
    })
    .count('* as count').first();

  if (statusIds) {
    executionsQuery.whereIn('status', statusIds.split(','));
    totalQuery.whereIn('status', statusIds.split(','));
  }
  executionsQuery.limit(limit).offset(offset);

  const executions = await executionsQuery;
  const total = await totalQuery;
  const totalCount = Number((total as any).count);
  const executionData = executions.map((execution) => (
    {
      id: execution.uid,
      case_id: execution.testCaseRef,
      status_id: execution.status,
      assignedto_id: execution.assignedTo,
      run_id: testRun.uid,
      title: execution.name,
      template_id: null,
      type_id: execution.customFields.tags?.length > 0 ? execution.customFields.tags[0].uid : null,
      priority_id: execution.priority,
      refs: null,
      custom_steps_separated: transformCustomSteps(execution.customFields.steps || []),
      ...transformTemplateFields(execution.templateFields),
      case_comments: [],
    }));

  const links = {
    next: Number(offset) + Number(limit) >= totalCount ? null : buildLink('api/v2/get_tests', Number(offset) + Number(limit), Number(limit), testRun.uid.toString(), filters),
    prev: Number(offset) <= 0 ? null : buildLink('api/v2/get_tests', Math.max(0, Number(offset) - Number(limit)), Number(limit), testRun.uid.toString(), filters),
  };

  return {
    offset: Number(offset),
    limit: Number(limit),
    size: executionData.length,
    _links: links,
    tests: executionData,
  };
};

const getExecution = async (req: Request) => {
  const id = req.query._id as string;
  if (!id) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field test_id is a required field',
    );
  }

  const execution = await req.models.TestExecution.query()
    .where({
      uid: id,
    })
    .first();

  if (!execution) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      'Field test_id not a valid test id',
    );
  }

  return {
    id: execution.uid,
    case_id: execution.testCaseRef,
    status_id: execution.status,
    assignedto_id: execution.assignedTo,
    run_id: execution.testRunUid,
    title: execution.name,
    template_id: null,
    type_id: execution.customFields.tags?.length > 0 ? execution.customFields.tags[0].uid : null,
    priority_id: execution.priority,
    refs: null,
    custom_steps_separated: transformCustomSteps(execution.customFields.steps || []),
    ...transformTemplateFields(execution.templateFields),
    case_comments: [],
  };
};
export default {
  getExecutions: httpHandler(getExecutions),
  getExecution: httpHandler(getExecution),
};
