import { Request } from 'express';
import { TestResult } from '@app/models/testResult';
import { httpH<PERSON><PERSON>, ApplicationError } from '@app/lib/http';
import { CreateResultDTO } from '@app/types/result';
import { StatusCodes } from 'http-status-codes';
import { buildLink } from '@app/utils/buildTestrailLinks';

const getResultsByExecution = async (req: Request) => {
  const executionUid = req.query._id as string;
  if (!executionUid) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field test_id is a required field',
    );
  }

  const testExecution = await req.models.TestExecution.query()
    .findById(executionUid);
  if (!testExecution) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      'Field test_id not a valid test id',
    );
  }

  const filters = req.query._filters as any;
  const {
    limit = 250,
    offset = 0,
    status_id: statusId,
  } = filters;
  const resultsQuery = req.models.TestResult.query()
    .where({ testExecutionUid: executionUid })
    .whereNull('testResults.deletedAt')
    .withGraphFetched('attachments');
  const totalQuery = req.models.TestResult.query()
    .where({ testExecutionUid: executionUid })
    .whereNull('testResults.deletedAt')
    .count('* as total');

  if (statusId) {
    resultsQuery.whereIn('status', statusId.split(','));
    totalQuery.whereIn('status', statusId.split(','));
  }

  const results = await resultsQuery
    .limit(limit)
    .offset(offset);
  const total = await totalQuery.first();
  const totalCount = (total as any).total || 0;

  const resultsData = results.map((result) => ({
    id: result.uid,
    test_id: result.testExecutionUid,
    status_id: result.status,
    created_on: new Date(result.createdAt).getTime(),
    assignedto_id: result.reporterUid,
    comment: result.comment,
    version: null,
    elapsed: null,
    defects: null,
    created_by: result.reporterUid,
    attachment_ids: result.attachments.map((attachment) => attachment.uid),
  }));

  const links = {
    next: Number(offset) + Number(limit) >= totalCount ? null : buildLink('api/v2/get_results', Number(offset) + Number(limit), Number(limit), executionUid, filters),
    prev: Number(offset) <= 0 ? null : buildLink('api/v2/get_results', Math.max(0, Number(offset) - Number(limit)), Number(limit), executionUid, filters),
  };

  return {
    offset: Number(offset),
    limit: Number(limit),
    size: resultsData.length,
    _links: links,
    results: resultsData,
  };
};

const getResultsByCase = async (req: Request) => {
  const runUid = req.query._id as string;
  const caseUid = req.query._id2 as string;
  if (!runUid) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field run_id is a required field',
    );
  }
  if (!caseUid) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field case_id is a required field',
    );
  }

  const testRun = await req.models.TestRun.query()
    .findById(runUid);
  if (!testRun) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      'Field run_id not a valid run id',
    );
  }

  const testCases = await req.models.TestCase.query()
    .where({ testCaseRef: caseUid });
  if (!testCases.length) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      'Field case_id not a valid case id',
    );
  }

  const caseUids = testCases.map((testCase) => testCase.uid);
  const executions = await req.models.TestExecution.query()
    .whereIn('testCaseRef', caseUids)
    .where({
      testRunUid: runUid,
    });
  const executionUids = executions.map((execution) => execution.uid);
  const filters = req.query._filters as any;
  const {
    limit = 250,
    offset = 0,
    status_id: statusId,
  } = filters;
  const resultsQuery = req.models.TestResult.query()
    .whereIn('testExecutionUid', executionUids)
    .whereNull('testResults.deletedAt')
    .withGraphFetched('attachments');
  const totalQuery = req.models.TestResult.query()
    .whereIn('testExecutionUid', executionUids)
    .whereNull('testResults.deletedAt')
    .count('* as total');

  if (statusId) {
    resultsQuery.whereIn('status', statusId.split(','));
    totalQuery.whereIn('status', statusId.split(','));
  }

  const results = await resultsQuery
    .limit(limit)
    .offset(offset);
  const total = await totalQuery.first();
  const totalCount = (total as any).total || 0;

  const resultsData = results.map((result) => ({
    id: result.uid,
    test_id: result.testExecutionUid,
    status_id: result.status,
    created_on: new Date(result.createdAt).getTime(),
    assignedto_id: result.reporterUid,
    comment: result.comment,
    version: null,
    elapsed: null,
    defects: null,
    created_by: result.reporterUid,
    attachment_ids: result.attachments.map((attachment) => attachment.uid),
  }));

  const links = {
    next: Number(offset) + Number(limit) >= totalCount ? null : buildLink('api/v2/get_results', Number(offset) + Number(limit), Number(limit), `${runUid}/${caseUid}`, filters),
    prev: Number(offset) <= 0 ? null : buildLink('api/v2/get_results', Math.max(0, Number(offset) - Number(limit)), Number(limit), `${runUid}/${caseUid}`, filters),
  };

  return {
    offset: Number(offset),
    limit: Number(limit),
    size: resultsData.length,
    _links: links,
    results: resultsData,
  };
};
const getResultsByRun = async (req: Request) => {
  const runUid = req.query._id as string;
  if (!runUid) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field test_id is a required field',
    );
  }
  const testRun = await req.models.TestRun.query()
    .findById(runUid);
  if (!testRun) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      'Field test_id not a valid test id',
    );
  }
  const executions = await req.models.TestExecution.query()
    .where({
      testRunUid: runUid,
    });
  const executionUids = executions.map((execution) => execution.uid);
  const filters = req.query._filters as any;
  const {
    limit = 250,
    offset = 0,
    status_id: statusId,
  } = filters;
  const resultsQuery = req.models.TestResult.query()
    .whereIn('testExecutionUid', executionUids)
    .whereNull('testResults.deletedAt')
    .withGraphFetched('attachments');
  const totalQuery = req.models.TestResult.query()
    .whereIn('testExecutionUid', executionUids)
    .whereNull('testResults.deletedAt')
    .count('* as total');

  if (statusId) {
    resultsQuery.whereIn('status', statusId.split(','));
    totalQuery.whereIn('status', statusId.split(','));
  }

  const results = await resultsQuery
    .limit(limit)
    .offset(offset);
  const total = await totalQuery.first();
  const totalCount = (total as any).total || 0;

  const resultsData = results.map((result) => ({
    id: result.uid,
    test_id: result.testExecutionUid,
    status_id: result.status,
    created_on: new Date(result.createdAt).getTime(),
    assignedto_id: result.reporterUid,
    comment: result.comment,
    version: null,
    elapsed: null,
    defects: null,
    created_by: result.reporterUid,
    attachment_ids: result.attachments.map((attachment) => attachment.uid),
  }));

  const links = {
    next: Number(offset) + Number(limit) >= totalCount ? null : buildLink('api/v2/get_results', Number(offset) + Number(limit), Number(limit), runUid, filters),
    prev: Number(offset) <= 0 ? null : buildLink('api/v2/get_results', Math.max(0, Number(offset) - Number(limit)), Number(limit), runUid, filters),
  };

  return {
    offset: Number(offset),
    limit: Number(limit),
    size: resultsData.length,
    _links: links,
    results: resultsData,
  };
};

const createResult = async (req: Request) => {
  const execUid = req.query._id as string;
  if (!execUid) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field test_id is a required field',
    );
  }
  const testExecution = await req.models.TestExecution.query()
    .findById(execUid);
  if (!testExecution) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      'Field test_id not a valid test id',
    );
  }
  const {
    status_id: status,
    comment,
  } = req.body;

  // Start a transaction
  const trx = await req.knexDB.transaction();
  try {
    // Inserting the test result and returning its UID
    const result = await TestResult.create(trx, {
      executionUid: Number(execUid),
      reporterUid: req.locals.user.uid,
      status,
      comment,
    });

    trx.commit();
    return {
      id: result.uid,
      test_id: result.testExecutionUid,
      status_id: result.status,
      created_on: new Date(result.createdAt).getTime(),
      assignedto_id: result.reporterUid,
      comment: result.comment,
      version: null,
      elapsed: null,
      defects: null,
      created_by: result.reporterUid,
      attachment_ids: [],
    };
  } catch (error) {
    // If there's an error, rollback the transaction
    await trx.rollback();
    throw error;
  }
};

const createResultForCase = async (req: Request) => {
  const runUid = req.query._id as string;
  const caseUid = req.query._id2 as string;
  if (!runUid) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field run_id is a required field',
    );
  }
  if (!caseUid) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field case_id is a required field',
    );
  }

  const testRun = await req.models.TestRun.query()
    .findById(runUid);
  if (!testRun) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      'Field run_id not a valid run id',
    );
  }

  const testCases = await req.models.TestCase.query()
    .where({ testCaseRef: caseUid });
  if (!testCases.length) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      'Field case_id not a valid case id',
    );
  }
  const caseUids = testCases.map((testCase) => testCase.uid);
  const execution = await req.models.TestExecution.query()
    .whereIn('testCaseRef', caseUids)
    .where({
      testRunUid: runUid,
    }).first();
  if (!execution) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      'Field case_id not a valid case id',
    );
  }
  const {
    status,
    comment,
  } = req.body;
  // Start a transaction
  const trx = await req.knexDB.transaction();
  try {
    // Inserting the test result and returning its UID
    const result = await TestResult.create(trx, {
      executionUid: execution.uid,
      reporterUid: req.locals.user.uid,
      status,
      comment,
    });

    trx.commit();
    return result;
  } catch (error) {
    // If there's an error, rollback the transaction
    await trx.rollback();
    throw error;
  }
};

const createResultForRun = async (req: Request) => {
  const dto = req.body as CreateResultDTO;
  const execUid: number = req.params.id as any;

  // Start a transaction
  const trx = await req.knexDB.transaction();
  try {
    // Inserting the test result and returning its UID
    const result = await TestResult.create(trx, {
      executionUid: execUid,
      reporterUid: req.locals.user.uid,
      status: dto.status,
      comment: dto.comment,
      stepUid: dto.stepUid,
      tagUids: dto.tagUids,
    });

    trx.commit();
    return {
      id: result.uid,
      test_id: result.testExecutionUid,
      status_id: result.status,
      created_on: new Date(result.createdAt).getTime(),
      assignedto_id: result.reporterUid,
      comment: result.comment,
      version: null,
      elapsed: null,
      defects: null,
      created_by: result.reporterUid,
      attachment_ids: [],
    };
  } catch (error) {
    // If there's an error, rollback the transaction
    await trx.rollback();
    throw error;
  }
};
const createResults = async (req: Request) => {
  const runUid = req.query._id as string;
  if (!runUid) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field run_id is a required field',
    );
  }
  const testRun = await req.models.TestRun.query()
    .findById(runUid);
  if (!testRun) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      'Field run_id not a valid run id',
    );
  }
  const { results } = req.body;
  if (!results || !Array.isArray(results) || results.length === 0) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field results is a required field',
    );
  }
  const resultsData = [];
  for (const res of results) {
    const {
      test_id: executionUid,
      status_id: status,
      comment,
    } = res;
    const testExecution = await req.models.TestExecution.query()
      .findById(executionUid);
    if (!testExecution) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        'Field test_id not a valid test id',
      );
    }
    if (testExecution.testRunUid !== Number(runUid)) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'Field test_id not a valid test id',
      );
    }
    const trx = await req.knexDB.transaction();
    // Inserting the test result and returning its UID
    const result = await TestResult.create(trx, {
      executionUid: testExecution.uid,
      reporterUid: req.locals.user.uid,
      status: Number(status),
      comment,
    });
    trx.commit();
    resultsData.push({
      id: result.uid,
      test_id: result.testExecutionUid,
      status_id: result.status,
      created_on: new Date(result.createdAt).getTime(),
      assignedto_id: result.reporterUid,
      comment: result.comment,
      version: null,
      elapsed: null,
      defects: null,
      created_by: result.reporterUid,
      attachment_ids: [],
    });
  }
  return resultsData;
};
const createResultsForCases = async (req: Request) => {
  const runUid = req.query._id as string;
  if (!runUid) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field run_id is a required field',
    );
  }
  const testRun = await req.models.TestRun.query()
    .findById(runUid);
  if (!testRun) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      'Field run_id not a valid run id',
    );
  }
  const resultsData = [];
  const trx = await req.knexDB.transaction();
  const { results } = req.body;
  if (!results || !Array.isArray(results) || results.length === 0) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Field results is a required field',
    );
  }
  for (const res of results) {
    const {
      case_id: caseUid,
      status_id: status,
      comment,
    } = res;
    const testCases = await req.models.TestCase.query()
      .where({ testCaseRef: caseUid });
    if (!testCases.length) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        'Field case_id not a valid case id',
      );
    }
    const execution = await req.models.TestExecution.query()
      .where({
        testRunUid: Number(runUid),
        testCaseRef: Number(caseUid),
      }).first();
    if (!execution) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        'Field case_id not a valid case id',
      );
    }

    const result = await TestResult.create(trx, {
      executionUid: execution.uid,
      reporterUid: req.locals.user.uid,
      status: Number(status),
      comment,
    });
    resultsData.push({
      id: result.uid,
      test_id: result.testExecutionUid,
      status_id: result.status,
      created_on: new Date(result.createdAt).getTime(),
      assignedto_id: result.reporterUid,
      comment: result.comment,
      version: null,
      elapsed: null,
      defects: null,
      created_by: result.reporterUid,
      attachment_ids: [],
    });
  }
  trx.commit();
  return resultsData;
};

export default {
  createResult: httpHandler(createResult),
  getResultsByExecution: httpHandler(getResultsByExecution),
  getResultsByCase: httpHandler(getResultsByCase),
  getResultsByRun: httpHandler(getResultsByRun),
  createResultForCase: httpHandler(createResultForCase),
  createResultForRun: httpHandler(createResultForRun),
  createResults: httpHandler(createResults),
  createResultsForCases: httpHandler(createResultsForCases),
};
