import { Request } from 'express';
import { ApplicationError, httpHandler } from '@app/lib/http';
import { StatusCodes } from 'http-status-codes';

const getTemplates = async (req: Request) => {
  const projectId = req.query._id;
  if (!projectId) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Project_id is required');
  }
  const project = await req.models.Project.query()
    .where('uid', projectId as string)
    .where('deletedAt', null)
    .first();
  if (!project) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, 'Project_id is not valid');
  }
  const templates = await req.models.TestTemplate.query()
    .whereNull('deletedAt')
    .where('projectUid', project.uid)
    .select('*')
    .orderBy('name', 'asc');

  return templates.map((template) => ({
    id: template.uid,
    name: template.name,
    is_default: template.isDefault,
    case_fields: template.customFields?.templateFields?.map((field) => {
      const systemName = `custom_case_${field.name.toLowerCase().replace(/\s+/g, '_')}`;
      return {
        system_name: systemName,
        name: field.name,
        type: field.dataType,
      };
    }) || [],
  }));
};

export default {
  getTemplates: httpHandler(getTemplates),
};
