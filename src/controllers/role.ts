import { ApplicationError, httpHandler } from '@app/lib/http';
import { UniqueViolationError, raw } from 'objection';

import { FGARawWrite } from '@ss-libs/ss-component-auth';
import { Knex } from 'knex';
import { Request } from 'express';
import { Role } from '@app/models/role';
import { StatusCodes } from 'http-status-codes';
import { WriteInput } from '@ss-libs/ss-component-auth/dist/openfga';
import _ from 'lodash';
import errorConstants from '@app/constants/errors';
import logger from '@app/config/logger';
import permissionsList from '@app/constants/permissions';
import env from '@app/config/env';

type MemberDTO = {
  userId: string;
  roleId: string;
};

type Resource = {
  type: 'org' | 'user';
  id: string | number;
};

type DetailedRole = Role & {
  cnt: number;
  projectAvatar?: string;
  avatarAttachmentUid?: string;
  permissions?: string[];
};

const createRole = async (req: Request) => {
  const trx = await req.knexDB.transaction();
  try {
    const { permissions, ...dto } = req.body;
    const owner = req.locals.handle.ownerUid;
    const { ownerType } = req.locals.handle;
    const projectUid = req.locals.project?.uid;

    const role = await req.models.Role.query(trx).insert({
      name: dto.name,
      description: dto.description,
      slug: _.snakeCase(dto.name),
      projectUid: projectUid || null,
    });

    const tagUids = dto.tagUids ?? [];

    if (tagUids) {
      const validTags = await req.models.Tag.query(trx)
        .whereIn('uid', tagUids)
        .where('systemType', 'tag');
      const inserts = validTags.map((t) => ({
        roleUid: role.uid,
        tagUid: t.uid,
      }));
      if (inserts.length > 0) {
        await req.models.RoleTag.query(trx)
          .insert(inserts)
          .onConflict(['roleUid', 'tagUid'])
          .ignore();
      }
    }

    const writes: WriteInput[] = permissions.map((relation) => ({
      objectType: ownerType,
      objectId: owner,
      relation,
      subjectType: 'role',
      subjectId: `${role.uid}#assignee`,
    }));

    // mark the handle as owner of the role as well
    writes.push({
      objectType: 'role',
      objectId: role.uid,
      relation: 'owner',
      subjectType: ownerType,
      subjectId: owner,
    });
    await req.fga.create(...writes);

    await trx.commit();
    return role;
  } catch (err) {
    await trx.rollback();
    if (err instanceof UniqueViolationError) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        errorConstants.DUPLICATE_ROLE,
      );
    }

    throw err;
  }
};

const listRoles = async (req: Request) => {
  const projectUid = req.locals.project?.uid;
  const { ownerUid } = req.locals.handle;
  const {
    includeProjects,
    includeOrgRoles,
    includeProjectLevelRoles,
    includePermissions,
    includeRoleCounts,
    includeUsers,
    q,
  } = req.query;
  const perPage = Number(req.query.per_page);
  let page: number = Number(req.query.current_page);

  if (page < 1) page = 1;
  const offset = (page - 1) * (perPage);

  const roles : Array<DetailedRole> = (await req.models.Role.query()
    .withGraphFetched('tags(isRoleTag)')
    .select('roles.*', raw('count(roles.uid) OVER() as cnt'))
    .where((builder) => {
      if (q) {
        builder.where('roles.name', 'ilike', `%${q}%`)
          .andWhere('roles.deletedAt', null);
      }
    })
    .modify((builder) => {
      if (projectUid) {
        builder
          .leftJoin('projects', 'roles.projectUid', 'projects.uid')
          .where('roles.projectUid', projectUid)
          .select('projects.name as projectName', 'projects.key as projectKey', 'projects.avatarAttachmentUid')
          .andWhere('roles.deletedAt', null);
        if (includeOrgRoles) {
          builder.orWhereNull('roles.projectUid')
            .andWhere('roles.deletedAt', null);
        }
      } else if (includeProjectLevelRoles) {
        builder
          .innerJoin('projects', 'roles.projectUid', 'projects.uid')
          .whereNotNull('roles.projectUid')
          .select('projects.name as projectName', 'projects.key as projectKey', 'projects.avatarAttachmentUid');
      } else {
        builder.whereNull('roles.projectUid')
          .andWhere('roles.deletedAt', null);
        if (Array.isArray(includeProjects) && includeProjects.length > 0) {
          builder.orWhereIn('roles.projectUid', includeProjects as string[]);
        }
      }
    })
    .andWhere('roles.deletedAt', null)
    .orderBy([{ column: 'roles.system', order: 'desc' }, { column: 'roles.createdAt', order: 'desc' }])
    .limit(perPage)
    .offset(offset)) as unknown as Array<Role & { cnt: number, projectAvatar?: string }>;

  const baseURL = `${env.BACKEND_URL}${env.API_INTERNAL_ROUTE}/${req.params.handle}`;

  if (includePermissions) {
    await Promise.all(
      roles.map(async (role) => {
        const roleUid = role.uid;
        const roleTuples = await req.fga.query(`role:${roleUid}#assignee`, `org:${ownerUid}`, '');
        const permissions = roleTuples.map((t) => t.key.relation);
        role.permissions = permissions;
      }),
    );
  }

  if (includeUsers) {
    for (const role of roles) {
      const tuples = await req.fga.query('', `role:${role.uid}`, 'assignee');
      const userIds = tuples
        .filter((t) => t.key.user.startsWith('user'))
        .map((t) => t.key.user?.split(':').pop());

      const users = await req.models.User.query()
        .whereIn('uid', userIds)
        .select('uid', 'firstName', 'lastName', 'email', 'avatar');
      role.assignees = users;
    }
  }

  const rolesResults = roles.map((role) => ({
    ...role,
    ...(role.avatarAttachmentUid ? { avatarUrl: `${baseURL}/projects/attachments/${role.avatarAttachmentUid}/object` } : {}),
  }));

  const pagination: Record<string, any> = {};

  if (includeRoleCounts) {
    const [orgRolesCount, projectRolesCount] : any = await Promise.all([
      req.models.Role.query()
        .whereNull('deletedAt')
        .whereNull('projectUid')
        .count('uid as orgRoles')
        .first(),
      req.models.Role.query()
        .whereNull('deletedAt')
        .whereNotNull('projectUid')
        .count('uid as projectRoles')
        .first(),
    ]);
    pagination.orgRolesCount = Number(orgRolesCount?.orgRoles) || 0;
    pagination.projectRolesCount = Number(projectRolesCount?.projectRoles) || 0;
  }

  pagination.total = Number(roles[0]?.cnt) ?? 0;
  pagination.per_page = perPage;
  pagination.offset = offset;
  pagination.to = offset + roles.length;
  pagination.last_page = Math.ceil(pagination.total / perPage);
  pagination.current_page = page;
  pagination.from = offset;
  // eslint-disable-next-line no-unused-vars
  pagination.roles = rolesResults;

  return pagination;
};

const getRolePermissions = async (req: Request, role: Role, resource: Resource) => {
  const tuples = await req.fga.query(
    `role:${role.uid}#assignee`,
    `${resource.type}:${resource.id}`,
    '',
  );
  return tuples.map((t) => t.key.relation);
};

const findRole = async (req: Request) => {
  const uid = req.params.id;
  const role : Role & { projectAvatar?: string, avatarAttachmentUid?: string } = await Role.findWithProject(req.knexDB, uid);

  if (!role) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.ROLE_NOT_FOUND,
    );
  }

  const baseURL = `${env.BACKEND_URL}${env.API_INTERNAL_ROUTE}/${req.params.handle}`;
  if (role.avatarAttachmentUid) {
    role.projectAvatar = `${baseURL}/projects/attachments/${role.avatarAttachmentUid}/object`;
  }

  const { ownerUid, ownerType } = req.locals.handle;
  const [assignees, permissions] = await Promise.all([
    req.fga.query('', `role:${uid}`, 'assignee'),
    getRolePermissions(req, role, {
      type: ownerType,
      id: ownerUid,
    }),
  ]);

  const users = assignees.filter((o) => (role.projectUid ? o.key.condition.name === 'override_role' : o.key.condition.name === 'default_role')).map((a) => a.key.user.split(':')[1]);

  const members = users.length === 0
    ? []
    : await req.models.User.query()
      .whereIn('uid', users)
      .select('uid', 'firstName', 'lastName', 'email', 'avatar');

  return { ...role, members, permissions };
};

const updateRole = async (req: Request) => {
  const trx = await req.knexDB.transaction();
  try {
    const uid = req.params.id;
    let role = await req.models.Role.query(trx)
      .whereNull('deletedAt')
      .where({ uid })
      .first();

    if (!role) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errorConstants.ROLE_NOT_FOUND,
      );
    }

    if (role.system) {
      throw new ApplicationError(
        StatusCodes.METHOD_NOT_ALLOWED,
        errorConstants.CANNOT_UPDATE_SYSTEM_ROLE,
      );
    }

    const { ownerType } = req.locals.handle;
    const owner = req.locals.handle.ownerUid;

    role.name = req.body.name ?? role.name;
    role.description = req.body.description ?? role.description;

    role = await role.$query(trx).patchAndFetch({
      name: req.body.name ?? role.name,
      description: req.body.description ?? role.description,
    });

    // handle role updates
    const tagUids = req.body.tagUids ?? [];
    if (tagUids.length > 0) {
      const validTags = await req.models.Tag.query(trx)
        .whereIn('uid', tagUids)
        .where('systemType', 'tag');

      // discard other tag associations
      await req.models.RoleTag.query(trx)
        .where('roleUid', role.uid)
        .whereNotIn(
          'tagUid',
          validTags.map((t) => t.uid),
        )
        .patch({ deletedAt: req.knexDB.fn.now() });

      const inserts = validTags.map((t) => ({
        roleUid: role.uid,
        tagUid: t.uid,
      }));
      if (inserts.length > 0) {
        await trx.raw(
          '? on conflict ("roleUid","tagUid") do update set "deletedAt"=null',
          [req.models.RoleTag.query(trx).toKnexQuery().insert(inserts)],
        );
      }
    }
    let permissions;
    if (req.body.permissions) {
      const initialPermissions = await getRolePermissions(req, role, {
        type: ownerType,
        id: owner,
      });
      permissions = req.body.permissions;

      const toAdd = _.difference(permissions, initialPermissions);
      const toDelete = _.difference(initialPermissions, permissions);

      if (toDelete.length > 0) {
        const deletes: FGARawWrite[] = [];
        for (const p of toDelete) {
          deletes.push({
            objectId: owner,
            objectType: ownerType,
            relation: p,
            subjectType: 'role',
            subjectId: `${uid}#assignee`,
          });
        }
        await req.fga.delete(...deletes);
      }

      if (toAdd.length > 0) {
        const creates: FGARawWrite[] = [];
        for (const p of toAdd) {
          creates.push({
            objectId: owner,
            objectType: ownerType,
            relation: p,
            subjectType: 'role',
            subjectId: `${uid}#assignee`,
          });
        }
        await req.fga.create(...creates);
      }
    }
    await trx.commit();
    return { ...role, permissions };
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const addMembers = async (req: Request) => {
  const roleId: string = req.params.id;
  const { ownerUid } = req.locals.handle;
  const { ownerType } = req.locals.handle;
  const accountTuple = `${ownerType}:${ownerUid}`;
  const role = await req.models.Role.query().findById(roleId);
  if (!role) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.ROLE_NOT_FOUND,
    );
  }

  if (role.slug === 'owner') {
    // check if user in session is an owner
    const isOwner = await req.fga.check(
      `user:${req.locals.user.uid}`,
      accountTuple,
      'owner',
      { current_project: '' },
    );
    if (!isOwner) {
      throw new ApplicationError(
        StatusCodes.FORBIDDEN,
        errorConstants.USER_IS_NOT_OWNER,
      );
    }
  }

  const nonMembers: string[] = [];
  const newMembers: string[] = [];

  for (const user of req.body.userIds) {
    // check if the user is a member
    const isMember = await req.fga.check(
      `user:${user}`,
      accountTuple,
      'member',
    );
    if (!isMember) {
      nonMembers.push(user);
      continue;
    }

    // avoid adding the same tuple twice in openFGA
    const isAssignee = await req.fga.check(
      `user:${user}`,
      `role:${roleId}`,
      'assignee',
    );
    if (!isAssignee) newMembers.push(user);
  }
  if (nonMembers.length > 0) {
    throw new ApplicationError(
      StatusCodes.CONFLICT,
      errorConstants.USER_NOT_MEMBER_OF_ORG,
      nonMembers,
    );
  }

  const tuples: FGARawWrite[] = newMembers.map((u) => ({
    objectType: 'role',
    objectId: roleId,
    subjectType: 'user',
    subjectId: u,
    relation: 'assignee',
    condition: {
      name: 'default_role',
      context: {
        excluded: [],
      },
    },
  }));

  if (newMembers.length > 0) await req.fga.create(...tuples);

  return null;
};

const removeMembers = async (req: Request) => {
  const roleId: string = req.params.id;
  const deletes: FGARawWrite[] = [];
  const nonMembers: string[] = [];

  try {
    const role = await req.models.Role.query()
      .where({ uid: roleId })
      .whereNull('deletedAt')
      .first();

    if (!role) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errorConstants.ROLE_NOT_FOUND,
      );
    }

    for (const userId of req.body.userIds) {
      const isAssignee = await req.fga.check(
        `user:${userId}`,
        `role:${role.uid}`,
        'assignee',
        { current_project: '' },
      );
      if (!isAssignee) {
        nonMembers.push(userId);
        continue;
      }

      deletes.push({
        objectId: role.uid,
        objectType: 'role',
        relation: 'assignee',
        subjectId: userId,
        subjectType: 'user',
      });
    }

    if (nonMembers.length > 0) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        errorConstants.USER_NOT_MEMBER_OF_ORG,
        nonMembers,
      );
    }

    if (deletes.length > 0) {
      if (role.slug === 'owner') {
        const existingMembers = await req.fga.query(
          '',
          `role:${roleId}`,
          'assignee',
        );

        if (deletes.length >= existingMembers.length) {
          throw new ApplicationError(
            StatusCodes.METHOD_NOT_ALLOWED,
            errorConstants.UNABLE_TO_REMOVE_ALL_OWNERS,
          );
        }
      }
      await Promise.all(_.chunk(deletes, 100).map((w) => req.fga.delete(...w)));
    }

    return null;
  } catch (err) {
    logger.error('Error while removing members from a role', err);
    throw err;
  }
};

const getMemberRoles = async (req: Request) => {
  const orgRoles = await req.models.Role.query().select(
    'name',
    'description',
    'uid',
  );
  const roles = [];

  for (const role of orgRoles) {
    const isAssignee = await req.fga.check(
      `user:${req.params.userUid}`,
      `role:${role.uid}`,
      'assignee',
      { current_project: '' },
    );
    if (isAssignee) roles.push(role);
  }

  return roles;
};

async function deleteRole(req: Request) {
  const { id } = req.params;
  const [role] = await req.models.Role.query()
    .where({ uid: id })
    .whereNull('deletedAt')
    .where('system', false)
    .patch({ deletedAt: req.knexDB.fn.now() })
    .returning('*');

  // unassign everyone from the role if role is found
  if (role) {
    const assignees = await req.fga.query('', `role:${id}`, 'assignee');
    const deletes: FGARawWrite[] = [];
    for (const user of assignees) {
      deletes.push({
        objectId: id,
        objectType: 'role',
        relation: 'assignee',
        subjectId: user.key.user?.split(':').pop(),
        subjectType: 'user',
      });
    }
    await Promise.all(_.chunk(deletes, 100).map((w) => req.fga.delete(...w)));
  }
}

async function deleteRoles(req: Request) {
  const { roleIds } = req.body;
  const deletedRoles = await req.models.Role.query()
    .whereIn('uid', roleIds)
    .whereNull('deletedAt')
    .where('system', false)
    .patch({ deletedAt: req.knexDB.fn.now() })
    .returning('*');

  if (deletedRoles && deletedRoles.length > 0) {
    const assigneePromises = deletedRoles.map((role) => req.fga.query('', `role:${role.uid}`, 'assignee'));

    const assigneesResults = await Promise.all(assigneePromises);

    const deleteOperations: FGARawWrite[] = assigneesResults.flatMap(
      (assignees, index) => assignees.map((assignee) => ({
        objectId: deletedRoles[index].uid,
        objectType: 'role',
        relation: 'assignee',
        subjectId: assignee.key.user?.split(':').pop(),
        subjectType: 'user',
      })),
    );

    await Promise.all(
      _.chunk(deleteOperations, 100).map((w) => req.fga.delete(...w)),
    );
  }
  return null;
}

const reAssignRoleToMembers = async (req: Request) => {
  const projectUid = req.locals.project?.uid;
  const { id } = req.params;
  const { members } = req.body;
  let overriddenRoles = req.body.overriddenRoles || null;
  const { ownerType, ownerUid } = req.locals.handle;

  const ownerRole = await req.models.Role.query().findOne({ system: true, slug: 'owner' });

  let ownersCount = (await req.fga.query(
    '',
    `role:${ownerRole.uid}`,
    'assignee',
  )).length;

  if (ownerRole.uid === id && !projectUid) {
    // Reassigning the owner role should have some precautions
    // Org should have at least one owner
    // The owner must be assigned from an owner role
    const isOwner = await req.fga.check(
      `user:${req.locals.user.uid}`,
      `${ownerType}:${ownerUid}`,
      'owner',
      { current_project: '' },
    );

    if (!isOwner) {
      throw new ApplicationError(
        StatusCodes.FORBIDDEN,
        errorConstants.MISSING_PERMISSION,
      );
    }
  }

  if (projectUid) { // Owner role at the org level cannot be reassigned within the project scope
    for (const member of members) {
      const isOrgOwner = await req.fga.check(`user:${member.userId}`, `${ownerType}:${ownerUid}`, 'owner', { current_project: '' });
      if (isOrgOwner) {
        throw new ApplicationError(
          StatusCodes.FORBIDDEN,
          errorConstants.ORG_OWNER_CANNOT_BE_REASSIGNED,
        );
      }
    }
  }

  if (members && members.length > 0) {
    const tuples: { toBeAdded: FGARawWrite[], toBeDeleted: FGARawWrite[] } = await members.reduce(async (accPromise: Promise<{ toBeDeleted: FGARawWrite[], toBeAdded: FGARawWrite[] }>, curr: MemberDTO) => {
      const acc = await accPromise;

      const fgaQuery = await req.fga.query(`user:${curr.userId}`, 'role:', 'assignee');

      const excludedProjects = new Set<string>();
      const newIncludedProjects = new Set<string>();

      const userCurrentDefaultRole = fgaQuery.find((r) => r.key.condition?.name === 'default_role');
      const userCurrentDefaultRoleUid = userCurrentDefaultRole?.key.object.split(':')[1];
      const newDefaultRole = projectUid ? userCurrentDefaultRoleUid : id;

      if (projectUid) {
        overriddenRoles = fgaQuery.filter((r) => r.key.condition?.name === 'override_role').reduce((acc, curr: Record<string, any>) => {
          curr.key.condition.context.allowed.forEach((project: string) => {
            const pUid = project.split(':')[1];
            if (+pUid !== projectUid) {
              acc.push({
                roleUid: curr.key.object.split(':')[1],
                projectUid: pUid,
              });
            }
          });
          return acc;
        }, []);

        overriddenRoles.push({
          roleUid: id,
          projectUid,
        });
      }

      if (!overriddenRoles) {
        acc.toBeDeleted.push(
          ...fgaQuery.filter((r) => r.key.condition?.name === 'override_role' && r.key.object.split(':')[1] === newDefaultRole).map((assignee: Record<string, any>) => {
            const projects = assignee.key.condition.context?.allowed || [];
            projects.forEach((project: string) => newIncludedProjects.add(project));
            return {
              objectId: assignee.key.object.split(':')[1],
              objectType: 'role',
              relation: 'assignee',
              subjectId: curr.userId,
              subjectType: 'user',
            };
          }),
        );
      }

      acc.toBeDeleted.push(
        ...fgaQuery
          .filter((r) => overriddenRoles || r.key.condition?.name === 'default_role')
          .map((assignee) => {
            const objectId = assignee.key.object.split(':')[1];
            const isDefaultRole = assignee.key.condition?.name === 'default_role';

            if (isDefaultRole && objectId === ownerRole.uid) {
              ownersCount -= 1;
            }

            return {
              objectId,
              objectType: 'role',
              relation: 'assignee',
              subjectId: curr.userId,
              subjectType: 'user',
            };
          }),
      );

      if (!overriddenRoles) {
        fgaQuery.forEach((r : Record<string, any>) => {
          if (r.key.condition?.name === 'override_role') {
            (r.key.condition.context.allowed as string[]).forEach((project) => {
              if (!newIncludedProjects.has(project)) { excludedProjects.add(project); }
            });
          }
        });
      }
      if (overriddenRoles?.length) {
        const restructuredOverrides = overriddenRoles.reduce((acc, role) => {
          if (!acc[role.roleUid]) {
            acc[role.roleUid] = [];
          }
          acc[role.roleUid].push(`project:${role.projectUid}`);
          return acc;
        }, {});

        for (const [roleId, projects] of Object.entries(restructuredOverrides)) {
          if (roleId !== newDefaultRole) {
            (projects as string[]).forEach((project) => {
              excludedProjects.add(project);
            });
            acc.toBeAdded.push({
              subjectId: curr.userId,
              subjectType: 'user',
              objectId: roleId,
              objectType: 'role',
              relation: 'assignee',
              condition: {
                name: 'override_role',
                context: {
                  allowed: projects,
                },
              },
            });
          }
        }
      }

      acc.toBeAdded.push({
        subjectId: curr.userId,
        subjectType: 'user',
        objectId: newDefaultRole,
        objectType: 'role',
        relation: 'assignee',
        condition: {
          name: 'default_role',
          context: {
            excluded: Array.from(excludedProjects),
          },
        },
      });

      return acc;
    }, Promise.resolve({ toBeDeleted: [], toBeAdded: [] }));

    if (!ownersCount) {
      throw new ApplicationError(
        StatusCodes.FORBIDDEN,
        errorConstants.UNABLE_TO_REASSIGN_ALL_OWNERS,
      );
    }

    await Promise.all(
      _.chunk(tuples.toBeDeleted, 100).map((w) => req.fga.delete(...w)),
    );

    await Promise.all(
      _.chunk(tuples.toBeAdded, 100).map((w) => req.fga.create(...w)),
    );
  }

  return null;
};

const getPermissions = async () => permissionsList;

export async function updateUserRole(
  req: Request,
  userId: string,
  roleId: string,
  trx?: Knex.Transaction,
) {
  logger.info(`attempting to update user ${userId}'s role to ${roleId}`);
  const tuples = await req.fga.query(`user:${userId}`, 'role:', 'assignee');
  const existingRoleId = tuples[0].key?.object?.split(':').pop();

  if (roleId === existingRoleId) return;

  const roles = await req.models.Role.query(trx).whereIn('uid', [
    roleId,
    existingRoleId,
  ]);

  const owner = roles?.find((r) => r.slug === 'owner');

  if (owner) {
    const userIsOwner = await req.fga.check(
      `user:${req.locals.user.uid}`,
      `role:${owner.uid}`,
      'assignee',
      { current_project: '' },
    );

    if (!userIsOwner) throw new Error(errorConstants.USER_IS_NOT_OWNER);

    // check if outgoing roleId is owner role, if yes, ensure at least one owner remains after the removal
    if (owner.uid === existingRoleId) {
      const owners = await req.fga.query(
        '',
        `role:${existingRoleId}`,
        'assignee',
      );

      if (owners.length <= 1) throw new Error(errorConstants.UNABLE_TO_REMOVE_ALL_OWNERS);
    }
  }

  await req.fga.delete({
    objectId: existingRoleId,
    objectType: 'role',
    subjectId: userId,
    subjectType: 'user',
    relation: 'assignee',
  });

  await req.fga.create({
    objectId: roleId,
    objectType: 'role',
    subjectId: userId,
    subjectType: 'user',
    relation: 'assignee',
  });
}

export default {
  createRole: httpHandler(createRole),
  listRoles: httpHandler(listRoles),
  findRole: httpHandler(findRole),
  updateRole: httpHandler(updateRole),
  addMembers: httpHandler(addMembers),
  removeMembers: httpHandler(removeMembers),
  getMemberRoles: httpHandler(getMemberRoles),
  deleteRole: httpHandler(deleteRole),
  deleteRoles: httpHandler(deleteRoles),
  reAssignRoleToMembers: httpHandler(reAssignRoleToMembers),
  getPermissions: httpHandler(getPermissions),
};
