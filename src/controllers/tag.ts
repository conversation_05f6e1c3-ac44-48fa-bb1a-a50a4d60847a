import { ListTagsDto } from '@app/types/tag';
import { Request } from 'express';
import { httpHandler } from '@app/lib/http';

const getTags = async (req: Request) => {
  const { entityType, includeArchived }: ListTagsDto = req.query as any;
  const projectUid = req.locals.project?.uid;

  const tags = await req.models.Tag.query()
    .where({ deletedAt: null, systemType: 'tag' })
    .where((q) => {
      if (req.query.entityType) q.whereRaw('"entityTypes" @> ?', [[entityType]]);

      if (projectUid) {
        q.where((builder) => {
          builder.whereNull('projectUid').orWhere({ projectUid });
        });
      } else {
        q.whereNull('projectUid');
      }
      if (!includeArchived) q.whereNull('archivedAt');
    })
    .select('*');

  return tags;
};

const createTag = async (req: Request) => {
  const { name, description, entityTypes } = req.body;
  const projectUid = req.locals.project?.uid;

  const tag = await req.models.Tag.query().insert({
    name,
    description,
    entityTypes,
    projectUid,
  });

  // set handle as owner in openfga
  await req.fga.create({
    objectType: 'tag',
    objectId: tag.uid,
    relation: 'owner',
    subjectType: req.locals.handle.ownerType,
    subjectId: req.locals.handle.ownerUid,
  });

  return tag;
};

const updateTag = async (req: Request) => {
  const {
    name, description, entityTypes, archived,
  } = req.body;
  const { id: tagId } = req.params;
  const updatedTagData: Record<string, any> = {};

  if (name) {
    updatedTagData.name = name;
  }

  if (description) {
    updatedTagData.description = description;
  }

  if (entityTypes) {
    updatedTagData.entityTypes = entityTypes;
  }

  if (archived) {
    if (archived === true) {
      updatedTagData.archivedAt = req.knexDB.fn.now();
    } else {
      updatedTagData.archivedAt = null;
    }
  }

  const updatedTag = await req.models.Tag.query()
    .findById(tagId)
    .patch(updatedTagData)
    .returning('*');

  return updatedTag;
};

const deleteTag = async (req: Request) => {
  await req.models.Tag.query()
    .findById(req.params.id)
    .patch({ deletedAt: req.knexDB.fn.now() });

  return { success: true };
};

export default {
  getTags: httpHandler(getTags),
  createTag: httpHandler(createTag),
  updateTag: httpHandler(updateTag),
  deleteTag: httpHandler(deleteTag),
};
