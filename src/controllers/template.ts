import { Request } from 'express';
import { User } from '@app/models/user';
import { httpHandler } from '@app/lib/http';
import i18n from 'i18n';

const getTemplates = async (req: Request) => {
  const owner = req.locals.handle.ownerUid;
  const { ownerType } = req.locals.handle;

  const pagination: any = {};
  const perPage: number = Number(req.query.per_page) || 10;
  let page: number = Number(req.query.current_page) || 1;

  if (page < 1) page = 1;
  const offset = (page - 1) * perPage;

  const uids = (
    await req.fga.query(`${ownerType}:${owner}`, 'template:', 'owner')
  ).map((tuple: any) => tuple.key.object.split(':')[1]);

  let templatesQuery = req.models.TestTemplate.query()
    .whereIn('uid', uids)
    .whereNull('deletedAt')
    .where('projectUid', req.locals.project.uid)
    .select('*');

  const {
    createdByIds, creationStartDate, creationEndDate, name,
  } = req.query;

  if (name) {
    templatesQuery = templatesQuery.whereILike('name', `%${name}%`);
  }

  if (createdByIds) {
    templatesQuery = templatesQuery.whereIn(
      'createdBy',
      createdByIds as string[],
    );
  }

  if (creationStartDate && creationEndDate) {
    templatesQuery = templatesQuery.whereBetween('createdAt', [
      creationStartDate as string,
      creationEndDate as string,
    ]);
  } else if (creationStartDate) {
    templatesQuery = templatesQuery.where(
      'createdAt',
      '>=',
      creationStartDate as string,
    );
  } else if (creationEndDate) {
    templatesQuery = templatesQuery.where(
      'createdAt',
      '<=',
      creationEndDate as string,
    );
  }

  const count: any = await req.models.TestTemplate.query()
    .whereIn('uid', uids)
    .whereNull('deletedAt')
    .where('projectUid', req.locals.project.uid)
    .count('uid as CNT')
    .first();

  const templates = await templatesQuery;

  if (templates.length) {
    const users: Record<string, User> = {};

    (
      await req.models.User.query()
        .whereIn(
          'uid',
          templates.map((s) => s.createdBy),
        )
        .select('uid', 'lastName', 'firstName', 'email')
    ).reduce((u, creator) => {
      u[creator.uid] = creator;
      return u;
    }, users);

    templates.forEach((t) => {
      t.creator = users[t.createdBy];
    });
  }

  pagination.total = count.CNT;
  pagination.per_page = perPage;
  pagination.offset = offset;
  pagination.to = offset + templates.length;
  pagination.last_page = Math.ceil(count.CNT / perPage);
  pagination.current_page = page;
  pagination.from = offset;
  pagination.templates = templates;
  return pagination;
};

const createTemplate = async (req: Request) => {
  const { name, templateFields, isDefault } = req.body;
  const template = await req.models.TestTemplate.query()
    .insert({
      name,
      customFields: { templateFields },
      createdBy: req.locals.user.uid,
      projectUid: req.locals.project.uid,
      isDefault,
    })
    .returning('*');

  if (isDefault) {
    await req.models.TestTemplate.query()
      .patch({ isDefault: false })
      .whereNot({ uid: template.uid })
      .whereNull('deletedAt');
  }

  await req.fga.create({
    objectType: 'template',
    objectId: template.uid,
    relation: 'owner',
    subjectType: req.locals.handle.ownerType,
    subjectId: req.locals.handle.ownerUid,
  });
  return template;
};

const updateTemplate = async (req: Request) => {
  const { name, templateFields, isDefault } = req.body;

  const oldTemplate = await req.models.TestTemplate.query()
    .findById(req.params.id);

  const payload = {
    ...(name && { name }),
    ...(isDefault && { isDefault }),
    customFields: {
      ...oldTemplate.customFields,
      templateFields: [...(templateFields || [])],
    },
  };

  if (isDefault && isDefault === true) {
    await req.models.TestTemplate.query()
      .patch({ isDefault: false })
      .whereNot({ uid: req.params.id })
      .whereNull('deletedAt');
  }

  return req.models.TestTemplate.query()
    .patchAndFetchById(req.params.id, payload);
};

const getTemplate = async (req: Request) => {
  const template = await req.models.TestTemplate.query()
    .where('projectUid', req.locals.project.uid)
    .whereNull('deletedAt')
    .findById(req.params.id);

  return template;
};

const deleteTemplate = async (req: Request) => {
  await req.models.TestTemplate.query()
    .findById(req.params.id)
    .where('projectUid', req.locals.project.uid)
    .patch({ deletedAt: req.knexDB.fn.now() });

  return { message: i18n.__('templateDeleted') };
};

export default {
  getTemplates: httpHandler(getTemplates),
  createTemplate: httpHandler(createTemplate),
  updateTemplate: httpHandler(updateTemplate),
  getTemplate: httpHandler(getTemplate),
  deleteTemplate: httpHandler(deleteTemplate),
};
