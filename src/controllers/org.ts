import { ApplicationError, httpH<PERSON><PERSON> } from '@app/lib/http';
import { Request, Response } from 'express';
import _ from 'lodash';

import { Attachment } from '@app/models/attachment';
import { FGARawWrite } from '@ss-libs/ss-component-auth';
import { Handle } from '@app/models/handle';
import { Knex } from 'knex';
import { Membership } from '@app/models/membership';
import { Org } from '@app/models/org';
import { StatusCodes } from 'http-status-codes';
import { UniqueViolationError } from 'objection';
import cannonicalName from '@app/utils/cannonicalName';
import { compare } from 'bcrypt';
import env from '@app/config/env';
import errorConstants from '@app/constants/errors';
import logger from '@app/config/logger';
import mimes from 'mime-types';
import ssPayments from '@ss-libs/ss-component-payments';
import { storageProvider } from '@ss-libs/ss-component-media';
import { v4 } from 'uuid';
import { DBServer } from '@app/models/dbServer';
import { Account } from '@app/temporal/activities/account';
import { updateUserRole } from './role';
import { startWorkflow } from '../temporal/client';

/**
 * remove member from an organization
 * DELETE
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.handle
 * @param {String} req.body.avatarUrl
 */
const removeMember = async (req: Request) => {
  const { userId } = req.params;
  const { ownerUid } = req.locals.handle;
  const { ownerType } = req.locals.handle;
  const org = await req.models.Org.query().findById(ownerUid);
  if (!org) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.ORG_NOT_FOUND,
    );
  }
  // ensure owner cannot be removed
  if (ownerType === 'user' && ownerUid === userId) {
    throw new ApplicationError(
      StatusCodes.CONFLICT,
      errorConstants.CANNOT_REMOVE_OWNER,
    );
  } else if (ownerType === 'org') {
    if (org.createdBy === userId) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        errorConstants.CANNOT_REMOVE_OWNER,
      );
    }
  }
  const userTuple = `user:${userId}`;
  // get directly attached permissions
  const [directPermissions, assignedRoles] = await Promise.all([
    req.fga.query(userTuple, `${ownerType}:${ownerUid}`, ''),
    req.fga.query(userTuple, 'role:', 'assignee'),
  ]);
  // get assigned roles
  const deletes: FGARawWrite[] = [];
  for (const tuple of directPermissions) {
    deletes.push({
      subjectType: 'user',
      subjectId: userId,
      objectType: ownerType,
      objectId: ownerUid,
      relation: tuple.key.relation,
    });
  }
  for (const tuple of assignedRoles) {
    const [objectType, objectId] = tuple.key.object.split(':');
    deletes.push({
      subjectType: 'user',
      subjectId: userId,
      objectId,
      objectType,
      relation: tuple.key.relation,
    });
  }

  await req.models.Membership.query()
    .where({
      accountType: ownerType,
      accountUid: ownerUid,
      userUid: userId,
      deletedAt: null,
    })
    .patch({ deletedAt: req.sharedKnexDB.fn.now() });

  if (deletes.length > 0) {
    await Promise.all(_.chunk(deletes, 100).map((w) => req.fga.delete(...w)));
  }

  if (ownerType === 'org' && org?.stripeId) {
    const [members] = await req.models.Membership.query()
      .where({ accountUid: ownerUid })
      .whereNull('deletedAt')
      .count('*');
    await ssPayments.updateSeatCount(ownerType, ownerUid, (<any>members).count);
  }
};

const newOrg = async (req: Request) => {
  const cHandle = cannonicalName(req.body.handle);
  const userId = req.locals.user.uid;
  const { name, file } = req.body;

  const trx = await req.sharedKnexDB.transaction();

  try {
    const org = await Org.query(trx)
      .insert({
        name,
        createdBy: userId,
      })
      .returning('*');

    await Handle.query(trx).insert({
      name: cHandle,
      ownerType: 'org',
      current: true,
      ownerUid: org.uid,
    });

    await Membership.query(trx).insert({
      accountType: 'org',
      accountUid: org.uid,
      userUid: userId,
    });

    const dbServer = await DBServer.query(trx)
      .findOne({
        'dbServers.isDefault': true,
        'appNode.isDefault': true,
      })
      .joinRelated('appNode');

    const response: any = {
      uid: org.uid,
      name: org.name,
      handle: cHandle,
      roleName: 'Account Admin',
      createdBy: org.createdBy,
      createdAt: org.createdAt,
      dbServerUid: dbServer?.uid,
    };

    const setupOrg: any = {
      ownerType: 'org',
      ownerUid: org.uid,
      orgName: org.name,
      dbServerUid: dbServer?.uid,
    };

    if (file) {
      try {
        const {
          mediaType, fileType, size, fileName,
        } = file;

        const attachmentUid = v4();

        const prefix = mediaType === 'attachment' ? org.uid : 'public';
        const key = `${prefix}/${attachmentUid}.${mimes.extension(fileType)}`;

        const presignedUpload = await storageProvider(org.uid).upload(
          mediaType,
          key,
          fileType,
          size,
        );

        const attachment = {
          uid: attachmentUid,
          name: fileName,
          ownerUid: org.uid,
          size,
          fileType,
          mediaType,
        };

        setupOrg.data = {
          attachments: [attachment],
        };

        await Org.query(trx).findOne({ uid: org.uid }).patch({
          avatarUrl: presignedUpload.object_url,
        });

        response.data = {
          objectKey: presignedUpload.key,
          signedUrl: presignedUpload.signed_url,
          objectUrl: presignedUpload.object_url,
          clientHeaders: presignedUpload.client_headers,
        };
      } catch {
        response.uploadFailed = true;
      }
    }
    const param: Account = {
      ownerType: 'org',
      ownerUid: org.uid,
      orgName: org.name,
      dbServerUid: dbServer?.uid,
    };
    await startWorkflow('setupAccountWorkflow', {
      taskQueue: 'setup-account-queue',
      workflowId: `setup.account.${org.uid}.${Date.now()}`,
      args: [param],
    });
    await trx.commit();
    return {
      ...response,
    };
  } catch (error) {
    await trx.rollback();

    if (error instanceof UniqueViolationError) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        errorConstants.HANDLE_DUPLICATED,
      );
    }
    throw error;
  }
};

/**
 * delete member
 * DELETE
 * @param {Object} req
 * @param {Object} res
 * @param {Object} req.orgId
 */
const deleteOrg = async (req: Request) => {
  const { handle } = req.locals;
  const orgId = handle.ownerUid;

  if (handle.ownerType !== 'org') throw new ApplicationError(StatusCodes.FORBIDDEN, '');

  const trx = await req.sharedKnexDB.transaction();
  try {
    const user = await req.models.User.query(trx).findById(req.locals.user.uid);
    if (!(await compare(req.body.password, user.passwordHash))) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        errorConstants.CURRENT_PASSWORD_INCORRECT,
      );
    }
    await req.models.Org.query(trx)
      .where({ uid: orgId, deletedAt: null })
      .patch({ deletedAt: req.knexDB.fn.now() });

    await req.models.Handle.query(trx).where('uid', handle.uid).patch({
      current: false,
    });

    await req.models.Dashboard.query().patch({
      deletedAt: req.knexDB.fn.now(),
    });
    await trx.commit();
    return null;
  } catch (error) {
    await trx.rollback();
    throw error;
  }
};

/**
 * change avatar of org
 *  PATCH
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.handle
 * @param {String} req.body.avatarUrl
 */
const updateOrg = async (req: Request) => {
  const orgId = req.locals.handle.ownerUid;

  const org = await req.models.Org.query()
    .findById(orgId)
    .patch(req.body)
    .returning('*');
  return org;
};

const uploadAttachment = async (req: Request) => {
  const { fileType, size, fileName } = req.body;

  const { ownerType, ownerUid } = req.locals.handle;
  const creatorUid = req.locals.user?.uid || null;
  const newAttachment = {
    name: fileName,
    ownerUid,
    size,
    fileType,
    mediaType: 'profile-picture',
    creatorUid,
  };

  if (ownerType !== 'org') throw new ApplicationError(StatusCodes.FORBIDDEN, '');

  const sharedTrx = await req.sharedKnexDB.transaction();
  const trx = await req.knexDB.transaction();

  try {
    const {
      key,
      signed_url: signedUrl,
      object_url: objectUrl,
      client_headers: clientHeaders,
      uid,
    } = await Attachment.createAttachment(newAttachment, trx);

    await Org.query(sharedTrx)
      .findById(req.locals.handle.ownerUid)
      .patch({ avatarUrl: objectUrl });

    await sharedTrx.commit();
    await trx.commit();

    return {
      uid,
      objectKey: key,
      signedUrl,
      objectUrl,
      clientHeaders,
    };
  } catch (err) {
    await sharedTrx.rollback();
    await trx.rollback();
    throw err;
  }
};

/**
 * Fetch paginated members of an organization
 * GET
 * @param {Object} request
 * @param {Object} response
 * @param {Number} request.query.next_page
 * @param {Number} request.query.per_page
 * @param {String} request.params.handle
 */
const getUsers = async (req: Request) => {
  try {
    const orgId = req.locals.handle.ownerUid;
    const [query] = await Promise.all([
      req.fga.paginatedQuery(
        '',
        `org:${orgId}`,
        'member',
        req.query.cursor as string,
      ),
    ]);

    const roles = (await req.models.Role.query().select('uid', 'name')).reduce(
      (acc, role) => {
        acc[role.uid] = role.name;
        return acc;
      },
      {},
    );

    let userUids = query.tuples.map((t) => t.key.user.split(':').pop());
    if (!userUids.length) {
      return { users: [], orgId, cursor: null };
    }

    const usersQuery = await req.models.User.query()
      .join('handles', 'users.uid', '=', 'handles.ownerUid')
      .whereIn('users.uid', userUids)
      .where('handles.current', true)
      .whereNull('users.deletedAt')
      .where('handles.ownerType', 'user')
      .select({
        uid: 'users.uid',
        firstName: 'users.firstName',
        lastName: 'users.lastName',
        email: 'users.email',
        handle: 'handles.name',
        avatar: 'users.avatar',
      });

    const roleMap = {};
    userUids = usersQuery.map((u) => u.uid);

    await Promise.all(
      userUids.map(async (uid) => {
        const fgaTuple : any = (await req.fga.query(`user:${uid}`, 'role:', 'assignee')); // reterieve only the default role
        const defaultRole = fgaTuple.filter((tuple) => tuple.key.condition?.name === 'default_role')[0];
        const overriddenRoles = fgaTuple.filter((tuple) => tuple.key.condition?.name === 'override_role').map((t) => ({
          roleId: t.key.object.split(':')[1],
          projectUids: t.key.condition.context.allowed?.map((p : any) => Number.parseInt(p.split(':')[1])) || [],
        }));
        const roleId = defaultRole?.key.object.split(':')[1] ?? null; // Show only the default role. On clicking "Edit", it should display the default role and the overridden role for each project.
        roleMap[uid] = {
          roleId,
          excluded_projects: defaultRole?.key.condition.context?.excluded.map((p : any) => Number.parseInt(p.split(':')[1])) || [],
          overriddenRoles,
        };
      }),
    );

    const memberTags = await req.models.MemberTag.query()
      .whereIn('userUid', userUids)
      .whereNull('memberTags.deletedAt')
      .innerJoin('tags', 'tags.uid', 'tagUid')
      .select('tags.*', 'userUid');

    const userTagsMap = {};
    memberTags.forEach((tag) => {
      if (!userTagsMap[tag.userUid]) userTagsMap[tag.userUid] = [];
      userTagsMap[tag.userUid].push(tag);
    });

    const auditLogs = await req.models.AuditLog.query()
      .select('actor', req.knexDB.raw('MAX("createdAt") as "createdAt"'))
      .whereIn('actor', userUids)
      .groupBy('actor');
    const lastActivityMap = Object.fromEntries(
      auditLogs.map((log) => [log.actor, log.createdAt]),
    );

    const projects = await req.models.Project.query().whereNull('deletedAt');
    const baseURL = `${env.BACKEND_URL}${env.API_INTERNAL_ROUTE}/${req.params.handle}`;
    const users = usersQuery.map((user) => ({
      ...user,
      overriddenRoles: roleMap[user.uid].overriddenRoles || [],
      role: {
        uid: roleMap[user.uid].roleId || null,
        name: roles[roleMap[user.uid].roleId] || null,
        projects: projects.filter((p) => roleMap[user.uid].excluded_projects.includes(p.uid)).map((p) => ({
          ...p,
          ...(p.avatarAttachmentUid ? { avatarUrl: `${baseURL}/projects/attachments/${p.avatarAttachmentUid}/object` } : {}),
        })) || [],
      },
      tags: userTagsMap[user.uid] || [],
      lastActivity: lastActivityMap[user.uid] || null,
    }));

    return { users, orgId, cursor: query.continuation_token };
  } catch (error) {
    logger.error('Failed to fetch user', error);
    if (error instanceof ApplicationError) {
      throw error;
    } else {
      throw new ApplicationError(
        StatusCodes.INTERNAL_SERVER_ERROR,
        errorConstants.INTERNAL_SERVER_ERROR,
      );
    }
  }
};

async function getOrgMembers(req: Request, uid: string, sharedKnex: Knex) {
  const members = await req.models.Membership.query(sharedKnex).where({
    accountUid: uid,
  });
  return members.map((m) => m.userUid);
}

const countUsers = async (req: Request) => {
  const orgId = req.locals.handle.ownerUid;

  const [members, pendingInvites] = await Promise.all([
    getOrgMembers(req, orgId, req.sharedKnexDB),
    req.models.Invite.query()
      .whereNull('accepted')
      .whereNull('deletedAt')
      .whereRaw('"expiresAt" >= current_timestamp')
      .select('uid'),
  ]);
  return { members: members.length, pending: pendingInvites.length };
};

const getOrganization = async (req: Request) => {
  const { handle } = req.params;

  const org = await req.models.Org.query()
    .join('handles', 'orgs.uid', 'handles.ownerUid')
    .where('handles.name', handle)
    .first()
    .returning('*');

  if (!org) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.ORG_NOT_FOUND,
    );
  }

  return { org };
};

const updateUsers = async (req: Request, res: Response) => {
  const { body } = req;
  const accountId = req.locals.handle.ownerUid;

  const incomingTags = new Set(
    (
      await req.models.Tag.query().whereIn('uid', [
        ...(body.tagUids ?? []),
        ...(body.tagReplacements?.newTagUids ?? []),
      ])
    ).map((t) => t.uid),
  );
  const errors: { userUid: string; message: string }[] = [];

  for (const userId of body.userUids) {
    const trx = await req.knexDB.transaction();
    try {
      const member = await req.models.Membership.query()
        .where({
          userUid: userId,
          accountUid: accountId,
        })
        .first();
      if (!member) continue;

      const tags = new Set(incomingTags);

      (
        await req.models.MemberTag.query(trx).where({
          userUid: userId,
          deletedAt: null,
        })
      ).map((t) => tags.add(t.tagUid));
      const removals = body.tagReplacements?.existingTagUids ?? [];

      if (removals.length > 0) {
        for (const t of removals) tags.delete(t);
        await req.models.MemberTag.query(trx)
          .where('userUid', userId)
          .whereIn('tagUid', removals)
          .patch({ deletedAt: trx.fn.now() });
      }

      if (tags.size > 0) {
        await trx.raw(
          '? on conflict ("userUid","tagUid") do update set "deletedAt"=null, "updatedAt"=current_timestamp',
          [
            req.models.MemberTag.query(trx)
              .toKnexQuery()
              .insert(
                Array.from(tags).map((t) => ({ userUid: userId, tagUid: t })),
              ),
          ],
        );
      }

      // handle role assignment
      await updateUserRole(req, userId, body.roleUid, trx);

      await trx.commit();
    } catch (err) {
      errors.push({ userUid: userId, message: err.message });
      await trx.rollback();
      logger.error(err);
      continue;
    }
  }

  if (errors.length === 0) return null;

  res.status(StatusCodes.MULTI_STATUS);
  return errors;
};

const initiateSSO = async (req: Request) => {
  const orgId = req.locals.handle.ownerUid;
  const org = await req.models.Org.query().findById(orgId);
  if (!org) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.ORG_NOT_FOUND,
    );
  }
  const ssoConfig = await req.models.SSOConfig.query().where({
    orgUid: orgId,
  }).first();

  if (!ssoConfig) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.CONFIG_NOT_FOUND,
    );
  }

  if (ssoConfig.config.oidc) {
    return {
      url: `${env.BACKEND_URL}${env.API_INTERNAL_ROUTE}/signin/oidc?orgHandle=${req.params.handle}`,
      type: 'oidc',
    };
  }

  throw new ApplicationError(
    StatusCodes.BAD_REQUEST,
    errorConstants.UNSUPPORTED_SSO_PROVIDER,
  );
};

export default {
  newOrg: httpHandler(newOrg),
  getUsers: httpHandler(getUsers),
  updateOrg: httpHandler(updateOrg),
  deleteOrg: httpHandler(deleteOrg),
  removeMember: httpHandler(removeMember),
  countUsers: httpHandler(countUsers),
  getOrganization: httpHandler(getOrganization),
  updateUsers: httpHandler(updateUsers),
  uploadAttachment: httpHandler(uploadAttachment),
  initiateSSO: httpHandler(initiateSSO),
};
