/* eslint-disable @typescript-eslint/no-unused-vars */
import { auth } from '@ss-libs/ss-component-auth';
import {
  bindSharedModels, loadDbComponent, loadHandle, loadProject, tenantContextExternalApi,
} from '@app/middlewares/handle';
import {
  Controller,
  Middlewares,
  Route,
  Request,
  Post,
  Body,
  Path,
} from 'tsoa';
import { permissions } from '@app/constants/auth';
import preferencesService from '@app/models/preferences';
import { IngressData } from '../../temporal/activities/ingress';
import { startWorkflow } from '../../temporal/client';
import { CreateDataDto } from '../../types/data';

const middlewares = (...permissions: string[]) => [
  auth().authenticateBearer,
  loadHandle(),
  tenantContextExternalApi(),
  bindSharedModels,
  loadDbComponent(),
  loadProject,
  auth().authz(...permissions),
];

@Route('v1/:handle/projects/:key/data')
export class DataController extends Controller {
  @Post()
  @Middlewares(...middlewares(permissions.write_activity))
  public async newData(
  // eslint-disable-next-line
    @Request() req: any,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Body() requestBody: CreateDataDto,
  ) {
    const handlePreference : any = await preferencesService.findOne(
      req.sharedKnexDB,
      req.locals.handle.ownerType,
      req.locals.handle.ownerUid,
    );

    const defaults = await preferencesService.getDefaults(
      req.sharedKnexDB,
      req.locals.handle.ownerType,
      req.locals.handle.ownerUid,
    );

    const param: IngressData = {
      body: requestBody,
      ownerUid: req.locals.handle.ownerUid,
      handlePreference,
      ...(req.locals.project ? { projectUid: req.locals.project.uid } : {}),
      defaults,
      ownerType: req.locals.handle.ownerType,
    };

    await startWorkflow('ingressWorkflow', {
      taskQueue: 'ingress-queue',
      workflowId: `${req.locals.handle.ownerUid}:ingress:${Date.now()}`,
      args: [param],
    });

    await req.res.status(202).send();
  }
}
