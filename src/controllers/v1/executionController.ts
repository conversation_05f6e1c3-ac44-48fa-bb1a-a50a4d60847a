/* eslint-disable @typescript-eslint/no-unused-vars */
import { auth, FGARawWrite } from '@ss-libs/ss-component-auth';
import { Request as Req } from 'express';
import {
  bindSharedModels,
  setResource,
  loadHandle,
  tenantContextExternalApi,
  loadDbComponent,
  loadProject,
} from '@app/middlewares/handle';
import { ApplicationError } from '@app/lib/http';
import { StatusCodes } from 'http-status-codes';
import { ValidateBody, Body } from '@app/middlewares/tsoaValidator';
import errorConstants from '@app/constants/errors';
import { User } from '@app/models/user';
import { paginated } from '@app/lib/model';
import {
  Controller,
  Get,
  Middlewares,
  Route,
  Request,
  Path,
  Post,
  Patch,
  Queries,
} from 'tsoa';
import { permissions } from '@app/constants/auth';
import {
  createExecutionSchema,
  updateExecutionSchema,
} from '@app/validators/v1/execution';
import { startWorkflow } from '@app/temporal/client';
import { PaginatedQuery } from '../../lib/model';
import { CreateExecutionDTO, UpdateExecutionDTO } from '../../types/execution';
import { UpdateRunStateDTO } from '../../temporal/activities/run';

const middlewares = (
  resource: string = 'execution',
  ...permissions: string[]
) => [
  setResource(resource),
  auth().authenticateBearer,
  loadHandle(),
  tenantContextExternalApi(),
  bindSharedModels,
  loadDbComponent(),
  loadProject,
  auth().authz(...permissions),
];

@Route('v1/{handle}/projects/{key}/executions')
export class ExecutionController extends Controller {
  /**
   * Retrieves the list of paginated executions for a particular project of a user or organization.
   * @param handle The unique identifier for user or an organization
   * @param projectKey The unique identifier for a project
   */
  @Get()
  @Middlewares(...middlewares('execution', permissions.read_activity))
  public async getExecutions(
    @Request() req: Req,
      @Path('handle') handle: string,
      @Path('key') projectKey: string,
      @Queries() queryParams: PaginatedQuery,
  ): Promise<any> {
    const { limit, offset } = queryParams;
    const executionQuery = req.models.TestExecution.query().where(
      'projectUid',
      req.locals.project.uid,
    );

    const paginatedResult = await paginated(
      executionQuery.toKnexQuery(),
      limit,
      offset,
      req.knexDB,
    );

    return paginatedResult;
  }

  /**
   * Retrieves the details of a specific test execution within a project for a user or organization.
   * @param handle The unique identifier for a user or an organization
   * @param projectKey The unique identifier for a project
   * @param id The unique identifier for the test execution
   */
  @Get('{id}')
  @Middlewares(...middlewares('execution', permissions.read_activity))
  public async getExecution(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Path() id: number,
  ) {
    const execution = await req.models.TestExecution.query()
      .findOne('testExecutions.uid', id)
      .select('*');
    return execution;
  }

  /**
   * Creates a new test execution within a project for a user or organization.
   * @param handle The unique identifier for a user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param executionDto The execution data to be created.
   * @returns The newly created execution object.
   */
  @Post()
  @Middlewares(...middlewares('execution', permissions.write_activity))
  @ValidateBody(createExecutionSchema)
  public async createExecution(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Body() executionDto: CreateExecutionDTO,
  ) {
    const { steps, ...rest } = executionDto;

    const trx = await req.knexDB.transaction();
    try {
      const execution = await req.models.TestExecution.query(trx)
        .insert(rest)
        .returning('*');

      if (steps && steps.length > 0) {
        const stepsToInsert: any[] = steps.map((step) => ({
          externalId: execution.externalId,
          source: execution.source,
          description: step.description,
          customFields: step.customFields,
          testStepUid: step?.testStepUid,
          testCaseUid: step.testStepUid,
          testExecutionUid: execution.uid, // Linking to the created test execution
        }));
        await req.models.TestExecutionStep.query(trx).insert(stepsToInsert);
      }
      await trx.commit();

      const owner = req.locals.handle.ownerUid;
      const { ownerType } = req.locals.handle;

      const writes: FGARawWrite[] = [
        {
          objectType: 'execution',
          objectId: execution.uid,
          relation: 'owner',
          subjectType: ownerType, // type of entity
          subjectId: owner, // entity id
        },
      ];
      await req.fga.create(...writes);

      if (execution.testRunUid) {
        const param: UpdateRunStateDTO = {
          ownerType: req.locals.handle.ownerType,
          ownerUid: req.locals.handle.ownerUid,
          runUids: [execution.testRunUid],
        };
        await startWorkflow('updateRunWorkflow', {
          taskQueue: 'update-run-queue',
          workflowId: `update.run.${execution.testRunUid}.${Date.now()}`,
          args: [param],
        });
      }

      return execution;
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  /**
   * Updates an existing execution within a project for a user or organization.
   * @param handle The unique identifier for a user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param id The unique identifier for the execution.
   * @param executionDto The execution data to be updated.
   * @returns The updated execution object.
   */
  @Patch('{id}')
  @Middlewares(...middlewares('execution', permissions.write_activity))
  @ValidateBody(updateExecutionSchema)
  public async updateExecution(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Path() id: number,
    @Body() executionDto: UpdateExecutionDTO,
  ) {
    const trx = await req.knexDB.transaction();
    const exec = await req.models.TestExecution.updateOne(
      trx,
      id,
      executionDto,
    );

    if (!exec) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errorConstants.TEST_EXECUTION_NOT_FOUND,
      );
    }

    await trx.commit();

    if (exec.assignedTo) {
      exec.assignedTo = (await User.getOne(
        req.sharedKnexDB,
        exec.assignedTo,
      )) as any;
    }

    if (exec.testRunUid) {
      const param: UpdateRunStateDTO = {
        ownerType: req.locals.handle.ownerType,
        ownerUid: req.locals.handle.ownerUid,
        runUids: [exec.testRunUid],
      };
      await startWorkflow('updateRunWorkflow', {
        taskQueue: 'update-run-queue',
        workflowId: `update.run.${exec.testRunUid}.${Date.now()}`,
        args: [param],
      });
    }
    return exec;
  }
}
