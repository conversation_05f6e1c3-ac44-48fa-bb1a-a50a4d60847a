/* eslint-disable @typescript-eslint/no-unused-vars */
import i18n from 'i18n';
import { auth } from '@ss-libs/ss-component-auth';
import { Request as Req } from 'express';
import { kebabCase } from 'lodash';
import { UniqueViolationError } from 'objection';
import { StatusCodes } from 'http-status-codes';
import { ApplicationError } from '@app/lib/http';
import {
  setResource, tenantContextExternalApi, loadDbComponent, loadHandle, loadProject, bindSharedModels,
} from '@app/middlewares/handle';
import { ValidateBody, Body } from '@app/middlewares/tsoaValidator';
import { permissions } from '@app/constants/auth';
import errorConstants from '@app/constants/errors';
import { paginated } from '@app/lib/model';
import {
  Controller,
  Get,
  Middlewares,
  Route,
  Request,
  Path,
  Delete,
  Post,
  Patch,
  Queries,
} from 'tsoa';
import {
  createCustomFieldSchema,
  updateCustomFieldSchema,
} from '@app/validators/v1/customField';
import { CustomField } from '@app/models/customField';
import { PaginatedQuery } from '../../lib/model';
import { CreateCustomFieldDto, UpdateCustomFieldDto } from '../../types/customField';

const middlewares = (
  resource: string = 'custom_field',
  ...permissions: string[]
) => [
  setResource(resource),
  auth().authenticateBearer,
  loadHandle(),
  tenantContextExternalApi(),
  bindSharedModels,
  loadDbComponent(),
  loadProject,
  auth().authz(...permissions),
];

@Route('v1/{handle}/projects/{key}/customFields')
export class CustomFieldController extends Controller {
  /**
   * Retrieves the list of paginated custom fields for a particular project of a user or organization.
   * @param handle The unique identifier for user or an organization.
   * @param projectKey The unique identifier for a project.
   */
  @Get('')
  @Middlewares(...middlewares('custom_field'))
  public async getCustomFields(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Queries() queryParams: PaginatedQuery,
  ) {
    const { limit, offset } = queryParams;
    const owner = req.locals.handle.ownerUid;
    const { ownerType } = req.locals.handle;

    const uids = (
      await req.fga.query(`${ownerType}:${owner}`, 'custom_field:', 'owner')
    ).map((tuple: any) => tuple.key.object.split(':')[1]);

    const sql = req.models.CustomField.query()
      .whereIn('uid', uids)
      .where('projectUid', req.locals.project.uid)
      .whereNull('deletedAt')
      .select('*');

    const paginatedResult = await paginated(
      sql.toKnexQuery(),
      limit,
      offset,
      req.knexDB,
    );

    return paginatedResult;
  }

  /**
   * Retrieves a specific custom field by its ID.
   * @param handle The unique identifier for user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param id The unique identifier of the custom field.
   */
  @Get('{id}')
  @Middlewares(...middlewares('custom_field'))
  public async getCustomField(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Path() id: string,
  ) {
    const customField = await req.models.CustomField.query()
      .where('projectUid', req.locals.project.uid)
      .whereNull('deletedAt')
      .findById(id);

    if (!customField) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, `Custom field with id ${id} not found`);
    }

    return customField;
  }

  /**
 * Creates a new custom field within a project for a user or organization.
 * @param handle The unique identifier for a user or an organization.
 * @param projectKey The unique identifier for a project.
 * @param customFieldDto The custom field data to be created.
 * @returns The newly created custom field object.
 */
  @Post('/')
  @Middlewares(...middlewares('custom_field', permissions.write_custom_field))
  @ValidateBody(createCustomFieldSchema)
  public async createCustomField(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Body() customFieldDto: CreateCustomFieldDto,
  ) {
    const {
      name, type, options, source,
    } = customFieldDto;
    const trx = await req.knexDB.transaction();
    try {
      const customField = await req.models.CustomField.query(trx)
        .insert({
          name,
          slug: kebabCase(name),
          type,
          options,
          source,
          projectUid: req.locals.project.uid,
        })
        .returning('*');

      await req.fga.create({
        objectType: 'custom_field',
        objectId: customField.uid,
        relation: 'owner',
        subjectType: req.locals.handle.ownerType,
        subjectId: req.locals.handle.ownerUid,
      });
      await trx.commit();
      return customField;
    } catch (err) {
      await trx.rollback();
      if (err instanceof UniqueViolationError) {
        throw new ApplicationError(
          StatusCodes.CONFLICT,
          errorConstants.CUSTOM_FIELD_EXISTS,
        );
      }
      throw err;
    }
  }

  /**
   * Updates an existing custom field.
   * @param handle The unique identifier for user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param id The unique identifier of the template.
   * @param customFieldDto The updated custom field data.
   */
  @Patch('{id}')
  @Middlewares(...middlewares('custom_field', permissions.write_custom_field))
  @ValidateBody(updateCustomFieldSchema)
  public async updateCustomField(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Path() id: string,
    @Body() customFieldDto: UpdateCustomFieldDto,
  ) {
    const {
      name, type, options, source,
    } = customFieldDto;

    try {
      const update: Partial<CustomField> = {
        ...(name && { name }),
        ...(type && { type }),
        ...(options && { options }),
        ...(source && { source }),
      };
      if (update.name) update.slug = kebabCase(update.name);

      const updatedCustomField = await req.models.CustomField.query()
        .findById(id)
        .where('projectUid', req.locals.project.uid)
        .patch(update)
        .returning('*');

      return updatedCustomField;
    } catch (err) {
      if (err instanceof UniqueViolationError) {
        throw new ApplicationError(
          StatusCodes.CONFLICT,
          errorConstants.CUSTOM_FIELD_EXISTS,
        );
      }
      throw err;
    }
  }

  /**
   * Deletes a custom field.
   * @param handle The unique identifier for user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param id The unique identifier of the custom field.
   * @returns A message indicating the successful deletion of the custom field.
   */
  @Delete('{id}')
  @Middlewares(...middlewares('custom_field', permissions.delete_custom_field))
  public async deleteCustomField(
    @Request() req: Req,
      @Path('handle') handle: string,
      @Path('key') projectKey: string,
      @Path() id: string,
  ): Promise<{ message: string }> {
    await req.models.CustomField.query()
      .findById(id)
      .where('projectUid', req.locals.project.uid)
      .patch({ deletedAt: req.knexDB.fn.now(), slug: req.knexDB.raw('null') });

    return { message: i18n.__('customFieldDeleted') };
  }
}
