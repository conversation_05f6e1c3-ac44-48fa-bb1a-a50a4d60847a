/* eslint-disable @typescript-eslint/no-unused-vars */
import i18n from 'i18n';
import { auth, FGARawWrite } from '@ss-libs/ss-component-auth';
import { StatusCodes } from 'http-status-codes';
import { Request as Req } from 'express';
import { ApplicationError } from '@app/lib/http';
import {
  setResource, tenantContextExternalApi, loadHandle, loadDbComponent, loadProject, bindSharedModels,
} from '@app/middlewares/handle';
import { getNextId, paginated } from '@app/lib/model';
import { permissions } from '@app/constants/auth';
import {
  Controller,
  Get,
  Middlewares,
  Route,
  Request,
  Path,
  Delete,
  Post,
  Patch,
  Queries,
} from 'tsoa';
import { createSharedStepSchema, updateSharedStepSchema } from '@app/validators/v1/sharedStep';
import { ValidateBody, Body } from '@app/middlewares/tsoaValidator';
import { PaginatedQuery } from '../../lib/model';
import { CreateSharedStepDto, UpdateSharedStepDto } from '../../types/step';

const middlewares = (resource: string = 'step', ...permissions: string[]) => [
  setResource(resource),
  auth().authenticateBearer,
  loadHandle(),
  tenantContextExternalApi(),
  bindSharedModels,
  loadDbComponent(),
  loadProject,
  auth().authz(...permissions),
];

@Route('v1/{handle}/projects/{key}/shared-steps')
export class SharedStepController extends Controller {
  /**
   * Retrieves the list of paginated shared steps for a particular project of a user or organization.
   * @param handle The unique identifier for user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param queryParams Query parameters for pagination.
   */
  @Get()
  @Middlewares(...middlewares('step'))
  public async getSharedSteps(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Queries() queryParams: PaginatedQuery,
  ) {
    const owner = req.locals.handle.ownerUid;
    const { ownerType } = req.locals.handle;
    const { limit, offset } = queryParams;

    const relatedSharedSteps = (
      await req.fga.query(`${ownerType}:${owner}`, 'step:', 'owner')
    ).map((tuple: any) => tuple.key.object.split(':')[1]);

    const sharedTestStepsQuery = req.models.SharedTestStep.query()
      .select('sharedTestSteps.*')
      .whereIn('sharedTestStepRef', relatedSharedSteps)
      .whereNull('deletedAt')
      .where('active', true)
      .where('projectUid', req.locals.project.uid);

    const page = await paginated(
      sharedTestStepsQuery.toKnexQuery(),
      limit,
      offset,
      req.knexDB,
    );

    return page;
  }

  /**
   * Retrieves a specific shared step by its unique identifier.
   * @param handle The unique identifier for user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param id The unique identifier for the shared step.
   * @returns The shared step object.
   */
  @Get('{id}')
  @Middlewares(...middlewares('step'))
  public async getSharedStep(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Path() id: number,
  ) {
    const sharedStep = await req.models.SharedTestStep.query()
      .where({ uid: id })
      .whereNull('deletedAt')
      .where('active', true)
      .where('projectUid', req.locals.project.uid)
      .first();

    if (!sharedStep) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        i18n.__('sharedStepsNotFound'),
      );
    }

    return sharedStep;
  }

  /**
 * Creates a new shared step within a project for a user or organization.
 * @param handle The unique identifier for a user or an organization.
 * @param projectKey The unique identifier for a project.
 * @param sharedStepDto The shared step data to be created.
 * @returns The newly created shared step object.
 */
  @Post()
  @Middlewares(...middlewares('step', permissions.write_step))
  @ValidateBody(createSharedStepSchema)
  public async createSharedStep(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Body() sharedStepDto: CreateSharedStepDto,
  ) {
    const trx = await req.knexDB.transaction();
    try {
      const { name, steps } = sharedStepDto;
      const uid = await getNextId(trx, req.models.SharedTestStep);
      const owner = req.locals.handle.ownerUid;
      const { ownerType } = req.locals.handle;

      const payload = {
        uid,
        name,
        steps,
        version: 1,
        active: true,
        projectUid: req.locals.project.uid,
        sharedTestStepRef: uid,
        createdBy: req.locals.accessToken.ownerUid,
      };

      const sharedStep = await req.models.SharedTestStep.query(trx)
        .insert({
          ...payload,
        })
        .returning('*');

      const writes: FGARawWrite[] = [
        {
          objectType: 'step',
          objectId: sharedStep.uid,
          relation: 'owner',
          subjectType: ownerType,
          subjectId: owner,
        },
      ];

      await req.fga.create(...writes);

      await trx.commit();

      return sharedStep;
    } catch (err) {
      await trx.rollback();
      throw err;
    }
  }

  /**
 * Updates an existing shared step within a project for a user or organization.
 * @param handle The unique identifier for a user or an organization.
 * @param projectKey The unique identifier for a project.
 * @param id The unique identifier for the shared step.
 * @param sharedStepDto The shared step data to be updated.
 * @returns The updated shared step object.
 */
  @Patch('{id}')
  @Middlewares(...middlewares('step', permissions.write_step))
  @ValidateBody(updateSharedStepSchema)
  public async updateSharedStep(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Path() id: string,
    @Body() sharedStepDto: UpdateSharedStepDto,
  ) {
    const { name, steps } = sharedStepDto;
    const trx = await req.knexDB.transaction();

    try {
      const [latestVersionOfSharedStep] = await Promise.all([
        req.models.SharedTestStep.query(trx)
          .where({ uid: id })
          .orderBy('version', 'DESC')
          .where('projectUid', req.locals.project.uid)
          .first(),
        req.models.SharedTestStep.query(trx)
          .where({ uid: id })
          .where('projectUid', req.locals.project.uid)
          .patch({ active: false }),
      ]);

      const payload = {
        name: name ?? latestVersionOfSharedStep.name,
        steps: steps ?? latestVersionOfSharedStep.steps,
        customFields: latestVersionOfSharedStep.customFields,
        externalId: latestVersionOfSharedStep.externalId,
        source: latestVersionOfSharedStep.source,
        projectUid: req.locals.project.uid,
      };

      const newSharedStep = await req.models.SharedTestStep.query()
        .insert({
          ...payload,
          version: latestVersionOfSharedStep.version + 1,
          active: true,
          sharedTestStepRef: latestVersionOfSharedStep.sharedTestStepRef,
          createdBy: latestVersionOfSharedStep.createdBy,
        })
        .returning('*');

      await trx.commit();

      return newSharedStep;
    } catch (err) {
      await trx.rollback();
      throw err;
    }
  }

  /**
   * Deletes an existing shared step within a project for a user or organization.
   * @param handle The unique identifier for a user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param id The unique identifier for the shared step.
   * @returns A message indicating the successful deletion of the shared step.
   */
  @Delete('{id}')
  @Middlewares(...middlewares('step', permissions.delete_step))
  public async deleteSharedStep(
    @Request() req: Req,
      @Path('handle') handle: string,
      @Path('key') projectKey: string,
      @Path() id: number,
  ): Promise<{ message: string }> {
    await req.models.SharedTestStep.query()
      .where({ uid: id })
      .patch({ deletedAt: req.knexDB.fn.now() });

    return { message: i18n.__('sharedStepDeleted') };
  }
}
