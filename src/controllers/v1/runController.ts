/* eslint-disable @typescript-eslint/no-unused-vars */
import i18n from 'i18n';
import { auth, FGARawWrite } from '@ss-libs/ss-component-auth';
import { ApplicationError } from '@app/lib/http';
import { StatusCodes } from 'http-status-codes';
import { Request as Req } from 'express';
import {
  setResource,
  tenantContextExternalApi,
  loadHandle,
  loadDbComponent,
  loadProject,
  bindSharedModels,
} from '@app/middlewares/handle';
import { ValidateBody, Body } from '@app/middlewares/tsoaValidator';
import { permissions } from '@app/constants/auth';
import logger from '@app/config/logger';
import errors from '@app/constants/errors';
import { paginated } from '@app/lib/model';
import {
  Controller,
  Get,
  Middlewares,
  Route,
  Delete,
  Request,
  Path,
  Post,
  Patch,
  Queries,
} from 'tsoa';
import {
  createTestRunSchema,
  updateTestRunSchema,
} from '@app/validators/v1/run';
import preferencesService from '@app/models/preferences';
import { startWorkflow } from '@app/temporal/client';
import { CreateRunDTO, UpdateRunDTO } from '../../types/run';
import { PaginatedQuery } from '../../lib/model';
import { UpdateRunStateDTO } from '../../temporal/activities/run';

const middlewares = (resource: string = 'run', ...permissions: string[]) => [
  setResource(resource),
  auth().authenticateBearer,
  loadHandle(),
  tenantContextExternalApi(),
  bindSharedModels,
  loadDbComponent(),
  loadProject,
  auth().authz(...permissions),
];

@Route('v1/{handle}/projects/{key}/runs')
export class RunController extends Controller {
  /**
   * Retrieves the list of paginated test runs for a particular project of a user or organization.
   * @param handle The unique identifier for user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param queryParams Optional query parameters for pagination and filtering.
   */
  @Get()
  @Middlewares(...middlewares('run', permissions.read_activity))
  public async getRuns(
    @Request() req: Req,
      @Path('handle') handle: string,
      @Path('key') projectKey: string,
      @Queries() queryParams?: PaginatedQuery,
  ): Promise<any> {
    const { limit, offset } = queryParams;
    const sql = req.models.TestRun.query()
      .withGraphFetched('[testMilestones,testPlans]')
      .modifyGraph('testMilestones', (builder) => {
        builder.select(['tags.name as name', 'tags.uid as uid']);
      })
      .where({ projectUid: req.locals.project.uid, systemType: 'run' })
      .whereNull('tags.deletedAt')
      .orderBy('tags.createdAt', 'DESC')
      .offset(offset)
      .limit(limit)
      .select('*')
      .toKnexQuery();

    const paginatedResult = await paginated(sql, limit, offset, req.knexDB);

    return paginatedResult;
  }

  /**
   * Retrieves the details of a specific test run within a project for a user or organization.
   * @param handle The unique identifier for a user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param id The unique identifier for the test run.
   * @returns The details of the test run.
   */
  @Get('{id}')
  @Middlewares(...middlewares('run', permissions.read_activity))
  public async getRun(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Path() id: number,
  ) {
    const run = await req.models.TestRun.query()
      .withGraphFetched('testMilestones')
      .where({
        uid: id,
        deletedAt: null,
      });

    if (!run.length) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, errors.RUN_NOT_FOUND);
    }

    return run[0];
  }

  /**
   * Creates a new test run within a project for a user or organization.
   * @param handle The unique identifier for a user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param testRunDto The template data to be created.
   * @returns The newly created test run object.
   */
  @Post()
  @Middlewares(...middlewares('run', permissions.write_entity))
  @ValidateBody(createTestRunSchema)
  public async createTestRun(@Request() req: Req, @Body() dto: CreateRunDTO) {
    const trx = await req.knexDB.transaction();
    try {
      const { ownerType, ownerUid } = req.locals.handle;

      const { defaults } = await preferencesService.getDefaultsAndCompleted(
        req.sharedKnexDB,
        ownerType,
        ownerUid,
      );
      const { testRun, executions } = await req.models.TestRun.newRun(
        trx,
        {
          externalId: dto.externalId,
          priority: dto.priority ?? defaults.testRun?.priority,
          status: dto.status ?? defaults.testRun?.status,
          name: dto.name,
          description: dto.description,
          projectUid: req.locals.project.uid,
          source: dto.source,
        },
        {
          caseUids: dto.caseUids,
          execPriority: defaults.testCase?.priority,
          execStatus: defaults.testCase?.status,
          milestoneUids: dto.milestoneUids,
        },
      );
      const fgaWrites: FGARawWrite[] = [
        {
          objectType: 'run',
          objectId: testRun.uid,
          relation: 'owner',
          subjectType: ownerType,
          subjectId: ownerUid,
        },
        ...executions.map((e) => ({
          objectType: 'execution',
          objectId: e.uid,
          relation: 'owner',
          subjectType: ownerType,
          subjectId: ownerUid,
        })),
      ];
      await req.fga.create(...fgaWrites);
      await trx.commit();

      const param: UpdateRunStateDTO = {
        runUids: [testRun.uid],
        ownerType,
        ownerUid,
      };
      await startWorkflow('updateRunWorkflow', {
        taskQueue: 'update-run-queue',
        workflowId: `update.run.${testRun.uid}.${Date.now()}`,
        args: [param],
      });
      return testRun;
    } catch (err) {
      await trx.rollback();
      throw err;
    }
  }

  /**
   * Updates an existing test run within a project for a user or organization.
   * @param handle The unique identifier for a user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param id The unique identifier for the test run.
   * @param testRunDto The template data to be updated.
   * @returns The updated test run object.
   */
  @Patch('{id}')
  @Middlewares(...middlewares('run', permissions.write_entity))
  @ValidateBody(updateTestRunSchema)
  public async updateTestRun(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Path() id: number,
    @Body() testRunDto: UpdateRunDTO,
  ) {
    const trx = await req.knexDB.transaction();
    try {
      const run = await req.models.TestRun.updateOne(trx, id, testRunDto);
      await trx.commit();
      const param: UpdateRunStateDTO = {
        runUids: [run.uid],
        ownerType: req.locals.handle.ownerType,
        ownerUid: req.locals.handle.ownerUid,
      };
      await startWorkflow('updateRunWorkflow', {
        taskQueue: 'update-run-queue',
        workflowId: `update.run.${run.uid}.${Date.now()}`,
        args: [param],
      });
      return run;
    } catch (err) {
      logger.info('Failed to update test run');
      await trx.rollback();
      throw err;
    }
  }

  /**
   * Deletes a test run.
   * @param handle The unique identifier for user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param id The unique identifier of the test run.
   * @returns A message indicating the successful deletion of the test run.
   */
  @Delete('{id}')
  @Middlewares(...middlewares('run', permissions.write_entity))
  public async deleteTestRun(
    @Request() req: Req,
      @Path('handle') handle: string,
      @Path('key') projectKey: string,
      @Path() id: number,
  ): Promise<{ message: string }> {
    const trx = await req.knexDB.transaction();
    try {
      const runUids = await req.models.TestRun.deleteByIds(trx, [id]);
      if (runUids.length === 0) {
        throw new ApplicationError(StatusCodes.NOT_FOUND, errors.RUN_NOT_FOUND);
      }
      await trx.commit();

      const param: UpdateRunStateDTO = {
        runUids,
        ownerType: req.locals.handle.ownerType,
        ownerUid: req.locals.handle.ownerUid,
      };
      await startWorkflow('updateRunWorkflow', {
        taskQueue: 'update-run-queue',
        workflowId: `update.run.${runUids}.${Date.now()}`,
        args: [param],
      });

      return { message: i18n.__('runDeleted') };
    } catch (err) {
      await trx.rollback();
      throw err;
    }
  }
}
