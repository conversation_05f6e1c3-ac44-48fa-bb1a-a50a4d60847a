/* eslint-disable @typescript-eslint/no-unused-vars */
import i18n from 'i18n';
import { auth } from '@ss-libs/ss-component-auth';
import { Request as Req } from 'express';
import { StatusCodes } from 'http-status-codes';
import { ApplicationError } from '@app/lib/http';
import errorConstants from '@app/constants/errors';
import {
  setResource, loadHandle, tenantContextExternalApi, bindSharedModels, loadDbComponent, loadProject,
} from '@app/middlewares/handle';
import { ValidateBody, Body } from '@app/middlewares/tsoaValidator';
import { paginated } from '@app/lib/model';
import { permissions } from '@app/constants/auth';
import {
  Controller,
  Get,
  Middlewares,
  Route,
  Request,
  Path,
  Post,
  Delete,
  Patch,
  Queries,
} from 'tsoa';
import { createMilestoneSchema, updateMilestoneSchema } from '@app/validators/v1/milestone';
import preferencesService from '@app/models/preferences';
import { startWorkflow } from '@app/temporal/client';
import { PaginatedQuery } from '../../lib/model';
import { CreateMilestoneDTO, UpdateMilestoneDTO } from '../../types/milestone';
import { UpdateMilestoneData } from '../../temporal/activities/milestone';

const middlewares = (
  resource: string = 'milestone',
  ...permissions: string[]
) => [
  setResource(resource),
  auth().authenticateBearer,
  loadHandle(),
  tenantContextExternalApi(),
  bindSharedModels,
  loadDbComponent(),
  loadProject,
  auth().authz(...permissions),
];

@Route('v1/{handle}/projects/{key}/milestones')
export class MilestoneController extends Controller {
  /**
   * Retrieves the list of paginated milstones for a particular project of a user or organization.
   * @param handle The unique identifier for user or an organization.
   * @param projectKey The unique identifier for a project.
   */
  @Get()
  @Middlewares(...middlewares('milestone', permissions.read_entity))
  public async getMilestones(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Queries() queryParams?: PaginatedQuery,
  ) {
    const { q, limit, offset } = queryParams;
    const sql = req.models.TestMilestone.query()
      .select('*')
      .where('systemType', 'milestone')
      .where('projectUid', req.locals.project.uid)
      .whereNull('deletedAt');

    if (q) {
      sql.whereILike('name', `%${q}%`);
    }

    const paginatedResult = await paginated(
      sql.toKnexQuery(),
      limit,
      offset,
      req.knexDB,
    );

    return paginatedResult;
  }

  /**
   * Retrieves a specific milestone by its unique identifier.
   * @param handle The unique identifier for user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param id The unique identifier for the milestone.
   */
  @Get('{id}')
  @Middlewares(...middlewares('milestone', permissions.read_entity))
  public async getMilestone(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Path() id: number,
  ) {
    const milestone = await req.models.TestMilestone.findOne(req.knexDB, { uid: id });
    if (!milestone) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errorConstants.MILESTONE_NOT_FOUND,
      );
    }

    return milestone;
  }

  /**
 * Creates a new test milestone within a project for a user or organization.
 * @param handle The unique identifier for a user or an organization.
 * @param projectKey The unique identifier for a project.
 * @param milestoneDto The test milestone data to be created.
 * @returns The newly created test milestone object.
 */
  @Post('/')
  @Middlewares(...middlewares('milestone', permissions.write_entity))
  @ValidateBody(createMilestoneSchema)
  public async createMilestone(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Body() milestoneDto: CreateMilestoneDTO,
  ) {
    const trx = await req.knexDB.transaction();
    const defaults = await preferencesService.getDefaults(
      req.sharedKnexDB,
      req.locals.handle.ownerType,
      req.locals.handle.ownerUid,
    );

    try {
      const milestone = await req.models.TestMilestone.create(trx, req.locals.project.uid, {
        ...milestoneDto,
        ...(milestoneDto.status ? undefined : { status: defaults.milestone.status }),
      });

      const owner = req.locals.handle.ownerUid;
      const { ownerType } = req.locals.handle;

      await req.fga.create({
        objectType: 'milestone',
        objectId: milestone.uid,
        relation: 'owner',
        subjectType: ownerType,
        subjectId: owner,
      });

      if (milestoneDto.runIds?.length > 0) {
        await req.models.TestMilestone.relatedQuery('testRuns', trx)
          .for(milestone.uid)
          .relate(milestoneDto.runIds);
      }

      if (milestoneDto.planIds?.length > 0) {
        await req.models.TestMilestone.relatedQuery('testPlans', trx)
          .for(milestone.uid)
          .relate(milestoneDto.planIds);
      }

      await trx.commit();

      const param: UpdateMilestoneData = {
        ownerType,
        ownerUid: owner,
        milestoneUids: [milestone.uid],
      };
      await startWorkflow('updateMilestoneWorkflow', {
        taskQueue: 'update-milestone-queue',
        workflowId: `update.milestone.${milestone.uid}.${Date.now()}`,
        args: [param],
      });

      return milestone;
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  /**
   * Updates an existing test milestone within a project for a user or organization.
   * @param handle The unique identifier for a user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param id The unique identifier for the milestone.
   * @param milestoneDto The milestone data to be updated.
   * @returns The updated milestone object.
   */
  @Patch('{id}')
  @Middlewares(...middlewares('milestone', permissions.write_entity))
  @ValidateBody(updateMilestoneSchema)
  public async updateMilestone(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Path() id: number,
    @Body() milestoneDto: UpdateMilestoneDTO,
  ) {
    const trx = await req.knexDB.transaction();
    try {
      const milestone = await req.models.TestMilestone.updateOne(trx, id, milestoneDto);
      if (!milestone) {
        throw new ApplicationError(
          StatusCodes.NOT_FOUND,
          errorConstants.MILESTONE_NOT_FOUND,
        );
      }
      await trx.commit();
      const param: UpdateMilestoneData = {
        milestoneUids: [milestone.uid],
        ownerType: req.locals.handle.ownerType,
        ownerUid: req.locals.handle.ownerUid,
      };
      await startWorkflow('updateMilestoneWorkflow', {
        taskQueue: 'update-milestone-queue',
        workflowId: `update.milestone.${milestone.uid}.${Date.now()}`,
        args: [param],
      });
      return milestone;
    } catch (err) {
      await trx.rollback();
      throw err;
    }
  }

  /**
   * Deletes an existing test milestone within a project for a user or organization.
   * @param handle The unique identifier for a user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param id The unique identifier for the milestone.
   * @returns A success message.
   */
  @Delete('{id}')
  @Middlewares(...middlewares('milestone', permissions.delete_entity))
  public async deleteMilestone(
    @Request() req: Req,
      @Path('handle') handle: string,
      @Path('key') projectKey: string,
      @Path() id: number,
  ): Promise<{ message: string }> {
    const [mstone] = await req.models.TestMilestone.deleteByIds(req.knexDB, [
      id,
    ]);

    if (mstone) {
      const param: UpdateMilestoneData = {
        milestoneUids: [mstone.uid],
        ownerType: req.locals.handle.ownerType,
        ownerUid: req.locals.handle.ownerUid,
      };
      await startWorkflow('updateMilestoneWorkflow', {
        taskQueue: 'update-milestone-queue',
        workflowId: `update.milestone.${mstone.uid}.${Date.now()}`,
        args: [param],
      });
    }

    return { message: i18n.__('milestoneDeleted') };
  }
}
