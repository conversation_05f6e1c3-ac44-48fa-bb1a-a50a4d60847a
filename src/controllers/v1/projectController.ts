/* eslint-disable @typescript-eslint/no-unused-vars */
import { auth } from '@ss-libs/ss-component-auth';
import { Request as Req } from 'express';
import { StatusCodes } from 'http-status-codes';
import { UniqueViolationError } from 'objection';
import {
  loadHandle, setResource, tenantContextExternalApi, bindSharedModels, loadDbComponent,
} from '@app/middlewares/handle';
import { permissions } from '@app/constants/auth';
import { ApplicationError } from '@app/lib/http';
import errorConstants from '@app/constants/errors';
import { ValidateBody, Body } from '@app/middlewares/tsoaValidator';
import {
  Controller,
  Middlewares,
  Route,
  Request,
  Path,
  Post,
  Get,
  Queries,
} from 'tsoa';
import { createProjectSchema } from '@app/validators/v1/project';
import { Project } from '@app/models/project';
import { CreateProjectDto } from '../../types/project';
import { paginated, PaginatedQuery } from '../../lib/model';

const middlewares = (resource: string = 'project', ...permissions: string[]) => [
  setResource(resource),
  auth().authenticateBearer,
  loadHandle(),
  tenantContextExternalApi(),
  bindSharedModels,
  loadDbComponent(),
  auth().authz(...permissions),
];

@Route('v1/{handle}/projects')
export class ProjectController extends Controller {
  /**
   * Retrieves the list of paginated projects for a user or organization.
   * @param handle The unique identifier for a user or an organization.
   * @param queryParams Query parameters for pagination.
   */
  @Get()
  @Middlewares(...middlewares('project'))
  public async getProjects(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Queries() queryParams: PaginatedQuery,
  ) {
    const { limit, offset } = queryParams;
    const query = req.models.Project.query().where('deletedAt', null);

    const page = await paginated(
      query.toKnexQuery(),
      limit,
      offset,
      req.knexDB,
    );

    return page;
  }

  /**
 * Creates a new project for a user or organization.
 * @param handle The unique identifier for a user or an organization.
 * @param projectDto The project data to be created.
 * @returns The newly created project object.
 */
  @Post()
  @Middlewares(...middlewares('project', permissions.write_project))
  @ValidateBody(createProjectSchema)
  public async createProject(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Body() projectDto: CreateProjectDto,
  ) {
    const trx = await req.knexDB.transaction();
    try {
      const { ownerUid, ownerType } = req.locals.accessToken;
      const project = await Project.newProject(projectDto, ownerType, ownerUid, trx);

      await req.fga.create(
        {
          objectType: 'project',
          objectId: project.uid,
          relation: 'owner',
          subjectType: ownerType,
          subjectId: ownerUid,
        },
        {
          objectType: 'project',
          objectId: project.uid,
          relation: 'parent',
          subjectType: ownerType,
          subjectId: ownerUid,
        },
      );

      await trx.commit();

      return project;
    } catch (error) {
      await trx.rollback();
      if (error instanceof UniqueViolationError) {
        throw new ApplicationError(
          StatusCodes.UNPROCESSABLE_ENTITY,
          errorConstants.PROJECT_KEY_EXISTS,
        );
      }
      throw error;
    }
  }
}
