/* eslint-disable @typescript-eslint/no-unused-vars */
import { StatusCodes } from 'http-status-codes';
import { auth } from '@ss-libs/ss-component-auth';
import { paginated } from '@app/lib/model';
import _ from 'lodash';
import { ApplicationError } from '@app/lib/http';
import { ValidateBody, Body } from '@app/middlewares/tsoaValidator';
import { Request as Req } from 'express';
import {
  setResource, tenantContext, loadHandle, tenantContextExternalApi, bindSharedModels, loadDbComponent, loadProject,
} from '@app/middlewares/handle';
import { permissions } from '@app/constants/auth';
import {
  Controller,
  Get,
  Middlewares,
  Route,
  Request,
  Path,
  Delete,
  Post,
  Queries,
} from 'tsoa';
import i18n from 'i18n';
import { createCasesSchema } from '@app/validators/v1/case';
import preferencesService from '@app/models/preferences';
import errorConstants from '@app/constants/errors';
import { ForeignKeyViolationError } from 'objection';
import { TestCase } from '../../models/testCase';
import { CaseQueryParams, CreateCaseDTO } from '../../types/case';

const middlewares = (resource: string = 'case', ...permissions: string[]) => [
  setResource(resource),
  tenantContext(),
  auth().authenticateBearer,
  loadHandle(),
  tenantContextExternalApi(),
  bindSharedModels,
  loadDbComponent(),
  loadProject,
  auth().authz(...permissions),
];

@Route('v1/{handle}/projects/{key}/cases')
export class CaseController extends Controller {
  /**
   * Retrieves the list of paginated cases for a particular project of a user or organization.
   * @param handle The unique identifier for user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param queryParams Query parameters for pagination and filtering.
   */
  @Get()
  @Middlewares(...middlewares('case', permissions.read_entity))
  public async getCases(
    @Request() req: Req,
      @Path('handle') handle: string,
      @Path('key') projectKey: string,
      @Queries() queryParams: CaseQueryParams,
  ): Promise<any> {
    const {
      limit, offset, tag, priority, q,
    } = queryParams;
    const casesQuery = req.models.TestCase.query()
      .orderBy('uid', 'desc')
      .withGraphFetched('tags(isCaseTag)')
      .where('projectUid', req.locals.project.uid)
      .whereNull('deletedAt')
      .where('active', true)
      .where((builder) => {
        if (tag) {
          builder.whereRaw(`customFields->'tags' @> '"${tag}"'`);
        }
        if (priority) {
          builder.where('priority', [priority]);
        }
        if (q) {
          builder.whereILike('name', `%${q}%`);
        }
        return q;
      });

    const paginatedResult = await paginated(
      casesQuery.toKnexQuery(),
      limit,
      offset,
      req.knexDB,
    );

    return paginatedResult;
  }

  /**
 * Retrieves the details of a specific test case within a project for a user or organization.
 * @param handle The unique identifier for a user or an organization.
 * @param projectKey The unique identifier for a project.
 * @param id The unique identifier for the test case.
 */
  @Get('{id}')
  @Middlewares(...middlewares('case', permissions.read_entity))
  public async getCase(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Path() id: string,
  ) {
    const caseItem = await req.models.TestCase.query()
      .withGraphFetched('tags(isCaseTag)')
      .where('testCaseRef', id)
      .where('deletedAt', null)
      .where('active', true)
      .orderBy('version', 'DESC')
      .first();

    if (!caseItem) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        i18n.__('testCaseNotFound'),
      );
    }

    return caseItem;
  }

  /**
   * Create single or bulk test cases into a project for a user or organization.
   * @param handle The unique identifier for a user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param casesDto Array of test cases to be created.
   * @returns Array of created test cases.
   */
  @Post()
  @Middlewares(...middlewares('case', permissions.write_entity))
  @ValidateBody(createCasesSchema)
  public async createCases(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Body() casesDto: CreateCaseDTO[],
  ) {
    const owner = req.locals.accessToken.ownerUid;
    const { ownerType } = req.locals.accessToken;

    const trx = await req.knexDB.transaction();
    const defaults = await preferencesService.getDefaults(
      req.sharedKnexDB,
      ownerType,
      owner,
    );

    try {
      const { cases, fgaWrites } = await TestCase.newCases(casesDto, defaults, req.locals.project.uid, owner, ownerType, trx);
      await Promise.all(
        _.chunk(fgaWrites, 100).map((group) => req.fga.create(...group)),
      );
      await trx.commit();
      return cases;
    } catch (err) {
      await trx.rollback();
      if (err instanceof ForeignKeyViolationError) {
        let msg: string;
        if (/template/.test(err.table)) {
          msg = errorConstants.INVALID_TEMPLATE_UID;
        } else if (/testCaseSteps/.test(err.table)) {
          msg = errorConstants.INVALID_SHARED_STEP_UID;
        }
        throw new ApplicationError(StatusCodes.UNPROCESSABLE_ENTITY, msg);
      }
      throw err;
    }
  }

  @Delete('{id}')
  @Middlewares(...middlewares('case', permissions.delete_entity))
  public async deleteCase(
    @Request() req: Req,
      @Path('handle') handle: string,
      @Path('key') projectKey: string,
      @Path() id: string,
  ): Promise<{ message: string }> {
    await req.models.TestCase.query()
      .findById(id)
      .patch({ deletedAt: req.knexDB.fn.now() });

    return { message: i18n.__('testCaseDeleted') };
  }
}
