/* eslint-disable @typescript-eslint/no-unused-vars */
import { auth } from '@ss-libs/ss-component-auth';
import { Request as Req } from 'express';
import {
  setResource, loadHandle, bindSharedModels, tenantContextExternalApi, loadDbComponent,
  loadProject,
} from '@app/middlewares/handle';
import { ApplicationError } from '@app/lib/http';
import { ValidateBody, Body } from '@app/middlewares/tsoaValidator';
import { permissions } from '@app/constants/auth';
import { paginated } from '@app/lib/model';
import {
  Controller,
  Get,
  Middlewares,
  Route,
  Request,
  Post,
  Path,
  Delete,
  Patch,
  Queries,
} from 'tsoa';
import i18n from 'i18n';
import {
  createTemplateSchema,
  updateTemplateSchema,
} from '@app/validators/v1/template';
import { StatusCodes } from 'http-status-codes';
import {
  TemplateQueryParams,
  CreateTemplateDto,
  UpdateTemplateDto,
} from '../../types/template';

const middlewares = (
  resource: string = 'template',
  ...permissions: string[]
) => [
  setResource(resource),
  auth().authenticateBearer,
  loadHandle(),
  tenantContextExternalApi(),
  bindSharedModels,
  loadDbComponent(),
  loadProject,
  auth().authz(...permissions),
];

@Route('v1/{handle}/projects/{key}/templates')
export class TemplateController extends Controller {
  /**
   * Retrieves the list of paginated templates for a particular project of a user or organization.
   * @param handle The unique identifier for user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param queryParams Optional query parameters for filtering and pagination.
   */
  @Get('')
  @Middlewares(...middlewares('template'))
  public async getTemplates(
    @Request() req: Req,
      @Path('handle') handle: string,
      @Path('key') projectKey: string,
      @Queries() queryParams?: TemplateQueryParams,
  ): Promise<any> {
    const {
      name,
      createdByIds,
      creationStartDate,
      creationEndDate,
      limit,
      offset,
    } = queryParams || {};

    const query = req.models.TestTemplate.query()
      .where('projectUid', req.locals.project.uid)
      .whereNull('deletedAt')
      .modify((qb) => {
        if (name) {
          qb.where('name', 'ilike', `%${name}%`);
        }
        if (createdByIds) {
          qb.whereIn('createdBy', createdByIds);
        }
        if (creationStartDate) {
          qb.where('createdAt', '>=', creationStartDate);
        }
        if (creationEndDate) {
          qb.where('createdAt', '<=', creationEndDate);
        }
      });

    return paginated(query.toKnexQuery(), limit, offset, req.knexDB);
  }

  /**
   * Retrieves a specific template by its ID.
   * @param handle The unique identifier for user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param id The unique identifier of the template.
   */
  @Get('{id}')
  @Middlewares(...middlewares('template'))
  public async getTemplate(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Path() id: string,
  ) {
    const template = await req.models.TestTemplate.query()
      .where('projectUid', req.locals.project.uid)
      .whereNull('deletedAt')
      .findById(id);

    if (!template) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, `Template with id ${id} not found`);
    }

    return template;
  }

  /**
   * Creates a new template within a project for a user or organization.
   * @param handle The unique identifier for a user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param templateDto The template data to be created.
   * @returns The newly created template object.
   */
  @Post('/')
  @Middlewares(...middlewares('template', permissions.write_template))
  @ValidateBody(createTemplateSchema)
  public async createTemplate(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Body() templateDto: CreateTemplateDto,
  ) {
    const payload = {
      name: templateDto.name,
      ...(templateDto.templateFields && {
        customFields: { templateFields: templateDto.templateFields },
      }),
    };

    const template = await req.models.TestTemplate.query()
      .insert({
        ...payload,
        createdBy: req.locals.accessToken.ownerUid,
        projectUid: req.locals.project.uid,
      })
      .returning('*');

    await req.fga.create({
      objectType: 'template',
      objectId: template.uid,
      relation: 'owner',
      subjectType: req.locals.accessToken.ownerType,
      subjectId: req.locals.accessToken.ownerUid,
    });
    return template;
  }

  /**
   * Updates an existing template.
   * @param handle The unique identifier for user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param id The unique identifier of the template.
   * @param templateDto The updated template data.
   */
  @Patch('{id}')
  @Middlewares(...middlewares('template', permissions.write_template))
  @ValidateBody(updateTemplateSchema)
  public async updateTemplate(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Path() id: number,
    @Body() templateDto: UpdateTemplateDto,
  ) {
    const template = await req.models.TestTemplate.query()
      .where('projectUid', req.locals.project.uid)
      .whereNull('deletedAt')
      .findById(id);

    if (!template) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, `Template with id ${id} not found`);
    }

    const { templateFields } = templateDto;

    const payload = {
      ...(templateDto.name && { name: templateDto.name }),
      customFields: {
        ...template.customFields,
        templateFields: [...(templateFields || [])],
      },
    };

    const [updatedTemplate] = await req.models.TestTemplate.query().where('uid', id).patch(payload).returning('*');

    return updatedTemplate;
  }

  /**
   * Deletes a template.
   * @param handle The unique identifier for user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param id The unique identifier of the template.
   */
  @Delete('{id}')
  @Middlewares(...middlewares('template', permissions.delete_template))
  public async deleteTemplate(
    @Request() req: Req,
      @Path('handle') handle: string,
      @Path('key') projectKey: string,
      @Path() id: number,
  ): Promise<{ message: string }> {
    const template = await req.models.TestTemplate.query()
      .where('projectUid', req.locals.project.uid)
      .whereNull('deletedAt')
      .findById(id);

    if (!template) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, `Template with id ${id} not found`);
    }

    await req.models.TestTemplate.query()
      .where('projectUid', req.locals.project.uid)
      .findById(id)
      .patch({ deletedAt: req.knexDB.fn.now() });

    return { message: i18n.__('templateDeleted') };
  }
}
