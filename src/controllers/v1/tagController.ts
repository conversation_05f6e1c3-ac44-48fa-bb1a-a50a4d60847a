/* eslint-disable @typescript-eslint/no-unused-vars */
import i18n from 'i18n';
import { auth } from '@ss-libs/ss-component-auth';
import { Request as Req } from 'express';
import {
  setResource, tenantContextExternalApi, loadHandle, bindSharedModels, loadDbComponent,
} from '@app/middlewares/handle';
import { paginated } from '@app/lib/model';
import { permissions } from '@app/constants/auth';
import { ValidateBody, Body } from '@app/middlewares/tsoaValidator';
import {
  Controller,
  Get,
  Middlewares,
  Route,
  Request,
  Path,
  Delete,
  Post,
  Patch,
  Queries,
} from 'tsoa';
import { createTagSchema, updateTagSchema } from '@app/validators/v1/tag';
import { CreateTagDto, UpdateTagDto, TagQueryParams } from '../../types/tag';

const middlewares = (resource: string = 'tag', ...permissions: string[]) => [
  setResource(resource),
  auth().authenticate<PERSON>earer,
  loadHandle(),
  tenantContextExternal<PERSON>pi(),
  bindSharedModels,
  loadDbComponent(),
  auth().authz(...permissions),
];

@Route('v1/{handle}/tags')
export class TagController extends Controller {
  /**
   * Retrieves the list of paginated tags of a user or organization.
   * @param handle The unique identifier for user or an organization.
   * @param queryParams Query parameters for pagination and filtering.
   */
  @Get()
  @Middlewares(...middlewares('tag'))
  public async getTags(
    @Request() req: Req,
      @Path('handle') handle: string,
      @Queries() queryParams: TagQueryParams,
  ): Promise<any> {
    const { limit, offset, entityType } = queryParams;
    const sql = req.models.Tag.query()
      .where({ deletedAt: null, systemType: 'tag' })
      .where((q) => {
        if (entityType) q.whereRaw('"entityTypes" @> ?', [[entityType]]);
      })
      .select('*')
      .toKnexQuery();

    const paginatedResult = await paginated(sql, limit, offset, req.knexDB);

    return paginatedResult;
  }

  /**
 * Retrieves the details of a specific tag for a user or an organization.
 * @param handle The unique identifier for a user or an organization.
 * @param id The unique identifier for the tag.
 */
  @Get('{id}')
  @Middlewares(...middlewares('tag'))
  public async getTag(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path() id: number,
  ) {
    const tag = await req.models.Tag.query()
      .whereNull('deletedAt')
      .where({ systemType: 'tag' })
      .findById(id)
      .select('*');

    return tag;
  }

  /**
 * Creates a new tag for a user or organization.
 * @param handle The unique identifier for a user or an organization.
 * @param tagDto The tag data to be created.
 * @returns The newly created tag object.
 */
  @Post()
  @Middlewares(...middlewares('tag', permissions.write_tag))
  @ValidateBody(createTagSchema)
  public async createTag(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Body() tagDto: CreateTagDto,
  ) {
    const tag = await req.models.Tag.query()
      .insert(tagDto)
      .returning('*');

    await req.fga.create({
      objectType: 'tag',
      objectId: tag.uid,
      relation: 'owner',
      subjectType: req.locals.handle.ownerType,
      subjectId: req.locals.handle.ownerUid,
    });

    return tag;
  }

  @Patch('{id}')
  @Middlewares(...middlewares('tag', permissions.write_tag))
  @ValidateBody(updateTagSchema)
  public async updateTag(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path() id: number,
    @Body() tagDto: UpdateTagDto,
  ) {
    const updatedTagData: Record<string, any> = { ...tagDto };

    if ('archived' in tagDto) {
      updatedTagData.archivedAt = tagDto.archived ? req.knexDB.fn.now() : null;
    }
    delete updatedTagData.archived;

    // Remove undefined properties to avoid patching them
    Object.keys(updatedTagData).forEach((key) => {
      if (updatedTagData[key] === undefined) {
        delete updatedTagData[key];
      }
    });

    const updatedTag = await req.models.Tag.query()
      .findById(id)
      .patch(updatedTagData)
      .returning('*');

    return updatedTag;
  }

  @Delete('{id}')
  @Middlewares(...middlewares('tag', permissions.delete_tag))
  public async deleteTag(
    @Request() req: Req,
      @Path('handle') handle: string,
      @Path() id: number,
  ): Promise<{ message: string }> {
    await req.models.Tag.query()
      .findById(id)
      .patch({ deletedAt: req.knexDB.fn.now() });

    return { message: i18n.__('tagDeleted') };
  }
}
