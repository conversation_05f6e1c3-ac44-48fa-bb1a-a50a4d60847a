/* eslint-disable @typescript-eslint/no-unused-vars */
import Objection from 'objection';
import { auth, FGARawWrite } from '@ss-libs/ss-component-auth';
import _ from 'lodash';
import { ApplicationError } from '@app/lib/http';
import { TestPlan } from '@app/models/testPlan';
import { StatusCodes } from 'http-status-codes';
import { Request as Req } from 'express';
import {
  setResource,
  loadHandle,
  tenantContextExternalApi,
  bindSharedModels,
  loadDbComponent,
  loadProject,
} from '@app/middlewares/handle';
import { permissions } from '@app/constants/auth';
import { ValidateBody, Body } from '@app/middlewares/tsoaValidator';
import errorConstants from '@app/constants/errors';
import {
  Controller,
  Get,
  Middlewares,
  Route,
  Request,
  Path,
  Post,
  Patch,
  Queries,
} from 'tsoa';
import { paginated } from '@app/lib/model';
import { createPlanSchema, updatePlanSchema } from '@app/validators/v1/plan';
import preferencesService from '@app/models/preferences';
import { startWorkflow } from '@app/temporal/client';
import {
  ListPlanDTO as ListPlanQueryParams,
  CreatePlanDTO,
  UpdatePlanDTO,
} from '../../types/plan';
import { UpdatePlanStateDTO } from '../../temporal/activities/plan';

const middlewares = (resource: string = 'plan', ...permissions: string[]) => [
  setResource(resource),
  auth().authenticateBearer,
  loadHandle(),
  tenantContextExternalApi(),
  bindSharedModels,
  loadDbComponent(),
  loadProject,
  auth().authz(...permissions),
];

@Route('v1/{handle}/projects/{key}/plans')
export class PlanController extends Controller {
  /**
   * Retrieves the list of paginated test plans for a particular project of a user or organization.
   * @param handle The unique identifier for user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param queryParams Query parameters for pagination and filtering.
   * @returns The list of paginated test plans.
   */
  @Get()
  @Middlewares(...middlewares('plan', permissions.read_entity))
  public async getPlans(
    @Request() req: Req,
      @Path('handle') handle: string,
      @Path('key') projectKey: string,
      @Queries() queryParams: ListPlanQueryParams,
  ): Promise<any> {
    const {
      limit,
      offset,
      minRunCount,
      minCreatedAt,
      status,
      priority,
      maxCreatedAt,
      maxRunCount,
      archived,
    } = queryParams;
    const sql = req.models.TestPlan.query()
      .where({
        'tags.deletedAt': null,
        systemType: 'plan',
        'tags.projectUid': req.locals.project.uid,
      })
      .toKnexQuery()
      .leftJoin(
        'testRuns',
        req.knexDB.raw(
          'tags.uid="testRuns"."testPlanUid" and "testRuns"."deletedAt" is null',
        ),
      )
      .leftJoin('testMilestonePlans', 'tags.uid', 'testMilestonePlans.planUid')
      .groupBy('tags.uid')
      .select(
        'tags.*',
        req.knexDB.raw(
          'count("testRuns".uid) as "testRunCount",count("testMilestonePlans"."planUid") as "testMilestoneCount"',
        ),
      )
      .orderBy('tags.createdAt', 'asc')
      .where((w) => {
        if (status) w.where('customFields->>\'status\'', status);
        if (priority) w.where('customFields->>\'priority\'', status);

        w.havingRaw('count("testRuns".uid) >= ?', [minRunCount]);
        if (maxRunCount) {
          w.havingRaw('count("testRuns".uid) <= ? ', [maxRunCount]);
        }

        w.where('tags.createdAt', '>=', minCreatedAt);
        if (maxCreatedAt) w.where('tags.createdAt', '<=', maxCreatedAt);

        if (archived) w.whereNotNull('tags.archivedAt');
        else w.whereNull('tags.archivedAt');

        return w;
      });

    const page = await paginated(sql, limit, offset, req.knexDB);
    return page;
  }

  /**
   * Retrieves the details of a specific test plan within a project for a user or organization.
   * @param handle The unique identifier for a user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param id The unique identifier for the test plan.
   */
  @Get('{id}')
  @Middlewares(...middlewares('plan', permissions.read_entity))
  public async getPlan(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Path() id: string,
  ) {
    const plan = await req.models.TestPlan.findOne(
      req.knexDB,
      { uid: id as any, deletedAt: null },
      true,
    );

    if (!plan) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errorConstants.PLAN_NOT_FOUND,
      );
    }

    await req.models.TestPlan.populateTags(req.knexDB, plan);
    for (const r of plan.runs ?? []) {
      await req.models.TestRun.populateConfig(req.knexDB, r);
    }

    return plan;
  }

  /**
   * Creates a new test plan within a project for a user or organization.
   * @param handle The unique identifier for a user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param planDto The plan data to be created.
   * @returns The newly created plan object.
   */
  @Post()
  @Middlewares(...middlewares('plan', permissions.write_entity))
  @ValidateBody(createPlanSchema)
  public async createPlan(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Body() planDto: CreatePlanDTO,
  ) {
    const { defaults } = await preferencesService.getDefaultsAndCompleted(
      req.sharedKnexDB,
      req.locals.handle.ownerType,
      req.locals.handle.ownerUid,
    );

    const trx = await req.knexDB.transaction();

    try {
      const { plan, executions, runs } = await req.models.TestPlan.newPlan(
        {
          ...planDto,
        },
        req.locals.project.uid,
        defaults,
        trx,
      );
      const { ownerType, ownerUid } = req.locals.handle;

      const fgaWrites: FGARawWrite[] = [
        {
          objectType: 'plan',
          objectId: plan.uid,
          subjectType: ownerType,
          subjectId: ownerUid,
          relation: 'owner',
        },
        ...runs.map((r) => ({
          objectType: 'run',
          objectId: r.uid,
          relation: 'owner',
          subjectType: ownerType,
          subjectId: ownerUid,
        })),
        ...executions.map((e) => ({
          objectType: 'execution',
          objectId: e.uid,
          relation: 'owner',
          subjectType: ownerType,
          subjectId: ownerUid,
        })),
      ];
      await req.fga.create(...fgaWrites);
      await trx.commit();

      const param: UpdatePlanStateDTO = {
        planUids: [plan.uid],
        ownerType,
        ownerUid,
      };
      await startWorkflow('updatePlanWorkflow', {
        taskQueue: 'update-plan-queue',
        workflowId: `update.plan.${plan.uid}.${Date.now()}`,
        args: [param],
      });
      return plan;
    } catch (err) {
      await trx.rollback();
      throw err;
    }
  }

  @Patch('{id}')
  @Middlewares(...middlewares('plan', permissions.write_entity))
  @ValidateBody(updatePlanSchema)
  public async updatePlan(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Path() id: number,
    @Body() planDto: UpdatePlanDTO,
  ) {
    const trx = await req.knexDB.transaction();
    const uid = id;
    const {
      milestoneUids, runUids, tagUids, status, priority, ...details
    } = planDto;

    try {
      const plan = await req.models.TestPlan.findOne(trx, { uid }, true);
      if (!plan) {
        throw new ApplicationError(
          StatusCodes.NOT_FOUND,
          errorConstants.PLAN_NOT_FOUND,
        );
      }
      const patch: Partial<Objection.ModelObject<TestPlan>> = {
        name: details.name ?? plan.name,
        description: details.description ?? plan.description ?? '',
        customFields: { ...plan.customFields },
      };
      if (tagUids) patch.customFields.tagUids = tagUids;
      if (status) patch.customFields.status = status;
      if (priority) patch.customFields.priority = priority;

      await plan.$query(trx).patch(patch);

      if (milestoneUids) {
        await req.models.TestPlan.updateMilestones(trx, plan, milestoneUids);
      }
      const { ownerType, ownerUid } = req.locals.handle;

      if (runUids) {
        const existingRuns = plan.runs.map((r) => r.uid);
        const newRuns = _.difference(runUids, existingRuns);
        const deleteRuns = _.difference(existingRuns, runUids);

        if (deleteRuns.length) {
          await req.models.TestPlanRun.deleteByPlanUid(
            trx,
            plan.uid,
            deleteRuns,
          );
        }

        if (newRuns.length) {
          const defaults = await preferencesService.getDefaults(
            req.sharedKnexDB,
            req.locals.handle.ownerType,
            req.locals.handle.ownerUid,
          );
          const runs = _.keyBy(
            await req.models.TestRun.query(trx)
              .whereIn('uid', newRuns)
              .where('deletedAt', null),
            (r) => r.uid,
          );
          const fgaWrites: FGARawWrite[] = [];
          for (const id of newRuns) {
            const r = await req.models.TestRun.duplicate(
              trx,
              {
                name: runs[id].name,
                status: defaults.testRun?.status,
                priority: defaults.testRun?.priority,
                projectUid: req.locals.project.uid,
              },
              {
                planUid: plan.uid,
                sourceRunUid: id,
                execPriority: defaults.testCase?.priority,
                execStatus: defaults.testCase?.status,
              },
            );
            fgaWrites.push(
              {
                objectType: 'run',
                objectId: r.testRun.uid,
                relation: 'owner',
                subjectType: ownerType,
                subjectId: ownerUid,
              },
              ...r.executions.map((e) => ({
                objectType: 'execution',
                objectId: e.uid,
                relation: 'owner',
                subjectType: ownerType,
                subjectId: ownerUid,
              })),
            );
          }
          if (fgaWrites.length > 0) await req.fga.create(...fgaWrites);
        }
      }
      const param: UpdatePlanStateDTO = {
        planUids: [plan.uid],
        ownerType,
        ownerUid,
      };
      await startWorkflow('updatePlanWorkflow', {
        taskQueue: 'update-plan-queue',
        workflowId: `update.plan.${plan.uid}.${Date.now()}`,
        args: [param],
      });

      await trx.commit();
      return plan;
    } catch (err) {
      await trx.rollback();
      throw err;
    }
  }
}
