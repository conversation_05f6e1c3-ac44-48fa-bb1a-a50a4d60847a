/* eslint-disable @typescript-eslint/no-unused-vars */
import i18n from 'i18n';
import { ApplicationError } from '@app/lib/http';
import { StatusCodes } from 'http-status-codes';
import { UniqueViolationError } from 'objection';
import { auth } from '@ss-libs/ss-component-auth';
import _ from 'lodash';
import { Request as Req } from 'express';
import { ValidateBody, Body } from '@app/middlewares/tsoaValidator';
import {
  setResource, tenantContextExternalApi, loadHandle, bindSharedModels, loadDbComponent, loadProject,
} from '@app/middlewares/handle';
import { paginated } from '@app/lib/model';
import {
  Controller,
  Get,
  Middlewares,
  Route,
  Request,
  Path,
  Delete,
  Post,
  Patch,
  Queries,
} from 'tsoa';
import { permissions } from '@app/constants/auth';
import errors from '@app/constants/errors';
import {
  createFolderSchema,
  updateFolderSchema,
} from '@app/validators/v1/folder';
import { PaginatedQuery } from '../../lib/model';
import { CreateFolderDto, UpdateFolderDto } from '../../types/folder';

const middlewares = (resource: string = 'folder', ...permissions: string[]) => [
  setResource(resource),
  auth().authenticateBearer,
  loadHandle(),
  tenantContextExternalApi(),
  bindSharedModels,
  loadDbComponent(),
  loadProject,
  auth().authz(...permissions),
];

@Route('v1/{handle}/projects/{key}/folders')
export class FolderController extends Controller {
  /**
   * Retrieves the list of paginated folders for a particular project of a user or organization.
   * @param handle The unique identifier for user or an organization
   * @param projectKey The unique identifier for a project
   * @param queryParams The query parameters for pagination and filtering
   */
  @Get()
  @Middlewares(...middlewares('folder', permissions.read_entity))
  public async getFolders(
    @Request() req: Req,
      @Path('handle') handle: string,
      @Path('key') projectKey: string,
      @Queries() queryParams: PaginatedQuery,
  ): Promise<any> {
    const { limit, offset } = queryParams;
    const projectId = req.locals.project.uid;
    const foldersQuery = req.models.Tag.query()
      .where('systemType', 'folder')
      .where('projectUid', projectId)
      .whereNull('deletedAt')
      .orderBy('name', 'ASC')
      .select('*');
    const paginatedResult = await paginated(
      foldersQuery.toKnexQuery(),
      limit,
      offset,
      req.knexDB,
    );

    return paginatedResult;
  }

  /**
 * Retrieves the details of a specific folder within a project for a user or organization.
 * @param handle The unique identifier for a user or an organization
 * @param projectKey The unique identifier for a project
 * @param id The unique identifier for the folder
 */
  @Get('{id}')
  @Middlewares(...middlewares('folder', permissions.read_activity))
  public async getFolder(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Path() id: number,
  ) {
    const folder = await req.models.Tag.query()
      .where({
        systemType: 'folder',
        uid: id,
        deletedAt: null,
      })
      .first();

    if (!folder) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errors.FOLDER_NOT_FOUND,
      );
    }

    return folder;
  }

  /**
 * Creates a new folder within a project for a user or organization.
 * @param handle The unique identifier for a user or an organization.
 * @param projectKey The unique identifier for a project.
 * @param folderDto The folder data to be created.
 * @returns The newly created folder object.
 */
  @Post('/')
  @Middlewares(...middlewares('folder', permissions.write_entity))
  @ValidateBody(createFolderSchema)
  public async createFolder(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Body() folderDto: CreateFolderDto,
  ) {
    const trx = await req.knexDB.transaction();

    try {
      const projectId = req.locals.project.uid;

      const folder = await req.models.Folder.create(trx, folderDto, projectId);

      const owner = req.locals.handle.ownerUid;
      const { ownerType } = req.locals.handle;

      await req.fga.create({
        objectType: 'folder',
        objectId: folder.uid,
        relation: 'owner',
        subjectType: ownerType,
        subjectId: owner,
      });

      await trx.commit();

      return folder;
    } catch (error) {
      await trx.rollback();
      if (error instanceof UniqueViolationError) {
        throw new ApplicationError(StatusCodes.CONFLICT, errors.FOLDER_EXISTS);
      }
      throw error;
    }
  }

  /**
   * Updates an existing folder within a project for a user or organization.
   * @param handle The unique identifier for a user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param id The unique identifier for the folder.
   * @param folderDto The folder data to be updated.
   * @returns The updated folder object.
   */
  @Patch('{id}')
  @Middlewares(...middlewares('folder', permissions.write_entity))
  @ValidateBody(updateFolderSchema)
  public async updateFolder(
  @Request() req: Req,
    @Path('handle') handle: string,
    @Path('key') projectKey: string,
    @Path() id: number,
    @Body() folderDto: UpdateFolderDto,
  ) {
    const trx = await req.knexDB.transaction();
    try {
      const folder = await req.models.Folder.update(trx, id, folderDto);
      await trx.commit();
      return folder;
    } catch (err) {
      await trx.rollback();
      throw err;
    }
  }

  /**
   * Deletes an existing folder within a project for a user or organization.
   * @param handle The unique identifier for a user or an organization.
   * @param projectKey The unique identifier for a project.
   * @param id The unique identifier for the folder.
   * @returns A success message.
   */
  @Delete('{id}')
  @Middlewares(...middlewares('folder', permissions.delete_entity))
  public async deleteFolder(
    @Request() req: Req,
      @Path('handle') handle: string,
      @Path('key') projectKey: string,
      @Path() id: number,
  ): Promise<{ message: string }> {
    await req.knexDB.transaction(async (trx) => {
      const folder = await req.models.Tag.query(trx).findById(id);
      if (!folder || folder.systemType !== 'folder' || folder.deletedAt) return;

      if (!folder.parentUid) {
        throw new ApplicationError(
          StatusCodes.METHOD_NOT_ALLOWED,
          errors.CANNOT_DELETE_ROOT_FOLDER,
        );
      }

      await req.models.Tag.query(trx).patchAndFetchById(id, {
        deletedAt: req.knexDB.fn.now(),
        slug: trx.raw('null'),
      });
    });

    return { message: i18n.__('folderDeleted') };
  }
}
