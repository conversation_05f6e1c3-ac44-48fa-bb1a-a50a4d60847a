import {
  AVAILBLE_INTEGRATIONS,
  GITHUB_SCOPES,
  INTEGRATION_DESCRIPTIONS,
  JIRA_SCOPES,
  SERVICE_CONFIGS,
} from '@app/constants/integration';
import { ApplicationError, httpHandler } from '@app/lib/http';
import { Request, Response } from 'express';
import { IntegrationServiceFactory } from '@app/integrations/index';
import { StatusCodes } from 'http-status-codes';
import _ from 'lodash';
import axios from 'axios';
import dayjs from 'dayjs';
import env from '@app/config/env';
import errors from '@app/constants/errors';
import logger from '@app/config/logger';
import { setupDB } from '@app/config/db';
import {
  getCreateMeta, getJiraBoards, getJiraSprints, getJiraUsers, getProjectAssignableUsers, IssueTypes,
} from '@app/utils/jiraHelper';
import { Attachment } from '@app/models/attachment';
import { IntegrationToken } from '@app/models/integrationToken';
import { decipherDecrypt } from '@app/utils/decryptData';
import { DecryptionService } from '@ss-libs/ss-component-encryption';
import { KEY_MANAGER } from '@app/config/keyManagerLoader';
import { terminateWorkflow } from '@app/temporal/client';
import { transformError } from '@app/utils/transformIntegrationError';
import { AttachmentData } from '../temporal/activities/attachment';
import { startWorkflow } from '../temporal/client';
import { sharedModels, tenantModels } from '../models';

const getAvailableIntegrations = async () => {
  try {
    return AVAILBLE_INTEGRATIONS;
  } catch (error) {
    logger.error('Error fetching available integrations:', error);
    throw new ApplicationError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      errors.INTERNAL_SERVER_ERROR,
    );
  }
};

// Method to get projects from external service
const integrationServiceProjects = async (req: Request) => {
  const { integrationUid } = req.params;
  const integration = await req.models.Integration.query().findById(integrationUid);
  if (!integration) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errors.INTEGRATION_NOT_FOUND,
    );
  }

  // check if integration is deleted
  if (integration.deletedAt) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_DELETED,
    );
  }

  if (integration.status === 'inactive') {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_AUTHENTICATION_REQUIRED,
      'Please re-authenticate the integration to continue',
    );
  }

  const service = IntegrationServiceFactory.getService(integration.service);
  const integrationToken = await req.models.IntegrationToken.query()
    .where('integrationUid', integrationUid)
    .where('ownerUid', req.locals.handle.ownerUid)
    .first();

  const resourceId = req.query.resourceId as string;
  const header = await service.prepareAuthHeader(integrationToken, req.knexDB);
  const { url } = integrationToken;
  if (!header) {
    await integration.$query().patch({ status: 'inactive' });
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_AUTHENTICATION_REQUIRED,
      'Please re-authenticate the integration to continue',
    );
  }
  const response = await service.getProjects({ resourceId, url }, header);
  if (!response.success) {
    // check status code if it is 401 then set integration to inactive
    if (Number(response.status) === 401) {
      await integration.$query().patch({ status: 'inactive' });
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        errors.INTEGRATION_AUTHENTICATION_REQUIRED,
        'Please re-authenticate the integration to continue',
      );
    }
    const externalErrors = [...(integration.externalErrors || []), {
      message: response.error,
      statusCode: response.status,
      timestamp: new Date().toISOString(),
      resolved: false,
    }];
    await req.models.Integration.query().where('uid', integrationUid).patch({
      status: 'error',
      externalErrors,
    });
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      response.error,
    );
  }
  return response.data;
};

// Special case of fetching orgs first then use orgid to fetch projects
const getOrganizations = async (req: Request) => {
  const { integrationUid } = req.params;
  const integration = await req.models.Integration.query().findById(integrationUid);
  if (!integration) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errors.INTEGRATION_NOT_FOUND,
    );
  }
  // check if integration is deleted
  if (integration.deletedAt) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_DELETED,
    );
  }

  if (integration.status === 'inactive') {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_AUTHENTICATION_REQUIRED,
      'Please re-authenticate the integration to continue',
    );
  }
  const integrationToken = await req.models.IntegrationToken.query()
    .where('integrationUid', integrationUid)
    .where('ownerUid', req.locals.handle.ownerUid)
    .first();
  if (!integrationToken) {
    await integration.$query().patch({ status: 'inactive' });
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errors.AUTH_TOKEN_NOT_FOUND,
      'Please re-authenticate the integration to continue',
    );
  }
  const service = IntegrationServiceFactory.getService(integration.service);
  const header = await service.prepareAuthHeader(integrationToken, req.knexDB);

  if (!header) {
    await integration.$query().patch({ status: 'inactive' });
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_AUTHENTICATION_REQUIRED,
      'Please re-authenticate the integration to continue',
    );
  }

  try {
    const resourceResponse = await axios.get(
      'https://api.atlassian.com/oauth/token/accessible-resources',
      header,
    );

    const resources = resourceResponse.data;
    const data = [];
    for (const resource of resources) {
      data.push({
        resourceId: resource.id,
        resourceName: resource.name,
      });
    }
    return data;
  } catch (error) {
    logger.error('Error fetching Jira resources:', error);
    if (error.response?.data?.status === 401) {
      await integration.$query().patch({ status: 'inactive' });
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        errors.INTEGRATION_AUTHENTICATION_REQUIRED,
        'Please re-authenticate the integration to continue',
      );
    }
    const errorString = transformError(error);
    const externalErrors = [...(integration.externalErrors || []), {
      message: errorString,
      statusCode: error.response?.data?.status,
      timestamp: new Date().toISOString(),
      resolved: false,
    }];
    await req.models.Integration.query().where('uid', integrationUid).patch({
      status: 'error',
      externalErrors,
    });
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errorString,
    );
  }
};

/* url example
http://localhost:5050/v1/org1/integrations??service=jira,github&projectUid=1,2,3
*/
const getIntegrations = async (req: Request) => {
  try {
    const {
      service: serviceQuery,
      projectUid: projectUidQuery,
      projectKey: projectKeyQuery,
      page = 1,
      limit = 10,
    } = req.query;

    // Parse query parameters
    const servicesFilter = typeof serviceQuery === 'string' ? serviceQuery.split(',') : null;
    const projectUidsFilter = typeof projectUidQuery === 'string'
      ? projectUidQuery
        .split(',')
        .map((id) => parseInt(id, 10))
        .filter((id) => !_.isNaN(id))
      : null;
    const projectKeysFilter = typeof projectKeyQuery === 'string'
      ? projectKeyQuery.split(',').map((key) => key.toUpperCase())
      : null;

    // Get total count of all integrations for this user
    const totalIntegrationsCount = await req.models.Integration.query()
      .whereNull('archivedAt')
      .count('uid as count')
      .first() as unknown as { count: number };

    // Build the base query with filters
    const baseQuery = req.models.Integration.query().whereNull('archivedAt');

    if (servicesFilter) {
      baseQuery.whereIn('service', servicesFilter);
    }

    if (projectUidsFilter && projectUidsFilter.length > 0) {
      baseQuery.whereRaw('?? && ?::int[]', ['projectUids', projectUidsFilter]);
    }

    if (projectKeysFilter && projectKeysFilter.length > 0) {
      // First get the project UIDs for the given keys
      const projectsByKeys = await req.models.Project.query()
        .select('uid')
        .whereIn('key', projectKeysFilter)
        .whereNull('deletedAt');

      const projectUidsFromKeys = projectsByKeys.map((p) => p.uid);

      if (projectUidsFromKeys.length > 0) {
        // If we already have a projectUids filter, intersect with it
        if (projectUidsFilter && projectUidsFilter.length > 0) {
          const combinedUids = _.intersection(projectUidsFilter, projectUidsFromKeys);
          baseQuery.whereRaw('?? && ?::int[]', ['projectUids', combinedUids]);
        } else {
          // Otherwise just filter by the project keys' UIDs
          baseQuery.whereRaw('?? && ?::int[]', ['projectUids', projectUidsFromKeys]);
        }
      } else {
        // If no projects found with given keys, return no results
        baseQuery.where('uid', -1); // This ensures no results
      }
    }

    // Get filtered count
    const filteredCount = await baseQuery.clone().count('uid as count').first() as unknown as { count: number };

    // Get paginated results
    const offset = (Number(page) - 1) * Number(limit);
    const filteredIntegrations = await baseQuery
      .clone()
      .offset(offset)
      .limit(Number(limit));

    // Rest of the queries remain the same...
    const uniqueServices = await req.models.Integration.query()
      .whereNull('archivedAt')
      .distinct('service')
      .then((results) => results.map((r) => r.service));

    const uniqueProjectUidsPromise = req.knexDB
      .raw(
        `
        SELECT DISTINCT unnest("projectUids") AS projectuid 
        FROM integrations 
        WHERE "archivedAt" IS NULL
      `,
      )
      .then((result: any) => result.rows.map((row: any) => row.projectuid));
    const uniqueProjectUids = await uniqueProjectUidsPromise;

    const projectsData = await req.models.Project.query()
      .select('uid', 'name', 'key', 'avatarAttachmentUid')
      .whereIn('uid', uniqueProjectUids);

    const baseURL = `${env.BACKEND_URL}${env.API_INTERNAL_ROUTE}/${req.params.handle}`;

    const projectsMapping = projectsData.reduce((acc, project) => {
      acc[project.uid] = {
        name: project.name,
        key: project.key,
        logoUrl: project.avatarAttachmentUid
          ? `${baseURL}/projects/attachments/${project.avatarAttachmentUid}/object`
          : null,
      };
      return acc;
    }, {});
    // Transform the results to include picUrl
    const transformedIntegrations = filteredIntegrations.map((integration) => ({
      uid: integration.uid,
      name: integration.name,
      service: integration.service,
      description: integration.description,
      projectUids: integration.projectUids || [],
      configuration: integration.configuration,
      status: integration.status,
      type: integration.type,
      syncedAt: integration.syncedAt,
      externalErrors: integration.externalErrors || [],
      projectLogos: (integration.projectUids || []).map((projectUid) => {
        const project = projectsMapping[projectUid];
        return project ? project.logoUrl : null;
      }),
      avatarAttachmentUid: integration.avatarAttachmentUid,
      updatedAt: integration.updatedAt,
      picUrl: integration.avatarAttachmentUid
        ? `${baseURL}/integrations/attachments/${integration.avatarAttachmentUid}/object`
        : null,
    }));

    const filteredTotal = Number(filteredCount?.count || 0);

    return {
      services: uniqueServices,
      projects: projectsMapping,
      pagination: {
        total: filteredTotal,
        totalIntegrations: Number(totalIntegrationsCount?.count || 0),
        page: Number(page),
        limit: Number(limit),
        totalPages: Math.ceil(filteredTotal / Number(limit)),
      },
      integrations: transformedIntegrations,
    };
  } catch (error) {
    logger.error('Error fetching integrations:', error);
    throw new ApplicationError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      errors.INTERNAL_SERVER_ERROR,
    );
  }
};

const getIntegration = async (req: Request) => {
  try {
    const { integrationUid } = req.params;
    const integration = await req.models.Integration.query()
      .where('uid', integrationUid)
      .where('archivedAt', null)
      .first();
    if (!integration) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errors.INTEGRATION_NOT_FOUND,
      );
    }
    return {
      integration,
    };
  } catch (error) {
    logger.error('Error fetching integration:', error);
    throw new ApplicationError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      errors.INTERNAL_SERVER_ERROR,
    );
  }
};

const createIntegration = async (req: Request) => {
  const {
    name,
    description,
    username,
    organizationUrl,
    accessToken,
    service,
    symmetricKeyData: symmetricKeyObj,
  } = req.body;

  const serviceInstance = IntegrationServiceFactory.getService(service);
  const keyManager = KEY_MANAGER;
  let token = '';

  try {
    // Decrypt symmetric key and access token
    const decrypter = new DecryptionService({ keyManager });
    const decryptedSymmetricKey = decrypter.decrypt('integration', {
      data: symmetricKeyObj.key,
      keyVersion: symmetricKeyObj.keyVersion,
    });

    const decryptedAccessToken = decipherDecrypt(
      accessToken,
      decryptedSymmetricKey,
      symmetricKeyObj.fieldMetadata.accessToken.tag,
      symmetricKeyObj.fieldMetadata.accessToken.iv,
    );

    token = Buffer.from(`${username}:${decryptedAccessToken}`).toString('base64');

    const authHeader = {
      headers: {
        Authorization: `Basic ${token}`,
        'Content-Type': 'application/json',
      },
    };

    // Fetch user data
    const userData = await serviceInstance.fetchUserData(authHeader, organizationUrl);
    if (!userData.success) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, userData.error);
    }

    const { entityTypes } = SERVICE_CONFIGS[service];
    // create access token
    const integration = await req.models.Integration.query().insert({
      name,
      description,
      service,
      type: 'basic',
      entityTypes: req.knexDB.raw('?::text[]', [
        entityTypes]),
      configuration: {
        url: organizationUrl,
        username,
        syncDuration: '3m', // Default sync duration for new integrations
      },
    });
    if (name === '') {
      await integration.$query().patch({ name: `${integration.service}-${integration.uid}` });
    }

    const creatorUid = (req as any).session?.user?.uid;
    const tokenBody = {
      accessToken: token,
      refreshToken: '',
      url: organizationUrl,
      creatorUid,
      ownerUid: req.locals.handle.ownerUid,
      retrieved: true,
      integrationUid: integration.uid,
    };

    const { encryptedIntegrationToken, symmetricKeyData } = await IntegrationToken.getEncryptedIntegrationToken(req.sharedKnexDB, tokenBody);

    tokenBody.accessToken = encryptedIntegrationToken.accessToken;
    tokenBody.refreshToken = encryptedIntegrationToken.refreshToken;

    const integrationToken = await req.models.IntegrationToken.query().insert(tokenBody);
    symmetricKeyData.entityId = integrationToken.uid;

    await keyManager.createSymmeticKey(req.knexDB, symmetricKeyData);

    await startWorkflow('fetchUserWorkFlow', {
      taskQueue: 'integration-queue',
      workflowId: `${req.locals.handle.name}:integration:user:${Date.now()}`,
      args: [req.locals.handle.ownerUid, integrationToken.uid],
    });

    await startWorkflow('integrationSyncWorkflow', {
      taskQueue: 'integration-sync-queue',
      workflowId: `${req.locals.handle.name}:integration:sync:${integration.service}:${integration.uid}`,
      args: [req.locals.handle.ownerUid, integration.uid],
      cronSchedule: SERVICE_CONFIGS[integration.service].cronSchedule,
    });

    return integration;
  } catch (error) {
    logger.error('Error creating integration:', error);
    if (error instanceof ApplicationError) {
      throw error;
    } else {
      throw new ApplicationError(
        StatusCodes.INTERNAL_SERVER_ERROR,
        errors.INTERNAL_SERVER_ERROR,
      );
    }
  }
};

const updateIntegration = async (req: Request) => {
  const { integrationUid } = req.params;
  const integration = await req.models.Integration.query().findById(integrationUid);
  if (!integration) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errors.INTEGRATION_NOT_FOUND,
    );
  }

  // check if integration is deleted
  if (integration.deletedAt) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_DELETED,
    );
  }

  if (integration.status === 'inactive') {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_AUTHENTICATION_REQUIRED,
      'Please re-authenticate the integration to continue',
    );
  }
  const { entityTypes } = SERVICE_CONFIGS[integration.service];
  try {
    const integrationBody: any = {};
    integrationBody.name = req.body.name;
    integrationBody.description = req.body.description;
    integrationBody.entityTypes = req.knexDB.raw('?::text[]', [entityTypes]);
    integrationBody.configuration = {
      ...req.body.configuration,
      url: integration.configuration.url,
      username: integration.configuration.username,
      syncDuration: integration.configuration.syncDuration,
    };
    if (req.body.projectUids) {
      integrationBody.projectUids = req.knexDB.raw('?::integer[]', [
        req.body.projectUids.map((id) => parseInt(id, 10)),
      ]);
    }
    // use current time if syncedAt is not provided
    integrationBody.syncedAt = dayjs().toISOString();
    await integration.$query().patch(integrationBody);
    try {
      await startWorkflow('integrationUpdateWorkflow', {
        taskQueue: 'integration-queue',
        workflowId: `${req.locals.handle.name}:integration:update:${integration.service}:${integration.uid}:${Date.now()}`,
        args: [req.locals.handle.ownerUid, integration.uid.toString()],
      });
    } catch (error) {
      logger.error(
        `Error updating defects for integrationUid: ${integration.uid}`,
        error,
      );
    }
    return integration;
  } catch (error) {
    logger.error('Error updating integration:', error);
    throw new ApplicationError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      errors.INTERNAL_SERVER_ERROR,
    );
  }
};

const deleteIntegration = async (req: Request) => {
  if (req.locals.handle.ownerType !== 'org') {
    throw new ApplicationError(StatusCodes.FORBIDDEN, errors.FORBIDDEN);
  }
  const integrationUid = parseInt(req.params.integrationUid);
  const integration = await req.models.Integration.query()
    .where('uid', integrationUid)
    .first();

  if (!integration) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errors.INTEGRATION_NOT_FOUND,
    );
  }

  try {
    // fetch oauth token and delete it
    await req.models.IntegrationToken.query()
      .where('integrationUid', integrationUid)
      .delete();
    // check if operation is delete
    const { operation } = req.query;
    if (operation === 'delete') {
      await integration.$query().delete();
      return {
        message: 'Integration deleted successfully',
      };
    }
    // Archive the integration instead of deleting it
    await integration.$query().patch({
      archivedAt: req.knexDB.fn.now(),
    });
    await startWorkflow('integrationDeleteWorkflow', {
      taskQueue: 'integration-queue',
      workflowId: `${req.locals.handle.name}:integration:delete:${integration.uid}:${Date.now()}`,
      args: [req.locals.handle.ownerUid, integration.uid.toString(), false],
    });

    // terminate the sync workflow to avoid any data fetching after deletion
    await terminateWorkflow('integration-sync-queue', `${req.locals.handle.name}:integration:sync:${integration.service}:${integration.uid}`);
    return {
      message: 'Integration archived successfully',
    };
  } catch (error) {
    logger.error('Error archiving integration:', error);
    throw new ApplicationError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      errors.INTERNAL_SERVER_ERROR,
    );
  }
};

const reauthenticateToken = async (req: Request) => {
  const { integrationUid } = req.params;
  const { token, username, symmetricKeyData: symmetricKeyObj } = req.body;
  const keyManager = KEY_MANAGER;
  let decryptedToken = '';
  try {
    // Decrypt symmetric key and access token
    const decrypter = new DecryptionService({ keyManager });
    const decryptedSymmetricKey = decrypter.decrypt('integration', {
      data: symmetricKeyObj.key,
      keyVersion: symmetricKeyObj.keyVersion,
    });
    const decryptedAccessToken = decipherDecrypt(
      token,
      decryptedSymmetricKey,
      symmetricKeyObj.fieldMetadata.accessToken.tag,
      symmetricKeyObj.fieldMetadata.accessToken.iv,
    );
    decryptedToken = decryptedAccessToken;
  } catch (error) {
    logger.error('Error decrypting token:', error);
    throw new ApplicationError(StatusCodes.INTERNAL_SERVER_ERROR, errors.INTERNAL_SERVER_ERROR);
  }
  // Find the integration
  const integration = await req.models.Integration.query().findById(integrationUid);
  if (!integration) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errors.INTEGRATION_NOT_FOUND);
  }

  // Check if integration is deleted
  if (integration.deletedAt) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_DELETED,
    );
  }

  try {
    const serviceInstance = IntegrationServiceFactory.getService(integration.service);
    let userEmail = integration.configuration.username;
    if (!userEmail) {
      userEmail = username;
    }
    // For basic auth type integrations
    if (integration.type === 'basic') {
      const authToken = Buffer.from(`${userEmail}:${decryptedToken}`).toString('base64');
      const authHeader = {
        headers: {
          Authorization: `Basic ${authToken}`,
          'Content-Type': 'application/json',
        },
      };

      // Verify credentials by fetching user data
      const userData = await serviceInstance.fetchUserData(
        authHeader,
        integration.configuration.url,
      );

      if (!userData.success) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          errors.INVALID_CREDENTIALS,
        );
      }

      // Find and update existing token or create new one
      const existingToken = await req.models.IntegrationToken.query()
        .where('integrationUid', integrationUid)
        .first();

      if (existingToken) {
        // Update integration token
        const tokenBody = {
          accessToken: decryptedToken,
          refreshToken: '',
          url: existingToken.url,
          ownerUid: existingToken.ownerUid,
          creatorUid: existingToken.creatorUid,
          integrationUid: existingToken.integrationUid,
          retrieved: true,
        };
        const { encryptedIntegrationToken, symmetricKeyData } = await IntegrationToken.getEncryptedIntegrationToken(req.sharedKnexDB, tokenBody);
        tokenBody.accessToken = encryptedIntegrationToken.accessToken;
        symmetricKeyData.entityId = existingToken.uid;
        await keyManager.createSymmeticKey(req.knexDB, symmetricKeyData);
        await existingToken.$query().patch(tokenBody);
      } else {
        throw new ApplicationError(StatusCodes.BAD_REQUEST, errors.INVALID_CREDENTIALS);
      }

      // Update integration status
      await integration.$query().patch({
        status: 'active',
        configuration: {
          ...integration.configuration,
          username: userEmail,
        },
      });
      // restart the sync workflow
      try {
        await startWorkflow('integrationSyncWorkflow', {
          taskQueue: 'integration-sync-queue',
          workflowId: `${req.locals.handle.name}:integration:sync:${integration.service}:${integration.uid}`,
          args: [req.locals.handle.ownerUid, integration.uid],
          cronSchedule: SERVICE_CONFIGS[integration.service].cronSchedule,
        });
      } catch (error) {
        logger.error(`Error starting workflow for integration: ${integration.uid}`);
      }
      return { message: 'Integration reauthenticated successfully' };
    }
  } catch (error) {
    logger.error('Error reauthenticating integration:', error);
    throw new ApplicationError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      errors.INTERNAL_SERVER_ERROR,
    );
  }
};

// Method to redirect to Jira's authorization page
const jiraRedirect = async (req: Request) => {
  try {
    const { handle } = req.params;
    const JIRA_CLIENT_ID = env.OAUTH_JIRA_CLIENT_ID;
    const JIRA_REDIRECT_URI = `${env.BACKEND_URL}${env.API_INTERNAL_ROUTE}/app/oauth`;
    const integrationUid = req.query.integrationUid || '';
    const state = `https://auth.atlassian.com;global;${handle};${integrationUid}`;

    // Construct the authorization URL
    const params = new URLSearchParams({
      audience: 'api.atlassian.com',
      client_id: JIRA_CLIENT_ID!,
      scope: JIRA_SCOPES.join(' '),
      redirect_uri: JIRA_REDIRECT_URI!,
      state,
      response_type: 'code',
      prompt: 'consent',
    });

    const authorizationUrl = `https://auth.atlassian.com/authorize?${params.toString()}`;

    // redirect to the authorization url
    return authorizationUrl;
  } catch (error) {
    logger.error('Error in jiraAuth:', error);
    throw new ApplicationError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      errors.INTERNAL_SERVER_ERROR,
    );
  }
};

const githubRedirect = async (req: Request) => {
  try {
    const { handle } = req.params;
    const GITHUB_CLIENT_ID = env.OAUTH_GITHUB_CLIENT_ID;
    const GITHUB_REDIRECT_URI = `${env.BACKEND_URL}${env.API_INTERNAL_ROUTE}/app/oauth`;
    const integrationUid = req.query.integrationUid || '';
    const state = `https://github.com;global;${handle};${integrationUid}`;

    // Construct the authorization URL
    const params = new URLSearchParams({
      client_id: GITHUB_CLIENT_ID!,
      redirect_uri: GITHUB_REDIRECT_URI,
      scope: GITHUB_SCOPES.join(' '),
      state,
      prompt: 'consent',
    });
    const authorizationUrl = `https://github.com/login/oauth/authorize?${params.toString()}`;
    return authorizationUrl;
  } catch (error) {
    logger.error('Error in githubAuth:', error);
    throw new ApplicationError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      errors.INTERNAL_SERVER_ERROR,
    );
  }
};
const personalTokenRedirect = async (req: Request) => {
  const { service, handle, integrationUid } = req.params;
  try {
    const url = service === 'jira' ? 'https://auth.atlassian.com' : 'https://github.com';
    const CLIENT_ID = env[`OAUTH_${service.toUpperCase()}_CLIENT_ID`];
    const REDIRECT_URI = `${env.BACKEND_URL}${env.API_INTERNAL_ROUTE}/app/oauth`;
    if (!integrationUid) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Integration UID is required');
    }
    const state = `${url};personal;${handle};${integrationUid}`;
    const params = new URLSearchParams({
      audience: service === 'jira' ? 'api.atlassian.com' : undefined,
      client_id: CLIENT_ID!,
      scope: service === 'jira' ? JIRA_SCOPES.join(' ') : GITHUB_SCOPES.join(' '),
      redirect_uri: REDIRECT_URI!,
      state,
      response_type: 'code',
      prompt: 'consent',
    });

    const authorizationUrl = service === 'jira' ? `${url}/authorize?${params.toString()}` : `${url}/login/oauth/authorize?${params.toString()}`;
    return authorizationUrl;
  } catch (error) {
    logger.error('Error in oauthRedirect:', error);
    throw new ApplicationError(StatusCodes.INTERNAL_SERVER_ERROR, errors.INTERNAL_SERVER_ERROR);
  }
};

const handleOAuthResponse = async (req: Request, res: Response) => {
  // Parse state and initialize
  const state = (req.query.state as string).split(';');
  const oauthBaseURL = state[0];
  const scope = state[1];
  try {
    // Determine service type from URL
    // eslint-disable-next-line no-nested-ternary
    const serviceType = oauthBaseURL.includes('github.com') ? 'github'
      : oauthBaseURL.includes('atlassian.com') ? 'jira'
        : null;

    if (!serviceType) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Invalid service type');
    }

    // Get service specific configs
    const clientId = env[`OAUTH_${serviceType.toUpperCase()}_CLIENT_ID`];
    const clientSecret = env[`OAUTH_${serviceType.toUpperCase()}_CLIENT_SECRET`];
    const redirectUri = `${env.BACKEND_URL}${env.API_INTERNAL_ROUTE}/app/oauth/`;

    // Get service instance to handle OAuth response
    const serviceInstance = IntegrationServiceFactory.getService(serviceType);
    const oauthResponse = await serviceInstance.handleOAuthResponse({
      oauthBaseURL,
      code: req.query.code as string,
      state: req.query.state as string,
      clientId,
      clientSecret,
      redirectUri,
    });

    // Based on the service parameter, determine which flow to follow
    if (scope === 'global') {
      return await handleGlobalOAuth(req, res, serviceType, oauthResponse, state[2], parseInt(state[3], 10));
    } if (scope === 'personal') {
      return await handlePersonalTokenOAuth(req, res, serviceType, oauthResponse, state[2], parseInt(state[3], 10));
    }
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Invalid service identifier');
  } catch (error) {
    logger.error('Error in OAuth response:', error);
    // If we have a handle, redirect to that handle's integrations page
    if (scope === 'global' || scope === 'personal') {
      return res.redirect(
        `${env.FRONTEND_URL}/${state[2]}/integrations/?error=internalServerError`,
      );
    }
    // For Pinata or unknown services, return the error html
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).send(
      '<html><body><h1>Error</h1><p>Internal Server Error</p></body></html>',
    );
  }
};

const handlePersonalTokenOAuth = async (
  req: Request,
  res: Response,
  serviceType: string,
  oauthResponse: any,
  handle: string,
  integrationUid: number,
) => {
  try {
    // Setup DBs and models
    const sharedDb = setupDB();
    const handleObj = await sharedModels.Handle.bindKnex(sharedDb)
      .query()
      .where('name', handle)
      .first();

    if (!handleObj || handleObj.ownerType !== 'org') {
      const error = !handleObj ? 'handle_not_found' : 'only_orgs_can_integrate';
      return res.redirect(`${env.FRONTEND_URL}/${handle}/integrations/?error=${error}`);
    }

    const tenantUid = handleObj.ownerUid;
    const tenantDb = setupDB(tenantUid);
    const models = {
      IntegrationToken: tenantModels.IntegrationToken.bindKnex(tenantDb),
      Integration: tenantModels.Integration.bindKnex(tenantDb),
    };
    const integration = await models.Integration.query()
      .where('uid', integrationUid)
      .first();
    if (!integration) {
      return res.redirect(`${env.FRONTEND_URL}/${handle}/integrations/?error=integration_not_found`);
    }
    const creatorUid = (req as any).session?.user?.uid;
    const integrationToken = {
      accessToken: oauthResponse.accessToken,
      refreshToken: oauthResponse.refreshToken,
      url: oauthResponse.url,
      creatorUid,
      ownerUid: creatorUid,
      expiresAt: dayjs().add(oauthResponse.expiresIn, 'second').toISOString(),
      retrieved: true,
    };

    const result = await handleExistingIntegration(models, creatorUid, integrationUid, integrationToken, handle, sharedDb, tenantDb, tenantUid, handleObj, false);

    // Queue user fetching
    try {
      await startWorkflow('fetchUserWorkFlow', {
        taskQueue: 'integration-queue',
        workflowId: `${handleObj.name}:integration:user:${Date.now()}`,
        args: [tenantUid, result.tokenUid],
      });
    } catch (error) {
      logger.error(`Error while queuing user fetch for ${serviceType}:`, error);
    }

    return res.redirect(result.redirectUrl);
  } catch (error) {
    logger.error('Error in Personal Token OAuth:', error);
    return res.redirect(
      `${env.FRONTEND_URL}/${handle}/integrations/?error=internalServerError`,
    );
  }
};

const handleGlobalOAuth = async (
  req: Request,
  res: Response,
  serviceType: string,
  oauthResponse: any,
  handle: string,
  integrationUid: number,
) => {
  try {
    // Setup DBs and models
    const sharedDb = setupDB();
    const handleObj = await sharedModels.Handle.bindKnex(sharedDb)
      .query()
      .where('name', handle)
      .first();

    if (!handleObj || handleObj.ownerType !== 'org') {
      const error = !handleObj ? 'handle_not_found' : 'only_orgs_can_integrate';
      return res.redirect(`${env.FRONTEND_URL}/${handle}/integrations/?error=${error}`);
    }

    const tenantUid = handleObj.ownerUid;
    const tenantDb = setupDB(tenantUid);
    const models = {
      IntegrationToken: tenantModels.IntegrationToken.bindKnex(tenantDb),
      Integration: tenantModels.Integration.bindKnex(tenantDb),
    };

    // Create OAuth token
    const creatorUid = (req as any).session?.user?.uid;
    const integrationToken = {
      accessToken: oauthResponse.accessToken,
      refreshToken: oauthResponse.refreshToken,
      url: oauthResponse.url,
      creatorUid,
      ownerUid: tenantUid,
      expiresAt: dayjs().add(oauthResponse.expiresIn, 'second').toISOString(),
      retrieved: true,
    };
    // Handle integration creation/update
    let tokenUid: string;
    let redirectUrl: string;
    if (integrationUid && !_.isNaN(integrationUid)) {
      // Handle existing integration
      const result = await handleExistingIntegration(models, tenantUid, integrationUid, integrationToken, handle, sharedDb, tenantDb, tenantUid, handleObj, true);
      tokenUid = result.tokenUid;
      redirectUrl = result.redirectUrl;
    } else {
      const result = await createNewIntegration(models, serviceType, integrationToken, handle, sharedDb, tenantDb);
      tokenUid = result.tokenUid;
      redirectUrl = result.redirectUrl;
      // Replace the direct syncIntegrationWorkflow call with the new startWorkflow
      await startWorkflow('integrationSyncWorkflow', {
        taskQueue: 'integration-sync-queue',
        workflowId: `${handleObj.name}:integration:sync:${serviceType}:${result.integrationUid}`,
        args: [tenantUid, result.integrationUid],
        cronSchedule: SERVICE_CONFIGS[serviceType].cronSchedule,
      });
    }

    // Queue user fetching
    try {
      await startWorkflow('fetchUserWorkFlow', {
        taskQueue: 'integration-queue',
        workflowId: `${handleObj.name}:integration:user:${Date.now()}`,
        args: [tenantUid, tokenUid],
      });
    } catch (error) {
      logger.error(`Error while queuing user fetch for ${serviceType}:`, error);
    }

    return res.redirect(redirectUrl);
  } catch (error) {
    logger.error('Error in TestfiestaOAuth handling:', error);
    return res.redirect(
      `${env.FRONTEND_URL}/${handle}/integrations/?error=internalServerError`,
    );
  }
};

const handleExistingIntegration = async (models, ownerUid, integrationUid, integrationToken, handle, sharedDb, tenantDb, tenantUid, handleObj, startFlow = false) => {
  const integration = await models.Integration.query()
    .where('uid', integrationUid)
    .first();

  if (!integration) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errors.INTEGRATION_NOT_FOUND);
  }

  const existingToken = await models.IntegrationToken.query()
    .where('integrationUid', integrationUid)
    .where('ownerUid', ownerUid)
    .first();

  let tokenUid;
  if (existingToken) {
    await IntegrationToken.updateIntegrationToken(sharedDb, tenantDb, models, integrationToken, existingToken.uid);
    tokenUid = existingToken.uid;
  } else {
    const token = await IntegrationToken.createIntegrationToken(sharedDb, tenantDb, models, integrationToken);
    await token.$query().patch({ integrationUid });
    tokenUid = token.uid;
  }

  try {
    await startWorkflow('integrationUpdateWorkflow', {
      taskQueue: 'integration-queue',
      workflowId: `${handleObj.name}:integration:update:${integration.service}:${integration.uid}:${Date.now()}`,
      args: [tenantUid, integration.uid.toString()],
    });
  } catch (error) {
    logger.error(
      `Error updating defects for integrationUid: ${integration.uid}`,
      error,
    );
  }
  if (startFlow) {
  // Update integration configuration to include URL
    await models.Integration.query().findById(integrationUid).patch({
      status: 'active',
      configuration: {
        ...integration.configuration,
        url: integrationToken.url || integration.configuration?.url, // Preserve URL in configuration
      },
    });
    try {
      await startWorkflow('integrationSyncWorkflow', {
        taskQueue: 'integration-sync-queue',
        workflowId: `${handleObj.name}:integration:sync:${integration.service}:${integration.uid}`,
        args: [tenantUid, integration.uid],
        cronSchedule: SERVICE_CONFIGS[integration.service].cronSchedule,
      });
    } catch (error) {
      logger.error(`Error starting workflow for integration: ${integration.uid}`);
    }
  }
  return {
    tokenUid,
    redirectUrl: `${env.FRONTEND_URL}/${handle}/integrations?success=true`,
  };
};
const createNewIntegration = async (models, serviceType, integrationToken, handle, sharedDb, tenantDb) => {
  const token = await IntegrationToken.createIntegrationToken(sharedDb, tenantDb, models, integrationToken);
  const tokenUid = token.uid;
  const { entityTypes } = SERVICE_CONFIGS[serviceType.toLowerCase()];

  // Create integration with URL in configuration
  const integration = await models.Integration.query().insert({
    name: `${serviceType}-${tokenUid}`,
    service: serviceType,
    type: 'oauth',
    description: INTEGRATION_DESCRIPTIONS[serviceType],
    configuration: {
      url: integrationToken.url, // Include URL in initial configuration
    },
    entityTypes: models.Integration.raw('?::text[]', [
      entityTypes,
    ]),
  });

  await token.$query().patch({ integrationUid: integration.uid });

  return {
    tokenUid,
    integrationUid: integration.uid,
    redirectUrl: `${env.FRONTEND_URL}/${handle}/integrations/new?name=${serviceType}&integrationUid=${integration.uid}`,
  };
};

const getIssueTypes = async (req: Request) => {
  // convert to number
  const integrationUid = Number(req.params.integrationUid);
  // const projectId = req.query.projectId;

  const integration = await req.models.Integration.query()
    .where('uid', integrationUid)
    .first();

  if (!integration) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errors.INTEGRATION_NOT_FOUND,
    );
  }

  // check if integration is deleted as this endpoint is only for active integrations
  if (integration.deletedAt) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_DELETED,
    );
  }

  if (integration.status === 'inactive') {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_AUTHENTICATION_REQUIRED,
      'Please re-authenticate the integration to continue',
    );
  }

  const service = IntegrationServiceFactory.getService(integration.service);
  const integrationToken = await req.models.IntegrationToken.query()
    .where('integrationUid', String(integrationUid))
    .where('ownerUid', req.locals.handle.ownerUid)
    .first();

  if (!integrationToken) {
    await integration.$query().patch({ status: 'inactive' });
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_AUTHENTICATION_REQUIRED,
      'Please re-authenticate the integration to continue',
    );
  }

  const authHeader = await service.prepareAuthHeader(integrationToken, req.knexDB);
  if (!authHeader) {
    await integration.$query().patch({ status: 'inactive' });
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_AUTHENTICATION_REQUIRED,
      'Please re-authenticate the integration to continue',
    );
  }
  const response = await IssueTypes(
    {
      resourceId: req.query.resourceId,
      projectId: req.query.projectId,
    },
    authHeader,
  );

  if (!response.success) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.EXTERNAL_API_ERROR,
      response.error,
    );
  }

  return response.data;
};

const getCreateMetadata = async (req: Request) => {
  const integrationUid = Number(req.params.integrationUid);
  const { resourceId } = req.query;
  const integration = await req.models.Integration.query()
    .where('uid', integrationUid)
    .first();

  if (!integration) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errors.INTEGRATION_NOT_FOUND,
    );
  }

  // check if integration is deleted as this endpoint is only for active integrations
  if (integration.deletedAt) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_DELETED,
    );
  }

  if (integration.status === 'inactive') {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_AUTHENTICATION_REQUIRED,
      'Please re-authenticate the integration to continue',
    );
  }

  const service = IntegrationServiceFactory.getService(integration.service);
  const integrationToken = await req.models.IntegrationToken.query()
    .where('integrationUid', integrationUid)
    .where('ownerUid', req.locals.handle.ownerUid)
    .first();

  if (!integrationToken) {
    await integration.$query().patch({ status: 'inactive' });
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.AUTH_TOKEN_NOT_FOUND,
      'Please re-authenticate the integration to continue',
    );
  }

  const authHeader = await service.prepareAuthHeader(integrationToken, req.knexDB);
  if (!authHeader) {
    await integration.$query().patch({ status: 'inactive' });
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_AUTHENTICATION_REQUIRED,
      'Please re-authenticate the integration to continue',
    );
  }
  const response = await getCreateMeta(
    {
      resourceId,
    },
    authHeader,
  );

  if (!response.success) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.EXTERNAL_API_ERROR,
      response.error,
    );
  }

  return response.data;
};

const fetchReporters = async (req: Request) => {
  const { integrationUid } = req.params;
  const integration = await req.models.Integration.query().findById(integrationUid);
  if (!integration) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errors.INTEGRATION_NOT_FOUND);
  }

  // check if integration is deleted as this endpoint is only for active integrations
  if (integration.deletedAt) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_DELETED,
    );
  }

  // rigth now this route will be only used for jira
  if (integration.service !== 'jira') {
    return [];
  }
  const integrationToken = await req.models.IntegrationToken.query()
    .where('integrationUid', integrationUid)
    .where('ownerUid', req.locals.handle.ownerUid)
    .first();
  if (!integrationToken) {
    await integration.$query().patch({ status: 'inactive' });
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_AUTHENTICATION_REQUIRED,
      'Please re-authenticate the integration to continue',
    );
  }
  const service = IntegrationServiceFactory.getService(integration.service);
  const authHeader = await service.prepareAuthHeader(integrationToken, req.knexDB);
  if (!authHeader) {
    await integration.$query().patch({ status: 'inactive' });
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_AUTHENTICATION_REQUIRED,
      'Please re-authenticate the integration to continue',
    );
  }
  const response = await getJiraUsers({ resourceId: req.query.resourceId }, authHeader);
  if (!response.success) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.EXTERNAL_API_ERROR,
      response.error,
    );
  }
  return response.data;
};

const fetchAssignableUsers = async (req: Request) => {
  const { integrationUid } = req.params;
  const integration = await req.models.Integration.query().findById(integrationUid);
  if (!integration) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errors.INTEGRATION_NOT_FOUND);
  }
  if (integration.service !== 'jira') {
    return [];
  }
  const integrationToken = await req.models.IntegrationToken.query()
    .where('integrationUid', integrationUid)
    .where('ownerUid', req.locals.handle.ownerUid)
    .first();
  if (!integrationToken) {
    await integration.$query().patch({ status: 'inactive' });
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_AUTHENTICATION_REQUIRED,
      'Please re-authenticate the integration to continue',
    );
  }
  const service = IntegrationServiceFactory.getService(integration.service);
  const authHeader = await service.prepareAuthHeader(integrationToken, req.knexDB);
  if (!authHeader) {
    await integration.$query().patch({ status: 'inactive' });
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_AUTHENTICATION_REQUIRED,
      'Please re-authenticate the integration to continue',
    );
  }
  const response = await getProjectAssignableUsers({ resourceId: req.query.resourceId, projectId: req.query.projectId }, authHeader);
  if (!response.success) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.EXTERNAL_API_ERROR,
      response.error,
    );
  }
  return response.data;
};

const fetchBoards = async (req: Request) => {
  const { integrationUid } = req.params;
  const integration = await req.models.Integration.query().findById(integrationUid);
  if (!integration) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errors.INTEGRATION_NOT_FOUND);
  }
  if (integration.status === 'inactive') {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_AUTHENTICATION_REQUIRED,
      'Please re-authenticate the integration to continue',
    );
  }
  if (integration.service !== 'jira') {
    return [];
  }
  const integrationToken = await req.models.IntegrationToken.query()
    .where('integrationUid', integrationUid)
    .where('ownerUid', req.locals.handle.ownerUid)
    .first();
  if (!integrationToken) {
    await integration.$query().patch({ status: 'inactive' });
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_AUTHENTICATION_REQUIRED,
      'Please re-authenticate the integration to continue',
    );
  }
  const service = IntegrationServiceFactory.getService(integration.service);
  const authHeader = await service.prepareAuthHeader(integrationToken, req.knexDB);
  if (!authHeader) {
    await integration.$query().patch({ status: 'inactive' });
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_AUTHENTICATION_REQUIRED,
      'Please re-authenticate the integration to continue',
    );
  }
  const response = await getJiraBoards({ resourceId: req.query.resourceId }, authHeader);
  if (!response.success) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.EXTERNAL_API_ERROR,
      response.error,
    );
  }
  return response.data;
};

const fetchSprints = async (req: Request) => {
  const { integrationUid } = req.params;
  const integration = await req.models.Integration.query().findById(integrationUid);
  if (!integration) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errors.INTEGRATION_NOT_FOUND);
  }
  if (integration.status === 'inactive') {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_AUTHENTICATION_REQUIRED,
      'Please re-authenticate the integration to continue',
    );
  }
  if (integration.service !== 'jira') {
    return [];
  }
  const integrationToken = await req.models.IntegrationToken.query()
    .where('integrationUid', integrationUid)
    .where('ownerUid', req.locals.handle.ownerUid)
    .first();
  if (!integrationToken) {
    await integration.$query().patch({ status: 'inactive' });
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_AUTHENTICATION_REQUIRED,
      'Please re-authenticate the integration to continue',
    );
  }
  const service = IntegrationServiceFactory.getService(integration.service);
  const authHeader = await service.prepareAuthHeader(integrationToken, req.knexDB);
  if (!authHeader) {
    await integration.$query().patch({ status: 'inactive' });
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.INTEGRATION_AUTHENTICATION_REQUIRED,
      'Please re-authenticate the integration to continue',
    );
  }
  const response = await getJiraSprints({ resourceId: req.query.resourceId, boardId: req.query.boardId }, authHeader);
  if (!response.success) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.EXTERNAL_API_ERROR,
      response.error,
    );
  }
  return response.data;
};

const getIntegrationData = async (req: Request) => {
  const { type } = req.query;

  switch (type as string) {
    case 'issueTypes':
      return getIssueTypes(req);
    case 'createMetadata':
      return getCreateMetadata(req);
    case 'reporters':
      return fetchReporters(req);
    case 'assignees':
      return fetchAssignableUsers(req);
    case 'boards':
      return fetchBoards(req);
    case 'sprints':
      return fetchSprints(req);
    default:
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        errors.EXTERNAL_API_ERROR,
        'Invalid data type requested',
      );
  }
};

const uploadAttachment = async (req: Request) => {
  const {
    fileType,
    size,
    fileName,
  } = req.body;
  const { integrationUid } = req.params;
  const { ownerUid } = req.locals.handle;
  const creatorUid = req.locals.user?.uid || null;

  // Verify integration exists
  const integration = await req.models.Integration.query()
    .findById(integrationUid)
    .whereNull('archivedAt')
    .first();

  if (!integration) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errors.INTEGRATION_NOT_FOUND,
    );
  }

  const newAttachment = {
    name: fileName,
    ownerUid,
    size,
    fileType,
    mediaType: 'attachment',
    creatorUid,
  };

  const trx = await req.knexDB.transaction();
  const previousAttachmentUid = integration.avatarAttachmentUid;
  try {
    const {
      key,
      signed_url: signedUrl,
      object_url: objectUrl,
      client_headers: clientHeaders,
      uid,
    } = await Attachment.createAttachment(newAttachment, trx);

    await trx('integrationAttachments').insert({
      attachmentUid: uid,
      integrationUid: Number(integrationUid),
    });

    await req.models.Integration.query(trx)
      .findById(integrationUid)
      .patch({ avatarAttachmentUid: uid });

    if (previousAttachmentUid) {
      const previousAttachment = await req.models.Attachment.query(trx)
        .findOne({ uid: previousAttachmentUid });

      const { ownerUid } = req.locals.handle;

      if (previousAttachment) {
        const param: AttachmentData = {
          type: 'delete',
          key: previousAttachment.key,
          ownerUid,
        };
        await Promise.all([
          startWorkflow('attachmentWorkflow', {
            taskQueue: 'attachment-queue',
            workflowId: `${ownerUid}:attachment:delete:${Date.now()}`,
            args: [param],
          }),
          Attachment.deleteAttachment(trx, previousAttachmentUid, 'integrationAttachments'),
        ]);
        // transaction will be committed in the deleteAttachment function
      }
    } else {
      await trx.commit();
    }

    return {
      uid,
      objectKey: key,
      signedUrl,
      objectUrl,
      clientHeaders,
    };
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const deleteAttachment = async (req: Request, res: Response) => {
  const trx = await req.knexDB.transaction();
  const attachmentId = <string>req.params.id;

  try {
    const attachment = await req.models.Attachment.query(trx).findOne({
      uid: attachmentId,
    });

    if (!attachment) return res.status(404).send();
    const integration = await req.models.Integration.query(trx).findOne({
      uid: req.params.integrationUid,
    });
    if (!integration) return res.status(404).send();

    await integration.$query(trx).patch({
      avatarAttachmentUid: null,
    });
    const { key } = attachment;
    const { ownerUid } = req.locals.handle;
    const param: AttachmentData = {
      type: 'delete',
      key,
      ownerUid,
    };
    await Promise.all([
      startWorkflow('attachmentWorkflow', {
        taskQueue: 'attachment-queue',
        workflowId: `${ownerUid}:attachment:delete:${Date.now()}`,
        args: [param],
      }),
      Attachment.deleteAttachment(trx, attachmentId, 'integrationAttachments'),
    ]);
    await trx.commit();
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const removeIntegrationErrors = async (req: Request) => {
  const { integrationUid } = req.params;
  const integration = await req.models.Integration.query().findById(integrationUid);
  if (!integration) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errors.INTEGRATION_NOT_FOUND);
  }
  const {
    index,
    removeAll,
  } = req.body;
  const { externalErrors } = integration;
  if (removeAll) {
    // set status to active and errors array to empty
    await req.models.Integration.query().findById(integrationUid).patch({
      status: 'active',
      externalErrors: [],
    });
  } else {
    // remove the error at the given index
    externalErrors.splice(Number(index), 1);
    await req.models.Integration.query().findById(integrationUid).patch({
      externalErrors,
      status: externalErrors.length > 0 ? 'error' : 'active',
    });
  }
  if (externalErrors.length === 0 || removeAll) {
    try {
      // try to restart sync workflow
      await startWorkflow('integrationSyncWorkflow', {
        taskQueue: 'integration-sync-queue',
        workflowId: `${req.locals.handle.name}:integration:sync:${integration.service}:${integration.uid}`,
        args: [req.locals.handle.ownerUid, integration.uid],
      });
      logger.info(`Restarted sync workflow for integration ${integrationUid}`);
    } catch (error) {
      logger.info(`Workflow already running for integration ${integrationUid}`);
    }
  }
  return {
    success: true,
    message: 'Integration errors removed',
  };
};

export default {
  getAvailableIntegrations: httpHandler(getAvailableIntegrations),
  integrationServiceProjects: httpHandler(integrationServiceProjects),
  getOrganizations: httpHandler(getOrganizations),
  jiraRedirect: httpHandler(jiraRedirect),
  getIntegrations: httpHandler(getIntegrations),
  getIntegration: httpHandler(getIntegration),
  createIntegration: httpHandler(createIntegration),
  updateIntegration: httpHandler(updateIntegration),
  deleteIntegration: httpHandler(deleteIntegration),
  githubRedirect: httpHandler(githubRedirect),
  handleOAuthResponse,
  uploadAttachment: httpHandler(uploadAttachment),
  deleteAttachment: httpHandler(deleteAttachment),
  getIntegrationData: httpHandler(getIntegrationData),
  reauthenticateToken: httpHandler(reauthenticateToken),
  personalTokenRedirect: httpHandler(personalTokenRedirect),
  removeIntegrationErrors: httpHandler(removeIntegrationErrors),
};
