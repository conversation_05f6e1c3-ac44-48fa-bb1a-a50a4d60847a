import { ApplicationError, httpHandler } from '@app/lib/http';

import { FGARawWrite } from '@ss-libs/ss-component-auth';
import { Request } from 'express';
import { StatusCodes } from 'http-status-codes';
import { User } from '@app/models/user';
import { getNextId } from '@app/lib/model';
import i18n from 'i18n';

const createSharedStep = async (req: Request) => {
  const { name, steps, expectedResultByStep } = req.body;

  const trx = await req.knexDB.transaction();
  const owner = req.locals.handle.ownerUid;
  const { ownerType } = req.locals.handle;

  try {
    const uid = await getNextId(trx, req.models.SharedTestStep);
    const sharedStep = await req.models.SharedTestStep.query(trx)
      .insert({
        uid,
        name,
        steps,
        expectedResultByStep,
        version: 1,
        active: true,
        projectUid: req.locals.project.uid,
        sharedTestStepRef: uid,
        createdBy: req.locals.user.uid,
      })
      .returning('*');

    await trx.commit();

    const writes: FGARawWrite[] = [
      {
        objectType: 'step',
        objectId: sharedStep.uid,
        relation: 'owner',
        subjectType: ownerType,
        subjectId: owner,
      },
    ];

    await req.fga.create(...writes);

    return sharedStep;
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const getSharedTestSteps = async (req: Request) => {
  const owner = req.locals.handle.ownerUid;
  const { ownerType } = req.locals.handle;

  const pagination: any = {};
  const perPage: number = Number(req.query.per_page) || 10;
  let page: number = Number(req.query.current_page) || 1;

  if (page < 1) page = 1;
  const offset = (page - 1) * perPage;

  const relatedSharedSteps = (
    await req.fga.query(`${ownerType}:${owner}`, 'step:', 'owner')
  ).map((tuple: any) => tuple.key.object.split(':')[1]);

  const selectFields: any = ['sharedTestSteps.*'];

  if (req.query.includeCount) {
    const [referencedResult, archivedResult, activeResult] = await Promise.all([
      req.models.TestCaseStep.query()
        .whereNull('deletedAt')
        .whereIn('sharedTestStepRef', relatedSharedSteps)
        .count()
        .as('referencedBy')
        .first(),
      req.models.SharedTestStep.query()
        .whereNull('deletedAt')
        .whereNotNull('archivedAt')
        .where('active', true)
        .where('projectUid', req.locals.project.uid)
        .count()
        .as('totalArchived')
        .first(),
      req.models.SharedTestStep.query()
        .whereNull('deletedAt')
        .whereNull('archivedAt')
        .where('active', true)
        .where('projectUid', req.locals.project.uid)
        .count()
        .as('totalActive')
        .first(),
    ]);

    pagination.meta = {
      referencedBy: parseInt((referencedResult as any).count),
      totalArchived: parseInt((archivedResult as any).count),
      totalActive: parseInt((activeResult as any).count),
    };
  }

  let sharedTestStepsQuery = req.models.SharedTestStep.query()
    .select([
      ...selectFields,
      req.models.SharedTestStep.relatedQuery('testCaseSteps')
        .whereNull('deletedAt')
        .count()
        .as('referencedBy'),
    ])
    .whereIn('sharedTestStepRef', relatedSharedSteps)
    .whereNull('deletedAt')
    .where('active', true)
    .where('projectUid', req.locals.project.uid);

  if (req.query.name) {
    sharedTestStepsQuery = sharedTestStepsQuery.whereILike(
      'sharedTestSteps.name',
      `%${req.query.name}%`,
    );
  }

  if (req.query.archived && req.query.archived === 'true') {
    sharedTestStepsQuery = sharedTestStepsQuery.whereNotNull(
      'sharedTestSteps.archivedAt',
    );
  }

  if (req.query.active && req.query.active === 'true') {
    sharedTestStepsQuery = sharedTestStepsQuery.where(
      'sharedTestSteps.active',
      true,
    );
  }

  if (req.query.minSteps || req.query.maxSteps) {
    const minSteps = parseInt(req.query.minSteps as string) || 0;
    const maxSteps = parseInt(req.query.maxSteps as string) || Number.MAX_SAFE_INTEGER;
    sharedTestStepsQuery = sharedTestStepsQuery.whereRaw(
      'jsonb_array_length(steps) BETWEEN ? AND ?',
      [minSteps, maxSteps],
    );
  }

  const sharedTestSteps = await sharedTestStepsQuery
    .offset(offset)
    .limit(perPage);
  const users: Record<string, User> = {};

  if (sharedTestSteps.length) {
    (
      await req.models.User.query()
        .whereIn(
          'uid',
          sharedTestSteps.map((s) => s.createdBy),
        )
        .select('uid', 'lastName', 'firstName', 'email')
    ).reduce((u, creator) => {
      u[creator.uid] = creator;
      return u;
    }, users);

    sharedTestSteps.forEach((s) => {
      s.creator = users[s.createdBy];
    });
  }

  const count: any = await req.models.SharedTestStep.query()
    .whereIn('uid', relatedSharedSteps)
    .whereNull('deletedAt')
    .where('projectUid', req.locals.project.uid)
    .count('uid as CNT')
    .first();

  pagination.total = count.CNT;
  pagination.per_page = perPage;
  pagination.offset = offset;
  pagination.to = offset + sharedTestSteps.length;
  pagination.last_page = Math.ceil(count.CNT / perPage);
  pagination.current_page = page;
  pagination.from = offset;
  pagination.sharedSteps = sharedTestSteps;

  return pagination;
};

const getSharedStep = async (req: Request) => {
  const { id: sharedStepId } = req.params;

  const sharedStep = await req.models.SharedTestStep.query()
    .withGraphFetched({ testCases: true })
    .where('uid', sharedStepId)
    .whereNull('deletedAt')
    .where('active', true)
    .where('projectUid', req.locals.project.uid)
    .first();

  if (!sharedStep) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      i18n.__('sharedStepsNotFound'),
    );
  }

  sharedStep.creator = await req.models.User.query()
    .findById(sharedStep.createdBy)
    .select('uid', 'firstName', 'lastName', 'email');

  return sharedStep;
};

const deleteSharedStep = async (req: Request) => {
  await req.models.SharedTestStep.query()
    .where({ uid: req.params.id })
    .patch({ deletedAt: req.knexDB.fn.now() });

  return { message: i18n.__('sharedStepDeleted') };
};

const deleteSharedSteps = async (req: Request) => {
  const { sharedStepIds } = req.body;

  await req.models.SharedTestStep.query()
    .whereIn('uid', sharedStepIds)
    .where('projectUid', req.locals.project.uid)
    .patch({ deletedAt: req.knexDB.fn.now() });

  return { message: i18n.__('sharedStepDeletedInBulk') };
};

const updateSharedStep = async (req: Request) => {
  const { id: sharedStepId } = req.params;
  const { name, steps } = req.body;
  const trx = await req.knexDB.transaction();

  try {
    const [latestVersionOfSharedStep] = await Promise.all([
      req.models.SharedTestStep.query(trx)
        .where({ uid: sharedStepId })
        .orderBy('version', 'DESC')
        .where('projectUid', req.locals.project.uid)
        .first(),
      req.models.SharedTestStep.query(trx)
        .where({ uid: sharedStepId })
        .where('projectUid', req.locals.project.uid)
        .patch({ active: false }),
    ]);

    const payload = {
      name: name ?? (latestVersionOfSharedStep.name as string),
      steps: steps ?? latestVersionOfSharedStep.steps,
      expectedResultByStep: latestVersionOfSharedStep.expectedResultByStep,
      customFields: latestVersionOfSharedStep.customFields,
      externalId: latestVersionOfSharedStep.externalId,
      source: latestVersionOfSharedStep.source,
      projectUid: req.locals.project.uid,
    };

    const newSharedStep = await req.models.SharedTestStep.query()
      .insert({
        ...payload,
        version: latestVersionOfSharedStep.version + 1,
        active: true,
        sharedTestStepRef: latestVersionOfSharedStep.sharedTestStepRef,
        createdBy: latestVersionOfSharedStep.createdBy,
      })
      .returning('*');

    await trx.commit();

    return newSharedStep;
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const updateSharedSteps = async (req: Request) => {
  // TODO - Handle updates with more than archiving
  const { sharedSteps } = req.body;

  const sharedStepsToArchive = [];
  const sharedStepsToUnArchive = [];

  if (sharedSteps && sharedSteps.length > 0) {
    for (const sharedStep of sharedSteps) {
      if (sharedStep.archived) {
        sharedStepsToArchive.push(sharedStep.id);
      } else {
        sharedStepsToUnArchive.push(sharedStep.id);
      }
    }
  }

  if (sharedStepsToArchive.length > 0) {
    await req.models.SharedTestStep.query()
      .whereIn('uid', sharedStepsToArchive)
      .where('projectUid', req.locals.project.uid)
      .patch({ archivedAt: req.knexDB.fn.now() });
  }

  if (sharedStepsToUnArchive.length > 0) {
    await req.models.SharedTestStep.query()
      .whereIn('uid', sharedStepsToUnArchive)
      .where('projectUid', req.locals.project.uid)
      .patch({ archivedAt: null });
  }

  return { message: i18n.__('toggleSharedStepArchivedInBulk') };
};

export default {
  createSharedStep: httpHandler(createSharedStep),
  getSharedTestSteps: httpHandler(getSharedTestSteps),
  getSharedStep: httpHandler(getSharedStep),
  deleteSharedStep: httpHandler(deleteSharedStep),
  deleteSharedSteps: httpHandler(deleteSharedSteps),
  updateSharedStep: httpHandler(updateSharedStep),
  updateSharedSteps: httpHandler(updateSharedSteps),
};
