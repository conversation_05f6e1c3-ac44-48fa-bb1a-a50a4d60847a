import { httpHand<PERSON> } from '@app/lib/http';
import { paginated } from '@app/lib/model';
import { WorkSpaceExecutionDTO } from '@app/types/workSpace';

import { Request } from 'express';

const getWorkspaceExecutions = async (req: Request) => {
  const {
    runUids,
    milestoneUids,
    planUids,
    projectUids,
    userUids,
    priorities,
    status,
    assignDateStart,
    assignDateEnd,
    dueDateStart,
    dueDateEnd,
    limit,
    offset,
    searchTerm,
    orderBy,
  }: WorkSpaceExecutionDTO = <any>req.query;

  const selectedRunUids = new Set<number>();
  const selectedMilestoneUids = new Set<number>();
  runUids.forEach((element) => {
    selectedRunUids.add(parseInt(element as string));
  });

  milestoneUids.forEach((element) => {
    selectedMilestoneUids.add(parseInt(element as string));
  });
  const runs = await req.models.TestMilestone.relatedQuery('testRuns').for(
    Array.from(selectedMilestoneUids),
  );

  const accessableProjects = [...(req.locals?.accessableProjects || []), ...projectUids];

  runs.forEach((element: any) => selectedRunUids.add(element.uid));

  const executionsQuery = req.models.TestExecution.query()
    .modify((q) => {
      if (searchTerm) {
        q.where((builder) => {
          builder
            .whereExists(
              req.models.TestCase.query()
                .whereRaw('"testCases"."uid" = "testExecutions"."testCaseUid"')
                .where('name', 'ilike', `%${searchTerm}%`),
            )
            .orWhereIn(
              'projectUid',
              req.models.Project.query()
                .select('uid')
                .where('key', 'ilike', `%${searchTerm}%`),
            )
            .orWhereIn(
              'projectUid',
              req.models.Project.query()
                .select('uid')
                .where('name', 'ilike', `%${searchTerm}%`),
            )
            .orWhereIn(
              'testRunUid',
              req.models.TestRun.query()
                .select('uid')
                .where('name', 'ilike', `%${searchTerm}%`)
                .whereNull('deletedAt')
                .whereNull('archivedAt'),
            )
            .orWhereIn(
              'testRunUid',
              req.models.TestPlanRun.query()
                .select('runUid')
                .whereIn(
                  'planUid',
                  req.models.TestPlan.query()
                    .select('uid')
                    .where('name', 'ilike', `%${searchTerm}%`)
                    .whereNull('deletedAt'),
                ),
            )
            .orWhereIn(
              'testRunUid',
              req.models.TestMilestoneRun.query()
                .select('runUid')
                .whereIn(
                  'milestoneUid',
                  req.models.TestMilestone.query()
                    .select('uid')
                    .where('name', 'ilike', `%${searchTerm}%`)
                    .whereNull('deletedAt')
                    .whereNull('archivedAt'),
                ),
            );
        });
      }

      if (accessableProjects?.length) {
        q.whereIn('projectUid', accessableProjects as []);
      }

      if (projectUids?.length) {
        q.whereIn('projectUid', projectUids as []);
      }
      if (priorities?.length) {
        q.whereIn('priority', priorities);
      }
      if (planUids?.length) {
        q.whereIn(
          'testRunUid',
          req.models.TestPlan.relatedQuery('runs').for(planUids).select('uid'),
        );
      }
      if (selectedRunUids?.size) {
        q.whereIn('testRunUid', [...selectedRunUids]);
      }
      if (userUids?.length) {
        q.whereIn('assignedTo', userUids);
      }

      if (dueDateStart && dueDateEnd) {
        q.whereBetween('dueAt', [dueDateStart, dueDateEnd]);
      } else if (dueDateStart) {
        q.where('dueAt', '>=', dueDateStart);
      } else if (dueDateEnd) {
        q.where('dueAt', '<=', dueDateEnd);
      }

      if (assignDateStart && assignDateEnd) {
        q.whereBetween('lastAssignedAt', [assignDateStart, assignDateEnd]);
      } else if (assignDateStart) {
        q.where('lastAssignedAt', '>=', assignDateStart);
      } else if (assignDateEnd) {
        q.where('lastAssignedAt', '<=', assignDateEnd);
      }

      q.where((builder) => {
        builder.whereExists(
          req.models.TestRun.query()
            .whereRaw('"tags"."uid" = "testExecutions"."testRunUid"')
            .whereNull('deletedAt')
            .whereNull('archivedAt'),
        );
      });
    })
    .whereNull('deletedAt')
    .select('*');

  orderBy?.forEach((o) => {
    const [column, order] = o.split(':');
    executionsQuery.orderBy(column, order as 'asc' | 'desc');
  });

  return paginated(
    executionsQuery.modify((q) => {
      if (status?.length) {
        q.whereIn('status', status);
      }
    }) as any,
    limit,
    offset,
    req.knexDB,
  );
};

const getWorkspaceOverview = async (req: Request) => {
  const projects = await req.models.Project.query()
    .select(['name', 'uid'])
    .whereNull('deletedAt')
    .whereNull('archivedAt')
    .whereIn('uid', req.locals.accessableProjects);

  const milestones = await req.models.TestMilestone.query()
    .select(['name', 'uid', 'projectUid'])
    .where({ deletedAt: null, archivedAt: null, systemType: 'milestone' });
  const testPlans = await req.models.TestPlan.query()
    .withGraphFetched('milestones as milestoneUids')
    .modifyGraph('milestoneUids', (builder) => {
      builder.select('tags.uid').where({ systemType: 'milestone' });
    })
    .select(['name', 'uid', 'projectUid'])
    .where({ deletedAt: null, archivedAt: null, systemType: 'plan' });
  const testRuns = await req.models.TestRun.query()
    .withGraphFetched('testMilestones as milestoneUids')
    .modifyGraph('milestoneUids', (builder) => {
      builder.select('tags.uid').where({ systemType: 'milestone' });
    })
    .select(['name', 'uid', 'projectUid'])
    .where({ deletedAt: null, archivedAt: null });

  return {
    projects,
    milestones,
    testPlans,
    testRuns,
  };
};

export default {
  getWorkspaceExecutions: httpHandler(getWorkspaceExecutions),
  getWorkspaceOverview: httpHandler(getWorkspaceOverview),
};
