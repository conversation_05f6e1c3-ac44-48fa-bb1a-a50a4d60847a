import { Request } from 'express';
import { StatusCodes } from 'http-status-codes';
import { httpH<PERSON><PERSON>, ApplicationError } from '@app/lib/http';
import errorConstants from '@app/constants/errors';
import { decryptSSOConfig } from '@ss-libs/ss-component-auth/dist/internal/sso';
import crypto from 'crypto';
import { KEY_MANAGER } from '@app/config/keyManagerLoader';
import { SSOConfigValue } from '@ss-libs/ss-component-auth/dist/sso/ssoConfig.model';

const validateConfig = (config: any) => {
  const protocol = Object.keys(config || {})[0];
  if (!protocol) {
    throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Protocol is required');
  }

  const protocolConfig = config[protocol];
  if (!protocolConfig) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Invalid protocol configuration',
    );
  }

  if (protocolConfig.groupMappings && Object.keys(protocolConfig.groupMappings).length > 0) {
    if (protocolConfig.defaultRole) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'Default role cannot be set when using group mappings',
      );
    }
  } else if (
    !protocolConfig.allowOnlyInvitedAccounts
    && !protocolConfig.defaultRole
  ) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      'Default role is required when not using group mappings and not requiring invites',
    );
  }

  return protocol;
};

const createSSOConfig = async (req: Request) => {
  if (req.locals.handle.ownerType !== 'org') {
    throw new ApplicationError(
      StatusCodes.FORBIDDEN,
      errorConstants.SSO_CONFIG_NOT_ALLOWED,
    );
  }
  const { config, symmetricKeyData } = req.body;
  const uid = crypto.randomUUID();
  symmetricKeyData.entityId = uid;
  await KEY_MANAGER.createSymmeticKey(req.sharedKnexDB, symmetricKeyData);
  const protocol = validateConfig(config);
  const orgUid = req.locals.handle.ownerUid;
  const ssoConfig = await req.models.SSOConfig.query()
    .insert({
      uid,
      orgUid,
      config,
    })
    .returning('*');

  return {
    uid: ssoConfig.uid,
    protocol,
    isActive: ssoConfig.isActive,
    issuerUrl: req.body.config.url,
    allowOnlyInvitedAccounts: req.body.config.allowOnlyInvitedAccounts,
    hasGroupMappings: !!req.body.config.groupMappings,
    defaultRole: req.body.config.defaultRole,
  };
};

const getSSOConfig = async (req: Request) => {
  if (req.locals.handle.ownerType !== 'org') {
    throw new ApplicationError(
      StatusCodes.FORBIDDEN,
      errorConstants.SSO_CONFIG_NOT_ALLOWED,
    );
  }

  const ssoConfig = await req.models.SSOConfig.query()
    .where({
      orgUid: req.locals.handle.ownerUid,
    })
    .first();

  if (!ssoConfig) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.CONFIG_NOT_FOUND,
    );
  }

  const protocol = Object.keys(ssoConfig?.config || {})[0];
  const configValue = await decryptSSOConfig({
    config: ssoConfig.config as unknown as SSOConfigValue,
    keyManager: KEY_MANAGER,
    db: req.sharedKnexDB,
    entityId: ssoConfig.uid,
  });

  return {
    uid: ssoConfig.uid,
    isActive: ssoConfig.isActive,
    protocol,
    config: configValue,
  };
};

const deleteSSOConfig = async (req: Request) => {
  if (req.locals.handle.ownerType !== 'org') {
    throw new ApplicationError(
      StatusCodes.FORBIDDEN,
      errorConstants.SSO_CONFIG_NOT_ALLOWED,
    );
  }

  await req.models.SSOConfig.query().deleteById(req.params.id);

  return { uid: req.params.id };
};

const updateSSOConfig = async (req: Request) => {
  if (req.locals.handle.ownerType !== 'org') {
    throw new ApplicationError(
      StatusCodes.FORBIDDEN,
      errorConstants.SSO_CONFIG_NOT_ALLOWED,
    );
  }
  const { config, symmetricKeyData, isActive } = req.body;
  const body: any = isActive !== undefined ? { isActive } : {};
  let protocol;
  if (config && symmetricKeyData) {
    body.config = config;
    protocol = validateConfig(config);
  }

  const [ssoConfig] = await req.models.SSOConfig.query()
    .patch({ ...body })
    .where({ uid: req.params.id })
    .returning('*');
  if (!ssoConfig) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.CONFIG_NOT_FOUND,
    );
  } else if (symmetricKeyData) {
    symmetricKeyData.entityId = ssoConfig.uid;
    await KEY_MANAGER.createSymmeticKey(req.sharedKnexDB, symmetricKeyData);
  }
  if (!protocol) protocol = validateConfig(ssoConfig.config);
  return {
    uid: ssoConfig.uid,
    protocol,
    isActive: ssoConfig.isActive,
    issuerUrl: ssoConfig.config.url,
    allowOnlyInvitedAccounts: ssoConfig.config.allowOnlyInvitedAccounts,
    hasGroupMappings: !!ssoConfig.config.groupMappings,
    defaultRole: ssoConfig.config.defaultRole,
  };
};

export default {
  createSSOConfig: httpHandler(createSSOConfig),
  getSSOConfig: httpHandler(getSSOConfig),
  deleteSSOConfig: httpHandler(deleteSSOConfig),
  updateSSOConfig: httpHandler(updateSSOConfig),
};
