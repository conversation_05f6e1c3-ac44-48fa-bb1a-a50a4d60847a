import {
  ApplicationError,
  UnauthorizedRequest,
  httpHand<PERSON>,
} from '@app/lib/http';
import { Request, Response } from 'express';
import {
  adjectives,
  animals,
  uniqueNamesGenerator,
} from 'unique-names-generator';

import { <PERSON>le } from '@app/models/handle';
import { StatusCodes } from 'http-status-codes';
import { User as _User } from '@app/models/user';
import appConstants from '@app/constants/app';
import { auth } from '@ss-libs/ss-component-auth';
import axios from 'axios';
import bcrypt from 'bcrypt';
import crypto from 'crypto';
import dayjs from 'dayjs';
import errors from '@app/constants/errors';
import formatDate from '@app/utils/formatDate';
import { generateAppKeyAndUserData } from '@app/utils/appKey';
import { hashPassword } from '@app/utils/hashPassword';
import logger from '@app/config/logger';
import path from 'path';
import { serializeErr } from '@app/lib/error';

type User = Partial<_User>;

const signIn = async (req: Request) => {
  const keyUser = req.locals.user;
  if (!keyUser) throw new UnauthorizedRequest();

  const matchedUser = await req.models.User.query()
    .where('email', req.body.email)
    .select('*')
    .first();
  const handle: Handle = await req.models.Handle.query()
    .where('ownerUid', matchedUser.uid)
    .first();

  if (!matchedUser) {
    throw new UnauthorizedRequest(errors.INVALID_CREDENTIALS);
  }

  const isMatch = await bcrypt.compare(
    req.body.password,
    matchedUser.passwordHash,
  );

  if (!isMatch) {
    throw new UnauthorizedRequest(errors.INVALID_CREDENTIALS);
  }

  const user = {
    uid: matchedUser.uid,
    email: matchedUser.email,
    username: handle.name,
    firstName: matchedUser.firstName,
    lastName: matchedUser.lastName,
    avatar: matchedUser.avatar,
    preferences: matchedUser.preferences,
  };

  // Delete old user and reassign everything thye own?
  // CTODO - fix this in the auth component because it should be one function here
  const token = auth().bearerFromRequest(req);
  if (token) {
    const strippedPrefix = token.substring(
      token.indexOf('_') + 1,
      token.length,
    );
    const indexPoint = strippedPrefix.indexOf('.');
    const uid = strippedPrefix.substring(0, indexPoint);

    // TODO - add to background task
    // Update AccessToken
    const foundToken = await req.models.AccessToken.query().findById(uid);
    foundToken.ownerType = 'user';
    foundToken.ownerUid = user.uid;
    await req.models.AccessToken.query().patch(foundToken);
    // TODO - Update cases, steps, executions, attachments, etc in OFGA
  }

  return user;
};

const updateProfile = async (req: Request) => {
  let keyUser: Partial<User> = req.locals.user;
  if (!keyUser) throw new UnauthorizedRequest(errors.NO_AUTHENTICATED_USER);

  const emailUser = await req.models.User.query()
    .where('email', req.body.email)
    .first();
  if (emailUser) {
    throw new ApplicationError(StatusCodes.CONFLICT, errors.EMAIL_IN_USE);
  }

  const passwordHash = await hashPassword(req.body.password);
  const trx = await req.knexDB.transaction();
  try {
    keyUser = <any>{
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      email: req.body.email,
      passwordHash,
      lastSignInIp: req.ip,
      lastSignInAt: formatDate(dayjs().toDate()),
    };
    await req.models.User.query(trx)
      .patch(keyUser)
      .where('uid', req.locals.user.uid);

    const user: User = {
      uid: req.locals.user.uid,
      email: keyUser.email,
      firstName: keyUser.firstName,
      lastName: keyUser.lastName,
    };
    // TODO - GH#119, sanitize email split below
    const username: string = keyUser.email.split('@')[0];
    const newHandle = `${username.replaceAll(/[^a-z\d-_]/gi, '')}_${crypto.randomBytes(6).toString('hex')}`;
    const handle = await req.models.Handle.query(trx).insert({
      name: newHandle,
      ownerType: 'user',
      current: true,
      ownerUid: user.uid,
    });

    logger.info({ 'Key User': keyUser });
    logger.info({ User: user });
    logger.info({ Handle: handle });

    await trx.commit();

    return user;
    // TODO - handle user name combination and decombination here and in PINATA
  } catch (err) {
    logger.log('info', `Error in signUp: ${err}`);
    // Rollback the transaction
    await trx.rollback();
    throw err;
  }
};

const signUpToken = async (req: Request) => {
  // Generate name from dictionary.
  const firstName = uniqueNamesGenerator({
    dictionaries: [adjectives],
    style: 'capital',
  });
  const lastName = uniqueNamesGenerator({
    dictionaries: [animals],
    style: 'capital',
  });

  const internalUser = await req.models.User.query().insert({
    firstName,
    lastName,
    lastSignInIp: req.ip,
    lastSignInAt: formatDate(dayjs().toString()),
  });
  return generateAppKeyAndUserData(req.knexDB, internalUser, req.fga);
};

// CTODO - Create method for signing up and providing real info via token

const jiraOauthToken = async (req: Request, res: Response) => {
  const accessToken = await req.models.IntegrationToken.query()
    .findById(req.params.tokenId)
    .first();

  if (accessToken) {
    let { user } = req.locals;
    if (!accessToken.retrieved) {
      const isExpired = dayjs(accessToken.createdAt) < dayjs().subtract(5, 'minutes');
      if (isExpired) {
        return res.status(401).json({ error: 'Token expired' }); // TODO i18n
      }
      user = await req.models.User.query()
        .findById(accessToken.ownerUid)
        .first();
    } else {
      // If we are just looking for a refresh token.
      user = req.locals.user;
    }

    if (!user) {
      return res.status(403).send();
    }

    await req.models.IntegrationToken.query().findById(accessToken.uid).patch({
      ownerUid: user.uid,
      retrieved: true,
    });

    user = <any>{
      uid: user.uid,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      avatar: user.avatar,
      preferences: {
        locale: user.preferences?.locale,
        verified: user.preferences?.verified,
      },
    };

    if (dayjs(accessToken.expiresAt) < dayjs().add(5, 'minute')) {
      let failed = null;
      // Refresh if its necessary
      await axios
        .post(
          accessToken.url,
          {
            client_id: appConstants.OAUTH_JIRA_CLIENT_ID,
            client_secret: appConstants.OAUTH_JIRA_CLIENT_SECRET,
            refresh_token: accessToken.refreshToken,
            grant_type: 'refresh_token',
          },
          {
            headers: {
              Accept: 'application/json',
            },
          },
        )
        .then(async (postResponse) => {
          accessToken.accessToken = postResponse.data.accessToken;
          accessToken.refreshToken = postResponse.data.refresh_token;
          accessToken.expiresAt = formatDate(
            dayjs().add(postResponse.data.expires_in, 'second').toString(),
          );

          await req.models.IntegrationToken.query().findById(accessToken.uid).update({
            accessToken: accessToken.accessToken,
            refreshToken: accessToken.refreshToken,
            expiresAt: accessToken.expiresAt,
          });
        })
        .catch((error) => {
          failed = error;
        });
      if (failed) {
        return res
          .status(failed?.status || 500)
          .json({ error: failed.message, provider: 'jira' })
          .send();
      }
    }

    // CTODO - any external links to the old oauth token owner should be merged
    //   with the newly discovered account owner - move to a helper function
    if (accessToken.ownerUid !== user.uid) {
      await req.models.ExternalEntity.query()
        // .where('source', 'jira')
        // CTODO - should we limit or merge all entities on this user id?
        //         Will there ever be more than one?
        .where('entityType', 'user')
        .where('entityUid', accessToken.ownerUid)
        .update({ entityUid: user.uid });

      // CTODO - What else besides external entities?  What about the user data
      //         on the user record itself?  Org memberships? ANything else?
    }

    let returnData = {};
    let keyData: any = { status: 200 };
    if (!req.locals.user) {
      // If this wasn't pre-authenticated, create them a token
      // TODO: Determine if we should do this here or have TFPinata do it with
      // the TF integration helper
      keyData = await generateAppKeyAndUserData(req.knexDB, user, req.fga);
      returnData = {
        testfiesta: { ...keyData.data },
      };
    }
    return res.status(keyData?.status).json({
      ...returnData,
      jira: {
        accessToken: accessToken.accessToken,
        expiresAt: accessToken.expiresAt,
      },
    });
  }
  return res.status(404).send();
};

const githubOauthResponse = async (
  req: Request & { models: any },
  res: Response,
) => {
  const splitState = (req.query.state as string).split(';');
  const oauthURL = `${splitState[0]}${appConstants.OAUTH_GITHUB_PATH}`;
  const tokenId = splitState[1];

  // TODO - refactor to avoid the "Xmas tree" with await (see JIRA)
  axios
    .post(
      oauthURL,
      {
        client_id: appConstants.OAUTH_GITHUB_CLIENT_ID,
        client_secret: appConstants.OAUTH_GITHUB_CLIENT_SECRET,
        code: req.query.code,
        grant_type: 'authorization_code',
      },
      {
        headers: {
          Accept: 'application/json',
        },
      },
    )
    .then(async (postResponse) => {
      const oauthTokenData = {
        accessToken: postResponse.data.accessToken,
        refresh_token: postResponse.data.refresh_token,
        url: oauthURL,
        expiresAt: formatDate(
          dayjs().add(postResponse.data.expires_in, 'second').toString(),
        ),
        retrievalId: tokenId,
      };

      try {
        // Insert OAuth token data into the database
        await req.models.IntegrationToken.query().insert(oauthTokenData);
        logger.log('info', 'OAuth token data inserted successfully.');
      } catch (insertError) {
        logger.log('error', `Error inserting OAuth token data: ${insertError}`);
        throw insertError; // Rethrow the error to be caught by the outer catch block
      }

      const userResponse = await axios.get('https://api.github.com/user', {
        headers: {
          Authorization: `Bearer ${postResponse.data.accessToken}`,
          Accept: 'application/json',
        },
      });

      if (
        !userResponse.data
        || !userResponse.data.id
        || !userResponse.data.email
        || !userResponse.data.name
      ) {
        throw new Error('GitHub user data is incomplete');
      }

      let internalUserUid;

      const externalUserEntity = await req.models.ExternalEntity.query()
        .where({
          source: 'github',
          entityType: 'user',
          sourceId: userResponse.data.id.toString(),
        })
        .first();

      if (!externalUserEntity) {
        // Query the 'users' table for a user with the given email.
        const existingUser = await req.models.User.query().findOne({
          email: userResponse.data.email,
        });

        if (existingUser) {
          // If a user with the given email exists, update the user's information.
          logger.log('info', 'Updating existing user with GitHub data.');
          await req.models.User.query()
            .patch({
              firstName: userResponse.data.name,
              lastName: userResponse.data.name, // No last name in GitHub
              lastSignInIp: req.ip,
              lastSignInAt: formatDate(dayjs().toString()),
              admin: false,
            })
            .where({ email: userResponse.data.email });

          internalUserUid = existingUser.uid;
        } else {
          // If a user with the given email does not exist, insert a new user.
          logger.log('info', 'Inserting new user with GitHub data.');
          const newUser = await req.models.User.query().insert({
            firstName: userResponse.data.name,
            lastName: userResponse.data.name, // No last name in GitHub
            email: userResponse.data.email,
            lastSignInIp: req.ip,
            lastSignInAt: formatDate(dayjs().toString()),
            admin: false,
          });

          internalUserUid = newUser.uid;
        }

        logger.log(
          'info',
          `Inserting ExternalEntity: ${JSON.stringify({
            source: 'github',
            sourceId: userResponse.data.id.toString(),
            entityType: 'user',
            entityUid: internalUserUid,
          })}`,
        );

        await req.models.ExternalEntity.query().insert({
          source: 'github',
          sourceId: userResponse.data.id.toString(),
          entityType: 'user',
          entityUid: internalUserUid,
        });
      } else {
        internalUserUid = externalUserEntity.entityUid;
      }
      logger.log('info', `Internal User UID: ${internalUserUid}`);
      await req.models.IntegrationToken.query()
        .where({ retrievalId: tokenId })
        .patch({ ownerUid: internalUserUid });
      logger.log('info', `uid: ${tokenId}, ownerUid: ${internalUserUid}`);
      return res.sendFile(
        path.join(__dirname, '../../public/github_success.html'),
      );
    })
    .catch((error) => {
      logger.log(
        'error',
        `Uncaught exception in githubOauthResponse: ${error.response} ${error.message} ${error.stack} ${error} `,
      );
      return res.status(500).send('Internal Server Error');
    });
};

const githubGetNewToken = async (req: Request, res) => {
  const accessToken = await req.models.IntegrationToken.query()
    .where('uid', req.params.tokenId)
    .orWhere('retrievalId', req.params.tokenId)
    .first();

  if (!accessToken) {
    logger.log(
      'info',
      `No accessToken found for tokenId: ${req.params.tokenId}`,
    );
    res.status(404).send('Token not found');
    return;
  }

  if (accessToken.retrievalId !== null) {
    // token is being retrieved for the first time
    let user;

    if (dayjs(accessToken.createdAt).isBefore(dayjs().subtract(5, 'minutes'))) {
      res.status(401).json({ error: 'Token expired' });
      return;
    }
    if (accessToken.ownerUid) {
      user = await req.models.User.query()
        .where('uid', accessToken.ownerUid)
        .first();
      logger.log('info', `User found:${user}`);
    }

    if (!user) {
      logger.log('info', `No user found for ownerUid:${accessToken.ownerUid}`);
      res.status(404).send('No user found');
      return;
    }

    await req.models.IntegrationToken.query()
      .patch({
        ownerUid: user.uid,
        retrievalId: 'null',
      })
      .where('uid', accessToken.uid);

    user = {
      uid: user.uid,
      email: user.email,
      firstName: user.firstName,
      avatar: user.avatar,
      preferences: user.preferences,
    };

    if (user && user.uid) {
      let keyData: any = { status: 200 };
      let returnData = {};

      if (!req.locals || !req.locals.user) {
        keyData = await generateAppKeyAndUserData(req.knexDB, user, req.fga);
        returnData = {
          testfiesta: { ...keyData.data },
        };
      }

      return res.status(keyData.status).json({
        ...returnData,
        github: {
          accessToken: accessToken.accessToken,
          expiresAt: accessToken.expiresAt,
        },
      });
    }
  } else {
    res.status(400).send('invalid token request');
  }
};

const githubOauthToken = async (req: Request, res) => {
  logger.log('info', 'GITHUB OAUTH TOKEN REQUEST');

  const accessToken = await req.models.IntegrationToken.query()
    .where('uid', req.params.tokenId)
    .first();

  if (accessToken) {
    let user;

    if (accessToken.retrieved) {
      const isExpired = dayjs(accessToken.createdAt).isBefore(
        dayjs().subtract(5, 'minutes'),
      );
      if (isExpired) {
        res.status(401).json({ error: 'Token expired' });
        return;
      }

      if (accessToken.ownerUid) {
        user = await req.models.User.query()
          .where('uid', accessToken.ownerUid)
          .first();
        logger.info(`User found: ${JSON.stringify(user)}`);
      }
    } else if (req.locals && req.locals.user) {
      user = req.locals.user;
    }

    if (!user) {
      logger.warn(`No user found for ownerUid: ${accessToken.ownerUid}`);
      res.status(404).send('No user found');
      return;
    }

    await req.models.IntegrationToken.query().where('uid', accessToken.uid).update({
      ownerUid: user.uid,
      retrieved: true,
    });

    user = {
      uid: user.uid,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      avatar: user.avatar ? JSON.parse(user.avatar) : {},
      preferences: user.preferences ? JSON.parse(user.preferences) : {},
    };

    logger.info(`Constructed user data: ${JSON.stringify(user)}`);

    if (dayjs(accessToken.expiresAt).isBefore(dayjs().add(5, 'minutes'))) {
      try {
        const postResponse = await axios.post(
          accessToken.url,
          {
            client_id: appConstants.OAUTH_GITHUB_CLIENT_ID,
            client_secret: appConstants.OAUTH_GITHUB_CLIENT_SECRET,
            refresh_token: accessToken.refreshToken,
            grant_type: 'refresh_token',
          },
          {
            headers: {
              Accept: 'application/json',
            },
          },
        );

        accessToken.accessToken = postResponse.data.accessToken;
        accessToken.refreshToken = postResponse.data.refresh_token;
        accessToken.expiresAt = formatDate(
          dayjs().add(postResponse.data.expires_in, 'second').toString(),
        );

        await req.models.IntegrationToken.query()
          .where('uid', accessToken.uid)
          .update({
            accessToken: accessToken.accessToken,
            refreshToken: accessToken.refreshToken,
            expiresAt: accessToken.expiresAt,
          });
      } catch (error) {
        logger.error(`Error refreshing token:${serializeErr(error)}`);
        res.status(500).json({ error: error.message, provider: 'github' });
        return;
      }
    }

    // Ensure user is properly constructed before using it.
    if (user && user.uid) {
      let keyData: any = { status: 200 };
      let returnData = {};

      if (!req.locals || !req.locals.user) {
        keyData = await generateAppKeyAndUserData(req.knexDB, user, req.fga);
        returnData = {
          testfiesta: { ...keyData.data },
        };
      }

      res.status(keyData.status).json({
        ...returnData,
        github: {
          accessToken: accessToken.accessToken,
          expiresAt: accessToken.expiresAt,
        },
      });
    } else {
      logger.warn(`User data is not properly constructed: ${user}`);
      res.status(500).send('Internal server error');
    }
  } else {
    logger.warn(`No accessToken found for tokenId: ${req.params.tokenId}`);
    res.status(404).send('Token not found');
  }
};

const getAllConfigs = async (req: Request) => {
  // TODO - Limit configs to those the user has access to and sanitize input
  const configs = await req.models.AppConfig.query().select();
  return configs;
};

const getConfig = async (req: Request) => {
  // TODO - Limit configs to those the user has access to and sanitize input
  const config = await req.models.AppConfig.query()
    .where('uid', req.params.configId)
    .first();
  return config;
};

const newConfig = async (req: Request) => {
  // TODO - Limit configs to those the user has access to and sanitize input
  const config = await req.models.AppConfig.query().insert(req.body);
  return config;
};

const upsertConfig = async (req: Request) => {
  // TODO - Limit configs to those the user has access to and sanitize input
  const config = await req.models.AppConfig.query()
    .findById(req.params.configId)
    .patch(req.body);
  if (!config) throw new ApplicationError(StatusCodes.NOT_FOUND, errors.CONFIG_NOT_FOUND);

  return config;
};

const deleteConfig = async (req: Request) => {
  // TODO - Limit configs to those the user has access to and sanitize input
  await req.models.AppConfig.query()
    .where('uid', req.params.configId)
    .patch({ deletedAt: req.knexDB.fn.now() });
};

const getAllCredentials = async (req: Request) => {
  const dbCredentials = await req.models.IntegrationToken.query().where(
    'ownerUid',
    req.locals.user.uid,
  );
  const credentials = {};
  dbCredentials.map(async (token) => {
    if (token.integrationUid) {
      // find the integration by uid
      const integration = await req.models.Integration.query()
        .where('uid', token.integrationUid)
        .first();
      if (!credentials[integration.service]) {
        credentials[integration.service] = [];
      }
      credentials[integration.service].push({
        accessToken: token.accessToken,
        expiresAt: token.expiresAt,
      });
    }
  });
  return credentials;
};

export default {
  signIn: httpHandler(signIn),
  updateProfile: httpHandler(updateProfile),
  signUpToken: httpHandler(signUpToken),
  jiraOauthToken,
  githubOauthResponse,
  githubOauthToken,
  githubGetNewToken,
  getAllConfigs: httpHandler(getAllConfigs),
  getConfig: httpHandler(getConfig),
  newConfig: httpHandler(newConfig),
  upsertConfig: httpHandler(upsertConfig),
  deleteConfig: httpHandler(deleteConfig),
  getAllCredentials: httpHandler(getAllCredentials),
};
