import { ApplicationError, http<PERSON><PERSON><PERSON> } from '@app/lib/http';
import {
  CreateMilestoneDTO,
  GetMilestoneRelationsDTO,
  ListMilestonesDTO,
  UpdateMilestoneDTO,
} from '@app/types/milestone';

import { Request } from 'express';
import { StatusCodes } from 'http-status-codes';
import errorConstants from '@app/constants/errors';
import preferencesService from '@app/models/preferences';
import logger from '@app/config/logger';
import { TestMilestone } from '@app/models/testMilestone';
import { startWorkflow } from '../temporal/client';
import { UpdateMilestoneData } from '../temporal/activities/milestone';
/**
 * create a test milestones
 * POST
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.body.org
 * @param {String} req.body.externalId
 * @param {String} req.body.source
 * @param {String} req.body.name
 * @param {Object} req.body.customFields
 */
const createMilestone = async (req: Request) => {
  // start transaction
  const trx = await req.knexDB.transaction();
  const dto: CreateMilestoneDTO = req.body;
  const defaults = await preferencesService.getDefaults(
    req.sharedKnexDB,
    req.locals.handle.ownerType,
    req.locals.handle.ownerUid,
  );
  try {
    const milestone = await req.models.TestMilestone.create(
      trx,
      req.locals.project.uid,
      {
        ...dto,
        ...(dto.status ? undefined : { status: defaults.milestone.status }),
      },
    );

    const owner = req.locals.handle.ownerUid;
    const { ownerType } = req.locals.handle;

    await req.fga.create({
      objectType: 'milestone',
      objectId: milestone.uid,
      relation: 'owner',
      subjectType: ownerType,
      subjectId: owner,
    });

    if (dto.runIds?.length > 0) {
      // relate the valid runIds
      await req.models.TestMilestone.relatedQuery('testRuns', trx)
        .for(milestone.uid)
        .relate(dto.runIds);
    }

    if (dto.planIds?.length > 0) {
      await req.models.TestMilestone.relatedQuery('testPlans', trx)
        .for(milestone.uid)
        .relate(dto.planIds);
    }

    await trx.commit();

    const param: UpdateMilestoneData = {
      ownerType,
      ownerUid: owner,
      milestoneUids: [milestone.uid],
    };
    await startWorkflow('updateMilestoneWorkflow', {
      taskQueue: 'update-milestone-queue',
      workflowId: `update.milestone.${milestone.uid}.${Date.now()}`,
      args: [param],
    });

    return milestone;
  } catch (error) {
    await trx.rollback();
    throw error;
  }
};

/**
 * get a list of test milestones
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.orgId
 * @param {Number} req.query.per_page
 * @param {Number} req.query.current_page
 */
const getMilestones = async (req: Request) => {
  const dto: ListMilestonesDTO = req.query as any;
  dto.projectUid = req.locals.project.uid;

  const milestones = await req.models.TestMilestone.findPaginated(
    req.knexDB,
    dto,
  );
  return milestones;
};

/**
 * get a list of test milestones by text query
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.orgId
 * @param {String} req.query.query
 */
const searchMilestones = async (req: Request) => {
  const { query } = req.query;

  // Get test milestones for the user's orgs
  const milestones = await req.models.TestMilestone.query()
    .select('*')
    .where('systemType', 'milestone')
    .where('projectUid', req.locals.project.uid)
    .whereNull('deletedAt')
    .whereILike('name', `%${query}%`);

  return { milestones };
};

/**
 * delete a test milestones by milestoneId
 * DELETE
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.milestoneId
 */
const deleteMilestone = async (req: Request) => {
  const [mstone] = await req.models.TestMilestone.deleteByIds(req.knexDB, [
    +req.params.id,
  ]);

  if (mstone) {
    const param: UpdateMilestoneData = {
      milestoneUids: [mstone.uid],
      ownerType: req.locals.handle.ownerType,
      ownerUid: req.locals.handle.ownerUid,
    };
    await startWorkflow('updateMilestoneWorkflow', {
      taskQueue: 'update-milestone-queue',
      workflowId: `update.milestone.${mstone.uid}.${Date.now()}`,
      args: [param],
    });
  }
};

/**
 * get a test milestones by milestoneId
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.milestoneId
 */
const getMilestone = async (req: Request) => {
  const milestone = await req.models.TestMilestone.findOne(req.knexDB, {
    uid: +req.params.id,
    projectUid: req.locals.project.uid,
  });

  if (!milestone) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.MILESTONE_NOT_FOUND,
    );
  }

  await req.models.TestMilestone.populateTags(req.knexDB, milestone);

  return TestMilestone.toDTO(milestone);
};

/**
 * update a test milestones by milestoneId
 * PATCH
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.milestoneId
 */
const updateMilestone = async (req: Request) => {
  const trx = await req.knexDB.transaction();
  const { id } = req.params as any;
  const dto: UpdateMilestoneDTO = req.body;
  try {
    const milestone = await req.models.TestMilestone.updateOne(trx, id, dto);
    if (!milestone) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errorConstants.MILESTONE_NOT_FOUND,
      );
    }
    await trx.commit();
    const param: UpdateMilestoneData = {
      milestoneUids: [milestone.uid],
      ownerType: req.locals.handle.ownerType,
      ownerUid: req.locals.handle.ownerUid,
    };
    await startWorkflow('updateMilestoneWorkflow', {
      taskQueue: 'update-milestone-queue',
      workflowId: `update.milestone.${milestone.uid}.${Date.now()}`,
      args: [param],
    });
    try {
      if (milestone.source === 'testrail') {
        startWorkflow('integrationEntitiesWorkflow', {
          taskQueue: 'integration-entities-queue',
          workflowId: `${req.locals.handle.ownerUid}:milestone:${Date.now()}`,
          args: [
            {
              entityType: 'milestone',
              data: milestone,
              tenantUid: req.locals.handle.ownerUid,
              task: 'updateEntity',
            },
          ],
        });
      }
    } catch (error) {
      logger.error(`Error updating single entity ${milestone.uid}:`, error);
    }
    return milestone;
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const getMilestoneRelations = async (req: Request) => {
  const dto: GetMilestoneRelationsDTO = req.query as any;
  switch (dto.relation) {
    case 'planCount':
      return req.models.TestMilestone.getPlanCount(
        req.knexDB,
        dto.milestoneUids,
      );
    case 'runCount':
      return req.models.TestMilestone.getRunCount(
        req.knexDB,
        dto.milestoneUids,
      );
    case 'caseCount':
      return req.models.TestMilestone.getCaseCount(
        req.knexDB,
        dto.milestoneUids,
      );
    case 'tag':
      return req.models.TestMilestone.getTags(req.knexDB, dto.milestoneUids);
    default:
      throw new ApplicationError(StatusCodes.CONFLICT, '');
  }
};

export default {
  createMilestone: httpHandler(createMilestone),
  getMilestones: httpHandler(getMilestones),
  getMilestone: httpHandler(getMilestone),
  searchMilestones: httpHandler(searchMilestones),
  deleteMilestone: httpHandler(deleteMilestone),
  updateMilestone: httpHandler(updateMilestone),
  getMilestoneRelations: httpHandler(getMilestoneRelations),
};
