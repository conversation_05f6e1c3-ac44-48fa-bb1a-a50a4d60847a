/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-trailing-spaces */
import { httpH<PERSON><PERSON>, ApplicationError } from '@app/lib/http';
import { StatusCodes } from 'http-status-codes';

import { Knex } from 'knex';
import { Request } from 'express';
import dataMapping from '@app/constants/dataMapping';
import dayjs from 'dayjs';
import _ from 'lodash';
import { handlePreferences } from '@app/utils/handlePreferences';
import {
  entityMapping, chartTypes, periodsMapping, charts, 
} from '@app/constants/dashboard';
import logger from '@app/config/logger';
import { TenantConfig } from '@app/types/tenantConfig';

import errorConstants from '@app/constants/errors';

interface QueryFilters {
  startRange?: any;
  endRange?: any;
  milestones?: number[];
  testPlans?: number[];
  testRuns?: number[];
  projects?: number[]
  tags?: number[],
  entityType?: EntityTypes,
  chartType?: string,
  relatedTo?: string[],
  view?: string
}

interface RelatedTo {
  entityType: string,
  fields: { [key: string]: any }[]
}

interface Filter {
  color?: string;
  name?: string;
  conditions: { operator: string; value: any, value2?: any }[];
}
enum Periods {
  last7Days,
  last14Days,
  lastMonth,
}

enum EntityTypes {
  testPlan = 'testPlan',
  milestone = 'milestone',
  testRun = 'testRun',
  testCase = 'testCase',
  testExecution = 'testExecution',
  defect = 'defect',
}

interface DashboardRequestBody {
  charts: Array<object>,
  period?: Periods | Array<string>
}

const getPeriods = (periodKey: string | Array<string>, configs?: TenantConfig) => {
  const currentDate = dayjs(new Date()).format('YYYY-MM-DD');
  const period = {
    startRange: null,
    endRange: currentDate,
  };

  const getPeriodDate = (value, unit) => dayjs(currentDate).subtract(value, unit).format('YYYY-MM-DD');

  if (periodKey) {
    if (Array.isArray(periodKey)) {
      [period.startRange, period.endRange] = periodKey;
    } else {
      period.startRange = getPeriodDate(periodsMapping[periodKey].value, periodsMapping[periodKey].unit);
    }
  } else period.startRange = getPeriodDate(periodsMapping.last7Days.value, periodsMapping.last7Days.unit);

  let endRange = dayjs(period.endRange).endOf('day');

  const days = endRange.diff(period.startRange, 'day');
  
  let isRangeExceeded = false;
  // Check if the date range exceeds the maximum allowed range, return the end date as the maximum range
  if (configs) {
    isRangeExceeded = days > Number(configs.dashboardMaxDateRange);
    if (isRangeExceeded) { endRange = dayjs(period.startRange).add(Number(configs.dashboardMaxDateRange), 'day').endOf('day'); }
  }

  return {
    startRange: period.startRange,
    endRange,
    isOverridden: isRangeExceeded,
  };
};

const getChartData = async (req: Request, entityType: EntityTypes, startRange, endRange, groupBy?) => {
  const { ownerUid, ownerType } = req.locals.handle;

  const preferences = await handlePreferences(req.sharedKnexDB, ownerType, ownerUid);
  
  let statuses = {};
  let priorities = {};

  if (entityType !== EntityTypes.defect) {
    statuses = preferences.statusColors.filter(
      (status) => status.entityType === entityMapping[entityType].preference,
    )
      .reduce((acc, curr) => {
        acc[curr.id] = {
          color: curr.color,
          name: curr.name,
          value: 0,
          selected: true,
          isCompleted: curr.isCompleted,
          isDefault: curr.isDefault,
          isSuccess: curr.isSuccess,
          isFailure: curr.isFailure,    
        };
 
        return acc;
      }, {});
     
    priorities = preferences.priorityColors.filter(
      (priority) => priority.entityType === entityMapping[entityType].preference,
    )
      .reduce((acc, curr) => {
        acc[curr.id] = {
          color: curr.color,
          name: curr.name,
          value: 0,
          selected: true,
          isCompleted: curr.isCompleted,
        };
  
        return acc;
      }, {});
  }

  const {
    milestones, testPlans, projects, tags, testRuns,
  } : QueryFilters = req.query;

  const accessableProjects = [ 
    ...(Array.isArray(projects) ? projects : []),
    ...(Array.isArray(req.locals.accessableProjects) ? req.locals.accessableProjects : []),
  ];

  const entityModel = req.models[entityMapping[entityType].model];
  const fieldsToSelect = [`${entityModel.tableName}.uid`, `${entityModel.tableName}.createdAt`];
  const entityQuery = entityModel.query();

  if (entityType !== EntityTypes.testExecution) { fieldsToSelect.push(`${entityModel.tableName}.name`); }
  if (Array.isArray(accessableProjects) && accessableProjects.length) {
    if (entityType === EntityTypes.defect) {
      const projectUids = accessableProjects.map((id) => Number(id)).filter((id) => !Number.isNaN(id));
      entityQuery.whereRaw(`
        "projectUids" && ARRAY[?]::INTEGER[]
      `, projectUids);
    } else {
      entityQuery.whereIn(`${entityModel.tableName}.projectUid`, accessableProjects);
    }
  }
  if (startRange) { entityQuery.where(`${entityModel.tableName}.createdAt`, '>=', startRange); } 
  if (endRange) { entityQuery.where(`${entityModel.tableName}.createdAt`, '<=', endRange); }

  const systemTypeMappings = {
    milestone: 'milestone',
    testPlan: 'plan',
    testRun: 'run',
  };

  // Remove deleted or archived entities
  if (entityType === EntityTypes.testExecution) {
    entityQuery.innerJoin('tags', 'tags.uid', 'testExecutions.testRunUid')
      .where('tags.systemType', 'run')
      .whereNull('tags.deletedAt')
      .whereNull('tags.archivedAt');
  }

  if ([EntityTypes.milestone, EntityTypes.testPlan, EntityTypes.testRun, EntityTypes.defect].includes(entityType)) {
    entityQuery.whereNull(`${entityModel.tableName}.deletedAt`);
    if (req.query?.view && req.query?.archived === 'true') { entityQuery.whereNotNull(`${entityModel.tableName}.archivedAt`); } else { entityQuery.whereNull(`${entityModel.tableName}.archivedAt`); }
  }

  if (entityType === EntityTypes.testCase) { 
    entityQuery.whereNull(`${entityModel.tableName}.deletedAt`)
      .where(`${entityModel.tableName}.active`, true);

    if (tags?.length) {
      entityQuery.innerJoin('testCaseTags', 'testCaseTags.testCaseRef', 'testCases.testCaseRef')
        .whereIn('testCaseTags.tagUid', tags).groupBy('testCases.uid');
    }
  }
  // ========== Filters ========== 
  // ========== Defects =============
  if (entityType === EntityTypes.defect) {
    // Filter using testPlans
    if (Array.isArray(testPlans) && testPlans.length) {
      entityQuery
        .joinRelated('executions')
        .innerJoin('testPlanRuns', 'testPlanRuns.runUid', 'executions.testRunUid')
        .whereIn('testPlanRuns.planUid', testPlans)
        .groupBy('defects.uid');
    }
    // Filter using testRuns
    if (Array.isArray(testRuns) || Array.isArray(milestones)) {
      let runUids = [];
      if (Array.isArray(testRuns) && testRuns.length) {
        runUids = [...runUids, ...testRuns];
      }

      if (Array.isArray(milestones) && milestones.length) {
        const milestoneIds = milestones.map((item: any) => Number.parseInt(item)).filter(Boolean);
        const milestoneRunIds = (await req.models.TestMilestone.relatedQuery('testRuns').for(
          Array.from(milestoneIds),
        )).map((item) => item.uid);
    
        runUids = [...runUids, ...milestoneRunIds];
      }

      entityQuery
        .joinRelated('executions')
        .whereIn('executions.testRunUid', runUids)
        .groupBy('defects.uid');
    }
  }

  // ========== Test Plans ==========
  // Filter using milestones  & Tags
  if (entityType === EntityTypes.testPlan && milestones?.length) {
    entityQuery.leftJoin('testMilestonePlans', 'tags.uid', 'testMilestonePlans.planUid')
      .groupBy('tags.uid')
      .whereIn('testMilestonePlans.milestoneUid', milestones);
  }

  if ([EntityTypes.testPlan, EntityTypes.milestone].includes(entityType) && tags?.length) {
    entityQuery.whereRaw(`
    "customFields"->'tagUids' IS NOT NULL 
    AND EXISTS (
      SELECT 1 
      FROM jsonb_array_elements_text("customFields"->'tagUids') AS tag
      WHERE tag::INT =  ANY (?::INT[])
    )
    `, [tags]); 
  }

  // ========== Test Runs ==========
  // Filter using both Milestones & Test Plans. This section applies filters
  if (entityType === EntityTypes.testRun && (milestones?.length || testPlans?.length)) {
    if (milestones?.length && Array.isArray(milestones)) {
      entityQuery.leftJoin('testMilestoneRuns', 'testMilestoneRuns.runUid', 'tags.uid')
        .where('tags.systemType', 'run')
        .whereIn('testMilestoneRuns.milestoneUid', milestones);
    }

    if (testPlans?.length && Array.isArray(testPlans)) { 
      entityQuery
        .where('tags.systemType', 'run')
        .leftJoin('testPlanRuns', 'testPlanRuns.runUid', 'tags.uid')
        .whereIn('testPlanRuns.planUid', testPlans); // Filter by testPlans
    }
  }
  // ========== Milestone ==========
  // No Filters Applied here.

  // ========== Test Case ==========
  // No Filters Applied here.

  // ========== Test Executions ==========
  // Filter using both Milestones & Test Plans & Tags
  if (entityType === EntityTypes.testExecution && (milestones || testPlans)) {
    if (Array.isArray(milestones)) {
      const milestoneIds = milestones.map((item: any) => Number.parseInt(item)).filter(Boolean);
      const runIds = (await req.models.TestMilestone.relatedQuery('testRuns').for(
        Array.from(milestoneIds),
      )).map((item) => item.uid);
      entityQuery.whereIn('testRunUid', runIds);
    }

    if (Array.isArray(testPlans)) {
      entityQuery
        .innerJoin('tags as runs', 'runs.uid', 'testExecutions.testRunUid')
        .where('runs.systemType', 'run') // Ensure we're only dealing with runs
        .innerJoin('testPlanRuns', 'testPlanRuns.runUid', 'runs.uid') // Join testRuns with testPlanRuns
        .whereIn('testPlanRuns.planUid', testPlans); // Filter by testPlans
    }
  }

  if (EntityTypes.testExecution === entityType && tags?.length) {
    entityQuery.whereRaw(`
    "testExecutions"."customFields"->'tags' IS NOT NULL 
    AND EXISTS (
      SELECT 1
      FROM jsonb_array_elements(("testExecutions"."customFields"->'tags')::jsonb) as tag
      WHERE (tag->>'uid')::INT = ANY (?::INT[])
    )
    `, [tags]); 
  }

  if (EntityTypes.testExecution === entityType && testRuns?.length) {
    entityQuery.whereIn('testRunUid', testRuns);
  }
  // Conditionally handle fields based on entity type:
  // - For 'milestone' and 'testPlan', select 'status' and 'priority' from the 'customFields' JSON object.
  // - For 'testRun' and 'testCase', directly select 'status' and 'priority' from the corresponding columns.
  // - For defects, select labels from the 'customFields' JSON object.

  if ([EntityTypes.milestone, EntityTypes.testPlan, EntityTypes.testRun].includes(entityType)) {
    entityQuery.where('systemType', systemTypeMappings[entityType]);
    if (Array.isArray(groupBy)) {
      groupBy.forEach((g) => {
        if (['uid'].includes(g.field)) { fieldsToSelect.push(`${entityModel.tableName}.${g.field}`); } else {
          fieldsToSelect.push(req.knexDB.raw(
            `"${entityModel.tableName}"."customFields"->>'${g.field}' as ${g.field}`,
          ) as any);
        }
      });
    }
  } else if (Array.isArray(groupBy)) {
    groupBy.forEach((g) => {
      if (['status', 'priority', 'source', 'externalId', 'uid', 'testCaseRef'].includes(g.field)) { fieldsToSelect.push(`${entityModel.tableName}.${g.field}`); } else {
        fieldsToSelect.push(req.knexDB.raw(
          `"${entityModel.tableName}"."customFields"->>'${g.field}' as ${g.field}`,
        ) as any);
      }
    });
  }
  const fetchedEntities = (await entityQuery.select(fieldsToSelect)).map((item) => ({
    ...item,
    ...(item.status ? { status: statuses[item.status]?.name, isCompletedStatus: statuses[item.status]?.isCompleted } : {}),
    ...(item.priority ? { priority: priorities[item.priority]?.name, isCompletedPriority: priorities[item.priority]?.isCompleted } : {}),

  }));
  return fetchedEntities;
};

const patchDashboard = async (req: Request) => {
  let uid = req.params.id || undefined;
  if (uid) {
    await req.models.Dashboard.query().findById(uid).patch({
      name: req.body.name,
      body: req.body.body,
    });
  } else {
    // TODO - fix with Objection model returning UUID - now integer
    await req.knexDB
      .raw(
        'SET @uuid = UUID(); '
          + 'INSERT INTO '
          + '`dashboards` (`uid`, `body`, `createdBy`, `name`, `orgUid`) '
          + 'VALUES (@uuid, ?, ?, ?, ?); '
          + 'SELECT @uuid;',
        [
          JSON.stringify(req.body.body),
          req.locals.user.uid,
          req.body.name,
          req.locals.handle.ownerUid,
        ],
      )
      .then((result: any) => {
        // What an ugly structure this returns...
        uid = result[0][2][0]['@uuid'];
      });
  }
  return {
    uid,
  };
};

const getRelations = async (req: Request, entityType: EntityTypes, entityValues: any[], relatedTo: RelatedTo[]) => {
  let etValues = entityValues;
  const entityUids = entityValues.map((item) => item.uid);
  
  // TODO: Use filters to refine Y-axis values for the relation charts.
  if (entityType === EntityTypes.testRun) {
    const executions : any = await req.models.TestExecution.query()
      .where({
        deletedAt: null,
      })
      .whereIn('testRunUid', entityUids)
      .withGraphFetched('defects')
      .modifyGraph('defects', (builder) => {
        builder.where('defectExecutions.deletedAt', null);
      });
    relatedTo.forEach((relation) => {
      if (relation.entityType === 'defectExecution') {
        etValues.reduce((acc, curr) => {
          const currExecutions = executions
            .filter((element) => element.testRunUid === curr.uid);
    
          curr.defectExecution = currExecutions.reduce((sum, element) => sum + (element.defects?.length || 0), 0);
          acc.push(curr);
          return acc;
        }, []);
      }

      if (relation.entityType === 'testExecution') {
        etValues.reduce((acc, curr) => {
          const currExecutions = executions
            .filter((element) => element.testRunUid === curr.uid);
          curr.testExecution = currExecutions.length;
          acc.push(curr);
    
          return acc;
        }, []);
      }
    });
  }
    
  if (entityType === EntityTypes.testPlan) {
    const plans: any = await req.models.TestPlan.query().where({
      systemType: 'plan',
      'tags.deletedAt': null,
    }).whereIn('tags.uid', entityUids)
      .toKnexQuery()
      .leftJoin(
        'testRuns',
        req.knexDB.raw(
          'tags.uid="testRuns"."testPlanUid" and "testRuns"."deletedAt" is null',
        ),
      )
      .leftJoin('testMilestonePlans', 'tags.uid', 'testMilestonePlans.planUid')
      .groupBy('tags.uid')
      .select(
        req.knexDB.raw(
          'count("testRuns".uid) as "testRunCount",count("testMilestonePlans"."planUid") as "testMilestoneCount"',
        ),
      );

    etValues = etValues.map((item) => {
      const rpCounts = plans.find((e) => e.uid === item.uid);

      const updatedItem = { ...item };

      relatedTo.forEach((relation) => {
        if (relation.entityType === 'milestone') {
          updatedItem.milestone = Number.parseInt(rpCounts?.testMilestoneCount ?? 0);
        }
  
        if (relation.entityType === 'testRun') {
          updatedItem.testRun = Number.parseInt(rpCounts?.testRunCount ?? 0);
        }
      });
      
      return updatedItem;
    });
  }
  
  if (entityType === EntityTypes.testCase) {
    const caseRefs = etValues.map((item) => item.testCaseRef);
    for (const relation of relatedTo) {
      if (relation.entityType === 'testExecution') {
        const fieldsToSelect = relation.fields
          .map((field) => `COALESCE("customFields"->>'${field.name}', '0.0')::float AS "${field.name}"`);
        
        const executions: any = await req.models.TestExecution
          .query()
          .whereIn('testCaseRef', caseRefs ?? [])
          .whereNull('deletedAt')
          .select(
            req.models.TestExecution.raw(fieldsToSelect.join(', ')),
            'testCaseRef',
          );
        
        const obj = etValues.reduce((acc, curr) => {
          if (!acc[curr.testCaseRef]) {
            acc[curr.testCaseRef] = {
              ...curr,
            };
          }
          return acc;
        }, {});

        for (const e of executions) {
          const { testCaseRef, ...executionWithoutTestCaseRef } = e;
        
          if (!obj[e.testCaseRef]?.[relation.entityType]) {
            obj[e.testCaseRef][relation.entityType] = [];
          }
        
          obj[e.testCaseRef][relation.entityType].push(executionWithoutTestCaseRef);
        }

        etValues = Object.values(obj);
      }
    }
  }
  return etValues;
};
const getDataAndTypes = async (
  db: Knex,
  orgId: any,
  dashboard: any,
  lastUpdates: any,
) => {
  const returnData: { [key: string]: any } = {};
  // TODO - add user-defined relationships and transitive relationships

  try {
    if (dashboard.body && dashboard.body.dataTypes) {
      // eslint-disable-next-line no-restricted-syntax
      for (const dataType of dashboard.body.dataTypes) {
        // const fromTime = lastUpdates[dataType];
        dayjs().subtract(7, 'year').format('YYYY-MM-DD HH:mm:ss');
        // CTODO - pass and limit
        // CTODO - Limit "event" data types based off of date filter passed in
        //         the qs (7 days default, don't allow > 30)
        // TODO - limit what we pull -
        // - pull based on Project UID
        // - pull UIDs from OpenFGA for ownership ?? < no way
        // Also use the Objection model here
        // Handle what field we limit by: createdAt, external_createdAt,
        //                                completed_at, etc

        // eslint-disable-next-line no-await-in-loop
        const dataResults = await db.from(dataType);
        // TODO...
        // .where("createdAt", ">", fromTime);
        returnData[dataType] = dataResults;
      }
    }

    return {
      data: returnData,
      relationships: dataMapping.INTERNAL_RELATIONSHIP_MAPPING,
    };
  } catch (err) {
    logger.log('info', `Unable get to data: ${err}`);
    return null;
  }
};
const getDashboards = async (req: Request) => {
  const dashboards = await req.models.Dashboard.query()
    .select('*')
    .whereNull('deletedAt')
    .whereNull('entityType');

  return dashboards.map(({
    name, uid, createdBy, systemDefault, editable, 
  }) => ({
    uid,
    name,
    systemDefault,
    editable,
    createdBy,
  }));
};

const applyFilters = (filters: Filter[], rec, fieldName) => {
  if (!rec[fieldName]) return { match: true, color: null, name: null };

  if (!filters || filters.length === 0) {
    return { match: true, color: null, name: null };
  }
  const record = rec[fieldName];
  for (const filter of filters) {
    const { color, name } = filter;
    let isMatch = false;
    for (const condition of filter.conditions) {
      const { value2, value, operator } = condition;
      if (typeof value === 'object') {
        // Custom case: If value is an object, it represents our custom value (e.g., completedStatus, failedStatus, etc.).
        if (value.value === 'completedStatus') {
          isMatch = operator === '=' ? rec.isCompletedStatus : !rec.isCompletedStatus;
        }
        if (value.value === 'completedPriority') {
          isMatch = operator === '=' ? rec.isCompletedPriority : !rec.isCompletedPriority;
        }
      } else {
        const pNumber = Number.parseInt(record);
        const recordLower = String(record).toLowerCase();
        const valueLower = String(value).toLowerCase();
        
        switch (operator) {
          case 'between':
            isMatch = pNumber >= Number.parseInt(value) && pNumber <= Number.parseInt(value2);
            break;
          case '=':
            isMatch = recordLower === valueLower;
            break;
          case '!=':
            isMatch = recordLower !== valueLower;
            break;
          case '<':
            isMatch = pNumber < value;
            break;
          case '>':
            isMatch = pNumber > value;
            break;
          case '<=':
            isMatch = pNumber <= value;
            break;
          case '>=':
            isMatch = pNumber >= value;
            break;
          default:
            isMatch = false;
        }
      }

      if (isMatch) {
        return { match: true, color: color || null, name: name || null };
      }
    }
  }

  return { match: false, color: null };
};

const getChart = async (req: Request, chartType: any, entityType: EntityTypes, startRange: any, endRange: any, groupBy: any[]) => {
  const { ownerType, ownerUid } = req.locals.handle;

  const preferences = await handlePreferences(req.sharedKnexDB, ownerType, ownerUid);
  const integrationLabels = await req.models.Tag.query()
    .where({ deletedAt: null, systemType: 'tag' })
    .whereRaw('"entityTypes" @> ?', [['defects']]);
    
  const chart = {};
  
  const generateRandomHexColor = () => `#${Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')}`;

  let statuses = {};
  let priorities = {};
  let labels = {};
  
  if (entityType !== EntityTypes.defect) {
    statuses = preferences.statusColors.filter(
      (status) => status.entityType === entityMapping[entityType].preference,
    )
      .reduce((acc, curr) => {
        acc[curr?.name.toLowerCase()] = {
          color: curr.color,
          value: 0,
        };
  
        return acc;
      }, {});
  
    priorities = preferences.priorityColors.filter(
      (priority) => priority.entityType === entityMapping[entityType].preference,
    )
      .reduce((acc, curr) => {
        acc[curr?.name.toLowerCase()] = {
          color: curr.color,
          value: 0,
        };
  
        return acc;
      }, {});
  }
  if (entityType === EntityTypes.defect) {
    labels = integrationLabels.reduce((acc, curr) => {
      acc[curr.slug] = {
        color: curr.customFields.color,
        name: curr.slug,
        value: 0,
        selected: true,
      };
      return acc;
    }, {});
  }

  const results = await getChartData(req, entityType, startRange, endRange, groupBy);
  if (chartTypes.NonAxisBasedCharts.includes(chartType)) {
    groupBy?.forEach((element) => {
      const { filters } = element;
      const data = results.reduce((acc, curr) => {
        const fieldName = element.field;
        
        if (!acc[fieldName]) {
          acc[fieldName] = {};
        }
        
        if (filters?.length) {
          const { match, color, name } = applyFilters(filters, curr, element.field);
          if (match) {
            if (!acc[fieldName][name]) { acc[fieldName][name] = { value: 0, color: null }; }

            acc[fieldName][name].value++;
            if (color) {
              acc[fieldName][name].color = color;
            }
          }
        } else if (fieldName === 'status') {
          acc[fieldName] = statuses;
            
          const st = curr[fieldName]?.toLowerCase();
          if (acc[fieldName][st]) { acc[fieldName][st].value++; }
        } else if (fieldName === 'priority') {
          acc[fieldName] = priorities;

          const pr = curr[fieldName]?.toLowerCase();
          if (acc[fieldName][pr]) { acc[fieldName][pr].value++; }
        } else if (fieldName === 'labels') {
          acc[fieldName] = labels;

          const itemLabels = JSON.parse(curr[fieldName] || '[]');

          itemLabels.forEach((label : Record<string, any>) => {
            if (acc[fieldName][label.name]) {
              acc[fieldName][label.name].value++;
            }
          });
        } else {
          if (!acc[fieldName][curr[fieldName]]) { acc[fieldName][curr[fieldName]] = { value: 0, color: generateRandomHexColor() }; }

          acc[fieldName][curr[fieldName]].value++;
        }
        return acc;
      }, {});

      chart[element.field] = data[element.field];
    });
    
    return chart;
  }
  if (chartTypes.XYCoordinateCharts.includes(chartType)) {
    const diffDays = dayjs(endRange).diff(startRange, 'day') + 1;
    
    const chartData = {};
    
    for (let i = 0; i < diffDays; i++) {
      const date = dayjs(startRange).add(i, 'day').format('MM/DD');
      chartData[date] = {};
      
      groupBy?.forEach((element) => {
        const fieldName = element.field;
        chartData[date][fieldName] = {};
        
        if (element.filters?.length) {
          const dt = element.filters.reduce((acc, curr) => {
            if (!acc[curr.name]) {
              acc[curr.name] = {
                color: curr.color,
                value: 0,
              };
            }
            return acc;
          }, {});
          chartData[date][fieldName] = dt;
        } else if (fieldName === 'status') {
          Object.assign(chartData[date][fieldName], _.cloneDeep(statuses));
        } else if (fieldName === 'priority') {
          Object.assign(chartData[date][fieldName], _.cloneDeep(priorities));
        } else if (fieldName === 'labels') {
          Object.assign(chartData[date][fieldName], _.cloneDeep(labels));
        }
      });
    }
  
    results.forEach((item) => {
      const itemDate = dayjs(item.createdAt).format('MM/DD');
      if (!chartData[itemDate]) return;
      
      groupBy?.forEach((element) => {
        const fieldName = element.field;
        const fieldValue = item[fieldName]?.toLowerCase();
        
        if (element.filters?.length) {
          const { match, color, name } = applyFilters(element.filters, item, fieldName);
          if (match) {
            if (!chartData[itemDate][fieldName][name]) {
              chartData[itemDate][fieldName][name] = {
                value: 0,
                color: color || generateRandomHexColor(),
              };
            }
            chartData[itemDate][fieldName][name].value++;
          }
        } else if (fieldName === 'status' || fieldName === 'priority') {
          if (chartData[itemDate][fieldName][fieldValue]) {
            chartData[itemDate][fieldName][fieldValue].value++;
          }
        } else if (fieldName === 'labels') {
          // Defects might have multiple labels, so we need to loop through them and increment the value for each label.
          // The labels field should be parsed as JSON or default to an empty array if not present.
          const labels = JSON.parse(item[fieldName] || '[]');
          labels.forEach((label : Record<string, any>) => {
            if (!chartData[itemDate][fieldName][label.name]) {
              chartData[itemDate][fieldName][label.name] = {
                value: 0,
                color: labels[label.name]?.color || generateRandomHexColor(),
              };
            }
            chartData[itemDate][fieldName][label.name].value++;
          });
        } else {
          if (!chartData[itemDate][fieldName][fieldValue]) {
            chartData[itemDate][fieldName][fieldValue] = {
              value: 0,
              color: generateRandomHexColor(),
            };
          }
          chartData[itemDate][fieldName][fieldValue].value++;
        }
      });
    });

    return chartData;
  }
};

const getDashboard = async (req: Request) => {
  const { uid } = req.params;
  const { view } = req.query;

  const dashboard = await req.models.Dashboard.query()
    .findOne({
      ...(uid && !view ? { uid } : { systemDefault: true }),
      ...(view && req.locals.project ? { entityType: view, projectUid: req.locals.project.uid, systemDefault: false } : {}),
      ...(!view ? { entityType: null } : {}), // Dashboards related to entities (run/plan) should not appear in the main dashboard list or be viewable outside their specific entity context.
    })
    .whereNull('deletedAt')
    .select(['body', 'updatedAt', 'uid']);
  
  if (view) { req.query.projects = [String(req.locals.project.uid)]; } // For run/plan view, enforce project filter using localProjectUid

  if (!dashboard) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.DASHBOARD_NOT_FOUND,
    );
  }
  
  const { startRange, endRange, isOverridden } = getPeriods(dashboard.body?.period, req.configs);

  const response = {
    charts: [],
    period: dashboard.body?.period ?? Object.keys(periodsMapping)[0],
    dashboardUid: dashboard.uid,
    isOverridden,
  };

  if (isOverridden) {
    response.period[1] = endRange;
    const newPeriod = [startRange, endRange];
    await req.models.Dashboard.query().patch({
      body: {
        ...dashboard.body,
        ...(dashboard.body.period ? { period: newPeriod } : {}),
      },
    }).where('uid', dashboard.uid);
  }

  if (dashboard.body && Array.isArray(dashboard.body.charts)) {
    for (const chart of dashboard.body.charts) {
      if (!Object.keys(entityMapping).includes(chart.entityType)) continue;

      if (chart.relatedTo && chart.relatedTo?.length) {
        const entityData = await getChartData(req, chart.entityType, startRange, endRange, chart.groupBy);
        const relations = await getRelations(req, chart.entityType, entityData, chart.relatedTo); // TODO process each relatedTo entity individually within the forEach loop, instead of handling all entities at once.
        chart.relatedTo.forEach((element : RelatedTo) => {
          if (!chart.entityData) { chart.entityData = {}; }
          chart.entityData[element.entityType] = chart.type === 'boxplotChart' ? relations : relations.reduce((acc, curr) => { // TODO: Boxplot response should be an array. Find a way to return all data with the same structure.
            acc[curr.name] = curr[element.entityType];
            return acc;
          }, {});
        });

        response.charts.push(chart);
        continue;
      }
            
      const data = await getChart(req, chart.type, chart.entityType, startRange, endRange, chart.groupBy);
      chart.entityData = data;

      response.charts.push(chart);
      // TODO we should handle the case of burnOut .. so we have to indicate the total of isCompleted / Not completed per day ?? 
    }
  }
  return response;
};

const dashboardOverview = async (req: Request) => {
  const projects = await req.models.Project.query()
    .select(['name', 'uid', 'key'])
    .whereNull('deletedAt')
    .whereNull('archivedAt')
    .modify((builder) => {
      if (req.locals.accessableProjects && Array.isArray(req.locals.accessableProjects)) {
        builder.whereIn('uid', req.locals.accessableProjects);
      }
    });

  const milestones = await req.models.TestMilestone.query()
    .select(['name', 'uid', 'projectUid'])
    .where({ deletedAt: null, archivedAt: null, systemType: 'milestone' });
  const testPlans = await req.models.TestPlan.query()
    .withGraphFetched('milestones as milestoneUids')
    .modifyGraph('milestoneUids', (builder) => {
      builder.select('tags.uid').where({ systemType: 'milestone' });
    })
    .select(['name', 'uid', 'projectUid'])
    .where({ deletedAt: null, archivedAt: null, systemType: 'plan' });

  return {
    milestones,
    testPlans,
    projects,
  };
};

// == updateDashboard Method: This method handles updating dashboard settings, such as the dashboard name (to be implemented), setting the default dashboard, and toggling the 'editable' property. 
// Note: The 'UpdateCharts' method should strictly be used for managing charts (adding, deleting, and rearranging).

const updateDashboard = async (req: Request) => {
  const { isDefault, editable, name } = req.body;
  const { uid } = req.params;
  const trx = await req.knexDB.transaction();

  try {
    const dashboard = await req.models.Dashboard.query(trx).findOne({
      uid,
    })
      .select(['name', 'editable', 'systemDefault'])
      .whereNull('deletedAt');

    if (!dashboard) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errorConstants.DASHBOARD_NOT_FOUND,
      );
    }
        
    if (dashboard.systemDefault === true && !isDefault) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        errorConstants.DEFAULT_DASHBOARD_RESTRICT,
      );
    }

    if (dashboard.systemDefault === false && isDefault) {
      await req.models.Dashboard.query(trx).patch({
        systemDefault: false,
      }).whereNull('deletedAt');
    }

    await req.models.Dashboard.query(trx).patch({
      name,
      systemDefault: isDefault,
      editable,
    }).where('uid', uid);

    await trx.commit();
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};
const updateCharts = async (req: Request) => {
  const { charts, period } : DashboardRequestBody = req.body;
  const { uid } = req.params;
  const { returnResult } = req.query;
  try {
    const dashboard = await req.models.Dashboard.query()
      .findOne({
        ...(uid ? { uid } : { systemDefault: true }),
      }).select(['editable', 'uid', 'body']);
      
    if (!dashboard) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errorConstants.DASHBOARD_NOT_FOUND,
      );
    }    

    if (!dashboard.editable) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        errorConstants.DASHBOARD_NOT_EDITABLE,
      );
    }

    if (dashboard && dashboard.editable) {
      await req.models.Dashboard.query().patch({
        body: {
          ...dashboard.body,
          ...(charts ? { charts } : {}),
          ...(period ? { period } : {}),
        },
      }).where('uid', dashboard.uid);
      
      if (returnResult === 'true') { return await getDashboard(req); }
    }
  } catch (error) {
    logger.error('Error while updating dashboard :', error);
  }
};

const deleteDashboard = async (req: Request) => {
  const { uid } = req.params;

  const trx = await req.knexDB.transaction();

  try {
    const dashboard = await req.models.Dashboard.query(trx).findOne({ uid }).whereNull('deletedAt');

    if (!dashboard) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errorConstants.DASHBOARD_NOT_FOUND,
      );
    }
    
    if (dashboard.systemDefault === true) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        errorConstants.DEFAULT_DASHBOARD_RESTRICT,
      );
    }
    
    await req.models.Dashboard.query(trx)
      .findOne({ uid, systemDefault: false })
      .patch({ deletedAt: req.knexDB.fn.now() });

    await trx.commit();
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const createDashboard = async (req: Request) => {
  const { name, isDefault } = req.body;
  const { ownerUid } = req.locals.handle;
  const payload = {
    name,
    createdBy: ownerUid,
    ...(isDefault ? { systemDefault: isDefault } : {}),
    editable: false,
    body: { charts },
  };

  try {
    const trx = await req.knexDB.transaction();
    if (isDefault) {
      await req.models.Dashboard.query(trx).patch({
        systemDefault: false,
      });
    }
    const dashboard = await req.models.Dashboard.query(trx).insert(payload).returning('*');
    await trx.commit();
    return dashboard;
  } catch (err) {
    logger.log('error', `Error inserting Dashboard: ${err}`);
    throw err;
  }
};

const fetchSingleDashboardChart = async (req: Request) => {
  const { view }: QueryFilters = req.query;
  const { uid, chartId } = req.params;
  
  const { charts, period } = await req.models.Dashboard.query()
    .findOne({
      ...(uid && !view ? { uid } : { systemDefault: true }),
      ...(view ? { entityType: view, projectUid: req.locals.project.uid, systemDefault: false } : {}),
    })
    .whereNull('deletedAt')
    .select([
      req.models.Dashboard.raw('body->>\'charts\' as charts'),
      req.models.Dashboard.raw('body->>\'period\' as period'),
    ]) as any;
    
  const parsedCharts = JSON.parse(charts);
  const chart = parsedCharts.find((element) => element.id === Number.parseInt(chartId));
    
  let parsedPeriod = period;
  if (Array.isArray(period)) {
    parsedPeriod = period;
  } else if (typeof period === 'string') {
    try {
      parsedPeriod = JSON.parse(period);
    } catch (e) {
      parsedPeriod = period;
    }
  }
  const { startRange, endRange } = getPeriods(parsedPeriod);

  if (chart?.relatedTo?.length) {
    const entityData = await getChartData(req, chart.entityType, startRange, endRange, chart.groupBy); // TODO move relation charts to getChart method
    const relations = await getRelations(req, chart.entityType, entityData, chart.relatedTo);
    const relationResults = {};
    chart.relatedTo.forEach((element : RelatedTo) => {
      relationResults[element.entityType] = chart.type === 'boxplotChart' ? relations : relations.reduce((acc, curr) => {
        acc[curr.name] = curr[element.entityType];
        return acc;
      }, {});
    });

    return relationResults;
  }

  const data = await getChart(req, chart.type, chart.entityType, startRange, endRange, chart.groupBy) as any;
  
  return data;
};

const getAllDashboards = async (req: Request) => {
  const orgId = req.locals.handle.ownerUid;
  const lastUpdates = req.params.update
    ? JSON.parse(decodeURIComponent(req.params.update))
    : {};
  // get all dashboards for given Org
  const dashboards = await req.models.Dashboard.query();
  // TODO - Create a new default if not found.
  // if (dashboards.length < 1) {
  //  return res.status(404).send();
  // }

  let dashboard;
  // TODO - generate a default dashboard preference on joining any org and user
  //        creation
  if (
    req.locals.user.preferences
    && req.locals.user.preferences[orgId]
    && req.locals.user.preferences[orgId]?.defaultDashboard
  ) {
    dashboard = await req.models.Dashboard.query()
      // .where("orgUid", orgId)
      .where('uid', req.locals.user.preferences[orgId].defaultDashboard)
      .first();
  } else {
    dashboard = await req.models.Dashboard.query()
      // .where("orgUid", orgId)
      .where('systemDefault', true)
      .first();
  }
  // TODO - Create a new default if not found.
  // if (dashboard.length < 1) {
  //  return res.status(404).send();
  // }
  const defaultDashboard = {
    uid: dashboard.uid,
    body: dashboard.body,
    name: dashboard.name,
  };

  // Only get the data and types for the dashboard we're looking at.
  const dataAndTypes = await getDataAndTypes(
    req.knexDB,
    orgId,
    dashboard,
    lastUpdates,
  );
  if (dataAndTypes !== null) {
    return {
      defaultDashboard,
      dashboards,
      ...dataAndTypes,
    };
  }
};

const dataTypes = async (req: Request) =>
  // CTODO - understand usage and abstract/combine with above
  // eslint-disable-next-line implicit-arrow-linebreak
  dataMapping.INTERNAL_RELATIONSHIP_MAPPING;
const dataByType = async (req: Request) => {
  // CTODO - abstract and combine with above
  const returnData: { [key: string]: any } = {};
  // const period: number =
  //   !Number.isNaN(req.query.period) && (req.query.period as any) < 61
  //     ? (req.query.period as unknown as number)
  //     : 700; // CTODO - back to 7
  // CTODO - separate events and long lived entities when only returning over a period
  // CTODO - understand when long lived entities are marked as closed/completed
  // eslint-disable-next-line no-restricted-syntax
  for (const type of req.query.types as string[]) {
    // TODO - limit what we pull -
    // - pull based on Project UID
    // - pull UIDs from OpenFGA for ownership ?? < no way
    // Also use the Objection model here
    // Handle what field we limit by: createdAt, external_createdAt,
    //                                completed_at, etc

    // eslint-disable-next-line no-await-in-loop
    const dataResults = await req.knexDB(type);
    // TODO...
    // .where(
    //  "createdAt",
    //  ">",
    //  dayjs().subtract(period, "day").format("YYYY-MM-DD HH:mm:ss"),
    // );
    returnData[type] = dataResults;
  }

  return returnData;
};

export default {
  patchDashboard: httpHandler(patchDashboard),
  getDashboard: httpHandler(getDashboard),
  dashboardOverview: httpHandler(dashboardOverview),
  updateDashboard: httpHandler(updateDashboard),
  getAllDashboards: httpHandler(getAllDashboards),
  dataTypes: httpHandler(dataTypes),
  dataByType: httpHandler(dataByType),
  fetchSingleDashboardChart: httpHandler(fetchSingleDashboardChart),
  getDashboards: httpHandler(getDashboards),
  createDashboard: httpHandler(createDashboard),
  deleteDashboard: httpHandler(deleteDashboard),
  updateCharts: httpHandler(updateCharts),
};
