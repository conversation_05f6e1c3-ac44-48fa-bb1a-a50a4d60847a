import { ApplicationError, httpH<PERSON><PERSON> } from '@app/lib/http';

import { ConfigurationDTO } from '@app/types/configuration';
import { Request } from 'express';
import { StatusCodes } from 'http-status-codes';
import { UniqueViolationError } from 'objection';
import errorConstants from '@app/constants/errors';

const createConfiguration = async (req: Request) => {
  const { name, options, description } = req.body;
  const trx = await req.knexDB.transaction();
  try {
    const config = await req.models.Configuration.create(trx, {
      name,
      options,
      description,
      projectUid: req.locals.project.uid,
    });
    await trx.commit();
    return config;
  } catch (err) {
    await trx.rollback();
    if (err instanceof UniqueViolationError) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        errorConstants.DUPLICATE_CONFIG,
      );
    }
    throw err;
  }
};

const getConfigurations = async (req: Request) => {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { per_page, current_page, query } = req.query;
  const pagination: any = {};
  const perPage: number = Number(per_page) || 10;
  const page: number = Math.max(Number(current_page) || 1, 1);
  const offset = (page - 1) * perPage;

  const configurations = await req.models.Configuration.query()
    .whereNull('deletedAt')
    .where('projectUid', req.locals.project.uid)
    .where('systemType', 'config')
    .withGraphFetched('[options(isConfigOption)]')
    .where((builder) => {
      if (query) {
        builder.whereILike('name', `%${query}%`);
      }
    })
    .offset(offset)
    .limit(perPage)
    .select('*', req.knexDB.raw('count(*) over() as total'));

  pagination.total = configurations.length
    ? +(configurations[0] as any).total
    : 0;
  pagination.per_page = perPage;
  pagination.offset = offset;
  pagination.to = offset + configurations.length;
  pagination.last_page = Math.ceil(pagination.total / perPage);
  pagination.current_page = page;
  pagination.from = offset;
  pagination.configurations = configurations.map((configuration: any) => {
    delete configuration.total;
    const options = Array.isArray(configuration.options)
      ? configuration.options
      : JSON.parse(configuration.options);
    return {
      ...configuration,
      options,
    };
  });

  return pagination;
};

const getConfiguration = async (req: Request) => {
  const { id } = req.params;

  const config = await req.models.Configuration.query()
    .where({ uid: id, deletedAt: null, systemType: 'config' })
    .withGraphFetched('[options(isConfigOption)]')
    .first();
  return config;
};

const updateConfiguration = async (req: Request) => {
  const dto: ConfigurationDTO = req.body;
  const uid: number = req.params.id as any;

  const trx = await req.knexDB.transaction();
  try {
    const config = await req.models.Configuration.update(trx, uid, dto);
    await trx.commit();
    return config;
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const deleteConfiguration = async (req: Request) => {
  const { id } = req.params;

  await req.models.Configuration.query()
    .where({ uid: id })
    .patch({ deletedAt: req.knexDB.fn.now() });
};

export default {
  createConfiguration: httpHandler(createConfiguration),
  getConfigurations: httpHandler(getConfigurations),
  getConfiguration: httpHandler(getConfiguration),
  updateConfiguration: httpHandler(updateConfiguration),
  deleteConfiguration: httpHandler(deleteConfiguration),
};
