import env from '@app/config/env';
import errorConstants from '@app/constants/errors';
import { ApplicationError, httpHandler } from '@app/lib/http';
import { Attachment } from '@app/models/attachment';
import { User } from '@app/models/user';
import {
  BulkUpdateExecutionDTO,
  ExecutionRelations,
  ListExecutionsDTO,
} from '@app/types/execution';
import { FGARawWrite } from '@ss-libs/ss-component-auth';
import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import i18n from 'i18n';
import { TestExecution } from '@app/models/testExecution';
import { AttachmentData } from '../temporal/activities/attachment';
import { UpdateRunStateDTO } from '../temporal/activities/run';
import { startWorkflow } from '../temporal/client';

interface QueryFilter {
  milestoneUids?: string[];
  selectedPlans?: string[];
  projectUids?: string[];
  query?: string;
  planUids?: string[];
  selectedRuns?: string[];
  selectedMilestones?: string[];
}
const createExecution = async (req: Request) => {
  const {
    externalId,
    source,
    testCaseUID,
    testCaseRef,
    testRunUID,
    status,
    steps,
    customFields,
  } = req.body;

  const trx = await req.knexDB.transaction();

  try {
    // Start a transaction
    // create the test execution
    const execution = await req.models.TestExecution.query(trx)
      .insert({
        externalId,
        source,
        status,
        testCaseUid: testCaseUID,
        testCaseRef,
        testRunUid: testRunUID,
        customFields,
      })
      .returning('*');

    if (steps && steps.length > 0) {
      const stepsToInsert: any[] = steps.map((step: any) => ({
        externalId,
        source,
        description: step.description,
        customFields: step.customFields,
        testStepUid: step?.testStepUid,
        testCaseUid: testCaseUID,
        testExecutionUid: execution.uid, // Linking to the created test execution
      }));
      await req.models.TestExecutionStep.query(trx).insert(stepsToInsert);
    }
    // If everything is successful, commit the transaction
    await trx.commit();

    const owner = req.locals.handle.ownerUid;
    const { ownerType } = req.locals.handle;

    // register the tuples for the user in session (creator) and handle (owner) concerning the test execution
    const writes: FGARawWrite[] = [
      {
        objectType: 'execution',
        objectId: execution.uid,
        relation: 'owner',
        subjectType: ownerType, // type of entity
        subjectId: owner, // entity id
      },
    ];
    await req.fga.create(...writes);

    if (execution.testRunUid) {
      const param: UpdateRunStateDTO = {
        ownerType: req.locals.handle.ownerType,
        ownerUid: req.locals.handle.ownerUid,
        runUids: [execution.testRunUid],
      };
      await startWorkflow('updateRunWorkflow', {
        taskQueue: 'update-run-queue',
        workflowId: `update.run.${execution.testRunUid}.${Date.now()}`,
        args: [param],
      });
    }

    return { ...execution, steps };
  } catch (error) {
    // If there's an error, rollback the transaction
    await trx.rollback();
    throw error;
  }
};

/**
 * get a list of test executions
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.orgId
 * @param {Number} req.query.per_page
 * @param {Number} req.query.current_page
 */
const getExecutions = async (req: Request) => {
  const dto: ListExecutionsDTO = req.query as any;
  dto.projectUid = req.locals.project.uid;

  const executions = await req.models.TestExecution.findPaginated(
    req.knexDB,
    dto,
  );

  return executions;
};

/**
 * delete a test executions by execId
 * DELETE
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.execId
 */
const deleteExecution = async (req: Request) => {
  const [exec] = await req.models.TestExecution.query()
    .where({ uid: req.params.id, deletedAt: null })
    .patch({
      deletedAt: req.knexDB.fn.now(),
    })
    .returning('*');

  const param: UpdateRunStateDTO = {
    ownerType: req.locals.handle.ownerType,
    ownerUid: req.locals.handle.ownerUid,
    runUids: [exec.testRunUid],
  };

  if (exec.testRunUid) {
    await startWorkflow('updateRunWorkflow', {
      taskQueue: 'update-run-queue',
      workflowId: `update.run.${exec.testRunUid}.${Date.now()}`,
      args: [param],
    });
  }
  await startWorkflow('syncExecutionAggregates', {
    taskQueue: 'sync-execution-aggregates',
    args: [param],
    workflowId: `syncExecutionAggregates-${param.ownerType}-${param.ownerUid}`,
  });
  return { message: 'testExecutionDeleted' };
};

/**
 * get a test executions by execId
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.execId
 */
const getExecution = async (req: Request) => {
  const { id } = req.params;

  const execution = (await req.models.TestExecution.query()
    .findById(id)
    .withGraphFetched('[attachments,steps]')
    .whereNull('deletedAt')) as any;

  if (!execution) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.TEST_EXECUTION_NOT_FOUND,
    );
  }

  const baseURL = `${env.BACKEND_URL}${env.API_INTERNAL_ROUTE}/${req.params.handle}`;

  const user = await req.models.User.query()
    .join('handles', 'users.uid', '=', 'handles.ownerUid')
    .where('handles.current', true)
    .where('handles.ownerType', 'user')
    .where('users.uid', execution.assignedTo)
    .select({
      uid: 'users.uid',
      firstName: 'users.firstName',
      lastName: 'users.lastName',
      email: 'users.email',
      handle: 'handles.name',
    })
    .first();

  execution.assignedTo = user;

  execution.tags = await req.models.Tag.findByIds(
    req.knexDB,
    execution.customFields?.tags?.map((t) => t.uid),
  );
  execution.steps = req.models.TestExecutionStep.buildTree(execution.steps);

  req.models.TestExecution.formatAttachment(execution, baseURL);

  return execution;
};

const updateExecution = async (req: Request) => {
  const { id } = req.params;

  const trx = await req.knexDB.transaction();
  try {
    const exec = await req.models.TestExecution.updateOne(trx, +id, req.body);

    if (!exec) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errorConstants.TEST_EXECUTION_NOT_FOUND,
      );
    }

    if (exec.assignedTo) {
      exec.assignedTo = (await User.getOne(
        req.sharedKnexDB,
        exec.assignedTo,
      )) as any;
    }

    if (exec.testRunUid) {
      const param: UpdateRunStateDTO = {
        ownerType: req.locals.handle.ownerType,
        ownerUid: req.locals.handle.ownerUid,
        runUids: [exec.testRunUid],
      };

      await startWorkflow('updateRunWorkflow', {
        taskQueue: 'update-run-queue',
        workflowId: `update.run.${exec.testRunUid}.${Date.now()}`,
        args: [param],
      });
      await startWorkflow('syncExecutionAggregates', {
        taskQueue: 'sync-execution-aggregates',
        args: [param],
        workflowId: `syncExecutionAggregates-${param.ownerType}-${param.ownerUid}`,
      });
    }
    await trx.commit();
    return exec;
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const updateExecutions = async (req: Request) => {
  const dto: BulkUpdateExecutionDTO = req.body;

  const trx = await req.knexDB.transaction();
  try {
    const execUpdateJobs = dto.executionUids.map((uid) => {
      const e = req.models.TestExecution.updateOne(trx, uid, dto);
      return e;
    });

    const executions = (await Promise.all(execUpdateJobs)).filter((e) => !!e); // filter out null executions

    const usersIds = executions.map((item) => item.assignedTo);

    const users = await User.getByIds(req.sharedKnexDB, usersIds);

    const result = users.reduce((acc, curr) => {
      acc[curr.uid] = curr;
      return acc;
    }, {});

    const runIds: number[] = [];

    executions.forEach((item) => {
      item.assignedTo = result[item.assignedTo];
      if (item.testRunUid) runIds.push(item.testRunUid);
    });

    const param: UpdateRunStateDTO = {
      ownerType: req.locals.handle.ownerType,
      ownerUid: req.locals.handle.ownerUid,
      runUids: runIds,
    };

    if (runIds.length > 0) {
      await startWorkflow('updateRunWorkflow', {
        taskQueue: 'update-run-queue',
        workflowId: `update.run.${runIds}.${Date.now()}`,
        args: [param],
      });
    }

    await startWorkflow('syncExecutionAggregates', {
      taskQueue: 'sync-execution-aggregates',
      args: [param],
      workflowId: `syncExecutionAggregates-${param.ownerType}-${param.ownerUid}`,
    });
    await trx.commit();
    return executions;
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const uploadAttachment = async (req: Request) => {
  const { fileType, size, fileName } = req.body;
  const { id: executionUid } = req.params;
  const { ownerUid } = req.locals.handle;
  const creatorUid = req.locals.user?.uid || null;
  const newAttachment = {
    name: fileName,
    ownerUid,
    size,
    fileType,
    mediaType: 'attachment',
    creatorUid,
  };

  const trx = await req.knexDB.transaction();

  try {
    const {
      key,
      signed_url: signedUrl,
      object_url: objectUrl,
      client_headers: clientHeaders,
      uid,
    } = await Attachment.createAttachment(newAttachment, trx);

    await trx('executionAttachments').insert({
      attachmentUid: uid,
      executionUid,
    });

    await trx.commit();

    return {
      uid,
      objectKey: key,
      signedUrl,
      objectUrl,
      clientHeaders,
    };
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const deleteAttachment = async (req: Request, res: Response) => {
  const trx = await req.knexDB.transaction();
  const attachmentId = <string>req.params.id;

  try {
    const attachment = await req.models.Attachment.query(trx).findOne({
      uid: attachmentId,
    });

    if (!attachment) return res.status(404).send();
    const { ownerUid } = req.locals.handle;
    const { key } = attachment;
    const param: AttachmentData = {
      type: 'delete',
      key,
      ownerUid,
    };
    await Promise.all([
      startWorkflow('attachmentWorkflow', {
        taskQueue: 'attachment-queue',
        workflowId: `${ownerUid}:attachment:delete:${Date.now()}`,
        args: [param],
      }),
      Attachment.deleteAttachment(trx, attachmentId, 'executionAttachments'),
    ]);
    await trx.commit();
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const deleteSteps = async (req: Request) => {
  const stepUids = req.query.stepUids as string[];

  await req.models.TestExecutionStep.query()
    .where({
      testExecutionUid: req.params.id,
      deletedAt: null,
    })
    .whereIn('uid', stepUids)
    .patch({ deletedAt: req.knexDB.fn.now() });
};

const getExecutionsUsers = async (req: Request) => {
  const assignedToUserIds = await req.models.TestExecution.query()
    .whereNotNull('assignedTo')
    .distinct()
    .select('assignedTo');

  const users = await req.models.User.query()
    .join('handles', 'users.uid', '=', 'handles.ownerUid')
    .where('handles.current', true)
    .where('handles.ownerType', 'user')
    .whereIn('users.uid', [
      ...assignedToUserIds.map((user) => user.assignedTo),
      req.locals.user.uid,
    ])
    .distinct()
    .select({
      uid: 'users.uid',
      firstName: 'users.firstName',
      lastName: 'users.lastName',
      email: 'users.email',
      handle: 'handles.name',
    });

  return { users };
};

const getExecutionsCountByStatus = async (req: Request) => {
  const { status }: { status?: number[] } = <any>req.query;
  const result = await TestExecution.getCountByStatus(req.knexDB, status);

  return result;
};

const getExecutionsProjects = async (req: Request) => {
  const projects = await req.models.Project.query()
    .whereNull('projects.deletedAt')
    .whereNull('projects.archivedAt')
    .select(['projects.uid', 'projects.name', 'projects.key'])
    .distinct();

  return { projects };
};

const getExecutionsMilestones = async (req: Request) => {
  const { projectUids, query, selectedMilestones }: QueryFilter = req.query;
  const milestonesQuery = req.models.Tag.query()
    .where({ systemType: 'milestone' })
    .whereNull('deletedAt')
    .whereNull('archivedAt')
    .select(['uid', 'name', 'projectUid'])
    .where((q) => {
      q.where((builder) => {
        if (projectUids?.length) {
          builder.whereIn('projectUid', projectUids as string[]);
        }
        if (query) {
          builder.whereILike('name', `%${query}%`);
        }
      });

      if (selectedMilestones?.length) {
        q.orWhereIn('uid', selectedMilestones as string[]);
      }
    })
    .limit(50);

  if (req.query.selectedMilestones?.length) {
    milestonesQuery.orderByRaw(
      `CASE WHEN uid IN (${req.query.selectedMilestones}) THEN 0 ELSE 1 END`,
    );
  }

  const milestones = await milestonesQuery;

  return { milestones };
};

const getExecutionsTestPlans = async (req: Request) => {
  const {
    milestoneUids, selectedPlans, projectUids, query,
  }: QueryFilter = req.query;

  const testPlansQuery = req.models.TestPlan.query()
    .select(['uid', 'name', 'projectUid'])
    .whereNull('deletedAt')
    .whereNull('archivedAt')
    .where({ systemType: 'plan' })
    .where((q) => {
      q.where((builder) => {
        if (milestoneUids?.length) {
          builder.whereIn(
            'uid',
            req.models.TestMilestonePlan.query()
              .whereIn('milestoneUid', milestoneUids as string[])
              .select('planUid'),
          );
        }
        if (query) {
          builder.whereILike('name', `%${query}%`);
        }
        if (projectUids?.length) {
          builder.whereIn('projectUid', projectUids as string[]);
        }
      });
      if (selectedPlans?.length) {
        q.orWhereIn('uid', selectedPlans as string[]);
      }
    })
    .limit(50);

  if (selectedPlans?.length) {
    testPlansQuery.orderByRaw(
      `CASE WHEN uid IN (${selectedPlans}) THEN 0 ELSE 1 END`,
    );
  }

  const testPlans = await testPlansQuery;

  return { testPlans };
};

const getExecutionsTestRuns = async (req: Request) => {
  const {
    planUids,
    query,
    projectUids,
    selectedRuns,
    selectedMilestones,
  }: QueryFilter = req.query;
  const testRunsQuery = req.models.TestRun.query()
    .select(['uid', 'name', 'projectUid'])
    .whereNull('deletedAt')
    .whereNull('archivedAt')
    .limit(50)
    .where((q) => {
      q.where((builder) => {
        if (planUids?.length) {
          builder.whereIn(
            'uid',
            req.models.TestPlan.relatedQuery('runs')
              .for(planUids as string[])
              .select('uid'),
          );
        }
        if (query) {
          builder.whereILike('name', `%${query}%`);
        }
        if (projectUids?.length) {
          builder.whereIn('projectUid', projectUids as string[]);
        }
      });

      if (selectedRuns?.length) {
        q.orWhereIn('uid', selectedRuns as string[]);
      }
    });

  if (selectedMilestones?.length) {
    testRunsQuery.whereExists(
      req.models.TestRun.relatedQuery('testMilestones').whereIn(
        'testMilestones.uid',
        selectedMilestones,
      ),
    );
  }

  if (selectedRuns?.length) {
    testRunsQuery.orderByRaw(
      `CASE WHEN uid IN (${selectedRuns}) THEN 0 ELSE 1 END`,
    );
  }
  const testRuns = await testRunsQuery;

  return { testRuns };
};

const getExecutionsRelations = async (req: Request) => {
  const { relation, executionUids }: ExecutionRelations = <any>req.query;

  switch (relation) {
    case 'tag': {
      return req.models.TestExecution.getTags(req.knexDB, executionUids);
    }
    case 'testRun': {
      return req.models.TestExecution.getTestRuns(req.knexDB, executionUids);
    }
    case 'testPlan': {
      return req.models.TestExecution.getTestPlans(req.knexDB, executionUids);
    }
    case 'milestone': {
      return req.models.TestExecution.getMilestones(req.knexDB, executionUids);
    }
    case 'project': {
      return req.models.TestExecution.getProjects(req.knexDB, executionUids);
    }
    default:
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        errorConstants.INVALID_REQUEST,
      );
  }
};

export default {
  getExecutionsUsers: httpHandler(getExecutionsUsers),
  getExecutionsCountByStatus: httpHandler(getExecutionsCountByStatus),
  getExecutionsProjects: httpHandler(getExecutionsProjects),
  getExecutionsMilestones: httpHandler(getExecutionsMilestones),
  getExecutionsTestPlans: httpHandler(getExecutionsTestPlans),
  getExecutionsTestRuns: httpHandler(getExecutionsTestRuns),
  getExecutionsRelations: httpHandler(getExecutionsRelations),

  createExecution: httpHandler(createExecution),
  getExecutions: httpHandler(getExecutions),
  getExecution: httpHandler(getExecution),
  updateExecution: httpHandler(updateExecution),
  updateExecutions: httpHandler(updateExecutions),
  deleteExecution: httpHandler(deleteExecution),
  uploadAttachment: httpHandler(uploadAttachment),
  deleteAttachment: httpHandler(deleteAttachment),
  deleteSteps: httpHandler(deleteSteps),
};
