import { Request } from 'express';
import { User } from '@app/models/user';
import { httpHandler } from '@app/lib/http';
import { keyBy } from 'lodash';

// Create a new comment
const createComment = async (req: Request) => {
  const { entityType, entityUid, body } = req.body;

  return req.models.Comment.query()
    .insert({
      entityType,
      entityUid,
      createdBy: req.locals.user.uid,
      body,
    })
    .returning('*');
};

// Get comments for a specific entity and entity UID and join the user table to get the user's name and avatar
const getComments = async (req: Request) => {
  const { entityType, entityUid } = req.params;

  // get comments for a specific entity and entity UID and join the user table to get the user's name and avatar join it by column createdBy
  // return this object structure { uid, entityType, entityUid, createdBy, body user: { name, avatar} } name should be aggregaed from firstName lastName
  // we shuld also add user handle
  const comments = await req.models.Comment.query()
    .where({ entityType, entityUid })
    .whereNull('deletedAt')
    .select('*');

  const users: Array<User & { handle: string }> = (await req.models.User.query()
    .whereIn(
      'users.uid',
      comments.map((c) => c.createdBy),
    )
    .join('handles', 'handles.ownerUid', '=', 'users.uid')
    .select(
      'users.uid as uid',
      'users.firstName',
      'users.lastName',
      'users.avatar',
      'handles.name as handle',
    )) as any;

  const usersMap = keyBy(users, 'uid');

  // map the result to desired format
  const formattedComments = comments.map((c) => {
    const user = usersMap[c.createdBy];
    return {
      ...c,
      user: {
        name: `${user?.firstName} ${user?.lastName}`,
        avatar: user?.avatar || {},
        handle: user?.handle,
      },
    };
  });

  return formattedComments;
};

/**
 * update a comment by commentId
 * PUT
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.commentId
 */
const updateComment = async (req: Request) => {
  const { id: commentId } = req.params;
  const { body } = req.body;

  const updatedComment = await req.models.Comment.query()
    .findById(commentId)
    .patch({ body });

  return updatedComment;
};

/**
 * delete a comment by commentId
 * DELETE
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.commentId
 */
const deleteComment = async (req: Request) => {
  const { id } = req.params;

  await req.models.Comment.query()
    .findById(id)
    .patch({ deletedAt: req.knexDB.fn.now() });
};

export default {
  createComment: httpHandler(createComment),
  getComments: httpHandler(getComments),
  updateComment: httpHandler(updateComment),
  deleteComment: httpHandler(deleteComment),
};
