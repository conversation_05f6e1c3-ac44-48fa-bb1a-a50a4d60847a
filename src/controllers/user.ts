import * as QRCode from 'qrcode';

import { ApplicationError, httpHand<PERSON> } from '@app/lib/http';

import { Attachment } from '@app/models/attachment';
import { FGARawWrite } from '@ss-libs/ss-component-auth';
import { Request } from 'express';
import { StatusCodes } from 'http-status-codes';
import _ from 'lodash';
import { authenticator } from '@otplib/preset-default';
import bcrypt from 'bcrypt';
import errorConstants from '@app/constants/errors';
import { hashPassword } from '@app/utils/hashPassword';
import isEmpty from '@app/utils/isEmpty';
import logger from '@app/config/logger';
import { setupDB } from '@app/config/db';
import { startWorkflow } from '../temporal/client';
import { AttachmentData } from '../temporal/activities/attachment';

const getOrgs = async (req: Request) => {
  const { user } = req.locals;

  const orgs: any = await req.models.Membership.query()
    .join('handles', 'ownerUid', 'accountUid')
    .join('orgs', 'handles.ownerUid', 'orgs.uid')
    .where('orgs.deletedAt', null)
    .where('userUid', user.uid)
    .where('memberships.deletedAt', null)
    .select({
      uid: 'orgs.uid',
      handle: 'handles.name',
      avatarUrl: 'orgs.avatarUrl',
      name: 'orgs.name',
      createdAt: 'orgs.createdAt',
    });

  const accounts = orgs.map((org) => ({
    uid: org.uid,
    handle: org.handle,
    avatarUrl: org.avatarUrl,
    name: org.name,
    type: 'org',
    createdAt: org.createdAt,
  }));
  return { orgs: accounts };
};

const updatePreferences = async (req: Request) => {
  const errors = [];
  const { preferences } = req.body;
  const currentUser = await req.models.User.query()
    .findById(req.locals.user.uid)
    .first();
  const relatedOrgs = await req.fga.getRelatedOrgOfUser(currentUser.uid);
  const userOrgs = relatedOrgs.map((org) => org.key.object.split(':')[1]);

  const newPreferences = currentUser.preferences;
  for (const org of userOrgs) {
    for (const val of ['defaultDashboard']) {
      if (preferences[org] && preferences[org][val]) {
        // eslint-disable-next-line no-await-in-loop
        const dashboard = await req.models.Dashboard.query()
          .where('orgUid', org)
          .where('uid', preferences[org][val])
          .first();
        if (dashboard) {
          newPreferences[org][val] = preferences[org][val];
        } else {
          errors.push(`Dashboard ${val} does not exist.`);
        }
      }
    }
  }

  for (const key of Object.keys(preferences)) {
    if (key === 'defaultDashboard') continue;

    newPreferences[key] = preferences[key];
  }

  await req.models.User.query().findById(req.locals.user.uid).patch({
    preferences: newPreferences,
  });
  if (!isEmpty(errors)) {
    throw new ApplicationError(StatusCodes.UNPROCESSABLE_ENTITY, errors[0]);
  }
  return {
    preferences: newPreferences,
  };
};

const updateProfile = async (req: Request) => {
  const currentUser = await req.models.User.query().findById(
    req.locals.user.uid,
  );
  const { ownerUid } = req.locals.handle;
  if (currentUser.avatar.user !== req.body.avatarUrl) {
    logger.log(
      'info',
      `Current user: ${JSON.stringify(currentUser)}:${req.locals.user.uid}`,
    );

    if (!isEmpty(currentUser.avatar.user)) {
      logger.log(
        'info',
        `Pushing data to worker: Deleting ${currentUser.avatar.user}`,
      );
      const param: AttachmentData = {
        type: 'delete',
        url: currentUser.avatar.user,
        ownerUid,
      };

      await startWorkflow('attachmentWorkflow', {
        taskQueue: 'attachment-queue',
        workflowId: `${ownerUid}:attachment:delete:${Date.now()}`,
        args: [param],
      });
    }
  }

  const params : Record<string, any> = {};
  for (const val of ['firstName', 'lastName', 'preferences']) {
    if (req.body[val]) {
      params[val] = req.body[val];
    }
  }
  if (req.body.avatarUrl) {
    params.avatar = {
      user: req.body.avatarUrl,
      system: currentUser.avatar.system, // Keep user avatar system value
    };
  }
  await req.models.User.query()
    .findById(req.locals.user.uid)
    .patch({
      ...params,
    });
  const updatedUser = {
    ...params,
  };
  return { user: updatedUser };
};

/**
 * search users using user name and email
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {String} request.query.query
 */
const searchUsers = async (req: Request) => {
  const token = `${req.query.query}%`;
  const selectedFields = ['users.uid', 'firstName', 'lastName', 'avatar'];
  const usersByUsername = await req.models.Handle.query()
    .join('users', 'ownerUid', 'users.uid')
    .select(...selectedFields, 'name as username')
    .whereILike('name', token);
  if (usersByUsername.length > 0) return usersByUsername;

  const usersByEmail = await req.models.User.query()
    .join('handles', 'handles.ownerUid', 'users.uid')
    .select(...selectedFields, 'email')
    .whereILike('email', token);
  return usersByEmail;
};

const changePassword = async (req: Request) => {
  const findOne = (id: string) => req.models.User.query().findById(id);

  const user = await findOne(req.locals.user.uid);
  const isMatch = await bcrypt.compare(
    req.body.currentPassword,
    user.passwordHash,
  );
  if (!isMatch) {
    throw new ApplicationError(
      StatusCodes.FORBIDDEN,
      errorConstants.CURRENT_PASSWORD_INCORRECT,
    );
  }
  const newPasswordHash = await hashPassword(req.body.newPassword);
  await findOne(req.locals.user.uid).patch({
    passwordHash: newPasswordHash,
  });
};

const getProfile = async (req: Request) => {
  const id = req.locals.user.uid;
  const [user, handle] = await Promise.all([
    req.models.User.query()
      .findById(id)
      .select([
        'uid',
        'firstName',
        'lastName',
        'email',
        'avatar',
        'preferences',
      ]),
    req.models.Handle.query().where({ ownerUid: id, current: true }).first(),
  ]);
  return { ...user, handle: handle.name };
};

const getPreferences = async (req: Request) => {
  const id = req.locals.user.uid;
  const user = await req.models.User.query()
    .findById(id)
    .select(['preferences']);
  return { preferences: user.preferences };
};

const deleteUser = async (req: Request) => {
  const { password } = req.body;

  if (req.params.id !== req.locals.user.uid) {
    throw new ApplicationError(StatusCodes.FORBIDDEN, errorConstants.FORBIDDEN);
  }

  const trx = await req.sharedKnexDB.transaction();
  try {
    const user = await req.models.User.query(trx).findById(req.params.id);

    if (!user) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errorConstants.USER_NOT_FOUND,
      );
    }

    const isMatch = await bcrypt.compare(password, user.passwordHash);

    if (!isMatch) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        errorConstants.CURRENT_PASSWORD_INCORRECT,
      );
    }

    const now = () => trx.fn.now();
    await req.models.User.query(trx)
      .findById(req.params.id)
      .patch({ deletedAt: now() });

    await req.models.Handle.query(trx)
      .where({ ownerUid: user.uid })
      .patch({ current: false });

    await req.models.Membership.query(trx)
      .where({ userUid: user.uid })
      .patch({ deletedAt: now() });
    await trx.commit();
    return { success: true };
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const uploadAttachment = async (req: Request) => {
  const { fileType, size, fileName } = req.body;
  const ownerUid = req.locals.user.uid;

  const newAttachment = {
    name: fileName,
    ownerUid,
    size,
    fileType,
    mediaType: 'profile-picture',
    creatorUid: ownerUid,
  };

  const tenant = await req.models.Tenant.query().where({ tenantUid: ownerUid })
    .withGraphFetched('dbServer')
    .first();

  const tenantTrx = await setupDB(req.locals.user.uid, tenant.dbServer).transaction();
  try {
    const {
      key,
      signed_url: signedUrl,
      object_url: objectUrl,
      client_headers: clientHeaders,
      uid,
    } = await Attachment.createAttachment(newAttachment, tenantTrx);

    await req.models.User.query()
      .findById(req.locals.user.uid)
      .patch({
        avatar: req.models.User.knex().raw(
          'jsonb_set("avatar", \'{user}\', to_jsonb(?::TEXT)::jsonb)',
          [objectUrl || null],
        ),
      });

    await tenantTrx.commit();

    return {
      uid,
      objectKey: key,
      signedUrl,
      objectUrl,
      clientHeaders,
    };
  } catch (err) {
    await tenantTrx.rollback();
    throw err;
  }
};

const createAuthentication = async (req: Request) => {
  const { type, phoneNumber } = req.body;

  const currentUser = await req.models.User.query()
    .findById(req.locals.user.uid)
    .first();

  const response = {
    type,
  } as any;

  const isNewSetup = !currentUser.preferences.authenticationType
    || currentUser.preferences.authenticationType !== type;
  currentUser.preferences.authenticationType = type;

  if (type === 'otp') {
    const secret = currentUser.preferences.secret || authenticator.generateSecret(20);

    const url = await generatQRCode(currentUser.email, secret);

    response.url = url;

    currentUser.preferences.secret = secret;
    currentUser.preferences.phoneNumber = null;
  } else {
    currentUser.preferences.phoneNumber = phoneNumber;
    currentUser.preferences.secret = null;

    response.phoneNumber = phoneNumber;
  }

  if (isNewSetup) {
    currentUser.preferences.recoveryCodes = [];
    currentUser.preferences.enableTwoStepAuthentication = false;
  }

  response.enableTwoStepAuthentication = currentUser.preferences.enableTwoStepAuthentication || false;

  await req.models.User.query().findById(req.locals.user.uid).patch({
    preferences: currentUser.preferences,
  });

  return response;
};

const validateAuthentication = async (req: Request) => {
  const { type, code, isNewSetup } = req.body;

  const currentUser = await req.models.User.query()
    .findById(req.locals.user.uid)
    .first();

  if (
    (type === 'otp' && !currentUser.preferences.secret)
    || (type === 'sms' && !currentUser.preferences.phoneNumber)
  ) {
    throw new Error('Two-step verification is not exist');
  }

  let verified = false;
  if (type === 'otp') {
    verified = authenticator.verify({
      token: code,
      secret: currentUser.preferences.secret,
    });
  } else {
    verified = code === '123456';
  }

  if (!verified || !isNewSetup) {
    return { verified };
  }

  const recoveryCodes: string[] = [];
  for (let i = 0; i < 6; i++) {
    recoveryCodes.push(generateRecoveryCodes());
  }

  return { verified, recoveryCodes };
};

const generatQRCode = async (
  email: string,
  secret: string,
): Promise<string> => {
  const auth = authenticator.keyuri(email, 'Testfiesta', secret);

  const url = await QRCode.toDataURL(auth);

  return url;
};

const generateRecoveryCodes = () => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = characters.length;
  const recoveryCodeLength = 14;

  let result = '';

  for (let i = 0; i < recoveryCodeLength; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }

  return result;
};

const leaveOrg = async (req: Request) => {
  const handle = await req.models.Handle.query()
    .where({ name: req.params.orgHandle })
    .first();

  if (!handle) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.HANDLE_NOT_FOUND,
    );
  }
  const { ownerType } = handle;
  const owner = handle.ownerUid;
  const userId = req.locals.user.uid;

  if (ownerType !== 'org') {
    throw new ApplicationError(
      StatusCodes.CONFLICT,
      errorConstants.HANDLE_IS_NOT_ORG,
    );
  }

  const isMember = await req.fga.check(
    `user:${userId}`,
    `${ownerType}:${owner}`,
    'member',
  );

  if (!isMember) {
    throw new ApplicationError(
      StatusCodes.FORBIDDEN,
      errorConstants.MISSING_PERMISSION,
    );
  }

  await req.models.Membership.query()
    .where({
      userUid: userId,
      accountUid: owner,
      accountType: ownerType,
    })
    .delete();

  const deletes: FGARawWrite[] = [];

  const userAssignee = await req.fga.query(`user:${userId}`, 'role:', 'assignee');
  const mappedRoles = userAssignee.map((item) => ({
    subjectType: 'user',
    subjectId: userId,
    objectType: 'role',
    objectId: item.key.object.split(':')[1],
    relation: 'assignee',
  }));

  deletes.push(...mappedRoles);

  const directRelations = await req.fga.query(
    `user:${userId}`,
    `${ownerType}:${owner}`,
    '',
  );
  for (const rels of directRelations) {
    deletes.push({
      objectId: owner,
      objectType: ownerType,
      relation: rels.key.relation,
      subjectId: userId,
      subjectType: 'user',
    });
  }

  await Promise.all(
    _.chunk(deletes, 100).map((chunk) => req.fga.delete(...chunk)),
  );
};

export default {
  getOrgs: httpHandler(getOrgs),
  updatePreferences: httpHandler(updatePreferences),
  updateProfile: httpHandler(updateProfile),
  searchUsers: httpHandler(searchUsers),
  changePassword: httpHandler(changePassword),
  getProfile: httpHandler(getProfile),
  deleteUser: httpHandler(deleteUser),
  createAuthentication: httpHandler(createAuthentication),
  validateAuthentication: httpHandler(validateAuthentication),
  leaveOrg: httpHandler(leaveOrg),
  getPreferences: httpHandler(getPreferences),
  uploadAttachment: httpHandler(uploadAttachment),
};
