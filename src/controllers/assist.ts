import { ApplicationError, httpHandler } from '@app/lib/http';
import { Request } from 'express';
import { ChatOpenAI } from '@langchain/openai';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { OutputFixingParser } from 'langchain/output_parsers';
import { StatusCodes } from 'http-status-codes';
import errorConstants from '@app/constants/errors';

import {
  caseImprovementTemplate, fieldImprovementTemplate, caseCreationTemplate, UserFile, InputField, Step, dynamicSchema, composeInitialCaseDetails,
  extractTextFromFiles, isAssistComplete,
} from '@app/constants/aiAssist';

const handleGetAssistResponse = async (req: Request) => {
  const {
    type, userPrompt, files, inputFields, steps, caseTitle, casePriority,
  }:
  {
    type: string,
    userPrompt: string,
    textFields: string[],
    files: UserFile[],
    fieldValue: string,
    fieldName: string,
    inputFields: InputField[],
    steps: Step[],
    caseTitle: string,
    casePriority: string,
  } = req.body;

  const promptTemplates = {
    caseCreation: () => caseCreationTemplate,
    fieldImprovement: () => fieldImprovementTemplate,
    caseImprovement: () => caseImprovementTemplate,
  };

  try {
    const model = new ChatOpenAI({
      apiKey: process.env.OPENAI_API_KEY,
      model: 'gpt-4o-mini',
      temperature: 0.5,
    });

    const outputSchema = dynamicSchema(inputFields.map((item) => item.fieldName), type !== 'fieldImprovement', steps);

    const parser = StructuredOutputParser.fromZodSchema(outputSchema);
    const chain = promptTemplates[type]().pipe(model);
    const response = await chain.invoke({
      format_instructions: parser.getFormatInstructions(),
      ...(type === 'caseCreation' ? { files: extractTextFromFiles(files) } : {}),
      ...(type !== 'caseCreation' ? { initialValues: composeInitialCaseDetails(caseTitle, casePriority, steps, inputFields) } : {}),
      ...(type !== 'caseImprovement' ? { userPrompt } : {}),
      inputFields: inputFields.map((item) => `"${item.fieldName}"`).join(),
    });
    if (isAssistComplete(response.content)) {
      try {
        return await parser.parse(response.content as string);
      } catch (err:any) {
        try {
          const res = OutputFixingParser.fromLLM(model, parser);
          return await res.parse(err);
        } catch {
          throw new ApplicationError(
            StatusCodes.BAD_REQUEST,
            errorConstants.NO_RESPONSE_FOUND,
          );
        }
      }
    } else {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        errorConstants.NO_RESPONSE_FOUND,
      );
    }
  } catch (error) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errorConstants.NO_RESPONSE_FOUND,
    );
  }
};
export default {
  handleGetAssistResponse: httpHandler(handleGetAssistResponse),
};
