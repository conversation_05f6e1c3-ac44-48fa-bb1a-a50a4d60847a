import { ApplicationError, httpHandler } from '@app/lib/http';

import { ForeignKeyViolationError } from 'objection';
import { Request } from 'express';
import { StatusCodes } from 'http-status-codes';
import errorConstants from '@app/constants/errors';

/**
 * create a test cases
 * POST
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.body.org
 * @param {String} req.body.externalId
 * @param {String} req.body.source
 * @param {String} req.body.name
 * @param {Object} req.body.customFields
 */
const createRepo = async (req: Request) => {
  const trx = await req.knexDB.transaction();
  try {
    const {
      externalId, customFields, source, name,
    } = req.body;
    const repo = await req.models.Repo.query(trx).insert({
      externalId,
      source,
      name: name || '',
      customFields,
      projectUid: req.locals.project.uid,
    });
    await trx.commit();

    const owner = req.locals.handle.ownerUid;
    const { ownerType } = req.locals.handle;

    await req.fga.create({
      objectType: 'repo',
      objectId: repo.uid,
      relation: 'owner',
      subjectType: ownerType, // type of entity
      subjectId: owner, // entity id
    });

    return repo;
  } catch (error) {
    await trx.rollback();
    throw error;
  }
};

/**
 * get paginated Repos for an organization
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.orgId
 * @param {Number} req.query.per_page
 * @param {Number} req.query.current_page
 */
const getRepos = async (req: Request) => {
  const reqData = req.query;
  const pagination: any = {};
  const perPage: number = Number(reqData.per_page) || 10;
  let page: number = Number(reqData.current_page) || 1;

  if (page < 1) page = 1;

  const offset = (page - 1) * perPage;

  const repos = await req.models.Repo.query()
    .where('projectUid', req.locals.project.uid)
    .offset(offset)
    .limit(perPage)
    .select('*', req.knexDB.raw('count(uid) over() as total')) as any[];

  pagination.total = repos.length ? +repos[0].total : 0;
  pagination.repos = repos.map((repo: any) => {
    repo.attachments = [];
    repo.reports = [];
    delete repo.total;
    return repo;
  });

  pagination.per_page = perPage;
  pagination.offset = offset;
  pagination.to = offset + repos.length;
  pagination.last_page = Math.ceil(pagination.total / perPage);
  pagination.current_page = page;
  pagination.from = offset;
  return pagination;
};

/**
 * get paginated branches for organization
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.repoId
 * @param {Number} req.query.per_page
 * @param {Number} req.query.current_page
 */
const getBranches = async (req: Request) => {
  const { id: repoId } = req.params;
  const reqData = req.query;
  const pagination: any = {};
  const perPage: number = Number(reqData.per_page) || 10;
  let page: number = Number(reqData.current_page) || 1;

  if (page < 1) page = 1;
  const offset = (page - 1) * perPage;

  const branches = await req.models.RepoBranch.query()
    .where('repoUid', repoId)
    .offset(offset)
    .limit(perPage)
    .select('*', req.knexDB.raw('count(uid) over() as total')) as any[];

  pagination.total = branches[0].total;
  pagination.branches = branches.map((item) => {
    delete item.total;
    return item;
  });

  pagination.per_page = perPage;
  pagination.offset = offset;
  pagination.to = offset + branches.length;
  pagination.last_page = Math.ceil(pagination.total / perPage);
  pagination.current_page = page;
  pagination.from = offset;

  return pagination;
};

/**
 * create a test cases
 * POST
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.body.org
 * @param {String} req.body.externalId
 * @param {String} req.body.name
 * @param {Object} req.body.customFields
 */
const createBranch = async (req: Request) => {
  // Start a transaction
  const trx = await req.knexDB.transaction();

  try {
    const { externalId, customFields, name } = req.body;

    const branch = await req.models.RepoBranch.query(trx).insert({
      externalId,
      name: name || '',
      customFields,
      repoUid: req.params.id,
    });

    const owner = req.locals.handle.ownerUid;
    const { ownerType } = req.locals.handle;
    await req.fga.create({
      objectType: 'branch',
      objectId: branch.uid,
      relation: 'owner',
      subjectType: ownerType, // type of entity
      subjectId: owner, // entity id
    });
    await trx.commit();

    return branch;
  } catch (error) {
    await trx.rollback();
    if (error instanceof ForeignKeyViolationError) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errorConstants.REPO_NOT_FOUND,
      );
    }
    throw error;
  }
};

export default {
  getRepos: httpHandler(getRepos),
  createRepo: httpHandler(createRepo),
  getBranches: httpHandler(getBranches),
  createBranch: httpHandler(createBranch),
};
