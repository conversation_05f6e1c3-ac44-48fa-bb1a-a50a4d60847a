import { ApplicationError, httpHandler } from '@app/lib/http';
import { Request } from 'express';

import { StatusCodes } from 'http-status-codes';
import { UniqueViolationError } from 'objection';
import errors from '@app/constants/errors';
import { kebabCase } from 'lodash';

const dtoToModel = (req: Request) => {
  const map = {
    name: 'name',
    source: 'source',
    projectId: 'projectUid',
    customFields: 'customFields',
    parentId: 'parentUid',
    externalId: 'externalId',
  };
  const body: Record<string, any> = {};

  for (const key of Object.keys(req.body)) {
    const value = req.body[key] || null;
    if (map[key]) body[map[key]] = value;
  }

  return body;
};

/**
 * create a test folders
 * POST
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.orgId
 * @param {String} req.body.externalId
 * @param {String} req.body.source
 * @param {String} req.body.name
 * @param {Object} req.body.customFields
 */
const createFolder = async (req: Request) => {
  const trx = await req.knexDB.transaction();

  try {
    const params = req.body;
    const projectId = req.locals.project.uid;
    const slug = kebabCase(params.name);

    // check if an identical folder exists in the same directory as incoming folder
    const existing = await req.models.Tag.query(trx)
      .where({
        slug,
        systemType: 'folder',
        parentUid: params.parentId ?? null,
      })
      .first();

    if (existing) {
      throw new ApplicationError(StatusCodes.CONFLICT, errors.FOLDER_EXISTS);
    }

    if (params.parentId) {
      const parent = await req.models.Tag.query(trx)
        .where({
          uid: params.parentId,
          systemType: 'folder',
          projectUid: projectId,
        })
        .first();

      if (!parent) {
        throw new ApplicationError(
          StatusCodes.UNPROCESSABLE_ENTITY,
          errors.INVALID_FOLDER_UID,
        );
      }
    }

    const folder = await req.models.Tag.query().insert({
      name: params.name,
      slug,
      source: params.source,
      externalId: params.externalId,
      parentUid: params.parentId,
      projectUid: projectId,
      entityTypes: ['cases'],
      systemType: 'folder',
    });
    const owner = req.locals.handle.ownerUid;
    const { ownerType } = req.locals.handle;

    await req.fga.create({
      objectType: 'folder',
      objectId: folder.uid,
      relation: 'owner',
      subjectType: ownerType, // type of entity
      subjectId: owner, // entity id
    });
    await trx.commit();

    return folder;
  } catch (error) {
    await trx.rollback();
    if (error instanceof UniqueViolationError) {
      throw new ApplicationError(StatusCodes.CONFLICT, errors.FOLDER_EXISTS);
    }
    throw error;
  }
};

/**
 * get root folders and their direct children
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.projectId
 */
const getProjectFolders = async (req: Request) => {
  const projectId = req.locals.project.uid;

  const [rootFolder] = await req.models.Folder.findMany(
    req.knexDB,
    { parentUid: null, projectUid: projectId },
    { limit: 1 },
  );

  if (!rootFolder) return { folders: [] };

  rootFolder.children = await req.models.Folder.findChildren(
    req.knexDB,
    rootFolder.uid,
  );
  return { folders: [rootFolder] };
};

/**
 * get a list of test folders
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.orgId
 * @param {Number} req.query.per_page
 * @param {Number} req.query.current_page
 */
const getFolders = async (req: Request) => {
  const reqData = req.query;
  const pagination: any = {};
  const perPage: number = Number(reqData.per_page) || 10;
  let page: number = Number(reqData.current_page) || 1;

  if (page < 1) page = 1;
  const offset = (page - 1) * perPage;

  const folders = await req.models.Tag.query()
    .where('systemType', 'folder')
    .whereNull('deletedAt')
    .offset(offset)
    .limit(perPage);

  const folderData = folders.map((folder: any) => ({
    ...folder,
    attachments: [],
    reports: [],
  }));
  const count: any = await req.models.Tag.query()
    .where('systemType', 'folder')
    .whereNull('deletedAt')
    .count('uid as CNT')
    .first();
  pagination.total = count.CNT;
  pagination.per_page = perPage;
  pagination.offset = offset;
  pagination.to = offset + folders.length;
  pagination.last_page = Math.ceil(count.CNT / perPage);
  pagination.current_page = page;
  pagination.from = offset;
  pagination.folders = folderData;
  return pagination;
};

/**
 * get a list of test folders by text query
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.orgId
 * @param {String} req.query.query
 */
const searchFolders = async (req: Request) => {
  const { query } = req.query;

  const folders = await req.models.Tag.query()
    .where('systemType', 'folder')
    .where((q) => {
      q.whereRaw(
        '"externalId" ilike :token or "source" ilike :token or name ilike :token',
        { token: `%${query}%` },
      );
    });

  return { folders };
};

/**
 * delete a test folders by folderId
 * DELETE
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.folderId
 */
const deleteFolder = async (req: Request) => {
  const { id } = req.params;
  await req.knexDB.transaction(async (trx) => {
    const folder = await req.models.Tag.query(trx).findById(id);
    if (!folder || folder.systemType !== 'folder' || folder.deletedAt) return;

    if (!folder.parentUid) {
      throw new ApplicationError(
        StatusCodes.METHOD_NOT_ALLOWED,
        errors.CANNOT_DELETE_ROOT_FOLDER,
      );
    }

    await req.models.Tag.query(trx).patchAndFetchById(id, {
      deletedAt: req.knexDB.fn.now(),
      slug: trx.raw('null'), // release the existing slug
    });
  });
};

/**
 * get a test folders by folderId
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.folderId
 */
const getFolder = async (req: Request) => {
  const includeChildren = (req.query.includeChildren as string)?.toLowerCase() === 'true';

  const folder = await req.models.Folder.findOne(
    req.knexDB,
    req.params.id as any,
  );
  if (!folder) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errors.FOLDER_NOT_FOUND);
  }

  if (includeChildren) {
    folder.children = await req.models.Folder.findChildren(
      req.knexDB,
      folder.uid,
    );
  }

  return folder;
};

const updateFolder = async (req: Request) => {
  const dto = dtoToModel(req);
  // remove null fields
  const params = Object.keys(dto).reduce(
    (obj, key) => {
      if (dto[key]) obj[key] = dto[key];
      return obj;
    },
    <Record<string, string>>{},
  );

  const trx = await req.knexDB.transaction();
  try {
    let folder = await req.models.Tag.query(trx).findById(req.params.id);
    if (!folder || folder?.systemType !== 'folder') {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errors.FOLDER_NOT_FOUND,
      );
    }

    if (params.name) {
      params.slug = kebabCase(params.name);
      // check if an identical folder exists in the same directory as incoming folder
      const existing = await req.models.Tag.query(trx)
        .where({
          slug: params.slug,
          systemType: 'folder',
          parentUid: params.parentUid ?? folder.parentUid,
        })
        .whereNot('uid', req.params.id)
        .first();
      if (existing) {
        throw new ApplicationError(StatusCodes.CONFLICT, errors.FOLDER_EXISTS);
      }
    }

    if (params.customFields) {
      params.customFields = {
        ...folder.customFields,
        ...(params.customFields && { customFields: params.customFields }),
      };
    }

    folder = await folder.$query(trx).patchAndFetch(params);
    await trx.commit();
    return folder;
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

export default {
  createFolder: httpHandler(createFolder),
  getFolders: httpHandler(getFolders),
  getFolder: httpHandler(getFolder),
  searchFolders: httpHandler(searchFolders),
  deleteFolder: httpHandler(deleteFolder),
  getProjectFolders: httpHandler(getProjectFolders),
  updateFolder: httpHandler(updateFolder),
};
