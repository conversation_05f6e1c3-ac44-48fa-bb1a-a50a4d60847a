import { ApplicationError, httpH<PERSON><PERSON> } from '@app/lib/http';
import { Request, Response } from 'express';
import _, { flatten } from 'lodash';

import { FGARawWrite } from '@ss-libs/ss-component-auth';
import { ForeignKeyViolationError, ModelObject } from 'objection';
import { Knex } from 'knex';
import { Pagination } from '@app/types/pagination';
import { StatusCodes } from 'http-status-codes';
import { Tag } from '@app/models/tag';
import { TestCase } from '@app/models/testCase';
import { TestCaseStep } from '@app/models/testCaseStep';
import { TestCaseTag } from '@app/models/testCaseTag';
import dayjs from 'dayjs';
import errorConstants from '@app/constants/errors';
import { getNextId, paginated, PaginatedResult } from '@app/lib/model';
import i18n from 'i18n';
import logger from '@app/config/logger';
import { Attachment } from '@app/models/attachment';
import preferencesService, * as prefs from '@app/models/preferences';
import {
  CreateCaseDTO,
  GetCaseDTO,
  GetCaseRelationDTO,
  ListCaseDTO,
  TagReplacement,
} from '@app/types/case';
import env from '@app/config/env';
import { slugify } from '@app/utils/string';
import { User } from '@app/models/user';
import { Folder } from '@app/models/folder';
import { startWorkflow } from '../temporal/client';
import { AttachmentData } from '../temporal/activities/attachment';

const getCases = async (req: Request) => {
  const q: ListCaseDTO = req.query as any;
  const sql = req.models.TestCase.query()
    .orderBy('uid', 'desc')
    .where({
      projectUid: req.locals.project.uid,
      deletedAt: null,
      active: true,
    })
    .select('*')
    .where((builder) => {
      if (q.tagUids) {
        builder.whereRaw(
          // eslint-disable-next-line @typescript-eslint/quotes
          "TRANSLATE((\"customFields\"->'tagUids')::JSONB::TEXT,'[]','{}')::TEXT[] && ?",
          [q.tagUids],
        );
      }

      if (q.parentUid) {
        builder.where('parentUid', q.parentUid);
      }

      if (q.priority) {
        builder.where({ priority: q.priority });
      }

      if (q.status) {
        builder.where({ status: q.status });
      }

      if (q.q) {
        builder.whereILike('name', `%${q.q}%`);
      }
      return builder;
    });

  const page = await paginated<TestCase>(
    sql as any,
    q.limit,
    q.offset,
    req.knexDB,
  );

  return page;
};

const getCaseRelations = async (req: Request) => {
  const dto: GetCaseRelationDTO = req.query as any;

  switch (dto.relation) {
    case 'tag':
      return req.models.TestCase.getTags(req.knexDB, dto.caseUids);
    case 'creator':
      return req.models.TestCase.getCreators(
        req.knexDB,
        req.sharedKnexDB,
        dto.caseUids,
      );
    default:
      throw new ApplicationError(StatusCodes.CONFLICT, '');
  }
};

/**
 * getSummary
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.id
 */
const getSummary = async (req: Request) => {
  const { caseId } = req.params;
  const before30Days = dayjs()
    .subtract(30, 'day')
    .format('YYYY-MM-DD hh:mm:ss');
  const executions = await req.models.TestExecution.query()
    .select('*')
    .where('createdAt', '>=', before30Days)
    .where('testCaseRef', caseId);
  const runs = await req.models.TestCase.query()
    .select({
      testRunUid: 'testRuns.uid',
      testRunSource: 'testRuns.source',
      testRunExternalId: 'testRuns.externalId',
      testRunName: 'testRuns.name',
      testRunDate: 'testRuns.createdAt',
    })
    .join('testRuns', 'testRuns.uid', '=', 'testCases.testRunUid')
    .where('testRuns.createdAt', '>=', before30Days)
    .where('testCases.uid', caseId)
    .where('testCases.deletedAt', null);
  return { executions, runs };
};

/**
 * Create a test cases
 * POST
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.body.org
 * @param {String} req.body.externalId
 * @param {String} req.body.source
 * @param {String} req.body.name
 * @param {Object} req.body.customFields
 * @param {Object[]} req.body.steps
 */
const createCase = async (req: Request) => {
  const {
    name,
    externalId,
    source,
    parentId,
    templateId,
    customFields,
    priority,
    steps,
    tagIds,
  } = req.body;
  const trx = await req.knexDB.transaction();
  const owner = req.locals.handle.ownerUid;
  const { ownerType } = req.locals.handle;
  const caseSteps: Array<Partial<TestCaseStep>> = [];
  const fgaWrites: FGARawWrite[] = [];
  const caseTags: Array<Partial<TestCaseTag>> = [];
  const { defaults } = await preferencesService.getDefaultsAndCompleted(
    req.sharedKnexDB,
    ownerType,
    req.locals.handle.ownerUid,
  );

  const sharedStepIds = new Set<number>();
  if (steps && steps.length > 0) {
    for (const step of steps) {
      if (step.sharedStepUid) {
        sharedStepIds.add(step.sharedStepUid);
      }
    }
  }

  const uid = await getNextId(trx, req.models.TestCase);

  const testCase = {
    externalId: externalId || '',
    source: source || '',
    name: name || '',
    customFields,
    projectUid: req.locals.project.uid,
    parentUid: parentId,
    priority: priority || defaults?.testCase.priority,
    ...(templateId && { testTemplateUid: templateId }),
    active: true,
    version: 1,
    createdBy: req.locals.user.uid,
    uid,
    testCaseRef: uid,
    steps,
  };

  const sharedStepsArray = [...sharedStepIds];

  if (sharedStepsArray?.length > 0) {
    caseSteps.push(
      ...sharedStepsArray.map((sharedStepId: number) => ({
        testCaseRef: uid,
        sharedTestStepRef: sharedStepId,
        testCaseAddedVersion: [1],
      })),
    );
  }

  if (tagIds && tagIds.length > 0) {
    for (const tagId of tagIds) {
      caseTags.push({ tagUid: tagId, testCaseRef: uid });
    }
  }

  fgaWrites.push({
    objectType: 'case',
    objectId: uid,
    relation: 'owner',
    subjectType: ownerType, // type of entity
    subjectId: owner, // entity id
  });

  const caseQuery = await req.models.TestCase.query(trx)
    .insert(testCase)
    .returning('*');

  if (parentId) {
    await attachParent(caseQuery.testCaseRef, 1, parentId, trx);
  }

  if (caseSteps.length > 0) {
    await req.models.TestCaseStep.query(trx).insert(caseSteps);
  }

  if (caseTags.length > 0) {
    await req.models.TestCaseTag.query(trx).insert(caseTags).returning('*');
  }

  await req.fga.create(...fgaWrites);

  await trx.commit();

  return caseQuery;
};

const createCases = async (req: Request) => {
  const { ownerType, ownerUid: owner } = req.locals.handle;
  const trx = await req.knexDB.transaction();

  try {
    const dto: CreateCaseDTO[] = await Folder.populateFolders(
      trx,
      req.body.cases,
      req.locals.project.uid,
    );
    const pref = await preferencesService.findOne(
      req.sharedKnexDB,
      ownerType,
      owner,
    );
    const defaults = preferencesService.filterDefaults(pref);

    const statuses: Record<prefs.EntityType, prefs.Status[]> = {} as any;
    statuses.testCase = [];
    for (const p of _.sortBy(pref.statusColors, (s) => s.id)) {
      if (statuses[p.entityType]) statuses[p.entityType].push(p);
      else statuses[p.entityType] = [p];
    }

    const priorities: Record<prefs.EntityType, prefs.Priority[]> = {} as any;
    priorities.testCase = [];
    for (const p of _.sortBy(pref.priorityColors, (p) => p.id)) {
      if (priorities[p.entityType]) priorities[p.entityType].push(p);
      else priorities[p.entityType] = [p];
    }

    const cases: Array<Partial<TestCase>> = [];
    const caseSteps: Array<Partial<TestCaseStep>> = [];
    const caseTags: Array<Partial<TestCaseTag>> = [];
    const fgaWrites: FGARawWrite[] = [];

    const tagsCache: Record<string, Tag> = {};

    for (const item of dto) {
      const sharedStepIds = new Set<number>();

      if (item.steps && item.steps.length > 0) {
        for (const step of item.steps) {
          if (step.sharedStepUid) {
            sharedStepIds.add(step.sharedStepUid);
          }
        }
      }
      const uid = await getNextId(trx, req.models.TestCase);

      if (item.parentId) {
        // check if it's a folder
        const folder = await req.models.Tag.query(trx)
          .where({ systemType: 'folder', uid: item.parentId, deletedAt: null })
          .first();
        if (!folder) {
          throw new ApplicationError(
            StatusCodes.UNPROCESSABLE_ENTITY,
            errorConstants.FOLDER_NOT_FOUND,
          );
        }
      }

      const nullValue: any = trx.raw('null');

      let status = defaults?.testCase.status;
      if (item.statusText && item.statusText.trim().length > 0) {
        const existing = statuses.testCase.find((s) => new RegExp(`^\\s*${s.name}\\s*$`, 'i').test(item.statusText));
        if (existing) status = existing.id;
        else {
          const newStatus: prefs.Status = {
            color: '#ccc',
            entityType: 'testCase',
            id: statuses.testCase[statuses.testCase.length - 1].id + 1,
            isCompleted: false,
            isDefault: false,
            isSuccess: false,
            isFailure: false,
            aliases: [],
            name: item.statusText.trim().toLowerCase(),
          };
          status = newStatus.id;
          statuses.testCase.push(newStatus);
        }
      }

      let priority = defaults?.testCase.priority;
      if (item.priorityText && item.priorityText.trim().length > 0) {
        const existing = priorities.testCase.find((s) => new RegExp(`^\\s*${s.name}\\s*$`, 'i').test(item.priorityText));

        if (existing) priority = existing.id;
        else {
          const newPriority: prefs.Priority = {
            color: '#ccc',
            entityType: 'testCase',
            id: statuses.testCase[statuses.testCase.length - 1].id + 1,
            isDefault: false,
            name: item.statusText.trim().toLowerCase(),
          };
          priority = newPriority.id;
          priorities.testCase.push(newPriority);
        }
      }

      cases.push({
        externalId: item.externalId || '',
        source: item.source || '',
        name: item.name || '',
        customFields: item.customFields,
        projectUid: req.locals.project.uid,
        repoUid: item.repoUID,
        priority,
        status,
        ...(item.templateId && { testTemplateUid: item.templateId }),
        active: true,
        version: 1,
        createdBy: req.locals.user.uid,
        uid,
        testCaseRef: uid,
        steps: item.steps,
        externalCreatedAt: item.externalCreatedAt ?? nullValue,
        externalUpdatedAt: item.externalUpdatedAt ?? nullValue,
        parentUid: item.parentId,
      });

      const sharedStepsArray = [...sharedStepIds];

      if (sharedStepsArray?.length > 0) {
        caseSteps.push(
          ...sharedStepsArray.map((sharedStepId: number) => ({
            testCaseRef: uid,
            sharedTestStepRef: sharedStepId,
            testCaseAddedVersion: [1],
          })),
        );
      }

      if (item.tagIds && item.tagIds.length > 0) {
        for (const tagId of item.tagIds) {
          caseTags.push({
            tagUid: tagId,
            testCaseRef: uid,
            testCaseAddedVersion: [1],
          });
        }
      } else if (item.tags && item.tags.length > 0) {
        for (const t of item.tags) {
          const slug = slugify(t);
          if (tagsCache[slug]) {
            caseTags.push({
              tagUid: tagsCache[slug].uid,
              testCaseRef: uid,
              testCaseAddedVersion: [1],
            });
            continue;
          }

          const [existingTag] = await req.models.Tag.findByName(trx, t, [
            'cases',
          ]);

          if (existingTag) {
            tagsCache[slug] = existingTag;
          } else {
            tagsCache[slug] = await req.models.Tag.query(trx).insert({
              name: t,
              entityTypes: ['cases', 'executions'],
            });
          }

          caseTags.push({
            tagUid: tagsCache[slug].uid,
            testCaseRef: uid,
            testCaseAddedVersion: [1],
          });
        }
      }

      fgaWrites.push({
        objectType: 'case',
        objectId: uid,
        relation: 'owner',
        subjectType: ownerType, // type of entity
        subjectId: owner, // entity id
      });
    }
    const savedCases = await req.models.TestCase.query(trx)
      .insert(cases)
      .returning('*');

    if (caseSteps.length > 0) {
      await req.models.TestCaseStep.query(trx).insert(caseSteps);
    }

    if (caseTags.length > 0) {
      await req.models.TestCaseTag.query(trx)
        .toKnexQuery()
        .insert(caseTags)
        .returning('*');
    }

    await Promise.all(
      _.chunk(fgaWrites, 100).map((group) => req.fga.create(...group)),
    );
    await trx.commit();

    await preferencesService.save(req.sharedKnexDB, ownerType, owner, {
      priorityColors: flatten(Object.values(priorities)),
      statusColors: flatten(Object.values(statuses)),
    });
    return savedCases;
  } catch (err) {
    await trx.rollback();

    if (err instanceof ForeignKeyViolationError) {
      let msg: string;
      if (/template/.test(err.table)) {
        msg = errorConstants.INVALID_TEMPLATE_UID;
      } else if (/testCaseSteps/.test(err.table)) {
        msg = errorConstants.INVALID_SHARED_STEP_UID;
      }
      throw new ApplicationError(StatusCodes.UNPROCESSABLE_ENTITY, msg);
    }
    throw err;
  }
};

/*
 * get a list of test cases by filter
 * @param {Object} req
 * @param {Object} res
 * @param {String} filter
 *
 */
const getFilteredCases = async (req: Request, filter: any) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const offset = (page - 1) * limit;

  const cases = await req.models.TestCase.query()
    .withGraphFetched('tags(isCaseTag)')
    .select('testCases.*')
    .where(filter)
    .where('active', true)
    .whereNull('testCases.deletedAt')
    .orderBy('name')
    .limit(limit)
    .offset(offset);
  const userIds = cases.filter((c) => !!c.createdBy).map((c) => c.createdBy);

  const users = _.keyBy(
    await req.models.User.query()
      .whereIn('uid', userIds)
      .select('uid', 'firstName', 'lastName', 'email', 'avatar'),
    (u) => u.uid,
  );

  for (const tc of cases) tc.creator = users[tc.createdBy] ?? null;

  return { cases };
};

/**
 * get a list of test cases by project
 * @param req {Object} req
 * @param res {Object} res
 * @returns
 */
const getCasesRelatedToProject = async (req: Request) => getFilteredCases(req, {
  'testCases.projectUid': req.locals.project.uid,
});

/**
 * get a list of test cases by folder
 * @param req {Object} req
 * @param res {Object} res
 * @returns
 */
const getCasesRelatedToFolder = async (req: Request) => {
  const { id } = req.params;
  let filter: object = { 'testCases.parentUid': id };

  if (req.query.priority) {
    filter = {
      ...filter,
      'testCases.priority': req.query.priority,
    };
  }

  if (req.query.tag) {
    filter = {
      ...filter,
      'testCases.customFields.tag': req.query.tag,
    };
  }

  if (req.query.searchKey) {
    filter = {
      ...filter,
      'testCases.name': req.query.searchKey,
    };
  }

  return getFilteredCases(req, filter);
};

/**
 * Update a test case, including adding new steps or updating existing ones
 * PATCH
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.id
 * @param {Object} req.body - The updated test case data
 */
const updateTestCase = async (req: Request) => {
  const { id: caseId } = req.params;
  const {
    name,
    customFields,
    priority,
    repoUID,
    parentId,
    source,
    steps,
    templateId,
    externalId,
    tagReplacements,
  } = req.body;
  const trx = await req.knexDB.transaction();

  try {
    const sharedStepIds = [];
    const caseSteps = [];

    // handle step updates
    if (steps && steps.length > 0) {
      for (const step of steps) {
        if (step.sharedStepUid) {
          sharedStepIds.push(step.uid);
        } else {
          caseSteps.push({
            id: step.id,
            title: step.title,
            description: step.description,
            expectedResult: step.expectedResult,
            children: step.children,
          });
        }
      }
    }
    const incomingUpdate = {
      ...(name && { name }),
      ...(externalId && { externalId }),
      ...(priority && { priority }),
      ...(customFields && { customFields }),
      ...(repoUID && { repoUid: repoUID }),
      ...(source && { source }),
      ...(parentId && { parentUid: parentId }),
      ...(caseSteps && { steps: caseSteps }),
      ...(templateId && { testTemplateUid: templateId }),
    };

    // get the latest version
    // mark all versions as inactive
    // create new version
    const [latestCase] = await Promise.all([
      req.models.TestCase.query(trx)
        .where({ testCaseRef: caseId })
        .orderBy('version', 'DESC')
        .first(),
      req.models.TestCase.query(trx)
        .where({ testCaseRef: caseId })
        .patch({ active: false }),
    ]);

    incomingUpdate.customFields = {
      ...latestCase.customFields,
      ...incomingUpdate.customFields,
    };

    const newFields = {
      ...incomingUpdate,
      version: latestCase.version + 1,
      createdBy: latestCase.createdBy,
      active: true,
    };

    Object.keys(latestCase).forEach((key) => {
      if (
        key !== 'uid'
        && key.indexOf('At') < 0
        // eslint-disable-next-line no-prototype-builtins
        && !newFields.hasOwnProperty(key)
        && latestCase[key]
      ) {
        newFields[key] = latestCase[key];
      }
    });
    const newCase = await req.models.TestCase.query(trx)
      .insert(newFields)
      .returning('*');

    await attachParent(
      latestCase.testCaseRef,
      newFields.version,
      parentId,
      trx,
    );

    const currentTestCaseSteps = await req.models.TestCaseStep.query(trx)
      .whereNull('deletedAt')
      .where('testCaseRef', caseId)
      .select('sharedTestStepRef', 'testCaseAddedVersion');

    const currentSharedStepIds = currentTestCaseSteps.map(
      (step) => step.sharedTestStepRef,
    );

    const removedSharedStepIds = currentSharedStepIds.filter(
      (id) => !sharedStepIds.includes(id),
    );

    if (removedSharedStepIds.length > 0) {
      await req.models.TestCaseStep.query(trx)
        .where('testCaseRef', caseId)
        .whereIn('sharedTestStepRef', removedSharedStepIds)
        .patch({
          deletedAt: req.knexDB.fn.now(),
        });
    }

    if (sharedStepIds && sharedStepIds.length > 0) {
      const testCaseSteps = sharedStepIds.map((sharedStepId: number) => {
        const existingStep = currentTestCaseSteps.find(
          (step) => step.sharedTestStepRef === sharedStepId,
        );
        return {
          testCaseRef: newCase.uid,
          sharedTestStepRef: sharedStepId,
          testCaseAddedVersion: existingStep
            ? [...existingStep.testCaseAddedVersion, newFields.version]
            : [newFields.version],
        };
      });
      await req.models.TestCaseStep.query(trx).insert(testCaseSteps);
    }

    const { tagIds } = req.body;

    if (tagIds || tagReplacements) {
      await updateTags(
        trx,
        newCase.testCaseRef,
        newCase.version,
        tagIds,
        tagReplacements,
      );

      await newCase.$fetchGraph('tags(isCaseTag)', { transaction: trx });
    }

    if (parentId) await attachParent(newCase.testCaseRef, newCase.version, parentId, trx);

    await trx.commit();
    try {
      if (newCase.source === 'testrail') {
        startWorkflow('integrationEntitiesWorkflow', {
          taskQueue: 'integration-entities-queue',
          workflowId: `${req.locals.handle.ownerUid}:case:${Date.now()}`,
          args: [
            {
              tenantUid: req.locals.handle.ownerUid,
              task: 'updateEntity',
              entityType: 'case',
              data: newCase,
            },
          ],
        });
      }
    } catch (error) {
      logger.error(`Error updating single entity ${newCase.uid}:`, error);
    }

    return newCase;
  } catch (error) {
    await trx.rollback();
    logger.error(`test case cannot be updated testCaseUId: ${caseId}`, error);
    throw error;
  }
};

/**
 * attaches a parent to the test case
 * @param latestCase test case details
 * @param version incoming version of the test case
 * @param parentId parent to be attached
 * @param trx db transaction
 * @returns
 */
export async function attachParent(
  caseRef: number,
  version: number,
  parentId: number,
  trx: Knex,
) {
  if (!parentId) return;

  // clear all existing attachments
  await trx.raw(
    `update "testCaseTags" as tc set "deletedAt"=current_timestamp, "testCaseRemovedVersion" = array_append("testCaseRemovedVersion",:version)
    from tags where "tagUid"=tags.uid and tags."systemType"='folder' and "testCaseRef"=:caseRef and tc."deletedAt" is null
    and "tagUid" <> :parentId
    `,
    { caseRef, version, parentId },
  );

  const attachment = await TestCaseTag.query(trx)
    .where({
      testCaseRef: caseRef,
      tagUid: parentId,
    })
    .first();

  if (!attachment) {
    const folder = await Tag.query(trx)
      .where({
        systemType: 'folder',
        deletedAt: null,
        uid: parentId,
      })
      .first();
    if (!folder) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errorConstants.FOLDER_NOT_FOUND,
      );
    }
    return;
  }

  if (attachment.deletedAt) {
    await TestCaseTag.query(trx)
      .findById(attachment.uid)
      .patch({
        deletedAt: trx.raw('null'),
        testCaseAddedVersion: trx.raw(
          'array_append("testCaseAddedVersion",?)',
          [version],
        ),
      });
  }
}

/**
 * Update test cases, including adding new steps or updating existing ones
 * PATCH
 * @param {Object} req
 * @param {Object} res
 * @param {Object} req.body.cases - The updated test case data
 */
const updateTestCases = async (req: Request, res: Response) => {
  const { cases } = req.body;
  const failedUpdates = [];

  for (const testCase of cases) {
    const trx = await req.knexDB.transaction();
    try {
      const {
        name,
        customFields,
        repoUID,
        source,
        steps,
        parentId,
        testCaseRef: caseId,
        templateId,
        priority,
        tagReplacements,
      } = testCase;

      const externalId = testCase.externalId || ' ';
      const incomingUpdate = {
        ...(name && { name }),
        ...(externalId && { externalId }),
        ...(customFields && { customFields }),
        ...(repoUID && { repoUid: repoUID }),
        ...(templateId && { testTemplateUid: templateId }),
        ...(parentId && { parentUid: parentId }),
        ...(source && { source }),
        ...(steps && { steps }),
        ...(priority && { priority }),
      };

      // get the latest version
      // mark all versions as inactive
      // create new version
      const [latestCase] = await Promise.all([
        req.models.TestCase.query(trx)
          .where({ testCaseRef: caseId })
          .orderBy('version', 'DESC')
          .first(),
        req.models.TestCase.query(trx)
          .where({ testCaseRef: caseId })
          .patch({ active: false }),
      ]);

      incomingUpdate.customFields = {
        ...latestCase.customFields,
        ...incomingUpdate.customFields,
      };

      const version = latestCase.version + 1;

      const newCase = await req.models.TestCase.query(trx).insert({
        ...incomingUpdate,
        createdBy: latestCase.createdBy,
        projectUid: req.locals.project.uid,
        testCaseRef: caseId,
        version,
        active: true,
      });

      await attachParent(latestCase.testCaseRef, version, parentId, trx);

      await updateTags(
        trx,
        newCase.testCaseRef,
        newCase.version,
        testCase.tagIds,
        tagReplacements,
      );

      // Commit the transaction
      await trx.commit();
    } catch (error) {
      logger.error(error);
      await trx.rollback();
      failedUpdates.push(testCase, error);
    }
  }
  if (failedUpdates.length === 0) {
    return {
      message: 'Test cases updated successfully',
    };
  }

  res.status(StatusCodes.MULTI_STATUS);
  return { message: errorConstants.PROBLEM_UPDATING_TEST_CASE, failedUpdates };
};

/**
 * updateTags retrieves the current list of tags for a case, append the tagIds, replaces
 * replacements.existingTagIds with replacements.newTagIds and marks all other tagIds as removed
 * @param caseRef
 * @param version
 * @param tagIds
 * @param replacements
 */
async function updateTags(
  trx: Knex.Transaction,
  caseRef: number,
  version: number,
  tagIds: number[],
  replacements: TagReplacement[],
) {
  const existingTags = await TestCaseTag.query(trx)
    .where({
      testCaseRef: caseRef,
      systemType: 'tag',
      'testCaseTags.deletedAt': null,
    })
    .innerJoin('tags', 'tags.uid', 'tagUid')
    .select('testCaseTags.*');
  const existingTagIds = existingTags.map((t) => t.tagUid);
  const finalTagIds = new Set(existingTagIds);

  if (replacements?.length > 0) {
    for (const replacement of replacements) {
      replacement.existingTagUids?.forEach((tagId) => finalTagIds.delete(tagId));
      replacement.newTagUids?.forEach((tagId) => finalTagIds.add(tagId));
    }
  }

  if (tagIds?.length > 0) {
    tagIds.forEach((tagId: number) => finalTagIds.add(tagId));
  }

  const tagList = Array.from(finalTagIds);

  const toBeRemoved = await TestCaseTag.query(trx)
    .innerJoin('tags', 'tags.uid', 'tagUid')
    .where({
      'tags.systemType': 'tag',
      testCaseRef: caseRef,
      'testCaseTags.deletedAt': null,
    })
    .where((q) => {
      if (tagList.length > 0) q.whereNotIn('tagUid', tagList);
    })
    .select('testCaseTags.uid');

  if (toBeRemoved.length > 0) {
    await TestCaseTag.query(trx)
      .whereIn(
        'uid',
        toBeRemoved.map(({ uid }) => uid),
      )
      .patch({
        deletedAt: trx.fn.now(),
        testCaseRemovedVersion: trx.raw(
          'array_append("testCaseRemovedVersion",?)',
          [version],
        ),
      });
  }

  if (tagList.length === 0) return;

  for (const tag of _.difference(tagList, existingTagIds)) {
    const insert = (await TestCaseTag.query(trx)
      .toKnexQuery()
      .insert({
        tagUid: tag,
        testCaseRef: caseRef,
        testCaseAddedVersion: [version],
      })
      .onConflict(['testCaseRef', 'tagUid'])
      .ignore()) as any;

    if (insert.rowCount > 0) continue;

    await TestCaseTag.query(trx)
      .where({ tagUid: tag, testCaseRef: caseRef })
      .patch({
        deletedAt: trx.raw('null'),
        testCaseRemovedVersion: trx.raw(
          'array_append("testCaseRemovedVersion",?)',
          [version],
        ),
      });
  }
}

/**
 * get a list of test cases by text query
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.locals.project.uid
 * @param {String} req.query.query
 */
const searchCases = async (req: Request) => {
  const { query, tag, priority } = req.query;
  // get cases owned by the org openfga
  const owner = req.locals.handle.ownerUid;
  const { ownerType } = req.locals.handle;
  const projectId = req.locals.project.uid;
  const relatedCases = (
    await req.fga.query(`${ownerType}:${owner}`, 'case:', 'owner')
  ).map((tuple: any) => tuple.key.object.split(':')[1]);

  // get cases owned by the org from database
  const cases = await req.models.TestCase.query()
    .select('*')
    .orWhereRaw(
      'externalId ilike :token or source ilike :token or name ilike :token',
      { token: `%${query}%` },
    )
    .whereIn('uid', relatedCases)
    .where('deletedAt', null)
    .where((builder) => {
      if (projectId) {
        builder.where('projectUid', projectId);
      }
      if (tag) {
        builder.orWhereRaw('customFields->>\'tag\' ilike :tag', {
          tag: `%${tag}%`,
        });
      }

      if (priority) {
        builder.orWhereRaw('customFields->>\'priority\' ilike :priority', {
          priority: `%${priority}%`,
        });
      }
    });

  return { cases };
};

/**
 * get a list of test executions related to a test case
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.id
 * @param {Number} req.query.per_page
 * @param {Number} req.query.current_page
 * @param {String} req.query.sort_by optional
 * @param {String} req.query.sort_order optional
 * @param {String} req.query.status optional
 */

const getExecutionsByCase = async (req: Request) => {
  const { caseId } = req.params;
  const reqData = req.query as any;
  const pagination: Pagination = {} as Pagination;
  const perPage = Number(reqData.per_page) || 10;
  let page = Number(reqData.current_page) || 1;
  if (page < 1) page = 1;

  const offset = (page - 1) * perPage;
  const sortBy = reqData.sort_by || 'createdAt'; // Default sort column
  const sortOrder = reqData.sort_order === 'desc' ? 'desc' : 'asc'; // Default sort order

  let query = req.models.TestExecution.query().where('testCaseRef', caseId);

  // Add sorting
  query = query.orderBy(sortBy, sortOrder);

  // Apply pagination
  query = query.offset(offset).limit(perPage);

  const executions = await query;

  // Calculate pagination details
  const totalExecutionsCount: any = await req.models.TestExecution.query()
    .where('testCaseRef', caseId)
    .count('uid as CNT')
    .first();

  pagination.total = totalExecutionsCount.CNT;
  pagination.per_page = perPage;
  pagination.offset = offset;
  pagination.to = offset + executions.length;
  pagination.last_page = Math.ceil(totalExecutionsCount.CNT / perPage);
  pagination.current_page = page;
  pagination.from = offset;
  pagination.items = executions;

  return pagination;
};

/**
 * delete a test cases by caseId
 * DELETE
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.id
 */
const deleteCase = async (req: Request) => {
  const { id: caseId } = req.params;
  await req.knexDB.transaction(async (trx) => {
    const testCase = await req.models.TestCase.query(trx).patchAndFetchById(
      caseId,
      { deletedAt: req.knexDB.fn.now() },
    );
    await req.models.TestCaseTag.query(trx)
      .where('testCaseRef', testCase.testCaseRef)
      .patch({ deletedAt: req.knexDB.fn.now() });
  });
};

/**
 * delete a test cases by caseIds
 * DELETE
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.body.ids
 */
const deleteTestCases = async (req: Request) => {
  const { ids } = req.body;

  await req.knexDB.transaction(async (trx) => {
    const testCases = await req.models.TestCase.query(trx)
      .whereIn('uid', ids)
      .patch({ deletedAt: req.knexDB.fn.now() })
      .returning('*');

    await req.models.TestCaseTag.query(trx)
      .whereIn(
        'testCaseRef',
        testCases.map((t) => t.testCaseRef),
      )
      .patch({ deletedAt: req.knexDB.fn.now() });
  });
};

/**
 * get a test cases by caseId
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.id
 */

const getCase = async (req: Request) => {
  const { id: caseId } = req.params;

  const caseItem = (await req.models.TestCase.query()
    .withGraphFetched(
      '[tags(isCaseTag), attachments, testExecutions.[testRun]]',
    )
    .where('testCaseRef', caseId)
    .where('deletedAt', null)
    .where('active', true)
    .orderBy('version', 'DESC')
    .first()) as TestCase & {
    attachments: any[];
    testExecutions: any[];
    runs: any[];
  };

  if (!caseItem) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      i18n.__('testCaseNotFound'),
    );
  }

  const baseURL = `${env.BACKEND_URL}${env.API_INTERNAL_ROUTE}/${req.params.handle}`;
  req.models.TestCase.formatAttachment(caseItem, baseURL);

  caseItem.runs = caseItem.testExecutions?.map((execution) => ({
    name: execution.testRun?.name,
    runUid: execution.testRun?.uid,
    executionUid: execution.uid,
    status: execution.testRun?.status,
    externalId: caseItem.externalId,
    caseUid: caseItem.uid,
  })) || [];

  delete caseItem.testExecutions;

  return caseItem;
};

const getCaseByVersion = async (req: Request) => {
  const { id: caseRef, versionId } = req.params;

  const testCase = await req.models.TestCase.query()
    .where({ uid: versionId, testCaseRef: caseRef })
    .withGraphFetched('[tags(isCaseTag), attachments]')
    .first();

  if (!testCase) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      i18n.__('testCaseNotFound'),
    );
  }

  const baseURL = `${env.BACKEND_URL}${env.API_INTERNAL_ROUTE}/${req.params.handle}`;
  req.models.TestCase.formatAttachment(testCase, baseURL);

  return testCase;
};

/**
 * @deprecated
 */
const groupByFolder = async (req: Request) => {
  const q: GetCaseDTO = req.query as any;
  const folderId = q.folderId ?? null;

  const casesQuery = req.models.TestCase.query()
    .withGraphFetched('[tags(isCaseTag), attachments]')
    .modifyGraph('tags(isCaseTag)', (q) => {
      q.select('tags.uid', 'tags.name');
    })
    .orderBy('testCases.uid', 'desc')
    .groupBy('testCases.uid')
    .where({
      'testCases.parentUid': folderId,
      'testCases.active': true,
      'testCases.deletedAt': null,
      projectUid: req.locals.project.uid,
    })
    .where((builder) => {
      if (q.priority) builder.where('priority', q.priority);
    })
    .select('testCases.*');

  const { items, ...page }: PaginatedResult<ModelObject<TestCase>> = await paginated(casesQuery as any, q.limit, q.offset, req.knexDB);

  const userIds = [];
  const caseIds: number[] = [];
  for (const c of items) {
    caseIds.push(c.uid);
    if (!_.isNull(c.createdBy)) userIds.push(c.createdBy);
  }

  const userSelects = ['uid', 'firstName', 'lastName', 'email', 'avatar'];

  const [folders, creatorList] = await Promise.all([
    req.models.Folder.findChildren(req.knexDB, folderId),
    userIds.length > 0
      ? req.models.User.query().whereIn('uid', userIds).select(userSelects)
      : <User[]>[],
  ]);
  const creators = _.keyBy(creatorList, (u) => u.uid);

  const executions = await req.models.TestExecution.query()
    .whereIn('testCaseUid', caseIds)
    .whereNull('testExecutions.deletedAt')
    .select([
      'testRuns.name',
      'testExecutions.uid as executionUid',
      'testRuns.uid as runUid',
      'testRuns.status as status',
      'testCases.externalId',
      'testCases.uid as caseUid',
    ])
    .innerJoin('testRuns', 'testRuns.uid', 'testExecutions.testRunUid')
    .innerJoin('testCases', 'testCases.uid', 'testExecutions.testCaseUid')
    .orderBy('testRuns.updatedAt', 'desc');

  const baseURL = `${env.BACKEND_URL}${env.API_INTERNAL_ROUTE}/${req.params.handle}`;

  (<any>page).cases = items.map((item) => {
    const attachments = item.attachments.map(({ uid, name, fileType }) => ({
      previewUrl: `${baseURL}/cases/attachments/${uid}/object`,
      name,
      type: fileType,
      uid,
    }));
    const runs = executions.filter(
      (element: any) => element.caseUid === item.uid,
    );
    return {
      ...item,
      parentUid: folderId ? +folderId : null,
      runs,
      attachments,
      creator: creators[item.createdBy] ?? null,
    };
  });

  (<any>page).folders = folders;

  return page;
};

const uploadAttachment = async (req: Request) => {
  const { fileType, size, fileName } = req.body;
  const { id } = req.params;
  const { ownerUid } = req.locals.handle;
  const creatorUid = req.locals.user?.uid || null;
  const newAttachment = {
    name: fileName,
    ownerUid,
    size,
    fileType,
    mediaType: 'attachment',
    creatorUid,
  };

  const trx = await req.knexDB.transaction();

  try {
    const {
      key,
      signed_url: signedUrl,
      object_url: objectUrl,
      client_headers: clientHeaders,
      uid,
    } = await Attachment.createAttachment(newAttachment, trx);

    await trx('caseAttachments').insert({
      attachmentUid: uid,
      caseRef: id,
    });

    await trx.commit();

    return {
      uid,
      objectKey: key,
      signedUrl,
      objectUrl,
      clientHeaders,
    };
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const deleteAttachment = async (req: Request, res: Response) => {
  const trx = await req.knexDB.transaction();
  const attachmentId = <string>req.params.id;
  const { ownerUid } = req.locals.handle;
  try {
    const attachment = await req.models.Attachment.query(trx).findOne({
      uid: attachmentId,
    });
    const { key } = attachment;

    if (!attachment) return res.status(404).send();

    const param: AttachmentData = {
      type: 'delete',
      key,
      ownerUid,
    };

    await Promise.all([
      startWorkflow('attachmentWorkflow', {
        taskQueue: 'attachment-queue',
        workflowId: `${ownerUid}:attachment:delete:${Date.now()}`,
        args: [param],
      }),
      Attachment.deleteAttachment(trx, attachmentId, 'caseAttachments'),
    ]);
    await trx.commit();
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

/**
 * Get count of test cases for a specific project
 * GET
 * @param {Object} req
 */
const getCasesCountByProject = async (req: Request) => {
  const count = await req.models.Project.countCases(
    req.knexDB,
    req.locals.project.uid,
  );
  return { count };
};

export default {
  createCases: httpHandler(createCases),
  createCase: httpHandler(createCase),
  updateTestCase: httpHandler(updateTestCase),
  updateTestCases: httpHandler(updateTestCases),
  getCase: httpHandler(getCase),
  searchCases: httpHandler(searchCases),
  deleteCase: httpHandler(deleteCase),
  deleteCases: httpHandler(deleteTestCases),
  getSummary: httpHandler(getSummary),
  getExecutionsByCase: httpHandler(getExecutionsByCase),
  getCasesRelatedToProject: httpHandler(getCasesRelatedToProject),
  getCasesRelatedToFolder: httpHandler(getCasesRelatedToFolder),
  groupByFolder: httpHandler(groupByFolder),
  getCases: httpHandler(getCases),
  uploadAttachment: httpHandler(uploadAttachment),
  deleteAttachment: httpHandler(deleteAttachment),
  getCaseByVersion: httpHandler(getCaseByVersion),
  getCasesCountByProject: httpHandler(getCasesCountByProject),
  getCaseRelations: httpHandler(getCaseRelations),
};
