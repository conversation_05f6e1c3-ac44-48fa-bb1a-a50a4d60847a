/* eslint-disable @typescript-eslint/quotes */
import { ApplicationError, httpHandler } from '@app/lib/http';

import { Request } from 'express';
import { StatusCodes } from 'http-status-codes';
import { TestPlan } from '@app/models/testPlan';
import errorConstants from '@app/constants/errors';
import {
  BulkUpdatePlanDTO,
  GetPlanRelationsDTO,
  ListPlanDTO,
  UpdatePlanDTO,
} from '@app/types/plan';
import { paginated } from '@app/lib/model';
import Objection, { ForeignKeyViolationError } from 'objection';
import _ from 'lodash';
import { FGARawWrite } from '@ss-libs/ss-component-auth';
import preferencesService from '@app/models/preferences';
import logger from '@app/config/logger';
import { startWorkflow } from '../temporal/client';
import { UpdatePlanStateDTO } from '../temporal/activities/plan';
/**
 * create a test plan
 * POST
 * @param {Object} req
 */
const createPlan = async (req: Request) => {
  const { defaults } = await preferencesService.getDefaultsAndCompleted(
    req.sharedKnexDB,
    req.locals.handle.ownerType,
    req.locals.handle.ownerUid,
  );

  const trx = await req.knexDB.transaction();
  try {
    const { plan, executions, runs } = await req.models.TestPlan.newPlan(
      {
        ...req.body,
      },
      req.locals.project.uid,
      defaults,
      trx,
    );
    const { ownerType, ownerUid } = req.locals.handle;

    const fgaWrites: FGARawWrite[] = [
      {
        objectType: 'plan',
        objectId: plan.uid,
        subjectType: ownerType,
        subjectId: ownerUid,
        relation: 'owner',
      },
      ...runs.map((r) => ({
        objectType: 'run',
        objectId: r.uid,
        relation: 'owner',
        subjectType: ownerType,
        subjectId: ownerUid,
      })),
      ...executions.map((e) => ({
        objectType: 'execution',
        objectId: e.uid,
        relation: 'owner',
        subjectType: ownerType,
        subjectId: ownerUid,
      })),
    ];
    await req.fga.create(...fgaWrites);
    await trx.commit();

    const param: UpdatePlanStateDTO = {
      planUids: [plan.uid],
      ownerType,
      ownerUid,
    };
    await startWorkflow('updatePlanWorkflow', {
      taskQueue: 'update-plan-queue',
      workflowId: `update.plan.${plan.uid}.${Date.now()}`,
      args: [param],
    });
    return plan;
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

/**
 * Duplicate Test Plan
 * POST
 */

const duplicatePlan = async (req: Request) => {
  const { defaults } = await preferencesService.getDefaultsAndCompleted(
    req.sharedKnexDB,
    req.locals.handle.ownerType,
    req.locals.handle.ownerUid,
  );
  const trx = await req.knexDB.transaction();
  try {
    const plans: TestPlan[] = [];

    const { ownerType, ownerUid } = req.locals.handle;
    const fgaWrites: FGARawWrite[] = [];

    const results = await req.models.TestPlan.duplicate(
      trx,
      req.body.plans,
      req.locals.project.uid,
      defaults,
    );
    results.forEach(({ executions, plan, runs }) => {
      plans.push(plan);
      fgaWrites.push(
        {
          objectType: 'plan',
          objectId: plan.uid,
          subjectType: ownerType,
          subjectId: ownerUid,
          relation: 'owner',
        },
        ...runs.map((r) => ({
          objectType: 'run',
          objectId: r.uid,
          relation: 'owner',
          subjectType: ownerType,
          subjectId: ownerUid,
        })),
        ...executions.map((e) => ({
          objectType: 'execution',
          objectId: e.uid,
          relation: 'owner',
          subjectType: ownerType,
          subjectId: ownerUid,
        })),
      );
    });
    await req.fga.create(...fgaWrites);
    await trx.commit();

    const param: UpdatePlanStateDTO = {
      planUids: plans.map((p) => p.uid),
      ownerType,
      ownerUid,
    };
    await startWorkflow('updatePlanWorkflow', {
      taskQueue: 'update-plan-queue',
      workflowId: `update.plan.${plans[0].uid}.${Date.now()}`,
      args: [param],
    });

    return plans;
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const getPlans = async (req: Request) => {
  const q: ListPlanDTO = <any>req.query;
  const sql = req.models.TestPlan.query()
    .where({
      'tags.deletedAt': null,
      systemType: 'plan',
      'tags.projectUid': req.locals.project.uid,
    })
    .select('tags.*')
    .orderBy('tags.createdAt', 'asc')
    .where((w) => {
      if (q.status) {
        w.whereRaw(`tags."customFields"->>'status' = ?`, [q.status]);
      }

      if (q.priority) w.where("customFields->>'priority'", q.status);

      if (q.minProgress) {
        w.whereRaw(`(tags."customFields"->>'progress')::decimal >= ?`, [
          q.minProgress,
        ]);
      }

      if (q.maxProgress) {
        w.whereRaw(`(tags."customFields"->>'progress')::decimal <= ?`, [
          q.maxProgress,
        ]);
      }

      if (q.statusUids?.length > 0) {
        w.whereRaw(`(tags."customFields"->>'status')::int in (??)`, [
          q.statusUids,
        ]);
      }

      if (q.priorityUids?.length > 0) {
        w.whereRaw(`(tags."customFields"->>'priority')::int in (??)`, [
          q.priorityUids,
        ]);
      }

      if (q.minCreatedAt) w.where('tags.createdAt', '>=', q.minCreatedAt);
      if (q.maxCreatedAt) w.where('tags.createdAt', '<=', q.maxCreatedAt);
      if (q.archived === true) w.whereNotNull('tags.archivedAt');
      else if (q.archived === false) w.whereNull('tags.archivedAt');

      return w;
    });

  if (q.tagUids?.length > 0) {
    // convert the tagUids in JSONB to TEXT[] and use the && operator
    sql.whereRaw(
      `TRANSLATE((tags."customFields"->'tagUids')::JSONB::TEXT,'[]','{}')::TEXT[] && ?`,
      [q.tagUids],
    );
  }

  if (q.milestoneUids) {
    sql.whereExists(
      TestPlan.relatedQuery('milestones').whereIn(
        'milestones.uid',
        q.milestoneUids,
      ),
    );
  }

  const page = await paginated(
    sql as any,
    q.limit,
    q.offset,
    req.knexDB,
    async (p: TestPlan) => {
      await req.models.TestPlan.populateTags(req.knexDB, p);
      return p;
    },
  );
  return page;
};

const getPlanRelations = async (req: Request) => {
  const dto: GetPlanRelationsDTO = req.query as any;

  switch (dto.relation) {
    case 'tag':
      return req.models.TestPlan.getTags(req.knexDB, dto.planUids);
    case 'milestoneCount':
      return req.models.TestPlan.getMilestoneCount(req.knexDB, dto.planUids);
    case 'runCount':
      return req.models.TestPlan.getRunCount(req.knexDB, dto.planUids);
    default:
      throw new ApplicationError(StatusCodes.CONFLICT, '');
  }
};

const bulkUpdate = async (req: Request) => {
  const trx = await req.knexDB.transaction();
  const body = req.body as BulkUpdatePlanDTO;
  let plans: TestPlan[];
  try {
    switch (body.action) {
      case 'addMilestones':
      case 'removeMilestones':
        plans = await req.models.TestPlan.bulkUpdateMilestones(
          trx,
          body.uids,
          body.milestoneUids,
          body.action,
        );
        break;

      case 'addRuns':
      case 'removeRuns':
        plans = await req.models.TestPlan.bulkUpdateRuns(
          trx,
          body.uids,
          body.runUids,
          body.action,
        );
        break;

      case 'archive':
      case 'delete':
        plans = await req.models.TestPlan.destroyMany(
          trx,
          body.uids,
          { [`${body.action}dAt`]: trx.fn.now() },
          body.action === 'archive' ? true : body.cascade,
        );
        break;

      case 'unarchive':
        plans = await req.models.TestPlan.unarchive(trx, body.uids);
        break;
      default:
        throw new ApplicationError(StatusCodes.CONFLICT, '');
    }
    await trx.commit();

    const param: UpdatePlanStateDTO = {
      planUids: plans.map((p) => p.uid),
      ownerType: req.locals.handle.ownerType,
      ownerUid: req.locals.handle.ownerUid,
    };
    await startWorkflow('updatePlanWorkflow', {
      taskQueue: 'update-plan-queue',
      workflowId: `update.plan.${plans[0].uid}.${Date.now()}`,
      args: [param],
    });
    return { affectedRows: plans.length };
  } catch (err) {
    await trx.rollback();
    if (err instanceof ForeignKeyViolationError) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        errorConstants.MILESTONE_NOT_FOUND,
      );
    }
    throw err;
  }
};

const getPlan = async (req: Request) => {
  const plan = await req.models.TestPlan.findOne(
    req.knexDB,
    { uid: req.params.id as any, deletedAt: null },
    true,
  );

  if (!plan) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.PLAN_NOT_FOUND,
    );
  }

  await req.models.TestPlan.populateTags(req.knexDB, plan);
  for (const r of plan.runs ?? []) {
    await req.models.TestRun.populateConfig(req.knexDB, r);
  }

  return plan;
};

const updatePlan = async (req: Request) => {
  const trx = await req.knexDB.transaction();
  const uid = req.params.id as unknown as number;
  const {
    milestoneUids,
    runUids,
    tagUids,
    status,
    priority,
    ...details
  }: UpdatePlanDTO = req.body;
  try {
    const plan = await req.models.TestPlan.findOne(trx, { uid }, true);
    if (!plan) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errorConstants.PLAN_NOT_FOUND,
      );
    }
    const patch: Partial<Objection.ModelObject<TestPlan>> = {
      name: details.name ?? plan.name,
      description: details.description ?? plan.description ?? '',
      customFields: { ...plan.customFields },
    };
    if (tagUids) patch.customFields.tagUids = tagUids;
    if (status) patch.customFields.status = status;
    if (priority) patch.customFields.priority = priority;

    await plan.$query(trx).patch(patch);

    if (milestoneUids) {
      await req.models.TestPlan.updateMilestones(trx, plan, milestoneUids);
    }
    const { ownerType, ownerUid } = req.locals.handle;

    if (runUids) {
      const existingRuns = plan.runs.map((r) => r.uid);
      const newRuns = _.difference(runUids, existingRuns);
      const deleteRuns = _.difference(existingRuns, runUids);

      if (deleteRuns.length) {
        await req.models.TestPlanRun.deleteByPlanUid(trx, plan.uid, deleteRuns);
      }

      if (newRuns.length) {
        const defaults = await preferencesService.getDefaults(
          req.sharedKnexDB,
          req.locals.handle.ownerType,
          req.locals.handle.ownerUid,
        );
        const runs = _.keyBy(
          await req.models.TestRun.query(trx)
            .whereIn('uid', newRuns)
            .where('deletedAt', null),
          (r) => r.uid,
        );
        const fgaWrites: FGARawWrite[] = [];
        for (const id of newRuns) {
          const run = runs[id];
          if (!run) continue;
          const r = await req.models.TestRun.duplicate(
            trx,
            {
              name: run.name,
              status: defaults.testRun?.status,
              priority: defaults.testRun?.priority,
              projectUid: req.locals.project.uid,
              configs: run.customFields?.configs,
              description: run.description,
              externalId: run.externalId,
              link: run.customFields?.link,
              source: run.source,
            },
            {
              planUid: plan.uid,
              sourceRunUid: id,
              execPriority: defaults.testCase?.priority,
              execStatus: defaults.testCase?.status,
            },
          );
          fgaWrites.push(
            {
              objectType: 'run',
              objectId: r.testRun.uid,
              relation: 'owner',
              subjectType: ownerType,
              subjectId: ownerUid,
            },
            ...r.executions.map((e) => ({
              objectType: 'execution',
              objectId: e.uid,
              relation: 'owner',
              subjectType: ownerType,
              subjectId: ownerUid,
            })),
          );
        }
        if (fgaWrites.length > 0) await req.fga.create(...fgaWrites);
      }
    }
    const param: UpdatePlanStateDTO = {
      planUids: [plan.uid],
      ownerType,
      ownerUid,
    };
    await startWorkflow('updatePlanWorkflow', {
      taskQueue: 'update-plan-queue',
      workflowId: `update.plan.${plan.uid}.${Date.now()}`,
      args: [param],
    });
    try {
      if (plan.source === 'testrail') {
        startWorkflow('integrationEntitiesWorkflow', {
          taskQueue: 'integration-entities-queue',
          workflowId: `${req.locals.handle.ownerUid}:plan:${Date.now()}`,
          args: [
            {
              entityType: 'plan',
              task: 'updateEntity',
              data: plan,
              tenantUid: req.locals.handle.ownerUid,
            },
          ],
        });
      }
    } catch (error) {
      logger.error(`Error updating single entity ${plan.uid}:`, error);
    }
    await trx.commit();
    return plan;
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const getRunsCountByPlan = async (req: Request) => {
  const count = await req.models.TestPlan.countRuns(
    req.knexDB,
    req.params.id as any,
  );
  return { count };
};

const getMilestonesCountByPlan = async (req: Request) => {
  const count = await req.models.TestPlan.countMilestones(
    req.knexDB,
    req.params.id as any,
  );
  return { count };
};

export default {
  createPlan: httpHandler(createPlan),
  duplicatePlan: httpHandler(duplicatePlan),
  getPlans: httpHandler(getPlans),
  bulkUpdate: httpHandler(bulkUpdate),
  getPlan: httpHandler(getPlan),
  updatePlan: httpHandler(updatePlan),
  getRunsCountByPlan: httpHandler(getRunsCountByPlan),
  getMilestonesCountByPlan: httpHandler(getMilestonesCountByPlan),
  getPlanRelations: httpHandler(getPlanRelations),
};
