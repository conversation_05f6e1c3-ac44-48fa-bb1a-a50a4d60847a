import { Request, Response } from 'express';
import { ApplicationError, httpHand<PERSON> } from '@app/lib/http';
import { StatusCodes } from 'http-status-codes';
import logger from '@app/config/logger';
import { IntegrationServiceFactory } from '@app/integrations/index';
import errors from '@app/constants/errors';
import { Attachment } from '@app/models/attachment';
import env from '@app/config/env';
import { Status } from '@app/models/preferences';
import { startWorkflow } from '../temporal/client';
import { AttachmentData } from '../temporal/activities/attachment';

const getDefectsOpenCount = async (req: Request) => {
  const { integrationService } = req.query;
  const count = await req.models.Defect.countOpenDefects(req.knexDB, integrationService as string, req.locals.project.uid);
  return { count };
};

const getDefectsClosedCount = async (req: Request) => {
  const { integrationService } = req.query;
  const count = await req.models.Defect.countClosedDefects(req.knexDB, integrationService as string, req.locals.project.uid);
  return { count };
};

const getDefectAttachments = async (req: Request) => {
  const { uid } = req.params;
  const attachments = await req.models.Defect.defectAttachments(req.knexDB, uid, req.locals.project.uid, req.locals.handle.name);
  return attachments;
};

const getDefectExecutions = async (req: Request) => {
  const { uid } = req.params;

  const defect = await req.models.Defect.query()
    .where('uid', uid)
    .whereNull('deletedAt')
    .withGraphFetched({
      executions: {
        testCase: true,
      },
    })
    .modifyGraph('executions', (builder) => {
      builder
        .select([
          'testExecutions.uid',
          'testExecutions.testCaseUid',
          'defectExecutions.executionUrl',
        ])
        .whereNull('testExecutions.deletedAt');
    })
    .modifyGraph('executions.testCase', (builder) => {
      builder
        .select(['uid', 'name'])
        .whereNull('deletedAt');
    })
    .first();

  if (!defect) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errors.DEFECT_NOT_FOUND);
  }

  // Transform executions with proper URLs
  const executions = (defect as any).executions?.map((execution: any) => ({
    uid: execution.uid,
    name: execution.testCase?.name,
    url: execution.executionUrl,
  })).filter((e: any) => e.name) || [];

  return executions;
};

const getDefectRuns = async (req: Request) => {
  const { uid } = req.params;

  // Verify defect exists and fetch runs in a single query
  const defect = await req.models.Defect.query()
    .where('uid', uid)
    .whereNull('deletedAt')
    .withGraphFetched({
      executions: {
        testRun: true,
      },
    })
    .modifyGraph('executions', (builder) => {
      builder
        .select([
          'testExecutions.uid',
          'testExecutions.testRunUid',
        ])
        .whereNull('testExecutions.deletedAt');
    })
    .modifyGraph('executions.testRun', (builder) => {
      builder
        .select(['uid', 'name'])
        .whereNull('deletedAt');
    })
    .first();

  if (!defect) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errors.DEFECT_NOT_FOUND);
  }

  // Extract unique runs
  const runs = (defect as any).executions?.map((execution: any) => ({
    uid: execution.testRun?.uid,
    name: execution.testRun?.name,
  })).filter((r: any) => r.name) || [];

  return [...new Map(runs.map((run) => [run.uid, run])).values()];
};

const getDefects = async (req: Request) => {
  const {
    priority,
    status,
    integration: integrationService,
    page = 1,
    limit = 10,
  } = req.query;

  let integrationUids: number[] = [];
  if (integrationService) {
    const integrations = await req.models.Integration.query()
      .select('uid')
      .where('service', (integrationService as string).toLowerCase())
      .whereNull('archivedAt')
      .whereNull('deletedAt');
    integrationUids = integrations.map((integration) => integration.uid);
  }

  // Build base query with explicit column selection
  let defectsQuery = req.models.Defect.query()
    .withGraphFetched({
      integration: true,
    })
    .modifyGraph('integration', (builder) => {
      builder
        .select(['uid', 'service', 'status'])
        .whereNull('deletedAt');
    })
    .whereRaw('? = ANY("projectUids")', [req.locals.project.uid])
    .whereNull('defects.deletedAt')
    .where((builder) => {
      builder.whereRaw('CASE WHEN "defects"."integrationSourceUid"::text ~ \'^[0-9]+$\' THEN true ELSE false END')
        .orWhereNull('defects.integrationSourceUid');
    });

  // Apply filters
  if (priority) {
    defectsQuery = defectsQuery.where('defects.priority', priority as string);
  }
  if (status) {
    defectsQuery = defectsQuery.where('defects.status', status as string);
  }
  if (integrationService) {
    defectsQuery = defectsQuery.whereIn('defects.integrationSourceUid', integrationUids);
  }

  // Apply pagination
  const offset = (Number(page) - 1) * Number(limit);
  const [defects, totalCount] = await Promise.all([
    defectsQuery.offset(offset).limit(Number(limit)),
    defectsQuery.resultSize(),
  ]);

  return {
    data: defects,
    pagination: {
      total: totalCount,
      page: Number(page),
      limit: Number(limit),
    },
  };
};

const getDefect = async (req: Request) => {
  const { uid } = req.params;
  const defect = await req.models.Defect.query()
    .where('uid', uid)
    .withGraphFetched({
      executions: {
        testCase: true,
        testRun: true,
      },
    })
    .modifyGraph('executions', (builder) => {
      builder
        .select([
          'testExecutions.uid',
          'testExecutions.testCaseUid',
          'testExecutions.testRunUid',
          'defectExecutions.executionUrl',
        ])
        .whereNull('testExecutions.deletedAt');
    })
    .modifyGraph('executions.testCase', (builder) => {
      builder
        .select(['uid', 'name'])
        .whereNull('deletedAt');
    })
    .modifyGraph('executions.testRun', (builder) => {
      builder
        .select(['uid', 'name'])
        .whereNull('deletedAt');
    })
    .first();

  if (!defect) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errors.DEFECT_NOT_FOUND);
  }

  const baseURL = `${env.BACKEND_URL}${env.API_INTERNAL_ROUTE}/${req.locals.handle.name}`;

  // Fetch attachments for this defect
  const attachments = await req.models.Attachment.query()
    .innerJoin('defectAttachments', 'attachments.uid', 'defectAttachments.attachmentUid')
    .where('defectAttachments.defectUid', defect.uid)
    .whereNull('attachments.deletedAt')
    .select('attachments.*');

  // Transform attachments with proper URLs
  const transformedAttachments = attachments.map((attachment) => ({
    uid: attachment.uid,
    name: attachment.name,
    fileType: attachment.fileType,
    previewUrl: `${baseURL}/defects/attachments/${attachment.uid}/object`,
  }));

  // Transform executions with proper URLs
  const executions = (defect as any).executions?.map((execution: any) => ({
    uid: execution.uid,
    name: execution.testCase?.name,
    url: execution.executionUrl,
  })).filter((e: any) => e.name) || [];

  // Extract unique runs
  const runs = (defect as any).executions?.map((execution: any) => ({
    uid: execution.testRun?.uid,
    name: execution.testRun?.name,
  })).filter((r: any) => r.name) || [];

  const uniqueRuns = [...new Map(runs.map((run) => [run.uid, run])).values()];

  return {
    uid: defect.uid,
    name: defect.name,
    priority: defect.priority,
    status: defect.status,
    assignedTo: defect.assignedTo,
    creator: defect.creator,
    createdAt: defect.createdAt,
    updatedAt: defect.updatedAt,
    integration: defect.integrationSourceUid,
    attachments: transformedAttachments,
    linkedExecutions: executions,
    linkedRuns: uniqueRuns,
  };
};

const updateDefect = async (req: Request) => {
  const findValidMapping = async (
    models: any,
    params: {
      source: string;
      entityType: string;
      entityUid: string | number;
      type: 'priority' | 'status';
      projectScope?: string;
    },
  ) => {
    const mappings = await models.ExternalEntity.query()
      .where({
        source: params.source,
        entityType: params.entityType,
        entityUid: params.entityUid,
      })
      .whereNull('deletedAt');

    // Loop through all possible mappings to find a valid one
    for (const mapping of mappings) {
      if ((mapping.customFields as { type?: string })?.type === params.type) {
        // If mapping has no projectScope, it's globally valid
        if (!(mapping.customFields as { projectScope?: string })?.projectScope) {
          return {
            isValid: true,
            mapping,
          };
        }
        // If mapping has projectScope, check if it matches
        if ((mapping.customFields as { projectScope?: string })?.projectScope === params.projectScope) {
          return {
            isValid: true,
            mapping,
          };
        }
      }
    }

    return {
      isValid: false,
      mapping: null,
    };
  };

  const { uid } = req.params;
  const defect = await req.models.Defect.query().where('uid', uid).first();
  if (!defect) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errors.DEFECT_NOT_FOUND);
  }

  const integration = await req.models.Integration.query()
    .where('uid', defect.integrationSourceUid)
    .first();

  const defectData: any = {};
  const externalData: any = {};

  if (req.body.priority) {
    const { isValid, mapping: priorityMapping } = await findValidMapping(req.models, {
      source: integration?.service,
      entityType: 'tag',
      entityUid: req.body.priority,
      type: 'priority',
      projectScope: (defect.customFields as { projectScope?: string })?.projectScope,
    });

    if (!priorityMapping) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'Invalid priority tag',
      );
    }

    if (!isValid) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        errors.INVALID_REQUEST,
      );
    }

    defectData.priority = Number(req.body.priority);
    externalData.priority = Number(priorityMapping.sourceId);
  }

  if (req.body.status) {
    const { isValid, mapping: statusMapping } = await findValidMapping(req.models, {
      source: integration?.service,
      entityType: 'tag',
      entityUid: req.body.status,
      type: 'status',
      projectScope: (defect.customFields as { projectScope?: string })?.projectScope,
    });

    if (!statusMapping) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'Invalid status tag',
      );
    }

    if (!isValid) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        errors.INVALID_REQUEST,
      );
    }

    defectData.status = Number(req.body.status);
    externalData.status = Number(statusMapping.sourceId);
    defectData.archivedAt = (statusMapping.customFields as { isClosed?: boolean })?.isClosed ? new Date().toISOString() : null;
  }

  if (req.body.name) {
    defectData.name = req.body.name;
    externalData.name = req.body.name;
  }
  if (req.body.state) {
    externalData.state = req.body.state;
    defectData.archivedAt = req.body.state === 'closed' ? new Date().toISOString() : null;
  }
  if (req.body.customFields?.tags) {
    externalData.labels = req.body.customFields.tags.map((tag) => tag.name);
    defectData.customFields = {
      ...defect.customFields,
      tags: req.body.customFields.tags.map((tag) => tag.uid),
    };
  } else {
    defectData.customFields = defect.customFields;
  }
  if (Object.keys(defectData).length > 0) {
    defectData.updatedAt = new Date().toISOString();
  }
  if (req.body.description && req.body.description !== '') {
    externalData.description = req.body.description;
  }

  if (!integration) {
    await req.models.Defect.query().where('uid', uid).patch(defectData);
    return { message: 'Defect updated successfully' };
  }

  const service = IntegrationServiceFactory.getService(integration.service);
  let integrationToken;
  integrationToken = await req.models.IntegrationToken.query()
    .where('integrationUid', defect.integrationSourceUid)
    .where('ownerUid', req.locals.user.uid)
    .first();

  if (!integrationToken) {
    integrationToken = await req.models.IntegrationToken.query()
      .where('integrationUid', defect.integrationSourceUid)
      .where('ownerUid', req.locals.handle.ownerUid)
      .first();
  }

  if (!integrationToken) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.AUTH_TOKEN_NOT_FOUND,
    );
  }

  const authHeader = await service.prepareAuthHeader(integrationToken, req.knexDB);

  try {
    // Update external service with mapped values
    const response = await service.updateEntity(
      defect.customFields.apiUrl,
      authHeader,
      'defect',
      externalData,
    );
    if (!response.success) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        response.error,
      );
    }
    defectData.customFields = {
      ...response.data.customFields,
      tags: defectData.customFields.tags,
    };
    // Update local database with internal tag IDs
    await req.models.Defect.query().where('uid', uid).patch(defectData);
    return { message: 'Defect updated successfully' };
  } catch (error) {
    logger.error(`Error updating defect in ${integration.service}:`, error);
    throw new ApplicationError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      errors.INTERNAL_SERVER_ERROR,
    );
  }
};

const createDefect = async (req: Request) => {
  const {
    integrationUid, executionId, testRunUid,
  } = req.body;

  const integration = await req.models.Integration.query()
    .findById(integrationUid)
    .whereNull('archivedAt');

  if (!integration) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errors.INTEGRATION_NOT_FOUND,
    );
  }

  const service = IntegrationServiceFactory.getService(integration.service);
  let integrationToken;
  integrationToken = await req.models.IntegrationToken.query()
    .where('integrationUid', integrationUid)
    .where('ownerUid', req.locals.user.uid)
    .first();

  if (!integrationToken) {
    integrationToken = await req.models.IntegrationToken.query()
      .where('integrationUid', integrationUid)
      .where('ownerUid', req.locals.handle.ownerUid)
      .first();
  }

  if (!integrationToken) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      errors.AUTH_TOKEN_NOT_FOUND,
    );
  }
  const authHeader = await service.prepareAuthHeader(integrationToken, req.knexDB);
  const response = await service.createEntity(authHeader, 'defect', req.body);

  if (!response.success) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      response.error,
    );
  }
  try {
    const defectData: any = {};
    if (response.data.priority) {
      const externalEntity = await req.models.ExternalEntity.query()
        .where('sourceId', response.data.priority)
        .where('source', integration.service)
        .where('entityType', 'tag')
        .first();
      if (externalEntity) {
        defectData.priority = Number(externalEntity.entityUid);
      }
    }
    if (response.data.status) {
      const externalEntity = await req.models.ExternalEntity.query()
        .where('sourceId', response.data.status)
        .where('source', integration.service)
        .where('entityType', 'tag')
        .first();
      if (externalEntity) {
        defectData.status = Number(externalEntity.entityUid);
      }
    }
    if (response.data.customFields.tags) {
      const mappedTags = [];
      for (const tag of response.data.customFields.tags) {
        const externalEntity = await req.models.ExternalEntity.query()
          .where('sourceId', tag)
          .where('source', integration.service)
          .where('entityType', 'tag')
          .first();
        if (externalEntity) {
          mappedTags.push(externalEntity.entityUid);
        }
      }
      defectData.customFields = {
        ...response.data.customFields,
        tags: mappedTags,
      };
    } else {
      defectData.customFields = response.data.customFields;
    }
    defectData.name = req.body.name;
    defectData.externalId = response.data.externalId;
    defectData.integrationSourceUid = String(integrationUid);
    defectData.projectUids = req.models.Defect.knex().raw('?::integer[]', [
      [req.locals.project.uid],
    ]);
    // Create defect in our database
    const defect = await req.models.Defect.query().insert(defectData);

    if (executionId) {
      const execution = await req.models.TestExecution.query()
        .where('uid', executionId)
        .whereNull('deletedAt')
        .first();

      if (!execution) {
        throw new ApplicationError(
          StatusCodes.NOT_FOUND,
          errors.TEST_EXECUTION_NOT_FOUND,
        );
      }

      // Get the folder for this test case
      // Get the folder id for this test case
      const testCase = await req.models.TestCase.query()
        .where({
          uid: execution.testCaseRef,
        }).first();

      const webUrl = `${process.env.FRONTEND_URL}/${req.locals.handle.name}/${req.locals.project.key}/defects/${integration.service}/${defect.uid}`;
      // Construct the complete URL with folder path
      const executionUrl = `${process.env.FRONTEND_URL}/${req.locals.handle.name}/${req.locals.project.key}/runs/${testRunUid}/folders/${testCase.parentUid}/executions/${executionId}`;
      // Create the link in our database
      await req.models.DefectExecution.query().insert({
        defectUid: defect.uid,
        executionUid: Number(executionId),
        executionUrl,
      });

      const response = await service.createRemoteLink(authHeader, {
        defectUrl: defect.customFields.apiUrl,
        link: executionUrl,
        projectKey: req.locals.project.key,
        executionUid: executionId,
        webUrl,
      });

      if (!response.success) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          response.error,
        );
      }
    }
    return defect;
  } catch (error) {
    logger.error('Error creating defect:', error);
    throw new ApplicationError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      error.message || 'Failed to create defect',
    );
  }
};

const getDefectPriorities = async (req: Request) => {
  const priorityTags = await req.models.Tag.query()
    .whereRaw('? = ANY("entityTypes")', ['defects'])
    .where('systemType', 'priority')
    .whereNull('deletedAt');

  const priorities = priorityTags
    .map((tag, index) => ({
      id: tag.uid,
      color: tag.customFields?.color || '#667085',
      entityType: 'defect',
      name: tag.name,
      isDefault: index === 0,
    }));

  return priorities;
};

const getDefectStatuses = async (req: Request) => {
  const statusTags = await req.models.Tag.query()
    .whereRaw('? = ANY("entityTypes")', ['defects'])
    .where('systemType', 'status')
    .whereNull('deletedAt');

  const statuses = statusTags.map((tag) => ({
    id: tag.uid,
    color: tag.customFields?.color || '#667085',
    entityType: 'defect',
    name: tag.name,
    isDefault: false,
    isCompleted: false,
    isSuccess: false,
    isFailure: false,
  } as Status));
  return statuses;
};

const getDefectStatusByScope = async (req: Request) => {
  const projectScope = req.query.projectScope as string;

  // Fetch tags where `projectScope` matches or is missing (global scope)
  const statusTags = await req.models.ExternalEntity.query()
    .where({
      source: 'jira',
      entityType: 'tag',
    })
    .whereNull('deletedAt');

  return statusTags
    .filter((tag: any) => {
      const hasProjectScope = 'projectScope' in tag.customFields;
      const matchesScope = hasProjectScope && tag.customFields.projectScope === projectScope;
      return !hasProjectScope || matchesScope;
    })
    .map((tag: any) => Number(tag.entityUid));
};

const linkDefectToExecution = async (req: Request) => {
  const { uid } = req.params;
  const { executionId, testRunUid } = req.body;

  const defect = await req.models.Defect.query()
    .where('uid', uid)
    .whereNull('deletedAt')
    .first();

  if (!defect) {
    throw new ApplicationError(StatusCodes.NOT_FOUND, errors.DEFECT_NOT_FOUND);
  }

  const execution = await req.models.TestExecution.query()
    .where('uid', executionId)
    .whereNull('deletedAt')
    .first();

  if (!execution) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      'Test execution not found',
    );
  }

  try {
    // Create the link in our database
    const testCase = await req.models.TestCase.query()
      .where({
        uid: execution.testCaseRef,
      })
      .first();
    const executionUrl = `${process.env.FRONTEND_URL}/${req.locals.handle.name}/${req.locals.project.key}/runs/${testRunUid}/folders/${testCase.parentUid}/executions/${executionId}`;
    await req.models.DefectExecution.query().insert({
      defectUid: Number(uid),
      executionUid: Number(executionId),
      executionUrl,
    });

    // If this is a JIRA defect, update the external link
    if (defect.integrationSourceUid) {
      const integration = await req.models.Integration.query()
        .where('uid', defect.integrationSourceUid)
        .first();

      const service = IntegrationServiceFactory.getService(integration.service);
      let integrationToken;
      integrationToken = await req.models.IntegrationToken.query()
        .where('integrationUid', integration.uid)
        .where('ownerUid', req.locals.user.uid)
        .first();

      if (!integrationToken) {
        integrationToken = await req.models.IntegrationToken.query()
          .where('integrationUid', integration.uid)
          .where('ownerUid', req.locals.handle.ownerUid)
          .first();
      }

      if (integrationToken) {
        const authHeader = await service.prepareAuthHeader(integrationToken, req.knexDB);
        const webUrl = `${process.env.FRONTEND_URL}/${req.locals.handle.name}/${req.locals.project.key}/defects/${integration.service}/${defect.uid}`;
        const response = await service.createRemoteLink(authHeader, {
          defectUrl: defect.customFields.apiUrl,
          link: executionUrl,
          projectKey: req.locals.project.key,
          executionUid: executionId,
          webUrl,
        });
        if (!response.success) {
          throw new ApplicationError(
            StatusCodes.BAD_REQUEST,
            response.error,
          );
        }
      }
    }

    return {
      message: 'Defect linked successfully',
    };
  } catch (error) {
    logger.error('Error linking defect to execution:', error);
    throw new ApplicationError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      errors.INTERNAL_SERVER_ERROR,
    );
  }
};

const uploadAttachment = async (req: Request) => {
  const {
    fileType,
    size,
    fileName,
  } = req.body;
  const { ownerUid } = req.locals.handle;
  const { uid: defectId } = req.params;
  const creatorUid = req.locals.user?.uid || null;

  // Verify defect exists
  const defect = await req.models.Defect.query().findById(defectId);
  if (!defect) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errors.DEFECT_NOT_FOUND,
    );
  }

  const newAttachment = {
    name: fileName,
    ownerUid,
    size,
    fileType,
    mediaType: 'attachment',
    creatorUid,
  };

  const trx = await req.knexDB.transaction();

  try {
    const {
      key,
      signed_url: signedUrl,
      object_url: objectUrl,
      client_headers: clientHeaders,
      uid,
    } = await Attachment.createAttachment(newAttachment, trx);

    await trx('defectAttachments').insert({
      attachmentUid: uid,
      defectUid: defectId,
    });
    if (defect.integrationSourceUid) {
      await startWorkflow('integrationEntitiesWorkflow', {
        taskQueue: 'integration-entities-queue',
        workflowId: `${req.locals.handle.ownerUid}:defect:${Date.now()}`,
        args: [{
          tenantUid: req.locals.handle.ownerUid,
          task: 'syncEntityAttachments',
          entityType: 'defect',
          data: {
            defectUid: defectId,
            attachmentKey: key,
            fileName,
            fileType,
          },
        }],
      });
    }

    await trx.commit();

    return {
      uid,
      objectKey: key,
      signedUrl,
      objectUrl,
      clientHeaders,
    };
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const deleteAttachment = async (req: Request, res: Response) => {
  const trx = await req.knexDB.transaction();
  const attachmentId = <string>req.params.id;

  try {
    const attachment = await req.models.Attachment.query(trx).findOne({
      uid: attachmentId,
    });

    if (!attachment) return res.status(404).send();
    const { ownerUid } = req.locals.handle;
    const { key } = attachment;
    const param: AttachmentData = {
      type: 'delete',
      key,
      ownerUid,
    };
    await Promise.all([
      startWorkflow('attachmentWorkflow', {
        taskQueue: 'attachment-queue',
        workflowId: `${ownerUid}:attachment:delete:${Date.now()}`,
        args: [param],
      }),
      Attachment.deleteAttachment(trx, attachmentId, 'defectAttachments'),
    ]);
    await trx.commit();
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

/**
 * Get count of defects for a specific project
 * GET
 * @param {Object} req
 */
const getDefectsCountByProject = async (req: Request) => {
  const count = await req.models.Project.countDefects(req.knexDB, req.locals.project.uid);
  return { count };
};

export default {
  getDefects: httpHandler(getDefects),
  getDefectsOpenCount: httpHandler(getDefectsOpenCount),
  getDefectsClosedCount: httpHandler(getDefectsClosedCount),
  getDefectAttachments: httpHandler(getDefectAttachments),
  getDefectExecutions: httpHandler(getDefectExecutions),
  getDefectRuns: httpHandler(getDefectRuns),
  getDefect: httpHandler(getDefect),
  updateDefect: httpHandler(updateDefect),
  createDefect: httpHandler(createDefect),
  getDefectPriorities: httpHandler(getDefectPriorities),
  getDefectStatuses: httpHandler(getDefectStatuses),
  linkDefectToExecution: httpHandler(linkDefectToExecution),
  uploadAttachment: httpHandler(uploadAttachment),
  deleteAttachment: httpHandler(deleteAttachment),
  getDefectStatusByScope: httpHandler(getDefectStatusByScope),
  getDefectsCountByProject: httpHandler(getDefectsCountByProject),
};
