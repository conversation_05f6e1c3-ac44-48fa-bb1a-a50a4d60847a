import { ApplicationError, httpH<PERSON><PERSON> } from '@app/lib/http';

import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { UniqueViolationError } from 'objection';
import errorConstants from '@app/constants/errors';
import i18n from 'i18n';
import { kebabCase } from 'lodash';
import env from '@app/config/env';
import { Attachment } from '@app/models/attachment';
import { getNextId, paginated } from '@app/lib/model';
import { CountProjectEntityType, GetProjectsDTO } from '@app/types/project';
import { DemoProjectData } from '@app/temporal/activities/projectDemo';
import { User } from '@app/models/user';
import { Knex } from 'knex';
import { TestMilestone } from '@app/models/testMilestone';
import { TestPlan } from '@app/models/testPlan';
import { TestRun } from '@app/models/testRun';
import { Project } from '@app/models/project';
import { startWorkflow } from '../temporal/client';
import { AttachmentData } from '../temporal/activities/attachment';
import { DefaultTemplateData } from '../temporal/activities/template';
import { ProjectData } from '../temporal/activities/softDeleteProjectRelatedRecords';
import { DefaultDashboardData } from '../temporal/activities/dashboard';

/**
 * create a test projects
 * POST
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.body.org
 * @param {String} req.body.externalId
 * @param {String} req.body.source
 * @param {String} req.body.name
 * @param {Object} req.body.customFields
 */
const createProject = async (req: Request) => {
  const trx = await req.knexDB.transaction();
  try {
    const { body } = req;
    const owner = req.locals.handle.ownerUid;
    const { ownerType } = req.locals.handle;

    let projectName = body.name;
    let projectKey = body.key;
    const uid = await getNextId(trx, req.models.Project);
    let status: Project['status'] = 'active';

    if (body.createDemo) {
      projectName = `Demo Project ${uid}`;
      projectKey = `demo${uid}`;
      status = 'pending';
    }

    const project = await req.models.Project.query(trx).insert({
      uid,
      externalId: body.externalId,
      source: body.source,
      name: projectName,
      key: projectKey.toUpperCase(),
      customFields: body.customFields,
      status,
    });

    const rootFolder = await req.models.Tag.query(trx)
      .insert({
        name: projectName,
        slug: kebabCase(body.name),
        projectUid: project.uid,
        customFields: { source: 'testfiesta' },
        entityTypes: ['cases'],
        systemType: 'folder',
      })
      .returning('*');
    await trx.commit();

    await req.fga.create(
      {
        objectType: 'project',
        objectId: project.uid,
        relation: 'owner',
        subjectType: ownerType,
        subjectId: owner,
      },
      {
        objectType: 'project',
        objectId: project.uid,
        relation: 'parent',
        subjectType: ownerType,
        subjectId: owner,
      },
    );

    const param: DefaultTemplateData = {
      ownerType,
      ownerUid: owner,
      userId: req.locals.user.uid,
      projectId: project.uid,
    };
    await startWorkflow('templateWorkflow', {
      taskQueue: 'template-queue',
      workflowId: `${owner}:template:${Date.now()}`,
      args: [param],
    });

    const param2: DefaultDashboardData = {
      projectUid: project.uid,
      ownerUid: owner,
      projectName: `${project.name} Default Dashboard`,
      systemDefault: false,
      editable: true,
    };

    await startWorkflow('dashboardWorkflow', {
      taskQueue: 'dashboard-queue',
      workflowId: `${owner}:dashboard:${Date.now()}`,
      args: [param2],
    });

    if (body.createDemo) {
      await startWorkflow('setupProjectDemoWorkflow', {
        taskQueue: 'create-project-demo-queue',
        workflowId: `${ownerType}/${owner}:project/${project.uid}:setupDemo:${Date.now()}`,
        args: [
          {
            ownerType,
            ownerUid: owner,
            projectId: project.uid,
            rootFolderUid: rootFolder.uid,
            userId: req.locals.user.uid,
          } as DemoProjectData,
        ],
      });
    }

    return project;
  } catch (error) {
    await trx.rollback();
    if (error instanceof UniqueViolationError) {
      throw new ApplicationError(
        StatusCodes.UNPROCESSABLE_ENTITY,
        errorConstants.PROJECT_KEY_EXISTS,
      );
    }
    throw error;
  }
};

/**
 * get a list of test projects
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {Number} req.query.per_page
 * @param {Number} req.query.current_page
 */
const getProjects = async (req: Request) => {
  const { limit, offset, includeCount } = req.query as any as GetProjectsDTO;
  const owner = req.locals.handle.ownerUid;
  const { ownerType } = req.locals.handle;
  const isOrgType = ownerType === 'org';
  const [relatedProjects, userAssignee, noAccessRole] = await Promise.all([
    req.fga
      .query(`${ownerType}:${owner}`, 'project:', 'owner')
      .then((result) => result.map((tuple: any) => tuple.key.object.split(':')[1])),
    ...(isOrgType
      ? [req.fga.query(`user:${req.locals.user.uid}`, 'role:', 'assignee')]
      : []),
    ...(isOrgType
      ? [
        req.models.Role.query()
          .findOne({ slug: 'no_access', system: true })
          .then((role) => role.uid),
      ]
      : []),
  ]);

  const userHasNoAccess = ((userAssignee as []) || []).reduce(
    (acc, curr: any) => {
      if (curr.key.object.split(':')[1] === noAccessRole) {
        if (curr.key.condition.name === 'default_role') {
          const excludedProjects = curr.key.condition.context?.excluded || [];
          const noAccessProjects = relatedProjects.filter(
            (projectUid) => !excludedProjects.includes(`project:${projectUid}`),
          );

          acc.push(...noAccessProjects);
        } else {
          const allowedProjects = curr.key.condition.context?.allowed || [];
          acc.push(
            ...allowedProjects.map(
              (projectUid: string) => projectUid.split(':')[1],
            ),
          );
        }
      }

      return acc;
    },
    [],
  );

  const accessibleProjects = relatedProjects.filter(
    (p) => !userHasNoAccess.includes(p),
  );
  let sql = req.models.Project.query()
    .select(['projects.*'])
    .whereIn('projects.uid', accessibleProjects)
    .whereNull('projects.deletedAt');

  if (req.query.status) {
    if (req.query.status === 'active') {
      sql = sql.whereNull('archivedAt');
    } else if (req.query.status === 'archived') {
      sql = sql.whereNotNull('archivedAt');
    }
  }

  const baseURL = `${env.BACKEND_URL}${env.API_INTERNAL_ROUTE}/${req.params.handle}`;

  const result = await paginated(
    sql as any,
    limit,
    offset,
    req.knexDB,
    async (project: any) => ({
      ...project,
      avatarUrl: project.avatarAttachmentUid
        ? `${baseURL}/projects/attachments/${project.avatarAttachmentUid}/object`
        : null,
    }),
  );

  if (includeCount) {
    const [archivedResult, activeResult] = await Promise.all([
      req.models.Project.query()
        .whereNull('deletedAt')
        .whereNotNull('archivedAt')
        .whereIn('uid', accessibleProjects)
        .count('uid as totalArchived')
        .first(),
      req.models.Project.query()
        .whereNull('deletedAt')
        .whereNull('archivedAt')
        .whereIn('uid', accessibleProjects)
        .count('uid as totalActive')
        .first(),
    ]);

    (result as any).meta = {
      totalArchived: parseInt((archivedResult as any).totalArchived),
      totalActive: parseInt((activeResult as any).totalActive),
    };
  }

  return result;
};

/**
 * get a list of test projects by text query
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.query.query
 */
const searchProjects = async (req: Request) => {
  const { query } = req.query;

  const relatedProjects = (
    await req.fga.query(
      `${req.locals.handle.ownerType}:${req.locals.handle.ownerUid}`,
      'project:',
      'owner',
    )
  ).map((tuple: any) => tuple.key.object.split(':')[1]);

  const projects = await req.models.Project.query()
    .select('*')
    .orWhereRaw(
      '"externalId" ilike :token or source ilike :token or name ilike :token',
      { token: `%${query}%` },
    )
    .whereIn('uid', relatedProjects);
  return { projects };
};

/**
 * delete a test projects by projectId
 * DELETE
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.projectId
 */
const deleteProject = async (req: Request) => {
  await req.models.Project.query()
    .findById(req.locals.project.uid)
    .patch({ deletedAt: req.knexDB.fn.now() });

  const param: ProjectData = {
    projectId: req.locals.project.uid,
    ownerUid: req.locals.handle.ownerUid,
  };

  await startWorkflow('softDeleteProjectRelatedRecordsWorkflow', {
    taskQueue: 'soft-delete-project-records-queue',
    workflowId: `${req.locals.handle.ownerUid}:soft-delete:${Date.now()}`,
    args: [param],
  });
};

const updateProject = async (req: Request) => {
  const {
    archived, source, name, externalId, customFields,
  } = req.body;

  const incomingUpdate = {
    source,
    name,
    externalId,
    customFields,
  };

  if (archived) {
    // @ts-expect-error allow archivedAt
    incomingUpdate.archivedAt = req.knexDB.fn.now();
  } else if (archived === false) {
    // @ts-expect-error allow archivedAt
    incomingUpdate.archivedAt = null;
  }

  const project = await req.models.Project.query().findById(
    req.locals.project.uid,
  );

  if (project.archivedAt && archived !== false) {
    throw new ApplicationError(
      StatusCodes.UNPROCESSABLE_ENTITY,
      errorConstants.ARCHIVED_PROJECT_CANNOT_BE_UPDATED,
    );
  }

  incomingUpdate.customFields = {
    ...project.customFields,
    ...incomingUpdate.customFields,
  };

  const updatedProject = await req.models.Project.query()
    .patch(incomingUpdate)
    .where('uid', project.uid)
    .returning('*');

  return updatedProject[0];
};

/**
 * get a test projects by projectId
 * GET
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.projectId
 */
const getProject = async (req: Request) => {
  const project = await req.models.Project.query()
    .findById(req.locals.project.uid)
    .whereNull('deletedAt');

  return project;
};

const keyInUse = async (req: Request) => {
  const { key } = req.params;
  const upperProjectKey = key.toUpperCase();

  const project = await req.models.Project.query()
    .where({ key: upperProjectKey })
    .whereNull('deletedAt');

  if (project.length) {
    throw new ApplicationError(StatusCodes.CONFLICT, errorConstants.KEY_FOUND);
  }

  return { keyInUse: false };
};

const unArchiveProject = async (req: Request) => {
  const project = await req.models.Project.query().findById(
    req.locals.project.uid,
  );

  if (project.archivedAt) {
    throw new ApplicationError(
      StatusCodes.UNPROCESSABLE_ENTITY,
      errorConstants.PROJECT_ALREADY_ARCHIVED,
    );
  }

  await req.models.Project.query()
    .patch({
      archivedAt: null,
    })
    .where('uid', project.uid);

  return { message: i18n.__('projectUnArchived') };
};

const getProjectUsers = async (req: Request) => {
  // Retrieve the list of users from the org.
  // Check the default role: if the excluded roles for the default role do not include the projectUid,
  // then the default role is assigned as the user's role in the project.
  // Otherwise, check for an override role specific to the project.
  // Filter the members who do not have access roles in the project.

  const orgId = req.locals.handle.ownerUid;
  const [query] = await Promise.all([
    req.fga.paginatedQuery(
      '',
      `org:${orgId}`,
      'member',
      req.query.cursor as string,
    ),
  ]);

  const roles = (
    await req.models.Role.query().select('uid', 'name', 'slug', 'projectUid')
  ).reduce((acc, {
    name, uid, slug, projectUid,
  }) => {
    acc[uid] = {
      name,
      slug,
      projectUid,
    };
    return acc;
  }, {});

  let userUids = query.tuples.map((t) => t.key.user.split(':').pop());
  if (!userUids.length) {
    return { users: [], orgId, cursor: null };
  }
  const usersQuery = await User.getByIds(req.sharedKnexDB, userUids);

  const roleMap = {};

  userUids = usersQuery.map((u) => u.uid);
  const memberTags = await req.models.MemberTag.query()
    .whereIn('userUid', userUids)
    .whereNull('memberTags.deletedAt')
    .innerJoin('tags', 'tags.uid', 'tagUid')
    .select('tags.*', 'userUid');

  const userTagsMap = {};
  memberTags.forEach((tag) => {
    if (!userTagsMap[tag.userUid]) userTagsMap[tag.userUid] = [];
    userTagsMap[tag.userUid].push(tag);
  });

  const auditLogs = await req.models.AuditLog.query()
    .select('actor', req.knexDB.raw('MAX("createdAt") as "createdAt"'))
    .whereIn('actor', userUids)
    .groupBy('actor');
  const lastActivityMap = Object.fromEntries(
    auditLogs.map((log) => [log.actor, log.createdAt]),
  );

  for (const user of usersQuery) {
    const userRoles: any = await req.fga.query(
      `user:${user.uid}`,
      'role:',
      'assignee',
    );

    for (const role of userRoles) {
      const roleId = role.key.object.split(':')[1];
      const roleName = roles[roleId]?.name || null;
      const roleSlug = roles[roleId]?.slug || null;

      const isDefaultRole = role.key.condition?.name === 'default_role'
        && !role.key.condition.context?.excluded?.includes(
          `project:${req.locals.project.uid}`,
        );

      const isOverrideRole = role.key.condition?.name === 'override_role'
        && role.key.condition.context?.allowed?.includes(
          `project:${req.locals.project.uid}`,
        );

      if (isDefaultRole) {
        roleMap[user.uid] = {
          roleId,
          isDefault: true,
          name: roleName,
          slug: roleSlug,
        };
      } else if (isOverrideRole) {
        roleMap[user.uid] = {
          roleId,
          isDefault: false,
          name: roleName,
          slug: roleSlug,
        };
      }
    }
  }
  const users = usersQuery
    .filter((u) => roleMap[u.uid].slug !== 'no_access')
    .map((user) => ({
      ...user,
      role: {
        uid: roleMap[user.uid]?.roleId || null,
        name: roleMap[user.uid]?.name || null,
        orgRole: !roles[roleMap[user.uid]?.roleId]?.projectUid,
      },
      tags: userTagsMap[user.uid] || [],
      lastActivity: lastActivityMap[user.uid] || null,
    }));

  return { users, orgId, cursor: query.continuation_token };
};

const uploadAttachment = async (req: Request) => {
  const { fileType, size, fileName } = req.body;
  const { ownerUid } = req.locals.handle;
  const { uid: id } = await req.models.Project.query()
    .findOne({
      key: req.params.key.toUpperCase(),
    })
    .select('uid');
  const creatorUid = req.locals.user?.uid || null;

  const newAttachment = {
    name: fileName,
    ownerUid,
    size,
    fileType,
    mediaType: 'attachment',
    creatorUid,
  };

  const trx = await req.knexDB.transaction();

  try {
    const {
      key,
      signed_url: signedUrl,
      object_url: objectUrl,
      client_headers: clientHeaders,
      uid,
    } = await Attachment.createAttachment(newAttachment, trx);

    await trx('projectAttachments').insert({
      attachmentUid: uid,
      projectUid: id,
    });

    await req.models.Project.query(trx)
      .findById(id)
      .patch({ avatarAttachmentUid: uid });

    await trx.commit();

    return {
      uid,
      objectKey: key,
      signedUrl,
      objectUrl,
      clientHeaders,
    };
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

const deleteAttachment = async (req: Request, res: Response) => {
  const trx = await req.knexDB.transaction();
  const attachmentId = <string>req.params.id;

  try {
    const attachment = await req.models.Attachment.query(trx).findOne({
      uid: attachmentId,
    });

    if (!attachment) return res.status(404).send();

    const { key } = attachment;
    const { ownerUid } = req.locals.handle;
    const param: AttachmentData = {
      type: 'delete',
      key,
      ownerUid,
    };
    await Promise.all([
      startWorkflow('attachmentWorkflow', {
        taskQueue: 'attachment-queue',
        workflowId: `${ownerUid}:attachment:delete:${Date.now()}`,
        args: [param],
      }),
      Attachment.deleteAttachment(trx, attachmentId, 'projectAttachments'),
    ]);
    await trx.commit();
  } catch (err) {
    await trx.rollback();
    throw err;
  }
};

/**
 * returns a count of active and total entities. it depends on
 * req.query.entityType to know what entity is being queried
 * @param req
 */
const countEntity = async (req: Request) => {
  const entityType: CountProjectEntityType = req.query.entityType as any;
  let method: (
    db: Knex,
    state: 'active' | 'archived',
    projectId?: number,
  ) => Promise<number>;
  const projectId = req.locals.project.uid;

  switch (entityType) {
    case 'milestone':
      method = TestMilestone.countMilestones;
      break;
    case 'plan':
      method = TestPlan.countPlans;
      break;
    case 'run':
      method = TestRun.countRuns;
      break;
    default:
      throw new ApplicationError(StatusCodes.UNPROCESSABLE_ENTITY, '');
  }

  const [active, archived] = await Promise.all([
    method(req.knexDB, 'active', projectId),
    method(req.knexDB, 'archived', projectId),
  ]);

  return { active, archived };
};

export default {
  createProject: httpHandler(createProject),
  getProjects: httpHandler(getProjects),
  getProject: httpHandler(getProject),
  keyInUse: httpHandler(keyInUse),
  searchProjects: httpHandler(searchProjects),
  deleteProject: httpHandler(deleteProject),
  updateProject: httpHandler(updateProject),
  unArchiveProject: httpHandler(unArchiveProject),
  uploadAttachment: httpHandler(uploadAttachment),
  deleteAttachment: httpHandler(deleteAttachment),
  getProjectUsers: httpHandler(getProjectUsers),
  countEntity: httpHandler(countEntity),
};
