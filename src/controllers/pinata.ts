// import { FGARawWrite, FgaService } from '@ss-libs/ss-component-auth';
import { Request, Response } from 'express';

// import { Attachment } from '@app/models/attachment';
// import { Knex } from 'knex';
import appConstants from '@app/constants/app';
// import env from '@app/config/env';
// import errors from '@app/constants/errors';
// import i18n from 'i18n';
import logger from '@app/config/logger';

// import mimes from 'mime-types';
// import { storageProvider } from '@ss-libs/ss-component-media';
// import { v4 as uuidv4 } from 'uuid';

const pinataLatest = async (req: Request, res: Response): Promise<Response> => {
  logger.log('info', `[PINATA][latest]: ${JSON.stringify(req.headers)}`);
  return res.status(200).json({ version: appConstants.PINATA_LATEST_VERSION });
};

/**
 * <PERSON>le PATCH request to push PINATA data into TEstFiesta system.
 * @param {Object} req
 * @param {Object} res
 * @param {String} req.params.orgUid
 */
const pushPinataData = async (
  req: Request,
  res: Response,
): Promise<Response> => res.send(200);
//  const pinataSession = req.body.session;
//  const pinataCase = req.body.case;
//  // TODO - Owner info is hardcoded but should be set from the app
//  const owner = req.locals.user.uid; // user or org id
//  const ownerType = 'user'; // should be "user" or "org"
//  const trx = await req.knexDB.transaction();
//
//  console.log(`-------------------------------------`);
//  console.log(`${JSON.stringify(req.body)}`);
//  console.log(`-------------------------------------`);
//  try {
//    // Try to find the matching test_execution using sessionID
//    let testExecution = await req.models.TestExecution.query(trx)
//      .where('externalId', pinataSession.sessionID)
//      .where('source', 'pinata')
//      .first();
//    // Try to find the matching test_execution using sessionID
//    let testCase = await req.models.TestCase.query(trx)
//      .where('externalId', pinataCase.caseID)
//      .where('source', 'pinata')
//      .first();
//    let newCase, newExecution;
//
//    // If there is no matching test_execution or test_case, create new ones
//    if (!testExecution) {
//      pinataSession.sessionID = uuidv4(); // TODO now int
//      if (!testCase) {
//        pinataCase.caseID = uuidv4(); // TODO now int
//        newCase = await req.models.TestCase.query(trx).insert({
//          externalId: pinataCase.caseID,
//          source: 'pinata',
//          name: `PINATA Exploratory Test ${pinataCase.caseID
//            .substring(pinataCase.caseID.length - 6)
//            .toUpperCase()}`,
//          customFields: {
//            ...pinataCase,
//          },
//        });
//        testCase = newCase;
//      }
//
//      newExecution = await req.models.TestExecution.query(trx).insert({
//        externalId: pinataSession.sessionID,
//        testCaseRef: testCase.uid,
//        source: 'pinata',
//        customFields: {
//          ...pinataSession,
//        },
//      });
//
//      testExecution = newExecution;
//    } else {
//      if (testExecution.testCaseRef !== testCase?.uid) {
//        // TODO - throw an error here because the data in PINATA doesn't line
//        //         up with data in the DB
//        //         OR PROBABLY just update the DB?
//      }
//    }
//
//    // TODO - deep compare and don't update if not necessary
//    // update test_execution with customFields
//    // This may be handled differently after we move to yjs and shared state
//    await req.models.TestExecution.query(trx)
//      .where('uid', testExecution.uid)
//      .patch({
//        customFields: {
//          ...testExecution.customFields,
//          ...pinataSession,
//        },
//      });
//
//    // Prepare steps for insertion and insert
//    const stepsInserted = await Promise.all(
//      pinataSession.items
//        .filter((item: any) => !item.uploaded)
//        .map(async (item: any) => {
//          // if (item.uploaded)
//          // TODO - Show insights - e.g. previous runs of this charter found bugs
//          //        on this charter here and here, you might want to check that
//          //        out (or currently open bugs from this charter even)
//          let testStep = await req.models.TestExecutionStep.query(trx)
//            .where('externalId', item.stepID)
//            .where('source', 'pinata')
//            .first();
//
//          if (!testStep) {
//            console.log('New step');
//            const newStepData = {
//              externalId: item.stepID,
//              testExecutionUid: testExecution.uid,
//              source: 'pinata',
//              customFields: item,
//            };
//
//            testStep =
//              await req.models.TestExecutionStep.query(trx).insert(newStepData);
//
//            // Handle non-media attachments differently
//            if (
//              item.fileType.indexOf('text') < 0 &&
//              item.fileType.indexOf('json') < 0
//            ) {
//              // Create attachment and return URL
//              const { presignedUpload } = await createAttachment(
//                trx,
//                req.fga,
//                req.models,
//                {
//                  externalId: item.attachmentID,
//                  source: 'pinata',
//                  name: item.fileName,
//                  size: item.fileSize,
//                  type: item.fileType,
//                  checksum: item.fileChecksum,
//                },
//                {
//                  type: 'test_execution_steps',
//                  id: testStep.uid,
//                },
//                { owner, ownerType },
//              );
//
//              return {
//                ...testStep,
//                uploadURL: presignedUpload.signed_url, // kept for backward compatibility
//                object_key: presignedUpload.key,
//                object_url: presignedUpload.object_url,
//                client_headers: presignedUpload.client_headers,
//              };
//            }
//          }
//
//          let presignedUpload;
//          // Handle non-media attachments differently
//          if (
//            item.fileType.indexOf('text') < 0 &&
//            item.fileType.indexOf('json') < 0
//          ) {
//            // Check if attachment exists, if it does or has a different
//            //         externalId, then create URL and return
//            const currentAttachment = await req.models.Attachment.query(trx)
//              .where('externalId', item.attachmentID)
//              .where('source', 'pinata')
//              .first();
//
//            console.log('Current step');
//            if (
//              !currentAttachment ||
//              currentAttachment?.checksum !== item.fileChecksum
//            ) {
//              console.log(
//                `New attachment, ID? ${JSON.stringify(currentAttachment)}`,
//              );
//              if (currentAttachment) {
//                console.log('Deleting old attachment connection');
//                console.log(
//                  `Old Check: ${currentAttachment.checksum}\n New Check: ${item.fileChecksum}`,
//                );
//                await req.models.Attachment.relatedQuery('test_execution_steps')
//                  .for(currentAttachment.uid)
//                  .delete();
//                // TODO - clean up the old attachment file if no other connections
//              }
//
//              console.log('Creating');
//              // Create attachment and return URL
//              const { presignedUpload: upload, attachment } =
//                await createAttachment(
//                  trx,
//                  req.fga,
//                  req.models,
//                  {
//                    externalId: item.attachmentID,
//                    source: 'pinata',
//                    name: item.fileName,
//                    size: item.fileSize,
//                    type: item.fileType,
//                    checksum: item.fileChecksum,
//                  },
//                  {
//                    type: 'test_execution_steps',
//                    id: testStep.uid,
//                  },
//                  { owner, ownerType },
//                );
//              console.log(`New attachment: ${JSON.stringify(attachment)}`);
//
//              presignedUpload = {
//                ...testStep,
//                uploadURL: upload.signed_url, // kept for backward compatibility
//                object_key: upload.key,
//                object_url: upload.object_url,
//                client_headers: upload.client_headers,
//              };
//              console.log(`Created: ${JSON.stringify(attachment)}`);
//            }
//          }
//
//          // TODO - deep compare and don't update if not necessary
//          // update test_execution with customFields
//          // This may be handled differently after we move to yjs and shared state
//          await req.models.TestExecutionStep.query(trx)
//            .where('uid', testStep.uid)
//            .patch({ customFields: item });
//          const updatedStep = await req.models.TestExecutionStep.query(
//            trx,
//          ).where('uid', testStep.uid);
//
//          return Object.assign(
//            {},
//            ...updatedStep,
//            presignedUpload
//              ? {
//                  uploadURL: presignedUpload.signed_url, // kept for backward compatibility
//                  object_key: presignedUpload.key,
//                  object_url: presignedUpload.object_url,
//                  client_headers: presignedUpload.client_headers,
//                }
//              : null,
//          );
//        }),
//    );
//
//    await trx.commit();
//    const writes: FGARawWrite[] = [];
//    if (newCase) {
//      writes.push({
//        subjectType: req.locals.handle.ownerType,
//        subjectId: req.locals.handle.ownerUid,
//        objectId: newCase.uid,
//        objectType: 'case',
//        relation: 'owner',
//      });
//    }
//
//    if (newExecution) {
//      writes.push({
//        subjectType: req.locals.handle.ownerType,
//        subjectId: req.locals.handle.ownerUid,
//        objectId: newExecution.uid,
//        objectType: 'execution',
//        relation: 'owner',
//      });
//    }
//
//    if (writes.length > 0) await req.fga.create(...writes);
//
//    // TODO - tuples for steps or are they owned by the owner of the
//    //        case/execution?
//
//    return res.status(200).send({
//      // CTODO - fix link
//      link: `${env.FRONTEND_URL}/executions/${testExecution.uid}`,
//      steps: stepsInserted,
//      data: { sessionID: pinataSession.sessionID, caseID: pinataCase.caseID },
//      message: i18n.__('pinataDataPushedSuccessfully'),
//    });
//  } catch (error) {
//    // Rollback the transaction
//    await trx.rollback();
//    logger.log('warn', `Error while pushing PINATA data: ${error}`);
//    return res
//      .status(500)
//      .send({ error: errors.FAILED_TO_PROCESS_PINATA_DATA });
//  }
  //  res.send(200);

// const createAttachment = async (
//  trx: Knex.Transaction,
//  fga: FgaService,
//  models: Request['models'],
//  metadata: {
//    externalId: string;
//    source: string;
//    name: string;
//    type: string;
//    size: number;
//    checksum: string;
//  },
//  relation: { type: string; id: string },
//  authz: { ownerType: string; owner: string },
// ) => {
//  let newAttachment: Attachment, presignedUpload;
//  try {
//    newAttachment = await models.Attachment.query(trx).insert(metadata);
//    await newAttachment.$relatedQuery(relation.type, trx).relate(relation.id);
//    presignedUpload = await storageProvider().upload(
//      'attachments',
//      `${newAttachment.uid}.${mimes.extension(metadata.type)}`,
//      metadata.type,
//      metadata.size,
//    );
//    await fga.create({
//      objectId: newAttachment.uid,
//      objectType: 'attachment',
//      relation: 'owner',
//      subjectId: authz.owner,
//      subjectType: authz.ownerType,
//    });
//  } catch (error) {
//    logger.log('warn', `Error: ${JSON.stringify(error)}`);
//    await trx.rollback();
//    throw new Error(error);
//  }
//  return {
//    presignedUpload,
//    attachment: newAttachment,
//  };
// };

export { pushPinataData, pinataLatest };
