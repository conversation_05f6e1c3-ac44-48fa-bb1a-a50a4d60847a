import { ApplicationError, httpHandler } from '@app/lib/http';
import logger from '@app/config/logger';
import { Request, Response } from 'express';
import { Invite } from '@app/models/invite';
import errorConstants from '@app/constants/errors';
import { StatusCodes } from 'http-status-codes';
import generateRandomKey from '@app/utils/generateRandomKey';
import dayjs from 'dayjs';
import formatDate from '@app/utils/formatDate';
import config from '@app/constants/config';
import { EmailNotification } from '@app/temporal/activities';
import env from '@app/config/env';
import _, { isNull } from 'lodash';
import { FGARawWrite } from '@ss-libs/ss-component-auth';
import { Knex } from 'knex';
import { MemberTag } from '@app/models/memberTag';
import ssPayments from '@ss-libs/ss-component-payments';
import { startWorkflow } from '../temporal/client';

interface SendInviteMailParams {
  from: string;
  link: string;
  orgName: string;
  to: string;
  newUser: boolean;
  tenantUid: string;
  inviteUid: string;
}

const newInvite = async (req: Request) => {
  logger.info(`Sending new invite: ${JSON.stringify(req.body)}`);
  const projectUid = req.locals.project?.uid;

  const org = await req.models.Org.query()
    .findById(req.locals.handle.ownerUid)
    .whereNull('deletedAt');

  if (!org) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.ORG_NOT_FOUND,
    );
  }
  logger.info(`Invite for org: ${org.uid}`);

  // Scenario to handle:
  // The user might first select a default role like "Tester", and then select a specific project that also has the "Tester" role.
  // In this case, the selected project should be removed.

  if (req.body.roles?.length) {
    req.body.roles.forEach((r) => {
      if (r.roleUid === req.body.roleUid) {
        // remove this from roles
        const index = req.body.roles.indexOf(r);
        if (index > -1) {
          req.body.roles.splice(index, 1);
        }
      }
    });
  }

  const inviteRoles = Array.from(
    new Set([req.body.roleUid, ...(req.body.roles?.map((r) => r.roleUid) || [])]),
  );
  const roles = await req.models.Role.query().whereIn('uid', inviteRoles);

  if (projectUid) { // Project Invite (Default role uid should be no access)
    const defaultRole = roles.find((r) => r.uid === req.body.roleUid);
    if (defaultRole.slug !== 'no_access') {
      throw new ApplicationError(
        StatusCodes.FORBIDDEN,
        errorConstants.MISSING_PERMISSION,
      );
    }
  }

  if (roles.length !== inviteRoles.length) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.ROLE_NOT_FOUND,
    );
  }

  const user = await req.models.User.query()
    .whereNull('deletedAt')
    .where((q) => {
      if (req.body.userUid) q.where('uid', req.body.userUid);
      else if (req.body.userEmail) q.where('email', req.body.userEmail.toLowerCase());
      else q.whereRaw('1<>1');
    })
    .first();

  if (!user && !req.body.userEmail) {
    throw new ApplicationError(
      StatusCodes.UNPROCESSABLE_ENTITY,
      errorConstants.EMAIL_OR_UID_REQUIRED,
    );
  }

  const email: string = user?.email ?? req.body.userEmail;

  let newUser = true;

  if (user) {
    newUser = false;
    // check if user is already member
    const isMember = await req.models.Membership.query()
      .where({
        userUid: user.uid,
        accountUid: req.locals.handle.ownerUid,
        deletedAt: null,
      })
      .first();
    if (isMember) {
      throw new ApplicationError(
        StatusCodes.UNPROCESSABLE_ENTITY,
        errorConstants.EXISTING_MEMBER,
      );
    }
  } else {
    const pendingInvite = await req.models.Invite.query()
      .whereNull('invites.accepted')
      .whereNull('invites.deletedAt')
      .andWhere('invites.email', email)
      .first();

    if (pendingInvite && !dayjs().isAfter(pendingInvite.expiresAt)) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        errorConstants.USER_ALREADY_INVITED,
      );
    }
  }
  const trx = await req.knexDB.transaction();
  const token = await generateRandomKey();
  let invite: Invite;
  try {
    await req.models.Invite.query(trx)
      .where({ email })
      .patch({ deletedAt: req.knexDB.fn.now() });

    invite = await req.models.Invite.query(trx)
      .insert({
        inviterUid: req.locals.user.uid,
        email,
        roleUid: req.body.roleUid,
        token,
        tagUids: req.knexDB.raw('?', [req.body.tagUids ?? []]),
        status: 'pending',
        expiresAt: formatDate(
          dayjs().add(config.INVITATION_EXPIRATION_DAYS, 'days').toDate(),
        ),
      })
      .returning('*');

    if (req.body.roles?.length) {
      const projectRoles = req.body.roles.map((p) => ({
        roleUid: p.roleUid,
        projectUid: p.projectUid,
        inviteUid: invite.uid,
      }));
      await req.models.ProjectInvite.query(trx).insert(projectRoles);
    }

    await trx.commit();
  } catch (err) {
    await trx.rollback();
    throw err;
  }
  const ssoConfig = await req.models.SSOConfig.query(req.sharedKnexDB).where({ orgUid: req.locals.handle.ownerUid, isActive: true }).first();

  const from = `${req.locals.user.firstName} ${req.locals.user.lastName}`;
  let link = `${env.FRONTEND_URL}/organizations/${req.locals.handle.name}/invite/${token}`;
  if (ssoConfig && ssoConfig.config.oidc?.allowOnlyInvitedAccounts) {
    link = `${env.FRONTEND_URL}/${req.locals.handle.name}/login`;
  }

  await sendInviteMail({
    from,
    link,
    to: email,
    orgName: org.name,
    newUser,
    inviteUid: invite.uid,
    tenantUid: req.locals.handle.ownerUid,
  });
  logger.info('Invite pushed to worker queue');
};
const acceptInvite = async (req: Request) => {
  const trx: Knex.Transaction = await req.knexDB.transaction();
  const sharedTrx: Knex.Transaction = await req.sharedKnexDB.transaction();
  try {
    const invite = await req.models.Invite.query(trx)
      .where('token', req.params.token)
      .whereNull('deletedAt')
      .first();

    if (!invite) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errorConstants.INVITATION_LINK_EXPIRED,
      );
    }

    const [error] = validateInvite(req, invite);
    if (error) {
      throw new ApplicationError(StatusCodes.UNPROCESSABLE_ENTITY, error);
    }

    const orgId = req.locals.handle.ownerUid;

    // assign user to role
    const userId = req.locals.user.uid;

    await req.models.Invite.query(trx)
      .findById(invite.uid)
      .patch({
        accepted: true,
        acceptedAt: formatDate(dayjs().toDate()),
      });

    await req.models.Membership.query(sharedTrx)
      .insert({
        accountType: req.locals.handle.ownerType,
        accountUid: req.locals.handle.ownerUid,
        userUid: req.locals.user.uid,
        deletedAt: sharedTrx.raw('null'),
        createdAt: sharedTrx.fn.now(),
        updatedAt: sharedTrx.fn.now(),
      })
      .onConflict(['accountUid', 'userUid'])
      .merge();

    const org = await req.models.Org.query(sharedTrx).findById(orgId);
    // update org member seats in background
    if (org?.stripeId) {
      const members = await getOrgMembers(req, orgId, sharedTrx);
      await ssPayments.updateSeatCount(
        'org',
        req.locals.handle.ownerUid,
        members.length,
      );
    }

    const memberTags: Array<Partial<MemberTag>> = [];
    for (const tagUid of invite.tagUids) {
      memberTags.push({
        userUid: req.locals.user.uid,
        tagUid,
        createdAt: trx.fn.now() as any,
        updatedAt: trx.fn.now() as any,
        deletedAt: trx.raw('null') as any,
      });
    }
    if (memberTags.length > 0) {
      await MemberTag.query(trx)
        .insert(memberTags)
        .onConflict(['userUid', 'tagUid'])
        .merge();
    }

    const fgaTuples: FGARawWrite[] = [
      {
        objectId: orgId,
        objectType: 'org',
        relation: 'member',
        subjectId: userId,
        subjectType: 'user',
      },
    ];
    const projectRoles = await req.models.ProjectInvite.query(trx).where('inviteUid', invite.uid).select('roleUid', 'projectUid');
    const excludedProjects = projectRoles.map((p) => `project:${p.projectUid}`);

    fgaTuples.push({
      objectId: invite.roleUid,
      objectType: 'role',
      relation: 'assignee',
      subjectId: userId,
      subjectType: 'user',
      condition: {
        name: 'default_role',
        context: {
          excluded: excludedProjects,
        },
      },
    });

    if (projectRoles.length) {
      const groupedRoles = projectRoles.reduce((acc, projectRole) => {
        if (!acc[projectRole.roleUid]) {
          acc[projectRole.roleUid] = [];
        }
        acc[projectRole.roleUid].push(`project:${projectRole.projectUid}`);
        return acc;
      }, {});

      Object.entries(groupedRoles).forEach(([roleUid, allowedProjects]) => {
        fgaTuples.push({
          objectId: roleUid,
          objectType: 'role',
          relation: 'assignee',
          subjectId: userId,
          subjectType: 'user',
          condition: {
            name: 'override_role',
            context: {
              allowed: allowedProjects,
            },
          },
        });
      });
    }
    await req.fga.create(...fgaTuples);
    await sharedTrx.commit();
    await trx.commit();
  } catch (error) {
    logger.log('info', `Unable to accept invite: ${error}`);
    await Promise.all([sharedTrx.rollback(), trx.rollback()]);
    if ([error.code, error.nativeError?.code].includes('ER_DUP_ENTRY')) throw new ApplicationError(StatusCodes.CONFLICT, error.message);

    throw error;
  }
};

async function sendInviteMail(params: SendInviteMailParams) {
  logger.info(`Sending ${params.orgName} invite mail to ${params.to}`);
  const orgName = _.capitalize(params.orgName);

  const param: EmailNotification = {
    to: params.to,
    subject: `Join ${orgName} on Testfiesta`,
    body: {
      template: 'org_invite',
      params: {
        orgName,
        newUser: params.newUser,
        inviteLink: params.link,
        invitedBy: _.capitalize(params.from),
      },
    },
    customArgs: { tenantUid: params.tenantUid, inviteUid: params.inviteUid },
  };

  startWorkflow('emailWorkflow', {
    taskQueue: 'email-queue',
    workflowId: `email:${Date.now()}`,
    args: [param],
  });
}

const resendInvite = async (req: Request, res: Response) => {
  const failures: { email: string; error: string }[] = [];
  for (const email of req.body.emails) {
    try {
      const invite = await req.models.Invite.query()
        .where({ email, deletedAt: null })
        .first();

      if (!invite) {
        throw new Error(errorConstants.INVITATION_NOT_EXIST);
      }

      const [error] = validateInvite(req, invite, false);
      if (error) {
        throw new Error(error);
      }

      const org = await req.models.Org.query().findById(
        req.locals.handle.ownerUid,
      );

      const ssoConfig = await req.models.SSOConfig.query(req.sharedKnexDB).where({ orgUid: req.locals.handle.ownerUid, isActive: true }).first();

      const from = `${req.locals.user.firstName} ${req.locals.user.lastName}`;
      let link = `${env.FRONTEND_URL}/organizations/${req.locals.handle.name}/invite/${invite.token}`;
      if (ssoConfig && ssoConfig.config.oidc?.allowOnlyInvitedAccounts) {
        link = `${env.FRONTEND_URL}/${req.locals.handle.name}/login`;
      }

      await sendInviteMail({
        from,
        link,
        to: invite.email,
        orgName: org.name,
        newUser: false,
        inviteUid: invite.uid,
        tenantUid: req.locals.handle.ownerUid,
      });
      return null;
    } catch (error) {
      failures.push({ email, error: error.message });
      continue;
    }
  }

  if (failures.length > 0) res.status(StatusCodes.MULTI_STATUS);
  return { failures };
};

const bulkUpdateInvite = async (req: Request, res: Response) => {
  const { updates } = req.body;
  const failures: { email: string; error: string }[] = [];
  const projectUid = req.locals.project?.uid;

  for (const update of updates) {
    const trx = await req.knexDB.transaction();
    try {
      const invite = await req.models.Invite.query(trx)
        .where({ email: update.email, deletedAt: null })
        .first();

      if (!invite) {
        throw new Error(errorConstants.INVITATION_NOT_EXIST);
      }

      const [error] = validateInvite(req, invite, false);
      if (error) {
        throw new Error(error);
      }

      const patches: Partial<Invite> = {};
      if (update.tagUids?.length) {
        const validUids = (
          await req.models.Tag.query(trx)
            .whereIn('uid', update.tagUids)
            .where('systemType', 'tag')
        ).map((t) => t.uid);
        if (validUids.length) patches.tagUids = trx.raw('?', [validUids]) as any;
      }

      if (update.roleUid && update.roleUid !== invite.roleUid && !projectUid) {
        const role = await req.models.Role.query(trx).findById(update.roleUid);
        if (!role) throw new Error(errorConstants.ROLE_NOT_FOUND);

        if (role.slug === 'owner') {
          const userIsOwner = await req.fga.check(
            `user:${req.locals.user.uid}`,
            `role:${role.uid}`,
            'assignee',
            { current_project: '' },
          );
          if (!userIsOwner) throw new Error(errorConstants.USER_IS_NOT_OWNER);
        }
        patches.roleUid = role.uid;
      }

      if (!_.isEmpty(patches)) {
        await req.models.Invite.query(trx).patchAndFetchById(
          invite.uid,
          patches,
        );
      }

      if (update.overriddenRoles) {
        await req.models.ProjectInvite.query(trx)
          .where('inviteUid', invite.uid)
          .delete();

        const projectInvites = update.overriddenRoles.map((r) => ({
          roleUid: r.roleUid,
          projectUid: r.projectUid,
          inviteUid: invite.uid,
        }));

        if (projectInvites.length) { await req.models.ProjectInvite.query(trx).insert(projectInvites); }
      }
      await trx.commit();
    } catch (err) {
      await trx.rollback();
      failures.push({ email: update.email, error: err.message });
      continue;
    }
  }

  if (failures.length > 0) res.status(StatusCodes.MULTI_STATUS);

  return { failures };
};

const getInvite = async (req: Request) => {
  const inviteData = await req.models.Invite.query()
    .where('token', req.params.token)
    .where('deletedAt', null)
    .first();

  if (!inviteData) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.INVITATION_LINK_EXPIRED,
    );
  }

  const [error] = validateInvite(req, inviteData, false);
  if (error) {
    throw new ApplicationError(StatusCodes.UNPROCESSABLE_ENTITY, error);
  }

  const [sender, org] = await Promise.all([
    req.models.User.query()
      .where('uid', inviteData.inviterUid)
      .whereNull('deletedAt')
      .select('firstName', 'lastName')
      .first(),
    req.models.Org.query().findById(req.locals.handle.ownerUid),
  ]);

  return {
    sender: {
      firstName: sender.firstName,
      lastName: sender.lastName,
    },
    email: inviteData.email,
    name: org.name,
    uid: inviteData.uid,
  };
};

const getPendingInvites = async (req: Request) => {
  const projectUid = req.locals.project?.uid;
  const status: 'pending' | 'expired' = req.query.status as any;
  const invites: any[] = await req.models.Invite.query()
    .innerJoin('roles', 'roles.uid', 'invites.roleUid')
    .whereNull('accepted')
    .whereNull('invites.deletedAt')
    .where((q) => {
      if (status === 'pending') q.whereRaw('"expiresAt" >= current_timestamp');
      if (status === 'expired') q.whereRaw('"expiresAt" < current_timestamp');
    })
    .modify((q) => {
      if (projectUid) {
        q.where('roles.slug', 'no_access')
          .innerJoin('projectInvites', 'projectInvites.inviteUid', 'invites.uid')
          .groupBy('invites.uid', 'roles.uid', 'projectInvites.inviteUid', 'projectInvites.projectUid')
          .havingRaw('COUNT("projectInvites"."projectUid") = 1')
          .andWhere('projectInvites.projectUid', projectUid);
      }
    })
    .select({
      uid: 'invites.uid',
      email: 'invites.email',
      roleUid: 'invites.roleUid',
      role: 'roles.name',
      expiresAt: 'invites.expiresAt',
      createdAt: 'invites.createdAt',
      status: 'invites.status',
      tags: 'invites.tagUids',
    });

  const projectInvites = await req.models.ProjectInvite.query()
    .whereIn('inviteUid', invites.map((invite) => invite.uid))
    .select('inviteUid', 'roleUid', 'projectUid');

  const projectInviteMap = projectInvites.reduce((acc, projectInvite) => {
    const { inviteUid, roleUid, projectUid } = projectInvite;
    if (!acc[inviteUid]) acc[inviteUid] = {};
    if (!acc[inviteUid][roleUid]) acc[inviteUid][roleUid] = [];
    acc[inviteUid][roleUid].push(projectUid);
    return acc;
  }, {});

  for (const invite of invites) {
    const roleProjects = projectInviteMap[invite.uid] || {};
    const user = (await req.models.User.query()
      .innerJoin('handles', 'users.uid', 'handles.ownerUid')
      .where({ email: invite.email })
      .select({
        firstName: 'users.firstName',
        lastName: 'users.lastName',
        username: 'handles.name',
        avatar: 'users.avatar',
      })
      .first()) as any;

    invite.role = {
      uid: invite.roleUid,
      name: invite.role,
    };

    invite.tags = invite.tags?.length
      ? await req.models.Tag.query()
        .whereIn('uid', invite.tags)
        .select('uid', 'name')
      : [];

    if (invite.projects?.length >= 0) {
      invite.projects = await req.models.Project.query()
        .whereIn('uid', invite.projects)
        .select('uid', 'name', 'key');
    }
    invite.overriddenRoles = roleProjects
      ? Object.entries(roleProjects).map(([roleUid, projectUids]) => ({
        roleId: roleUid,
        projectUids,
      }))
      : [];

    if (!user) continue;
    invite.firstName = user.firstName;
    invite.lastName = user.lastName;
    invite.username = user.username;
    invite.avatar = user.avatar;
  }

  return invites;
};

const declineInvite = async (req: Request) => {
  const invite = await req.models.Invite.query()
    .where('token', req.params.token)
    .where('deletedAt', null)
    .first();
  if (!invite) {
    throw new ApplicationError(
      StatusCodes.NOT_FOUND,
      errorConstants.INVITATION_LINK_EXPIRED,
    );
  }

  const [error] = validateInvite(req, invite);
  if (error) {
    throw new ApplicationError(StatusCodes.UNPROCESSABLE_ENTITY, error);
  }

  await req.models.Invite.query()
    .findById(invite.uid)
    .patch({
      accepted: false,
      acceptedAt: formatDate(dayjs().toDate()),
    });
};

const deleteInviteByEmail = async (req: Request) => {
  // automatically rollback on error
  await req.knexDB.transaction(async (trx) => {
    await req.models.Invite.query(trx)
      .where({ email: req.body.email })
      .patch({ deletedAt: req.knexDB.fn.now() });

    await trx.commit();
  });
};

const validateInvite = (req: Request, invite: Invite, checkEmail = true) => {
  logger.info(`Validating invite: token:${invite.token}`);
  if (checkEmail) {
    if (invite.email !== req.locals.user.email) {
      logger.info(
        `Email for logged in user [${req.locals.user.email}] doesn't match invite email [${invite.email}]`,
      );
      return [errorConstants.INVITATION_EMAIL_NO_MATCH];
    }
  }
  if (!isNull(invite.accepted)) {
    logger.info(
      `Invite uid:${invite.uid.toString()} has already been accepted.`,
    );
    return [errorConstants.INVITATION_LINK_USED];
  }

  if (dayjs().isAfter(invite.expiresAt)) {
    logger.info(`Invite uid:${invite.uid.toString()} has already expired.`);
    return [errorConstants.INVITATION_LINK_EXPIRED];
  }

  return [];
};

async function getOrgMembers(req: Request, uid: string, sharedKnex: Knex) {
  const members = await req.models.Membership.query(sharedKnex).where({
    accountUid: uid,
  });
  return members.map((m) => m.userUid);
}

export default {
  newInvite: httpHandler(newInvite),
  resendInvite: httpHandler(resendInvite),
  bulkUpdateInvite: httpHandler(bulkUpdateInvite),
  getInvite: httpHandler(getInvite),
  deleteInviteByEmail: httpHandler(deleteInviteByEmail),
  declineInvite: httpHandler(declineInvite),
  acceptInvite: httpHandler(acceptInvite),
  getPendingInvites: httpHandler(getPendingInvites),

};
