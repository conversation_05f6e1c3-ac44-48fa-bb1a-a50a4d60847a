import { ApplicationError, httpHandler } from '@app/lib/http';

import { CustomField } from '@app/models/customField';
import { Request } from 'express';
import { StatusCodes } from 'http-status-codes';
import { UniqueViolationError } from 'objection';
import errorConstants from '@app/constants/errors';
import { kebabCase } from 'lodash';

const getCustomFields = async (req: Request) => {
  const owner = req.locals.handle.ownerUid;
  const { ownerType } = req.locals.handle;

  const uids = (
    await req.fga.query(`${ownerType}:${owner}`, 'custom_field:', 'owner')
  ).map((tuple: any) => tuple.key.object.split(':')[1]);

  const customFields = await req.models.CustomField.query()
    .whereIn('uid', uids)
    .where('projectUid', req.locals.project.uid)
    .whereNull('deletedAt')
    .select('*');
  return customFields;
};

const createCustomField = async (req: Request) => {
  const {
    name, type, options, source,
  } = req.body;
  const trx = await req.knexDB.transaction();
  try {
    const customField = await req.models.CustomField.query(trx)
      .insert({
        name,
        slug: kebabCase(name),
        type,
        options,
        source,
        projectUid: req.locals.project.uid,
      })
      .returning('*');

    await req.fga.create({
      objectType: 'custom_field',
      objectId: customField.uid,
      relation: 'owner',
      subjectType: req.locals.handle.ownerType,
      subjectId: req.locals.handle.ownerUid,
    });
    await trx.commit();
    return customField;
  } catch (err) {
    await trx.rollback();
    if (err instanceof UniqueViolationError) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        errorConstants.CUSTOM_FIELD_EXISTS,
      );
    }
    throw err;
  }
};

const updateCustomField = async (req: Request) => {
  const {
    name, type, options, source,
  } = req.body;

  try {
    const update: Partial<CustomField> = {
      name,
      type,
      options,
      source,
    };
    if (update.name) update.slug = kebabCase(update.name);

    const updatedCustomField = await req.models.CustomField.query()
      .findById(req.params.id)
      .where('projectUid', req.locals.project.uid)
      .patch(update)
      .returning('*');

    return updatedCustomField;
  } catch (err) {
    if (err instanceof UniqueViolationError) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        errorConstants.CUSTOM_FIELD_EXISTS,
      );
    }
    throw err;
  }
};

const deleteCustomField = async (req: Request) => {
  await req.models.CustomField.query()
    .findById(req.params.id)
    .where('projectUid', req.locals.project.uid)
    .patch({ deletedAt: req.knexDB.fn.now(), slug: req.knexDB.raw('null') });
};

export default {
  getCustomFields: httpHandler(getCustomFields),
  createCustomField: httpHandler(createCustomField),
  updateCustomField: httpHandler(updateCustomField),
  deleteCustomField: httpHandler(deleteCustomField),
};
