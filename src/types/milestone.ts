import { PaginatedQuery } from '@app/lib/model';

/**
 * Data Transfer Object for creating a new milestone.
 */
export interface CreateMilestoneDTO {
  /**
   * The name of the milestone.
   * A string representing the title or label of the milestone.
   */
  name: string;

  /**
   * The description of the milestone.
   * Optional field that provides additional details or context about the milestone.
   */
  description?: string;

  /**
   * The start date of the milestone.
   * A string in ISO 8601 format representing the start date of the milestone (e.g., "2025-03-01T00:00:00Z").
   */
  startDate: string;

  /**
   * The due date of the milestone.
   * A string in ISO 8601 format representing the due date or deadline for the milestone (e.g., "2025-04-01T00:00:00Z").
   */
  dueAt: string;

  /**
   * The status of the milestone.
   * A number representing the current status of the milestone (e.g., 1 for "In Progress", 2 for "Completed").
   */
  status: number;

  /**
   * The IDs of the plans associated with the milestone.
   * An array of numbers representing the IDs of the plans linked to the milestone.
   */
  planIds?: number[];

  /**
   * The IDs of the runs associated with the milestone.
   * An array of numbers representing the IDs of the test runs linked to the milestone.
   */
  runIds?: number[];

  /**
   * The tag UIDs associated with the milestone.
   * Optional field to specify an array of tag IDs for categorizing or labeling the milestone.
   */
  tagUids?: number[];
}

export interface UpdateMilestoneDTO extends Partial<CreateMilestoneDTO> {
  archived?: boolean;
}

export interface ListMilestonesDTO extends PaginatedQuery {
  projectUid: number;
  /**
   * The status of the milestone.
   * A number representing the current status of the milestone (e.g., 1 for "In Progress", 2 for "Completed").
   */
  status?: number;
  tagUids?: number[];
  archived: boolean;
}

export const milestoneRelations = <const>[
  'runCount',
  'tag',
  'planCount',
  'caseCount',
];
export interface GetMilestoneRelationsDTO {
  relation: (typeof milestoneRelations)[number];
  milestoneUids: number[];
}
