import { EntityType } from '../models/tag';
import { PaginatedQuery } from '../lib/model';

export interface TagQueryParams extends PaginatedQuery {
  entityType?: string;
}

/**
 * Data Transfer Object for creating a new tag.
 */
export interface CreateTagDto {
  /**
   * The name of the tag.
   */
  name: string;
  /**
   * The description of the tag.
   */
  description?: string;
  /**
   * The entity types of the tag.
   */
  entityTypes: EntityType[];
}

/**
 * Data Transfer Object for updating an existing tag.
 */
export interface UpdateTagDto {
  /**
   * The name of the tag.
   */
  name?: string;
  /**
   * The description of the tag.
   */
  description?: string;
  /**
   * The entity types of the tag.
   */
  entityTypes?: EntityType[];
  /**
   * The boolean flag to archive/unarchive a tag.
   */
  archived?: boolean;
}

export interface ListTagsDto {
  entityType: EntityType;
  includeArchived: boolean;
}
