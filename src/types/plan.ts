import { PaginatedQuery } from '../lib/model';

/**
 * Data Transfer Object for creating a new plan.
 */
export interface CreatePlanDTO extends PlanDTO {
  /**
   * The test runs associated with the plan.
   * Optional field that contains the test runs to be included in the plan, each with its UID and configuration details.
   * @example
   * [{ uid: 1, configuration: { type: 'simple', sets: [['browser:firefox'], ['browser:safari']] } }]
   */
  testRuns?: { uid: number; configuration: ConfigurationDTO }[];

  /**
   * The configuration details for the plan.
   * Optional field that specifies the configuration for the plan, such as the type of configuration and the sets of options it includes.
   * @example
   * { type: 'simple', sets: [['browser:firefox'], ['browser:safari']] }
   */
  configuration?: ConfigurationDTO;

  /**
   * The tag UIDs associated with the plan.
   * Optional field to specify the tags linked to the plan. Tags help categorize or label the plan for better organization and filtering.
   */
  tagUids?: number[];
}

/**
 * Defines the structure of the configuration for a plan.
 *
 * @property {string} type - The type of configuration: either 'simple' or 'matrix'.
 * @property {string[][]} sets - List of configuration sets where each set contains configuration options prefixed by their id.
 *
 * @example
 * {
 *   "type": "matrix",
 *   "sets": [
 *     ["browser:firefox", "browser:safari"],
 *     ["os:windows", "os:mac"]
 *   ]
 * }
 */
export type ConfigurationDTO = {
  type: 'simple' | 'matrix';
  /**
   * a configuration set is a list of configuration options prefixed by their id
   * @example
   * const config = {id:browser,option:["firefox","safari"]}
   * const configSet = ["brower:firefox","browser:safari"]
   */
  sets: string[][];
};

export interface DuplicatePlanDTO {
  uid: number;
  runUids?: number[];
}

/**
 * Base structure for a plan.
 */
interface PlanDTO {
  /**
   * The name of the plan.
   * This field defines the name or title of the plan.
   */
  name: string;

  /**
   * A brief description of the plan.
   * Optional field that provides additional context or information about the plan.
   */
  description?: string;

  /**
   * The milestone UIDs associated with the plan.
   * Optional field that links the plan to one or more milestones. Milestones represent significant points in the project or timeline.
   */
  milestoneUids?: number[];

  /**
   * The status of the plan.
   * Optional field that defines the current state of the plan, such as pending, active, or completed, usually represented as a numeric value.
   */
  status?: number;

  /**
   * The priority level of the plan.
   * Optional field that represents the priority of the plan, typically represented as a numeric value (e.g., 1 = high, 2 = medium, 3 = low).
   */
  priority?: number;

  /**
   * Custom fields associated with the plan.
   * Optional field that allows the addition of key-value pairs to capture additional metadata or context for the plan.
   */
  customFields?: Record<string, string>;

  /**
   * The external identifier for the plan.
   * Optional field to reference the plan externally, useful for integration with other systems or tools.
   */
  externalId?: string;

  /**
   * The source of the plan.
   * Optional field that provides information about the origin of the plan, such as the tool or platform from which it was created.
   */
  source?: string;
}

/**
 * Data Transfer Object for listing paginated plans.
 */
export interface ListPlanDTO extends PaginatedQuery {
  status?: number;
  priority?: number;
  /**
   * list of statuses to filter by.
   * only plans with statuses here will be returned
   */
  statusUids?: number[];
  /**
   * list of priorities to filter by.
   * only plans with the speficied priorities will be returned
   */
  priorityUids?: number[];
  /**
   * filter for archived plans
   */
  archived: boolean;
  /**
   * mininum number of runs
   */
  minRunCount: number;
  /**
   * maximum number of test runs
   */
  maxRunCount?: number;
  /**
   * minimum creation date
   */
  minCreatedAt: string;
  /**
   * maximum creation date
   */
  maxCreatedAt?: string;
  /**
   * minimum progress
   */
  minProgress: number;
  /**
   * max progress
   */
  maxProgress?: number;
  /**
   * list of tags to filter by
   */
  tagUids?: number[];
  /**
   * list of milestones to filter by
   */
  milestoneUids?: number[];
}

export const bulkUpdateActions = <const>[
  'delete',
  'archive',
  'unarchive',
  'addMilestones',
  'removeMilestones',
  'addRuns', // add test tuns to the specified test plans
  'removeRuns', // remove the speciied test runs from the specified test plans
];
export type BulkUpdateAction = (typeof bulkUpdateActions)[number];

export interface BulkUpdatePlanDTO {
  milestoneUids?: number[];
  uids?: number[];
  action: (typeof bulkUpdateActions)[number];
  cascade: boolean;
  runUids?: number[];
}

/**
 * Data Transfer Object for updating an existing plan.
 */
export interface UpdatePlanDTO {
  /**
   * The name of the plan.
   * Optional field to update the name or title of the plan.
   */
  name?: string;

  /**
   * A brief description of the plan.
   * Optional field to update the description, providing additional context or details about the plan.
   */
  description?: string;

  /**
   * The tag UIDs associated with the plan.
   * Optional field to update the tags linked to the plan. Tags help categorize or label the plan for easier organization.
   */
  tagUids?: number[];

  /**
   * The milestone UIDs associated with the plan.
   * Optional field to update the milestones linked to the plan. Milestones represent key points in the project or timeline.
   */
  milestoneUids?: number[];

  /**
   * The run UIDs associated with the plan.
   * Optional field to update the test runs included in the plan. These are the specific runs that are part of the plan's execution.
   */
  runUids?: number[];

  /**
   * The status of the plan.
   * Optional field to update the current state of the plan, such as pending, active, or completed, typically represented by a numeric value.
   */
  status?: number;

  /**
   * The priority level of the plan.
   * Optional field to update the priority of the plan, generally represented as a numeric value (e.g., 1 = high, 2 = medium, 3 = low).
   */
  priority?: number;
}

export const planRelations = <const>['milestoneCount', 'runCount', 'tag'];
export interface GetPlanRelationsDTO {
  relation: (typeof planRelations)[number];
  planUids: number[];
}
