import { PaginatedQuery } from '../lib/model';
import { TemplateField } from './templateCustomField';

export interface TemplateQueryParams extends PaginatedQuery {
  name?: string;
  createdByIds?: string[];
  creationStartDate?: string;
  creationEndDate?: string;
}

/**
 * Data Transfer Object for creating a new template.
 */
export interface CreateTemplateDto {
  /**
   * The name of the template.
  */
  name: string;
  templateFields?: TemplateField[];
}

/**
 * Data Transfer Object for updating an existing template.
 * @param {string} name - Optional updated name of the template.
 * @param {TemplateField[]} [templateFields] - Optional updated list of custom fields.
 */
export interface UpdateTemplateDto {
  /**
   * The name of the template.
  */
  name?: string;
  templateFields?: TemplateField[];
}
