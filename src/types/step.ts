/**
 * Represents a shared step item within a test case.
 */
export interface SharedStepItem {
  /**
   * The unique identifier for the shared step item.
   */
  id: string;

  /**
   * The title of the shared step item.
   * Optional field that describes the name or label of the shared step.
   */
  title?: string;

  /**
   * The description of the shared step item.
   * Optional field that provides additional details about the shared step.
   */
  description?: string;

  /**
   * The expected result for the shared step item.
   * Optional field that outlines what the expected outcome of the shared step should be.
   */
  expectedResult?: string;

  /**
   * A list of child shared step items.
   * This allows nested shared steps to be defined. Each child is a `SharedStepItem`.
   * Optional field that can be used to structure complex shared steps.
   */
  children?: SharedStepItem[];

  /**
   * The unique identifier for the shared step associated with this item.
   * Optional field to link this item to a specific shared step.
   */
  sharedStepUid?: number;
}

/**
 * Represents a test case step that extends shared steps functionality.
 */
export interface TestCaseStepItem extends SharedStepItem {
  children?: TestCaseStepItem[];
  sharedStepUid?: number;
}

/**
 * Data Transfer Object for creating a new shared step.
 */
export interface CreateSharedStepDto {
  /**
   * The name of the shared step.
   * This field defines the name or label for the shared step.
   */
  name: string;

  /**
   * The list of steps for the shared step.
   * This is an array of `SharedStepItem` objects that define the individual steps
   * within the shared step. Each item can contain a title, description, expected result,
   * and optional child steps.
   */
  steps: SharedStepItem[];
}

/**
 * Data Transfer Object for updating an existing shared step.
 */
export interface UpdateSharedStepDto {
  /**
   * The name of the shared step.
   * Optional field that allows you to update the name or label of the shared step.
   */
  name?: string;

  /**
   * The list of steps for the shared step.
   * Optional field that allows you to update the steps within the shared step.
   * It is an array of `SharedStepItem` objects, where each item represents a step,
   * including its title, description, expected result, and optional child steps.
   */
  steps?: SharedStepItem[];
}
