import { PaginatedQuery } from '@app/lib/model';

export interface RunDTO {
  externalId?: string;
  source?: string;
  link?: string;
  priority: number;
  status: number;
  projectUid: number;
  name: string;
  description?: string;
  dueAt?: Date;
  tagUids?: number[];
  configs?: string[];
}

type ConfigurationDTO = {
  type: 'simple' | 'matrix';
  /**
   * a configuration set is a list of configuration options prefixed by their id
   * @example
   * const config = {id:browser,option:["firefox","safari"]}
   * const configSet = ["brower:firefox","browser:safari"]
   */
  sets: string[][];
};

export interface DuplicateRunsDTO {
  testRuns: {
    uid: number;
    // configuration to be applied to the specific run
    configuration: ConfigurationDTO;
  }[];
  // configuration to be applied to all runs
  configuration: ConfigurationDTO;
}

export interface DuplicateRunOpts {
  planUid?: number;
  sourceRunUid: number;
  execStatus: number;
  execPriority: number;
  executionUids?: number[];
}

export interface NewRunOpts {
  caseUids?: number[];
  milestoneUids?: number[];
  execStatus: number;
  execPriority: number;
}

export interface CreateRunDTO {
  externalId?: string;
  source?: string;
  link?: string;
  priority?: number;
  status?: number;
  name: string;
  description?: string;
  dueAt?: Date;
  tagUids?: number[];
  configs?: string[];
  caseUids?: number[];
  milestoneUids?: number[];
}

export const bulkUpdateActions = <const>[
  'archive',
  'unarchive',
  'addMilestones',
  'removeMilestones',
  'addPlans',
  'updateDueDate',
  'removePlans',
];

export interface BulkUpdateRunDTO {
  milestoneUids?: number[];
  planUids?: number[];
  uids: number[];
  action: (typeof bulkUpdateActions)[number];
  dueAt?: Date;
}

export interface UpdateRunDTO {
  name?: string;
  description?: string;
  status?: number;
  priority?: number;
  dueAt?: Date;
  addMilestoneUids?: number[];
  removeMilestoneUids?: number[];
  addTagUids?: number[];
  removeTagUids?: number[];
  addPlanUids?: number[];
  removePlanUids?: number[];
  archive?: boolean;
  configs?: string[];
}

export interface UpdateRunCasesDTO {
  /**
   * represents the test cases from which new executions will be generated
   * and attached to the test run in question
   */
  addCaseUids: number[];
  /**
   * list of executions to unlink from a test run
   */
  removeExecUids: number[];
}

export interface ListRunsDTO extends PaginatedQuery {
  /**
   * planUid to filter runs by
   * if null is passed , it fetches all runs that aren't linked to a plan
   */
  planUid?: number | null;
  /**
   * list of priotities to filter by, returns test runs that match any of
   * the prorities here
   */
  priorityUids?: number[];
  /**
   * list of statuses to filter by, returns test runs that match any of
   * the statuses here
   */
  statusUids?: number[];
  /**
   * list of configs to filter by, returns test runs that match any of
   * the config here
   * config is expected to be of the format
   * "configId::optionId"
   */
  configs?: string[];
  /**
   * minimum test case count filter
   */
  minTestCaseCount?: number;
  /**
   * max test case count filter
   */
  maxTestCaseCount?: number;
  /**
   * list of milestones to filter by
   * returns test runs that are attached to any of the milestones
   */
  milestoneUids?: number[];
  /**
   * due date from
   */
  fromDueDate?: string;
  /**
   * due date from
   */
  toDueDate?: string;
  /**
   * creation date from
   */
  fromCreatedAt?: string;
  /**
   * creation date to
   */
  toCreatedAt?: string;
  /**
   * minimum progress
   */
  minProgress?: number;
  /**
   * max progress
   */
  maxProgress?: number;
  /**
   * list of tags to filter by
   * returns test runs that are attached to any of the tags
   */
  tagUids?: number[];
  /**
   * project uid to filter by
   */
  projectUid: number;
}

export const runRelations = <const>['tag', 'config', 'milestone'];
export interface GetRunRelationsDTO {
  relation: (typeof runRelations)[number];
  runUids: number[];
}
