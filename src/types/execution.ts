import { PaginatedQuery } from '@app/lib/model';
import { TestCaseStepItem } from './step';

/**
 * Data Transfer Object for updating an existing execution.
 */
export interface UpdateExecutionDTO {
  /**
   * The status of the execution.
   * Optional field to specify the status of the execution.
   */
  status?: number;

  /**
   * The priority of the execution.
   * Optional field to set the priority level of the execution (e.g., high, medium, low).
   */
  priority?: number;

  /**
   * The due date of the execution.
   * Optional field to set the due date for the execution in ISO 8601 format (e.g., "2025-03-10T12:00:00").
   */
  dueAt?: Date;

  /**
   * The user assigned to the execution.
   * Optional field to specify the user responsible for the execution.
   */
  assignedTo?: string;

  /**
   * The tag UIDs associated with the execution.
   * Optional field to associate tags with the execution, represented by their unique identifiers.
   */
  tagUids?: number[];

  /**
   * The tag replacements for the execution.
   * Optional field to replace existing tags with new ones.
   * `existingTagUids` is an array of existing tags to be replaced, and `newTagUids` is an array of the new tags.
   */
  tagReplacements?: {
    existingTagUids?: number[];
    newTagUids?: number[];
  }[];

  /**
   * The name of the execution.
   * Optional field to update the name or title of the execution.
   */
  name?: string;

  /**
   * The steps associated with the execution.
   * Optional field to update or add new steps for the execution.
   */
  steps: TestExecutionStepDTO[];

  /**
   * The template fields for the execution.
   * Optional field to update or specify template fields associated with the execution.
   */
  templateFields?: any;
}

// omit the children interface, so client send everything as a flat structure
interface TestExecutionStepDTO
  extends Omit<TestCaseStepItem, 'id' | 'children'> {
  /**
   * optional uid, if sent, it means the step is to be updated. If not sent, it means a new step is to be added
   */
  uid?: string;
  /**
   * position of the step when ordering the steps
   */
  position: number;
  /**
   * new status of the step
   */
  status?: number;
}

/**
 * Represents a step within an execution.
 */
export interface ExecutionStep {
  /**
   * The unique identifier for the test step.
   * Required field to specify the UID of the test step.
   */
  testStepUid: string;

  /**
   * The description of the test step.
   * Required field to describe the test step's action or purpose.
   */
  description: string;

  /**
   * The custom fields for the test step.
   * Required field to store custom information for the test step in a key-value format.
   */
  customFields: Record<string, any>;
}

/**
 * Data Transfer Object for creating a new execution.
 */
export interface CreateExecutionDTO {
  /**
   * The external identifier for the execution.
   * Optional field to specify an external ID for the execution.
   */
  externalId?: string;

  /**
   * The source of the execution.
   * Optional field to specify the source of the execution (e.g., "manual", "automated").
   */
  source?: string;

  /**
   * The unique identifier of the associated test case.
   * Optional field to link the execution to a specific test case.
   */
  testCaseUid?: number;

  /**
   * The reference number for the associated test case.
   * Optional field to provide an reference number for the test case.
   */
  testCaseRef?: number;

  /**
   * The unique identifier of the associated test run.
   * Optional field to link the execution to a specific test run.
   */
  testRunUid?: number;

  /**
   * The status of the execution.
   * Optional field to specify the status of the execution (e.g., "not started", "in progress", "completed").
   */
  status?: number;

  /**
   * The custom fields for the execution.
   * Required field to store custom information for the execution in a key-value format.
   */
  customFields: Record<string, any>;

  /**
   * The steps associated with the execution.
   * Optional field to include the steps involved in the execution.
   */
  steps?: ExecutionStep[];
}

/**
 * Data Transfer Object for bulk updating executions.
 */
export interface BulkUpdateExecutionDTO extends UpdateExecutionDTO {
  /**
   * The unique identifiers of the executions to be updated.
   * Required field to specify the UIDs of the executions that will be updated in bulk.
   */
  executionUids: number[];
}

export interface ListExecutionsDTO extends PaginatedQuery {
  runUid?: number;
}
export const executionRelations = <const>[
  'tag',
  'testRun',
  'testPlan',
  'milestone',
  'project',
];
export type ExecutionRelation = (typeof executionRelations)[number];

export interface ExecutionRelations {
  relation: ExecutionRelation;
  executionUids: number[];
}
