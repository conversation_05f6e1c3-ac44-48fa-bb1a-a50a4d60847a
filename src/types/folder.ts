/**
 * Data Transfer Object for creating a new folder.
 */
export interface CreateFolderDto {
  /**
   * The name of the folder.
   * A string representing the title or label of the folder.
   */
  name: string;

  /**
   * The external ID of the folder.
   * Optional field to associate the folder with an external identifier, such as from another system or service.
   */
  externalId?: string;

  /**
   * The source of the folder.
   * Optional field to specify the origin or source system of the folder (e.g., "manual", "imported").
   */
  source?: string;

  /**
   * The custom fields associated with the folder.
   * Optional field that allows additional metadata or custom attributes to be stored in the folder.
   */
  customFields?: Record<string, any>;

  /**
   * The parent ID of the folder.
   * A number representing the unique identifier of the parent folder. Used for nesting folders.
   */
  parentId: number;

  /**
   * The status of the folder.
   * Optional field that represents the current status of the folder (e.g., 1 for "Active", 2 for "Archived").
   */
  status?: number;
}

/**
 * Data Transfer Object for updating an existing folder.
 */
export interface UpdateFolderDto {
  /**
   * The name of the folder.
   * Optional field to update the title or label of the folder.
   */
  name?: string;

  /**
   * The external ID of the folder.
   * Optional field to update the external identifier of the folder.
   */
  externalId?: string;

  /**
   * The source of the folder.
   * Optional field to update the source or origin of the folder.
   */
  source?: string;

  /**
   * The custom fields associated with the folder.
   * Optional field to update or add additional metadata or custom attributes to the folder.
   */
  customFields?: Record<string, any>;

  /**
   * The parent ID of the folder.
   * Optional field to update the unique identifier of the parent folder, allowing for reorganization of folders.
   */
  parentId?: number;

  /**
   * The status of the folder.
   * Optional field to update the current status of the folder.
   */
  status?: number;
}

export interface FolderQueryParams {
  /**
 * The type of entity associated with the folder.
 * Can be either 'case' (for test cases) or 'execution' (for test executions).
 */
  entityType?: 'case' | 'execution'

  /**
   * The ID of the test run.
   * Used to filter folders based on their association with a specific test run.
   */
  testRunId?: number
}
