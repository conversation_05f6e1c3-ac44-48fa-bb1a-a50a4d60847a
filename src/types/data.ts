interface FolderData {
  name: string;
  externalId: string;
  source: string;
  [key: string]: any;
}
interface RunData {
  name: string,
  externalId: string;
  source: string;
  [key: string]: any;
}
interface ExecutionData {
  externalId?: string;
  caseRef: string;
  runRef: string;
  source: string;
  [key: string]: any;
}
interface CaseData {
  name: string,
  externalId: string;
  source: string;
  folderExternalId?: string;
  [key: string]: any;
}
export interface CreateDataDto {
  entities: {
    cases?: {
      entries: CaseData[]
    },
    folders?: {
      entries: FolderData[]
    },
    runs?: {
      entries: RunData[]
    },
    executions?: {
      entries: ExecutionData[]
    }
  }
}
export const entities = ['cases', 'runs', 'folders', 'executions'];
