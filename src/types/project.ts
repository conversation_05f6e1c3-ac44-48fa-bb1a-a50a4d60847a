import { PaginatedQuery } from '@app/lib/model';

export interface CreateProjectDto {
  name: string;
  key: string;
  externalId?: string;
  source?: string;
  customFields: Record<string, any>;
}

export interface GetProjectsDTO extends PaginatedQuery {
  includeCount?: boolean;
}

export const countProjectEntityTypes = <const>['run', 'milestone', 'plan'];
export type CountProjectEntityType = (typeof countProjectEntityTypes)[number];
