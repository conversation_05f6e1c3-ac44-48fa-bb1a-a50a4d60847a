import { CustomFieldTypes } from '../models/customField';

/**
 * Data Transfer Object for creating a new custom field.
 */
export interface CreateCustomFieldDto {
  /**
   * The name of the custom field.
   * A string representing the title or label of the custom field.
   */
  name: string;

  /**
   * The type of the custom field.
   * Optional field to specify the data type of the custom field (e.g., text, number, date).
   * Defaults to `undefined` if not provided.
   */
  type?: CustomFieldTypes;

  /**
   * The source of the custom field.
   * Optional field to specify the origin or source of the custom field (e.g., "manual", "imported").
   */
  source?: string;

  /**
   * The options for the custom field.
   * Optional field to define the available choices for the custom field, applicable for certain types like dropdowns or radio buttons.
   * Should be an array of strings representing the options.
   */
  options?: string[];
}

/**
 * Data Transfer Object for updating an existing custom field.
 */
export interface UpdateCustomFieldDto {
  /**
   * The name of the custom field.
   * Optional field to update the title or label of the custom field.
   */
  name?: string;

  /**
   * The type of the custom field.
   * Optional field to update the data type of the custom field (e.g., text, number, date).
   */
  type?: CustomFieldTypes;

  /**
   * The source of the custom field.
   * Optional field to update the source or origin of the custom field.
   */
  source?: string;

  /**
   * The options for the custom field.
   * Optional field to update or add new options for the custom field.
   * Should be an array of strings representing the updated options.
   */
  options?: string[];
}
