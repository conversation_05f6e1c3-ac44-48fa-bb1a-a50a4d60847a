export type CustomFieldDataType =
  | 'text'
  | 'radio'
  | 'date'
  | 'step'
  | 'attachment'
  | 'link'
  | 'checkbox'
  | 'dropdown'
  | 'none';

/**
 * Represents a single custom field within a template.
 */
export interface TemplateField {
  id: string;
  /**
   * The name of the custom template field.
  */
  name: string;
  /**
   * The data type of the custom template field.
  */
  dataType: CustomFieldDataType;
  /**
   * The default value of the custom template field.
  */
  defaultValue?: string;
  /**
   * The value of the custom template field.
  */
  value?: string;
  options?: string[];
}

/**
 * Represents a collection of custom fields used within a template.
 * @param {TemplateField[]} templateFields - An array of custom fields associated with the template.
 */
export interface CustomFields {
  templateFields: TemplateField[]
}
