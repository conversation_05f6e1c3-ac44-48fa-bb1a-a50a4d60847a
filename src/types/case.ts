import { PaginatedQuery } from '../lib/model';

export interface CaseQueryParams extends PaginatedQuery {
  priority?: string;
  tag?: string;
}

/**
 * Data Transfer Object for creating a new case.
 */
export interface CreateCaseDTO {
  /**
   * Custom fields associated with the case.
   * This is an object that contains custom fields relevant to the case. The structure can vary depending on the specific custom fields for the case.
   */
  customFields: any;

  /**
   * The source from which the case originates.
   * A string that represents the origin or the system from which the case is being created.
   */
  source?: string;

  /**
   * The name of the case.
   * A string that provides the title or label for the case.
   */
  name: string;

  /**
   * The priority of the case.
   * Optional field to define the importance or urgency of the case. It can be represented numerically (e.g., 1 = high priority).
   */
  priority?: number;

  status?: number;

  /**
   * The repository UID where the case is located.
   * A string that uniquely identifies the repository to which the case belongs.
   */
  repoUID?: string;

  /**
   * The parent case ID.
   * A number that represents the ID of the parent case, allowing the case to be part of a hierarchy or grouping.
   */
  parentId: number;

  /**
   * The steps associated with the case.
   * An array of steps that describe the process, actions, or workflow related to the case.
   */
  steps: any[];

  /**
   * The external ID of the case.
   * A string that represents an external identifier, often used to correlate or link the case with external systems or databases.
   */
  externalId?: string;

  /**
   * The project ID to which the case belongs.
   * A string that identifies the project that the case is part of.
   */
  projectId: number;

  /**
   * The template ID associated with the case.
   * Optional field to specify a template to be applied to the case. It links the case to a predefined template for consistency in its structure.
   */
  templateId?: string;

  /**
   * The tag IDs associated with the case.
   * Optional field that includes an array of tag IDs to categorize or label the case for better filtering or grouping.
   */
  tagIds?: number[];

  /**
   * list of tag names to be attached to the case, typically used when importing tags
   */
  tags?: string[];

  /**
   * external createdAt timestamp
   */
  externalCreatedAt?: string;

  /**
   * external updatedAt timestamp
   */
  externalUpdatedAt?: string;

  /**
   * status as text
   */
  statusText?: string;

  /**
   * priority as text
   */
  priorityText?: string;

  createdBy?: string;
}

export interface ListCaseDTO extends PaginatedQuery {
  status?: number;
  priority?: number;
  tagUids?: number[];
  parentUid: string;
}

export const caseRelations = <const>['tag', 'creator'];
export interface GetCaseRelationDTO {
  relation: (typeof caseRelations)[number];
  caseUids: number[];
}

export interface TagReplacement {
  existingTagUids: number[];
  newTagUids: number[];
}

export interface GetCaseDTO extends PaginatedQuery {
  priority?: number;
  tagUids: number[];
  groupByFolder?: boolean;
  folderId?: number;
}
