export type Pagination = {
  total: number; // Total number of items
  per_page: number; // Number of items per page
  current_page: number; // Current page number
  last_page: number; // Total number of pages
  from: number; // The record number of the first item on the page
  to: number; // The record number of the last item on the page
  offset: number; // The number of skipped items
  items?: any[]; // Optional: Array of items on the current page
};
