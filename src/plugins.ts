import * as media from '@ss-libs/ss-component-media';
import * as notifications from '@ss-libs/ss-component-notifications';

import { Knex } from 'knex';

import { config } from 'dotenv';
import express from 'express';
import { resourceManager } from '@ss-libs/ss-component-admin';
import payments from '@ss-libs/ss-component-payments';
import authComponent from '@ss-libs/ss-component-auth';
import { userService, notificationService } from '@app/services';
import { registerEncryptionRoutes } from '@ss-libs/ss-component-encryption';
import path from 'path';
import { systemAvatars } from '@app/constants/avatars';
import { Org } from '@app/models/org';
import { DEFAULT_OIDC_SCOPE } from '@app/models/ssoConfig';
import { tenantManager } from '@app/lib/tenants';
import { Tenant } from '@app/models/tenant';
import { initializeStorage } from '@app/utils/storage';
import env from './config/env';
import { adminConfig } from './models';
import { authenticateOrg, authenticateUser } from './constants/auth';
import { configureStorageType } from './middlewares/storage';
import logger from './config/logger';
import { KEY_MANAGER } from './config/keyManagerLoader';

config();
export function setupPlugins(db: Knex): express.Router {
  const router = express.Router();

  router.use([env.API_INTERNAL_ROUTE], (req, res, next) => {
    req.sharedKnexDB = db;
    next();
  });
  logger.info('Loading auth...');
  router.use(
    [env.API_INTERNAL_ROUTE],
    authComponent({
      db,
      ttl: env.JWT_EXPIRATION_TIME,
      jwtSecret: env.JWT_SECRET,
      sessionSecret: env.JWT_SIGNING_SECRET,
      logger,
      sendMail: userService.sendResetPasswordMail,
      onCreateUser: userService.onCreateUser,
      excludes: [
        'passwordHash',
        'passwordResetToken',
        'passwordResetAttemptsCount',
        'passwordResetAt',
        'stripeId',
        'deletedAt',
        'admin',
      ],
      oidc: {
        redirectCallbackUrl: env.OIDC_REDIRECT_CALLBACK_URL,
        callbackUrl: env.OIDC_CALLBACK_URL,
        scope: DEFAULT_OIDC_SCOPE,
      },
      googleAuth: {
        clientId: env.OAUTH_GOOGLE_CLIENT_ID,
        clientSecret: env.OAUTH_GOOGLE_CLIENT_SECRET,
        callbackUrl: env.OAUTH_GOOGLE_CALLBACK_URL,
        redirectSignInCallbackUrl:
          env.OAUTH_GOOGLE_REDIRECT_SIGN_IN_CALLBACK_URL,
        redirectSignUpCallbackUrl:
          env.OAUTH_GOOGLE_REDIRECT_SIGN_UP_CALLBACK_URL,
      },
      loadTenant: (tenant) => tenantManager.loadTenant(tenant as Tenant),
      systemAvatars,
      keyManager: KEY_MANAGER,
    }),
  );

  logger.info('Loading notifications...');
  router.use(
    [env.API_INTERNAL_ROUTE],
    notifications.init({
      emails: {
        credentials: { apiKey: env.SENDGRID_KEY },
        from: env.MAIL_USER,
        provider: 'sendgrid',
        templatesDir: path.join(process.cwd(), 'dist/src/mail'),
      },
      webhooks: {
        path: '/webhooks/sendgrid',
        handler: notificationService.onNotificationWebhook,
        signingSecret: env.SENDGRID_SIGNING_SECRET,
      },
    }),
  );

  logger.info('Loading payments...');
  router.use(
    [env.API_INTERNAL_ROUTE],
    payments.stripeHooks({
      db,
      stripe: {
        secretKey: env.STRIPE_SECRET_KEY,
        signingSecret: env.STRIPE_SIGNING_SECRET,
      },
      models: {
        org: {
          tableName: Org.tableName,
          auth: authenticateOrg,
          defaultPrice: env.STRIPE_DEFAULT_ORG_PRICE_ID,
          subscriptionType: 'arrears',
          tableColumn: 'stripeId',
        },
      },
      routes: {},
    }),
  );

  router.use(
    [env.API_INTERNAL_ROUTE],
    registerEncryptionRoutes({ db, logger }),
  );

  logger.info('Loading admins...');
  router.use(
    [env.API_INTERNAL_ROUTE],
    resourceManager({
      authn: authenticateUser,
      models: <any>adminConfig,
      db,
      tablename: 'super_admins',
    }),
  );

  logger.info('Loading storage...');
  try {
    initializeStorage(db);
    router.use(
      [env.API_INTERNAL_ROUTE],
      configureStorageType,
      media.storageRoutes(),
    );
  } catch (error) {
    logger.log('warn', `Failed to init shared cloud storage with: ${error}`);
  }

  return router;
}
