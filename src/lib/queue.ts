import { Job, Queue as _Queue, Worker as _Worker } from 'bullmq';

import { Knex } from 'knex';
import logger from '@app/config/logger';
import { redisConfig } from '@app/constants/redis';
import { serializeErr } from './error';

const queues: _Queue[] = [];

export function queue<T>(name: string) {
  const queue = new _Queue(name, { connection: redisConfig });
  queues.push(queue);
  return {
    async push(name: string, data: T) {
      await queue.add(name, data);
    },
    async close() {
      await queue.disconnect();
      await queue.close();
    },
  };
}

const map: Record<string, _Worker> = {};

export interface Worker<T> {
  queue: string;
  fn: (data: Job<T>) => Promise<void>;
}
export type WorkerFactory<T> = (db: Knex) => Worker<T>;

export function startWorker<T>(opts: Worker<T>) {
  if (map[opts.queue]) {
    throw new Error(
      `unable to register handler for queue: ${opts.queue}, handler already exists`,
    );
  }

  const worker = new _Worker(opts.queue, opts.fn, {
    connection: redisConfig,
    settings: {
      backoffStrategy: (attempts: number) => 2 ** (attempts - 1) * 2000,
    },
  });

  worker.on('active', (job) => logger.info({ queue: opts.queue, state: 'active', id: job.id }));

  worker.on('failed', (job, error) => {
    logger.error({
      queue,
      state: 'failed',
      data: job.id,
      error: serializeErr(error),
    });
  });

  worker.on('completed', (job) => logger.info({ queue, state: 'completed', data: job.id }));

  map[opts.queue] = worker;
  return worker;
}

export async function stopWorkers() {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/naming-convention
  for (const [_, worker] of Object.entries(map)) {
    await worker.close();
  }
}

export async function closeQueues() {
  for (const q of queues) {
    await q.close();
    logger.info(`successfully closed queue... [${q.name}]`);
  }
}
