import { ModelClass, Model as _Model } from 'objection';

import { Knex } from 'knex';

export class Model extends _Model {
  createdAt: Date | string;

  updatedAt: Date | string;

  deletedAt: Date | string;

  $beforeUpdate() {
    this.updatedAt = new Date().toISOString();
  }
}

export interface CustomFields {}

export async function getNextId(
  trx: Knex.Transaction | Knex,
  model: ModelClass<any>,
): Promise<number> {
  const { rows } = await trx.raw(
    `select nextval('"${model.tableName}_uid_seq"')`,
  );
  return +rows[0].nextval;
}

type RawPaginatedResults<T> = T & { count?: string };

export interface PaginatedResult<T> {
  items: T[];
  count: number;
  nextOffset: number;
  metadata?: any;
}

/**
 * Defines the query parameters for paginated API requests.
 */
export interface PaginatedQuery {
  limit: number;
  offset: number;
  q?: string;
}

/**
 * returns a paginated list of entities based on the query, limit and offset
 * @param sql query
 * @param limit page size
 * @param offset number of items to skip, typically, it corresponds to ((pageNumber)-1 * pageSize)
 * @param ctx db context
 * @param modify hook to modify the items gotten from the db
 * @returns
 */
export async function paginated<T>(
  sql: Knex.QueryBuilder<T>,
  limit: number,
  offset: number,
  ctx: Knex,
  modify?: (T: T) => Promise<T> | T,
): Promise<PaginatedResult<T>> {
  const raw: RawPaginatedResults<T>[] = await sql
    .select(ctx.raw('count(*) OVER() AS count'))
    .limit(limit)
    .offset(offset);

  if (raw.length === 0) {
    return { count: 0, items: [], nextOffset: 0 };
  }

  const total = parseInt(raw[0].count);

  const items = raw.map(async (r) => {
    delete r.count;
    if (modify) r = await modify(r);
    return r;
  });

  return {
    count: total,
    items: await Promise.all(items),
    nextOffset: offset + raw.length,
  };
}
