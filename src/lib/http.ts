/* eslint-disable max-classes-per-file */
import { NextFunction, Request, Response } from 'express';

import { StatusCodes } from 'http-status-codes';
import { asyncLocalStorage } from '@app/middlewares/requestTraceId';
import errorConstants from '@app/constants/errors';
import { setActor } from '@ss-libs/ss-component-db';

/**
 * HTTP error codes as classes. This serves as the base class.
 */
export class ApplicationError extends Error {
  constructor(
    readonly code: StatusCodes,
    readonly message: string,
    readonly data?: any,
  ) {
    super(message);
  }
}

export class UnauthorizedRequest extends ApplicationError {
  constructor(msg = errorConstants.NO_AUTHENTICATED_USER) {
    super(StatusCodes.UNAUTHORIZED, msg);
  }
}

/**
 * decorator for controllers. it should be noted that any request handler to be decorated using this
 * must never end the http request in anyway, errors should be thrown, while responses should simply
 * be returned.
 * @param fn
 * @returns
 */
export function httpHandler(
  fn: (req: Request, res?: Response) => Promise<any>,
) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const data = await fn(req, res);
      const requestTraceId = asyncLocalStorage.getStore() as unknown as string;

      if (req.knexDB && requestTraceId) {
        const actor = req.locals && req.locals.user ? req.locals.user.uid : null;
        await setActor(
          { db: req.knexDB, tablename: 'auditLogs' },
          { actor, requestTraceId },
        );
      }

      if (req.locals.accessToken) {
        await req.models.AccessToken.query()
          .where({ uid: req.locals.accessToken.uid })
          .patch({ lastUsedAt: req.knexDB.fn.now() });
      }

      if (res.headersSent) return;

      res.json(data ?? null);
    } catch (err) {
      next(err);
    }
  };
}
