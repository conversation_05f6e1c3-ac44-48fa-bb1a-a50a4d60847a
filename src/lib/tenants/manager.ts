import { Tenant } from '@app/models/tenant';
import logger from '@app/config/logger';
import { setupDB } from '@app/config/db';
import { setupOpenfga } from '@app/config/openfga';
import { CacheEntry, TenantCache } from './cache';

export class TenantManager {
  private readonly cache = new TenantCache();

  constructor() {
    this.cache.on('unset', this.closeTenant);
  }

  /**
   * loadTenant sets up the necessary db and openfga connections required to
   * serve this particulet tenant
   * @param uid uid representing the tenant, should be the orgUid or userUid
   * @returns
   */
  public async loadTenant(tenant: Tenant): Promise<CacheEntry> {
    const cacheHit = this.cache.retrieve(tenant.tenantUid);
    if (cacheHit) {
      logger.info(
        `Cache hit for tenant ${tenant.tenantUid}, db: ${cacheHit.db.client.config.connection.database}, openFGA: (${cacheHit.fga.storeId}/${cacheHit.fga.authorizationModelId})`,
      );
      return cacheHit;
    }

    const db = setupDB(tenant.tenantUid, tenant.dbServer);
    const fga = await setupOpenfga({
      authModelId: tenant.openfgaAuthModelId,
      storeId: tenant.openfgaStoreId,
    });

    logger.info(
      `Caching connections for tenant ${tenant.tenantUid}, db: ${db.client.config.connection.database}, openFGA: (${fga.storeId}/${fga.authorizationModelId})`,
    );
    const entry: CacheEntry = { db, fga };
    this.cache.set(tenant.tenantUid, entry);
    return entry;
  }

  public async closeTenant({ db, fga }: CacheEntry) {
    logger.info(
      `Destroying stale connections for db: ${db.client.config.connection.database}, openFGA: (${fga.storeId}/${fga.authorizationModelId})`,
    );
    await db.destroy();
  }

  public async shutdown() {
    const state = this.cache.dump();

    const destroyDBs: Promise<any>[] = [];

    for (const k of Object.keys(state.cache)) {
      const entry = state.cache[k];
      logger.info(`evicting resources for tenant ${k}`);
      destroyDBs.push(entry.db.destroy());
      if (state.tiemouts[k]) clearTimeout(state.tiemouts[k]);
    }

    await Promise.all(destroyDBs);
    this.cache.empty();
  }
}

export const tenantManager = new TenantManager();
