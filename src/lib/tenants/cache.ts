import { EventEmitter } from 'stream';
import { Knex } from 'knex';
import { OpenFgaClient } from '@openfga/sdk';

export interface CacheEntry {
  db: Knex;
  fga: OpenFgaClient;
}

/**
 * TenantCache is a transient storage manager for tenant db connections,
 * it is fundamentally a key value store with a configuration ttl for cleaning up stale db connections
 */
export class TenantCache extends EventEmitter<{ unset: CacheEntry[] }> {
  /**
   * private cache representing the store
   */
  private static _cache: Record<string, CacheEntry> = {};

  private static _timeouts: Record<string, NodeJS.Timeout> = {};

  /**
   * ttl for entries, in seconds
   */
  private readonly ttl: number;

  /**
   *
   * @param ttl ttl in seconda
   */
  constructor(ttl: number = 300) {
    super();
    this.ttl = ttl;
  }

  /**
   * indexes a given entry in the cache storage. it performs a full overwrite, hence if an entry exists before,
   * it'll get replaced
   * @param uid
   */
  public set(uid: string, entry: CacheEntry) {
    const existingEntry = TenantCache._cache[uid];

    if (existingEntry) {
      this.unset(uid);
    }

    this.index(uid, entry);
  }

  /**
   * retrieve returns null if no entry matches the provided uid. If an entry is found, its ttl gets extended
   * by the preconfigured ttl
   * @param uid
   * @returns
   */
  public retrieve(uid: string) {
    const entry = TenantCache._cache[uid];
    if (!entry) return null;

    // extend ttl
    this.extendTimeout(uid);

    return entry;
  }

  /**
   * index commits the entry to memory and sets it's timeout
   * @param uid
   * @param entry
   */
  private index(uid: string, entry: CacheEntry) {
    TenantCache._cache[uid] = entry;
    this.extendTimeout(uid);
  }

  /**
   * unset removes an existing entry from the cache
   * @param uid
   * @param entry
   */
  private unset(uid: string) {
    this.clearTimeout(uid);
    const entry = TenantCache._cache[uid];
    delete TenantCache._cache[uid];
    this.emit('unset', entry);
  }

  /**
   * clearTimeout destroys every ttl schedules for a given entry
   * @param uid
   */
  private clearTimeout(uid: string) {
    clearTimeout(TenantCache._timeouts[uid]);
    delete TenantCache._timeouts[uid];
  }

  /**
   * extendTimeout increases the duration of an entry by the prevonfigured ttl
   * @param uid
   */
  private extendTimeout(uid: string) {
    this.clearTimeout(uid);
    TenantCache._timeouts[uid] = setTimeout(
      () => this.unset(uid),
      this.ttl * 1000,
    );
  }

  dump() {
    return { cache: TenantCache._cache, tiemouts: TenantCache._timeouts };
  }

  empty() {
    TenantCache._cache = {};
    TenantCache._timeouts = {};
  }
}
