export const temporalNamespaces = <const>['integrations', 'emails', 'userActions', 'migrations', 'systemActions'];

export type TemporalNamespace = (typeof temporalNamespaces)[number];

export const queueConfig = {
  // Integration queues
  'integration-queue': { namespace: 'integrations' },
  'integration-sync-queue': { namespace: 'integrations' },
  'integration-entities-queue': { namespace: 'integrations' },

  // Email queues
  'email-queue': { namespace: 'emails' },
  'email-events-queue': { namespace: 'emails' },

  // User queues
  'ingress-queue': { namespace: 'userActions' },
  'update-milestone-queue': { namespace: 'userActions' },
  'update-plan-queue': { namespace: 'userActions' },
  'update-run-queue': { namespace: 'userActions' },

  // Migration queues
  'storage-migration-queue': { namespace: 'migrations' },

  // Operational queues
  'attachment-queue': { namespace: 'systemActions' },
  'free-handles-queue': { namespace: 'systemActions' },
  'template-queue': { namespace: 'systemActions' },
  'dashboard-queue': { namespace: 'systemActions' },
  'soft-delete-project-records-queue': { namespace: 'systemActions' },
  'setup-account-queue': { namespace: 'systemActions' },
  'role-queue': { namespace: 'systemActions' },
  'create-project-demo-queue': { namespace: 'systemActions' },
} as const satisfies Record<
string,
{
  namespace: TemporalNamespace;
}
>;

// All task queues
export type TaskQueue = keyof typeof queueConfig;
