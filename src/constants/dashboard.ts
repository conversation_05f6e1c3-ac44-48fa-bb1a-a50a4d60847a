export interface Chart {
  id: number,
  type: string,
  groupBy?: object[],
  isMultiSeries?: boolean,
  relatedTo?: Record<string, any>[],
  entityType: string,
  x: number,
  y: number,
  w: number,
  h: number,
}
export const entityMapping = {
  testCase: {
    preference: 'testCase',
    model: 'TestCase',
  },
  testRun: {
    preference: 'testRun',
    model: 'TestRun',
  },
  milestone: {
    preference: 'milestone',
    model: 'TestMilestone',
  },
  testPlan: {
    preference: 'testPlan',
    model: 'TestPlan',
  },
  testExecution: {
    preference: 'testCase',
    model: 'TestExecution',
  },
  defect: {
    model: 'Defect',
  },
};

export const chartTypes = {
  NonAxisBasedCharts: ['counterChart', 'donutChart', 'polarChart', 'radialChart', 'StatusPriorityBarChart', 'radarChart'], // TODO Refactor Status/Priority Bar chart .. Default Bar Chart
  XYCoordinateCharts: ['lineChart', 'barChart', 'rangeChart', 'columnChart', 'areaChart', 'scatterChart', 'heatmapChart', 'treemapChart'],
};

export const periodsMapping = {
  last7Days: {
    value: 7,
    unit: 'day',
  },
  last14Days: {
    value: 14,
    unit: 'day',
  },
  lastMonth: {
    value: 1,
    unit: 'month',
  },
};

export const charts: Array<Chart> = [
  {
    id: 1,
    type: 'counterChart',
    entityType: 'testCase',
    groupBy: [{ field: 'uid' }],
    x: 0,
    y: 0,
    w: 3,
    h: 4,
  },
  {
    id: 2,
    type: 'counterChart',
    entityType: 'testRun',
    groupBy: [{ field: 'uid' }],
    x: 3,
    y: 0,
    w: 3,
    h: 4,
  },
  {
    id: 3,
    type: 'counterChart',
    entityType: 'testPlan',
    groupBy: [{ field: 'uid' }],
    x: 6,
    y: 0,
    w: 3,
    h: 4,
  },
  {
    id: 4,
    type: 'counterChart',
    entityType: 'milestone',
    groupBy: [{ field: 'uid' }],
    x: 9,
    y: 0,
    w: 3,
    h: 4,
  },
  {
    id: 5,
    type: 'donutChart',
    entityType: 'testCase',
    groupBy: [{ field: 'priority' }],
    x: 0,
    y: 4,
    w: 4,
    h: 10,
  },
  {
    id: 6,
    type: 'donutChart',
    entityType: 'testRun',
    groupBy: [{ field: 'status' }],
    x: 4,
    y: 4,
    w: 4,
    h: 10,
  },
  {
    id: 7,
    type: 'donutChart',
    entityType: 'testPlan',
    groupBy: [{ field: 'status' }],
    x: 8,
    y: 4,
    w: 4,
    h: 10,
  },
  {
    id: 8,
    type: 'lineChart',
    entityType: 'testCase',
    groupBy: [{ field: 'status' }],
    x: 0,
    y: 14,
    w: 12,
    h: 10,
  },
];

export const planCharts: Array<Chart> = [
  {
    id: 1,
    type: 'donutChart',
    entityType: 'testPlan',
    groupBy: [{ field: 'status' }],
    x: 0,
    y: 0,
    h: 10,
    w: 4,
  },
  {
    id: 2,
    type: 'donutChart',
    entityType: 'testPlan',
    groupBy: [{ field: 'priority' }],
    x: 4,
    y: 0,
    h: 10,
    w: 4,
  },
  {
    id: 4,
    type: 'radarChart',
    entityType: 'testPlan',
    groupBy: [{ field: 'status' }],
    isMultiSeries: true,
    x: 8,
    y: 0,
    h: 10,
    w: 4,
  },
  {
    id: 6,
    type: 'barChart',
    entityType: 'testPlan',
    relatedTo: [
      {
        entityType: 'testRun',
      },
    ],
    x: 0,
    y: 20,
    h: 10,
    w: 12,
  },
  {
    id: 7,
    type: 'lineChart',
    entityType: 'testPlan',
    groupBy: [{ field: 'status' }],
    isMultiSeries: true,
    x: 0,
    y: 10,
    h: 10,
    w: 12,
  },
];

export const runCharts: Array<Chart> = [
  {
    id: 1,
    type: 'donutChart',
    entityType: 'testRun',
    groupBy: [{ field: 'status' }],
    x: 0,
    y: 0,
    h: 10,
    w: 4,
  },
  {
    id: 2,
    type: 'donutChart',
    entityType: 'testRun',
    groupBy: [{ field: 'priority' }],
    x: 4,
    y: 0,
    h: 10,
    w: 4,
  },
  {
    id: 4,
    type: 'radarChart',
    entityType: 'testRun',
    groupBy: [{ field: 'status' }],
    isMultiSeries: true,
    x: 8,
    y: 0,
    h: 10,
    w: 4,
  },
  {
    id: 6,
    type: 'barChart',
    entityType: 'testRun',
    relatedTo: [
      {
        entityType: 'defectExecution',
      },
    ],
    x: 0,
    y: 20,
    h: 10,
    w: 12,
  },
  {
    id: 7,
    type: 'lineChart',
    entityType: 'testRun',
    groupBy: [{ field: 'status' }],
    isMultiSeries: true,
    x: 0,
    y: 10,
    h: 10,
    w: 12,
  },
];
export const testPlanDetailViewCharts: Array<Chart> = [
  {
    id: 1,
    type: 'lineChart',
    entityType: 'testRun',
    groupBy: [
      { field: 'status' },
      { field: 'priority' },
    ],
    x: 0,
    y: 0,
    h: 10,
    w: 12,
  },
  {
    id: 2,
    type: 'lineChart',
    entityType: 'testRun',
    groupBy: [
      { field: 'status' },
      { field: 'priority' },
    ],
    relatedTo: [
      { entityType: 'defectExecution' },
    ],
    x: 0,
    y: 10,
    h: 10,
    w: 12,
  },
  {
    id: 3,
    type: 'lineChart',
    entityType: 'defect',
    groupBy: [
      { field: 'labels' },
    ],
    isMultiSeries: true,
    x: 0,
    y: 20,
    h: 10,
    w: 12,
  },
];

export const testRunDetailViewCharts: Array<Chart> = [
  {
    id: 1,
    type: 'lineChart',
    entityType: 'testExecution',
    groupBy: [
      {
        field: 'status',
      },
      {
        field: 'priority',
      },
    ],
    x: 0,
    y: 0,
    h: 10,
    w: 12,
  },
  {
    id: 2,
    type: 'lineChart',
    entityType: 'defect',
    groupBy: [
      {
        field: 'labels',
      },
    ],
    isMultiSeries: true,
    x: 0,
    y: 10,
    h: 10,
    w: 12,
  },
  {
    id: 3,
    type: 'radarChart',
    entityType: 'testExecution',
    groupBy: [
      {
        field: 'status',
      },
      {
        field: 'priority',
      },
    ],
    isMultiSeries: true,
    x: 0,
    y: 20,
    h: 10,
    w: 4,
  },
  {
    id: 4,
    type: 'radarChart',
    entityType: 'defect',
    groupBy: [
      {
        field: 'labels',
      },
    ],
    isMultiSeries: true,
    x: 4,
    y: 20,
    h: 10,
    w: 4,
  },
  {
    id: 5,
    type: 'StatusPriorityBarChart',
    entityType: 'testExecution',
    groupBy: [
      {
        field: 'status',
      },
      {
        field: 'priority',
      },
    ],
    x: 8,
    y: 20,
    h: 11,
    w: 4,
  },
];

export const milestoneDetailViewCharts: Array<Chart> = [
  {
    id: 1,
    type: 'lineChart',
    entityType: 'testRun',
    groupBy: [
      { field: 'status' },
      { field: 'priority' },
    ],
    x: 0,
    y: 0,
    h: 10,
    w: 12,
  },
  {
    id: 2,
    type: 'lineChart',
    entityType: 'testPlan',
    groupBy: [
      { field: 'status' },
      { field: 'priority' },
    ],
    isMultiSeries: true,
    x: 0,
    y: 10,
    h: 10,
    w: 12,
  },
  {
    id: 3,
    type: 'lineChart',
    entityType: 'defect',
    groupBy: [
      { field: 'labels' },
    ],
    isMultiSeries: true,
    x: 0,
    y: 20,
    h: 10,
    w: 12,
  },
];
