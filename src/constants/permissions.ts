export default [
  {
    id: 1,
    type: 1,
    name: 'Project',
    description: 'Manage all projects, including creation, editing, and deletion.',
    actions: [
      {
        id: 101, name: 'Create / Edit', description: 'Create projects or edit project settings for efficient project management', value: 'write_project',
      },
      {
        id: 102, name: 'Delete', description: 'Delete projects to maintain a clean and organized project list.', value: 'delete_project',
      },
    ],
  },
  {
    id: 2,
    type: 1,
    name: 'Entites',
    description: 'Manage all test planning entities, including creation, editing, and deletion.',
    actions: [
      {
        id: 201, name: 'View', description: 'View and manage all planning entities, including Test Cases, Test Runs, Test Milestones, and Test Plans for efficient test management.', value: 'read_entity',
      },
      {
        id: 202, name: 'Create / Edit', description: 'Create and edit all planning entities, including Test Cases, Test Runs, Test Milestones, and Test Plans to ensure comprehensive test management.', value: 'write_entity', required: [201],
      },
      {
        id: 203, name: 'Delete', description: 'Delete any planning entities, including Test Cases, Test Runs, Test Milestones, and Test Plans, ensuring effective management and cleanup of test data.', value: 'delete_entity', required: [201],
      },
    ],
  },
  {
    id: 3,
    type: 1,
    name: 'Activities',
    description: 'Manage all test activities, including test executions and test results.',
    actions: [
      {
        id: 301, name: 'View', description: 'View all test activities, including test executions and test results, to monitor testing progress.', value: 'read_activity',
      },
      {
        id: 302, name: 'Create / Edit', description: 'Create and edit test activities, including test executions and test results, for detailed test tracking and management.', value: 'write_activity', required: [301],
      },
      {
        id: 303, name: 'Delete', description: 'Delete test activities, including test executions and test results, to maintain accurate and relevant test data.', value: 'delete_activity', required: [301],
      },
    ],
  },
  {
    id: 4,
    type: 1,
    name: 'Tags',
    description: 'Manage tags, including creating and deleting tags for test cases and other entities.',
    actions: [
      {
        id: 401, name: 'Add / Edit', description: 'Add or edit tags for organizing and categorizing test cases and other entities', value: 'write_tag',
      },
      {
        id: 402, name: 'Delete', description: 'Delete tags to manage and maintain the organization of test cases and other entities.', value: 'delete_tag',
      },
    ],
  },
  {
    id: 5,
    type: 1,
    name: 'Shared steps',
    description: 'Manage shared steps to be reused across multiple test cases.',
    actions: [
      {
        id: 501, name: 'Create / Edit', description: 'Create shared steps to be used across multiple test cases.', value: 'write_step',
      },
      {
        id: 502, name: 'Delete', description: 'Delete shared steps.', value: 'delete_step',
      },
    ],
  },
  {
    id: 6,
    type: 1,
    name: 'Templates',
    description: 'Manage templates for test cases.',
    actions: [
      {
        id: 601, name: 'Create / Edit', description: 'Create and edit templates for test cases.', value: 'write_template',
      },
      {
        id: 602, name: 'Delete', description: 'Delete templates for test cases.', value: 'delete_template',
      },
    ],
  },
  {
    id: 7,
    type: 1,
    name: 'Custom fields',
    description: 'Manage custom fields',
    actions: [
      {
        id: 701, name: 'Add / Edit', description: 'Add and edit custom field.', value: 'write_custom_field',
      },
      {
        id: 702, name: 'Delete', description: 'Delete custom field.', value: 'delete_custom_field',
      },
    ],
  },
  {
    id: 8,
    type: 1,
    name: 'Dashboard',
    description: 'Customize the dashboard and manage its charts.',
    actions: [
      {
        id: 801, name: 'View', description: 'View the dashboard and its charts.', value: 'read_dashboard',
      },
      {
        id: 802, name: 'Add / Edit', description: 'Create and edit charts for the dashboard.', value: 'write_dashboard',
      },
      {
        id: 803, name: 'Delete', description: 'Delete charts from the dashboard.', value: 'delete_dashboard', required: [801],
      },
    ],
  },
  {
    id: 9,
    type: 1,
    name: 'Integration',
    description: 'Manage and integrate apps like Jira, Slack, GitHub, and more.',
    actions: [
      {
        id: 901, name: 'View', description: 'View integrated apps and their settings.', value: 'read_integration',
      },
      {
        id: 902, name: 'Add / Edit', description: 'Integrate and configure apps like Jira, Slack, GitHub, etc.', value: 'write_integration', required: [901],
      },
      {
        id: 903, name: 'Delete', description: 'Remove integrated apps.', value: 'delete_integration', required: [901],
      },
    ],
  },
  {
    id: 10,
    type: 1,
    name: 'Defects',
    description: 'Manage and track defects within the system.',
    actions: [
      {
        id: 1001, name: 'View', description: 'View all defects and their details to track and monitor defect statuses.', value: 'read_defect', required: [901],
      },
      {
        id: 1002, name: 'Link / Edit', description: 'Create and edit defects', value: 'write_defect', required: [1001],
      },
      {
        id: 1003, name: 'Unlink', description: 'Delete defects', value: 'delete_defect', required: [1001],
      },
    ],
  },
  {
    id: 11,
    type: 1,
    name: 'Roles',
    description: 'Manage roles within the organization',
    actions: [
      {
        id: 1101, name: 'View', description: 'View roles within the organization.', value: 'read_role',
      },
      {
        id: 1102, name: 'Create / Edit', description: 'Create and edit roles within the organization.', value: 'write_role', required: [1101],
      },
      {
        id: 1103, name: 'Delete', description: 'Delete roles within the organization.', value: 'delete_role', required: [1101],
      },
    ],
  },
  {
    id: 12,
    type: 1,
    name: 'Members',
    description: 'Manage members of the organization.',
    actions: [
      {
        id: 1201, name: 'View', description: 'View members of the organization.', value: 'read_member',
      },
      {
        id: 1202, name: 'Create / Edit', description: 'Invite new members and accept invitations to join the organization.', value: 'write_member', required: [1201, 1101],
      },
      {
        id: 1203, name: 'Delete', description: 'Remove members from the organization.', value: 'delete_member', required: [1201],
      },
    ],
  },
  {
    id: 13,
    type: 1,
    name: 'Settings',
    description: 'Manage organization settings and API keys.',
    actions: [
      {
        id: 1301, name: 'Update Organization', description: 'Update organization details.', value: 'write_setting',
      },
      {
        id: 1303, name: 'Generate Key', description: 'Generate new API keys for the organization.', value: 'write_key',
      },
      {
        id: 1304, name: 'Delete Key', description: 'Delete existing API keys.', value: 'delete_key',
      },
    ],
  },
  {
    id: 13,
    type: 3,
    name: 'Billing role',
    description: 'Manage billing information',
    actions: [
      {
        id: 1301, name: 'Full access', description: 'Full access to subscription details, payment history and to modify billing settings and payment methods', value: 'full_billing',
      },
      {
        id: 1302, name: 'Limited access', description: 'No access to modify billing settings and payment methods', value: 'limited_billing',
      },
      {
        id: 1303, name: 'No access', description: 'No access', value: 'no_billing',
      },
    ],
  },
];
