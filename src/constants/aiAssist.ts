import { ChatPromptTemplate } from '@langchain/core/prompts';
import { z } from 'zod';

export interface InputField {
  fieldName: string,
  fieldValue: string
}

const stepSchema :any = z.object({
  description: z.string(),
  expectedResult: z.string(),
  children: z.array(z.lazy(() => stepSchema)).optional(),
});

export type Step = z.infer<typeof stepSchema>;

const fieldSchema = z.object({
  fieldName: z.string().describe('The field name provided'),
  fieldValue: z.string().describe('The improved value, generated by AI, based on the field name or it\'s initial value if exists.'),
  changeSummary: z.string().describe('A concise explanation of the changes made and why they improve the clarity or quality'),
});

export interface UserFile {
  name: string,
  content: string
}

export const extractTextFromFiles = (files: Array<UserFile>) => {
  let content = '';
  files.forEach((file) => {
    content += `\n---- File: ${file.name} ----\n`;
    content += `${file.content}\n`;
  });
  return content;
};

// This function needs more updates in the templates to be used.
export const isAssistComplete = (output: string): boolean => !output.includes('Insufficient information');

export const composeInitialCaseDetails = (caseTitle : string, casePriority: string, steps: Array<z.infer<typeof stepSchema>>, inputFields: InputField[], isDescribingCase: boolean = false) => { // isDescribingCase is a flag used to omit unnecessary lines when only the structure of the test case needs to be output, without additional details or improvements.
  let content = '';
  if (caseTitle) { content += `The case title provided by the user is as follows: **${caseTitle}**\n`; }
  if (casePriority) { content += `The priority level provided by the user is as follows: **${casePriority}**\n`; }
  if (steps && steps.length) {
    if (isDescribingCase) { content += 'Taking into account the steps provided by the user, ensure that the fields are aligned with the structure, format, and level of detail in the test case.'; }
    for (const [index, step] of steps.entries()) {
      content += `\n---- Step: Parent Step ${index + 1} ----\n`;
      content += `Description: **${step.description}** ----\n`;
      content += `Expected Result: **${step.expectedResult}**\n`;

      content += `Number of childs for Parent Step ${index + 1} are ${step.children?.length ?? 0}`;
      // If the step has children, include them
      if (step.children && step.children.length) {
        for (const [index, child] of steps.entries()) {
          content += `  ---- Child Step: ${index + 1} For Parent Step ${index + 1}----\n`;
          content += `  Description: **${child.expectedResult}**\n`;
          content += `  Expected Result: **${child.expectedResult}**\n`;
        }
      }
    }
  }
  if (inputFields && inputFields.length) {
    if (isDescribingCase) { content += 'Based on the input fields provided by the user, ensure that each field contains the correct **name** and **value**, aligned with the structure and detail required for the test case.'; }
    inputFields.forEach((inputField) => {
      content += `\n---- Input Field Name: **"${inputField.fieldName}"** ----\n`;
      content += `Input Field Value: **${inputField.fieldValue}**\n`;
    });
  }
  return content;
};

export const dynamicSchema = (inputFields: string[], stepsRequired: boolean, steps: any[]) => z.object({
  ...(inputFields?.length ? {
    inputFields: z.array(fieldSchema).superRefine((fields, ctx) => {
      fields.forEach((field, index) => {
        if (inputFields[index] !== field.fieldName) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: `Input fields missing: The field '${inputFields[index]}' is expected but not provided. Please ensure field '${inputFields[index]}' is included.`,
            path: ['inputFields'],
          });
        }
      });
    }),
  } : {}),
  ...(!stepsRequired ? {} : {
    steps: steps && steps.length ? z
      .array(stepSchema)
      .length(steps.length)
      .superRefine((stepsData, ctx) => {
        stepsData.forEach((step, index) => {
          if (steps[index]?.children && step.children) {
            const expectedChildrens = steps[index].children.length;
            const receivedChildrens = step.children.length;
            if (expectedChildrens !== receivedChildrens) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: `Step ${index + 1}:  ${expectedChildrens}, but got ${receivedChildrens}`,
                path: ['steps', index, 'children'],
              });
            }
          }
        });
      }) : z.array(stepSchema),
  }),
});

export const dynamicScoreSchema = (testCasesLength: number, scoreSchema: any) => z.array(scoreSchema).length(testCasesLength);
/**
 * Postpone it now
 *
 * Few-shots
 * setting few-shot examples for each type: [caseImprovement, caseCreation, fieldImprovement].
 */
// export const examples = [
//   {
//     "input": "Requirement: Create a functional test case to validate the Email Verification functionality, focusing on the following features: Ensuring that users receive a verification email upon registration and are able to verify their email address through the provided link\n Required Fields: ['pre-condition', 'operating-system', 'status]",
//     "output": "The test case required is to validate the Email Verification functionality. The required fields for this test case are:\n- Pre-condition: The condition that must be met before running the test.\n- Operating system: The platforms on which the test should be executed (e.g., Windows, macOS, Linux).\n- Status: The final result of the test, indicating pass or fail.\n\nSteps to validate the functionality:\n1. Register a new user account on the application.\n   - **Expected Result**: A new user account is successfully created.\n2. Verify that the user receives a verification email in their registered email inbox.\n   - **Expected Result**: The verification email is received by the user.\n3. Check the contents of the email to ensure it includes the correct verification link.\n   - **Expected Result**: The email contains a valid verification link.\n4. Click the verification link provided in the email.\n   - **Expected Result**: The user is redirected to the application’s email verification page.\n5. Confirm that the user is successfully redirected to the application and their email status is marked as 'Verified'.\n   - **Expected Result**: The email status is updated to 'Verified' and the user is successfully redirected.\n6. Ensure that no further email verification prompts appear once the email has been verified.\n   - **Expected Result**: No further email verification prompts are shown after successful verification."
//   },
//   {
//     "input": "Requirement: Generate a functional test case for Remember Me functionality, focused on the following features: Persistence of user session across sessions. User should remain logged in after closing and reopening the browser (based on 'Remember Me' option)\n Required Fields: ['pre-condition', 'browser-type', 'session-duration', 'status']",
//     "output": "The test case required is to validate the Remember Me functionality. The required fields for this test case are:\n- Pre-condition: The condition that must be met before running the test.\n- Browser-type: The type of browser on which the test should be executed (e.g., Chrome, Firefox, Safari).\n- Session-duration: The expected duration the user session should persist across browser sessions.\n- Status: The final result of the test, indicating pass or fail.\n\nSteps to validate the functionality:\n1. Log in to the application and select the 'Remember Me' option.\n   - **Expected Result**: The user is successfully logged in, and the session is initialized with the 'Remember Me' option active.\n2. Close the browser window.\n   - **Expected Result**: The browser closes without logging the user out.\n3. Reopen the same browser and navigate back to the application.\n   - **Expected Result**: The user remains logged in and is not required to log in again.\n4. Verify that the session persists for the specified session duration (e.g., days or weeks as per the application's settings).\n   - **Expected Result**: The session persists for the defined session duration without requiring a re-login.\n5. Log out manually to ensure the session ends when the user chooses to log out.\n   - **Expected Result**: The user is logged out, and subsequent visits to the application require login."
//   }
// ]

export const caseCreationTemplate = ChatPromptTemplate.fromMessages([
  ['system', `Instructions: 
  Purpose and Context:
    The assistant should act as a tool to provide concise and relevant responses.
    Its primary function is to suggest improvements to the clarity and quality of a given field value.
    The assistant should return only the updated field value and its brief explanation of what changes were made based on the provided instructions and content.
  Relevance Enforcement:
    Attempt to generate a valid response based on the user's message. If the user's message is unclear or doesn't seem to make sense, **try to interpret and respond appropriately**. If, after attempting, you're still unable to generate a relevant response, then return: "Insufficient information provided to generate the requested output"
    The assistant must ignore irrelevant messages or content unrelated to the field being updated.
    Any response that does not directly contribute to updating the field value should be excluded.
    **You are a test case creation assistant**. Your task is to guide and assist in generating just one test case based on user-provided information.\nThe following are the **field names** provided by the user: **[{inputFields}]**. Ensure that these field names are included **exactly as provided** \n{format_instructions}`],
  ['user', 'User prompt: {userPrompt}\n Required Fields: **[{inputFields}]** \n{files}'],
]);

export const caseImprovementTemplate = ChatPromptTemplate.fromMessages([
  ['system', `Instructions: 
  Purpose and Context:
    The assistant should act as a tool to provide concise and relevant responses.
    Its primary function is to suggest improvements to the clarity and quality of a given field value.
    The assistant should return only the updated field value and its brief explanation of what changes were made based on the provided instructions and content.
  Relevance Enforcement:
    Attempt to generate a valid response based on the user's message. If the user's message is unclear or doesn't seem to make sense, **try to interpret and respond appropriately**. If, after attempting, you're still unable to generate a relevant response, then return: "Insufficient information provided to generate the requested output"
    The assistant must ignore irrelevant messages or content unrelated to the field being updated.
    Any response that does not directly contribute to updating the field value should be excluded.
    **You are a test case improvement assistant**. Your task is to guide and assist in enhancing a test case based on the initial values provided by the user. Please help correct spelling mistakes and fix any incorrect information.\nThe number of child steps in children key must match the number of children in the parent step. In case of parent step doen't include childs .. children key must be empty array\n{format_instructions}`],
  ['user', `The following are the inputs provided by the user, including the steps and input fields that should be carefully considered when generating or updating the test case. Please ensure that the structure, format, and level of detail for each input align with the expected requirements for the test case.
    {initialValues}
  `],
]);

export const fieldImprovementTemplate = ChatPromptTemplate.fromMessages([
  ['system', `Instructions: 
  Purpose and Context:
    The assistant should act as a tool to provide concise and relevant responses.
    Its primary function is to suggest improvements to the clarity and quality of a given field value.
    The assistant should return only the updated field value and its brief explanation of what changes were made based on the provided instructions and content.
  Relevance Enforcement:
    The assistant must ignore irrelevant messages or content unrelated to the field being updated.
    Any response that does not directly contribute to updating the field value should be excluded.
    **You are a field improvement assistant**. Your task is to enhance the provided field value based on its field name. Use the context of the field name to improve the value while maintaining its original intent and meaning.\n {format_instructions}`],
  ['user', 'Requirement: {userPrompt}\n, {initialValues}.\nProvide improved suggestion to enhance the clarity and quality of this field content.'],

]);

export const PROMPT_TYPES = ['caseCreation', 'fieldImprovement', 'caseImprovement'];
