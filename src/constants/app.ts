import env from '@app/config/env';

interface Config {
  PINATA_LATEST_VERSION: string;
  OAUTH_JIRA_CLIENT_ID: string;
  OAUTH_JIRA_CLIENT_SECRET: string;
  OAUTH_JIRA_PATH: string;
  OAUTH_GITHUB_CLIENT_ID: string;
  OAUTH_GITHUB_CLIENT_SECRET: string;
  OAUTH_GITHUB_PATH: string;
  OAUTH_GITHUB_URL: string;
}

const config: Config = {
  PINATA_LATEST_VERSION: 'v0.11.0',
  OAUTH_JIRA_CLIENT_ID: `${env.OAUTH_JIRA_CLIENT_ID}`,
  OAUTH_JIRA_CLIENT_SECRET: `${env.OAUTH_JIRA_CLIENT_SECRET}`,
  OAUTH_JIRA_PATH: '/oauth/token',

  OAUTH_GITHUB_URL: 'https://github.com',
  OAUTH_GITHUB_CLIENT_ID: `${env.OAUTH_GITHUB_CLIENT_ID}`,
  OAUTH_GITHUB_CLIENT_SECRET: `${env.OAUTH_GITHUB_CLIENT_SECRET}`,
  OAUTH_GITHUB_PATH: '/login/oauth/accessToken',
};

export default config;
