import { EntityType } from '@app/models/tag';

export const DEFAULT_TAGS: { name: string; entityTypes: EntityType[] }[] = [
  {
    name: 'automated',
    entityTypes: ['cases', 'executions', 'runs', 'plans', 'milestones'],
  },
  {
    name: 'unit',
    entityTypes: ['cases', 'executions', 'runs', 'plans', 'milestones'],
  },
  {
    name: 'functional',
    entityTypes: ['cases', 'executions', 'runs', 'plans', 'milestones'],
  },
  {
    name: 'exploratory',
    entityTypes: ['cases', 'executions', 'runs', 'plans', 'milestones'],
  },
];
