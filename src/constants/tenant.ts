export const tenantConfig : Record<string, any> = {
  GET: [
    {
      paths: [
        '/:handle/dashboards/:uid?',
        '/:handle/projects/:key/dashboards/:uid?',
        '/:handle/dashboards/:uid?/chart/:chartId',
        '/:handle/projects/:key/dashboards/:uid?/chart/:chartId',
      ],
      configs: {
        dashboardMaxDateRange: 90,
      },
    },
  ],
  PATCH: [
    {
      paths: [
        '/:handle/dashboards/:uid/charts',
      ],
      configs: {
        dashboardMaxDateRange: 90,
      },
    },
  ],
};

// reterieve all default tenant configs e.g {dashboardMaxDateRange: 90, etc..}
export const defaultTenantConfigs = Object.keys(tenantConfig).reduce((acc, curr) => {
  tenantConfig[curr].forEach((endpoint: any) => {
    Object.keys(endpoint.configs).forEach((key) => {
      if (!acc[key]) {
        acc[key] = endpoint.configs[key];
      }
    });
  });
  return acc;
}, {});
