export default {
  AVAILABLE_TYPES: [
    'apps',
    'repos',
    'test_projects',
    'test_milestones',
    'test_suites',
    'test_cases',
    'testFolders',
    'test_plans',
    'test_runs',
    'test_executions',
  ],
  INTERNAL_RELATIONSHIP_MAPPING: {
    test_projects: [
      {
        primary_table: 'test_projects',
        primary_key: 'uid',
        secondary_table: 'test_milestones',
        secondary_key: 'project_uid',
        direction: 'in',
      },
      {
        primary_table: 'test_projects',
        primary_key: 'uid',
        secondary_table: 'test_plans',
        secondary_key: 'project_uid',
        direction: 'in',
      },
      {
        primary_table: 'test_projects',
        primary_key: 'uid',
        secondary_table: 'test_runs',
        secondary_key: 'project_uid',
        direction: 'in',
      },
      {
        primary_table: 'test_projects',
        primary_key: 'uid',
        secondary_table: 'testFolders',
        secondary_key: 'project_uid',
        direction: 'in',
      },
      {
        primary_table: 'test_projects',
        primary_key: 'uid',
        secondary_table: 'test_cases',
        secondary_key: 'project_uid',
        direction: 'in',
      },
    ],
    test_milestones: [
      {
        primary_table: 'test_milestones',
        primary_key: 'uid',
        secondary_table: 'test_runs',
        secondary_key: 'milestone_uid',
        direction: 'in',
      },
      {
        primary_table: 'test_milestones',
        primary_key: 'project_uid',
        secondary_table: 'test_projects',
        secondary_key: 'uid',
        direction: 'out',
      },
    ],
    testFolders: [
      {
        primary_table: 'testFolders',
        primary_key: 'uid',
        secondary_table: 'test_cases',
        secondary_key: 'parent_uid',
        direction: 'in',
      },
      {
        primary_table: 'testFolders',
        primary_key: 'project_uid',
        secondary_table: 'test_projects',
        secondary_key: 'uid',
        direction: 'out',
      },
    ],
    test_cases: [
      // Bridge milestones and plans
      {
        primary_table: 'test_cases',
        primary_key: 'case_ref',
        secondary_table: 'test_executions',
        secondary_key: 'test_case_uid',
        direction: 'in',
      },
      {
        primary_table: 'test_cases',
        primary_key: 'parent_uid',
        secondary_table: 'testFolders',
        secondary_key: 'uid',
        direction: 'out',
      },
    ],
    test_runs: [
      // Bridge milestones and plans
      {
        primary_table: 'test_runs',
        primary_key: 'uid',
        secondary_table: 'test_executions',
        secondary_key: 'run_uid',
        direction: 'in',
      },
      {
        primary_table: 'test_runs',
        primary_key: 'project_uid',
        secondary_table: 'test_projects',
        secondary_key: 'uid',
        direction: 'out',
      },
    ],
    test_executions: [
      {
        primary_table: 'test_executions',
        primary_key: 'test_case_uid',
        secondary_table: 'test_cases',
        secondary_key: 'case_ref',
        direction: 'out',
      },
      {
        primary_table: 'test_executions',
        primary_key: 'testRunUid',
        secondary_table: 'test_runs',
        secondary_key: 'uid',
        direction: 'out',
      },
    ],
  },
  EXTERNAL_RELATIONSHIP_MAPPING: {
    testrail: {
      test_projects: {
        in: {
          'test_projects:externalId': [
            'test_milestones:customFields.project_id',
            'test_plans:customFields.project_id',
            'test_runs:customFields.project_id',
            'test_suites:customFields.project_id',
          ],
        },
      },
      test_milestones: {
        in: {
          'test_milestones:externalId': [
            'test_cases:customFields.milestone_id',
            'test_plans:customFields.milestone_id',
            'test_runs:customFields.milestone_id',
          ],
        },
        out: {
          'test_milestones:customFields.project_id': [
            'test_projects:externalId',
          ],
        },
      },
      test_suites: {
        in: {
          'test_suites:externalId': [
            'test_cases:customFields.suite_id',
            'test_runs:customFields.suite_id',
            'testFolders:customFields.suite_id',
          ],
        },
        out: {
          'test_suites:customFields.project_id': ['test_projects:externalId'],
        },
      },
      test_cases: {
        out: {
          'test_cases:customFields.milestone_id': [
            'test_milestones:externalId',
          ],
          'test_cases:customFields.suite_id': ['test_suites:externalId'],
        },
        in: {
          'test_cases:externalId': ['test_executions:customFields.case_id'],
        },
      },
      testFolders: {
        out: {
          'testFolders:customFields.suite_id': ['test_suites:externalId'],
        },
      },
      test_plans: {
        out: {
          'test_plans:customFields.milestone_id': [
            'test_milestones:externalId',
          ],
          'test_plans:customFields.project_id': ['test_projects:externalId'],
        },
        in: {
          'test_plans:externalId': ['test_runs:customFields.plan_id'],
        },
      },
      test_runs: {
        out: {
          'test_runs:customFields.milestone_id': ['test_milestones:externalId'],
          'test_runs:customFields.plan_id': ['test_plans:externalId'],
          'test_runs:customFields.project_id': ['test_projects:externalId'],
          'test_runs:customFields.suite_id': ['test_suites:externalId'],
        },
        in: {
          'test_runs:externalId': ['test_executions:customFields.run_id'],
        },
      },
      test_executions: {
        out: {
          // CTODO - naming? and spacing
          'test_executions:customFields.case_id': ['test_cases:externalId'],
          'test_executions:customFields.run_id': ['test_runs:externalId'],
        },
      },
    },
    zephyr_cloud: {},
    zephy_scale: {},
    xray: {},
    practitest: {},
    qtest: {},
    qase: {},
  },
  EXTERNAL_TYPE_MAPPING: {
    project: 'test_projects',
    projects: 'test_projects',
    milestone: 'test_milestones',
    milestones: 'test_milestones',
    suite: 'test_suites',
    suites: 'test_suites',
    set: 'test_suites',
    sets: 'test_suites',
    case: 'test_cases',
    cases: 'test_cases',
    instance: 'test_cases',
    instances: 'test_cases',
    section: 'testFolders',
    sections: 'testFolders',
    folder: 'testFolders',
    folders: 'testFolders',
    plan: 'test_plans',
    plans: 'test_plans',
    run: 'test_runs',
    runs: 'test_runs',
    cycle: 'test_runs',
    cycles: 'test_runs',
    execution: 'test_executions',
    executions: 'test_executions',
    test: 'test_executions',
    tests: 'test_executions',
    log: 'test_executions',
    logs: 'test_executions',
    result: 'test_executions',
    results: 'test_executions',
  },
};
