import { Priority, Status } from '@app/models/preferences';

const DEFAULT_PREFERENCE = {
  statusColors: [
    // test cases
    {
      id: 1,
      color: '#42A5F5',
      entityType: 'testCase',
      name: 'Active',
      isDefault: true,
      isCompleted: false,
      isSuccess: false,
      isFailure: false,
      aliases: [],
    },
    {
      id: 2,
      color: '#66BB6A',
      entityType: 'testCase',
      name: 'Passed',
      isDefault: false,
      isCompleted: true,
      isSuccess: true,
      isFailure: false,
      aliases: [],
    },
    {
      id: 3,
      color: '#FFA726',
      entityType: 'testCase',
      name: 'Incomplete',
      isDefault: false,
      isCompleted: false,
      isSuccess: false,
      isFailure: false,
      aliases: [],
    },
    {
      id: 4,
      color: '#EF5350',
      entityType: 'testCase',
      name: 'Failed',
      isDefault: false,
      isCompleted: true,
      isSuccess: false,
      isFailure: true,
      aliases: [],
    },
    // test plans
    {
      id: 5,
      color: '#42A5F5',
      entityType: 'testPlan',
      name: 'Active',
      isDefault: true,
      isCompleted: false,
      isSuccess: false,
      isFailure: false,
      aliases: [],
    },
    {
      id: 6,
      color: '#66BB6A',
      entityType: 'testPlan',
      name: 'Passed',
      isDefault: false,
      isCompleted: true,
      isSuccess: true,
      isFailure: false,
      aliases: [],
    },
    {
      id: 7,
      color: '#FFA726',
      entityType: 'testPlan',
      name: 'Incomplete',
      isDefault: false,
      isCompleted: false,
      isSuccess: false,
      isFailure: false,
      aliases: [],
    },
    {
      id: 8,
      color: '#EF5350',
      entityType: 'testPlan',
      name: 'Failed',
      isDefault: false,
      isCompleted: true,
      isSuccess: false,
      isFailure: true,
      aliases: [],
    },
    // test runs
    {
      name: 'Failed',
      entityType: 'testRun',
      color: '#50EFD9',
      id: 9,
      isDefault: false,
      isCompleted: true,
      isSuccess: false,
      isFailure: true,
      aliases: [],
    },
    {
      id: 10,
      color: '#66BB6A',
      entityType: 'testRun',
      name: 'Active',
      isDefault: true,
      isCompleted: false,
      isSuccess: false,
      isFailure: false,
      aliases: [],
    },
    {
      id: 12,
      color: '#7E57C2',
      entityType: 'testRun',
      name: 'In progress',
      isDefault: false,
      isCompleted: false,
      isSuccess: false,
      isFailure: false,
      aliases: [],
    },
    {
      id: 13,
      color: '#667085',
      entityType: 'testRun',
      name: 'Passed',
      isDefault: false,
      isCompleted: true,
      isSuccess: false,
      isFailure: false,
      aliases: [],
    },
    // defects
    {
      id: 14,
      color: '#42A5F5',
      entityType: 'defect',
      name: 'New',
      isDefault: true,
      isCompleted: false,
      isSuccess: false,
      isFailure: false,
      aliases: [],
    },
    {
      id: 15,
      color: '#EF5350',
      entityType: 'defect',
      name: 'Incomplete',
      isDefault: false,
      isCompleted: false,
      isSuccess: false,
      isFailure: false,
      aliases: [],
    },
    {
      id: 16,
      color: '#66BB6A',
      entityType: 'defect',
      name: 'Active',
      isDefault: false,
      isCompleted: false,
      isSuccess: false,
      isFailure: false,
      aliases: [],
    },
    {
      id: 17,
      color: '#FFA726',
      entityType: 'defect',
      name: 'Blocked',
      isDefault: false,
      isCompleted: false,
      isSuccess: false,
      isFailure: false,
      aliases: [],
    },
    {
      id: 18,
      color: '#7E57C2',
      entityType: 'defect',
      name: 'Testing',
      isDefault: false,
      isCompleted: false,
      isSuccess: false,
      isFailure: false,
      aliases: [],
    },
    {
      id: 19,
      color: '#667085',
      entityType: 'defect',
      name: 'Done',
      isDefault: false,
      isCompleted: true,
      isSuccess: false,
      isFailure: false,
      aliases: [],
    },
    // milestones
    {
      id: 20,
      color: '#66BB6A',
      entityType: 'milestone',
      name: 'Active',
      isDefault: true,
      isCompleted: false,
      isSuccess: false,
      isFailure: false,
      aliases: [],
    },
    {
      id: 21,
      color: '#42A5F5',
      entityType: 'milestone',
      name: 'Upcoming',
      isDefault: false,
      isCompleted: false,
      isSuccess: false,
      isFailure: false,
      aliases: [],
    },
    {
      id: 22,
      color: '#FFA726',
      entityType: 'milestone',
      name: 'Blocked',
      isDefault: false,
      isCompleted: false,
      isSuccess: false,
      isFailure: false,
      aliases: [],
    },
    {
      id: 23,
      color: '#667085',
      entityType: 'milestone',
      name: 'Closed',
      isDefault: false,
      isCompleted: true,
      isSuccess: false,
      isFailure: false,
      aliases: [],
    },
  ] as Status[],
  priorityColors: [
    {
      id: 1,
      color: '#66BB6A',
      entityType: 'testCase',
      name: 'Low',
      isDefault: true,
    },
    {
      id: 2,
      color: '#FFA726',
      entityType: 'testCase',
      name: 'Medium',
      isDefault: false,
    },
    {
      id: 3,
      color: '#EF5350',
      entityType: 'testCase',
      name: 'High',
      isDefault: false,
    },
    {
      id: 4,
      color: '#66BB6A',
      entityType: 'testPlan',
      name: 'Low',
      isDefault: true,
    },
    {
      id: 5,
      color: '#FFA726',
      entityType: 'testPlan',
      name: 'Medium',
      isDefault: false,
    },
    {
      id: 6,
      color: '#EF5350',
      entityType: 'testPlan',
      name: 'High',
      isDefault: false,
    },
    {
      id: 7,
      color: '#66BB6A',
      entityType: 'testRun',
      name: 'Low',
      isDefault: false,
    },
    {
      id: 8,
      color: '#FFA726',
      entityType: 'testRun',
      name: 'Medium',
      isDefault: false,
    },
    {
      id: 9,
      color: '#EF5350',
      entityType: 'testRun',
      name: 'High',
      isDefault: false,
    },
    {
      id: 10,
      color: '#66BB6A',
      entityType: 'defect',
      name: 'Low',
      isDefault: true,
    },
    {
      id: 11,
      color: '#FFA726',
      entityType: 'defect',
      name: 'Medium',
      isDefault: false,
    },
    {
      id: 12,
      color: '#EF5350',
      entityType: 'defect',
      name: 'High',
      isDefault: false,
    },
  ] as Priority[],
};

export default DEFAULT_PREFERENCE;
