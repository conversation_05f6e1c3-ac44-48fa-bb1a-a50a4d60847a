// temporary urls images for integrations
export const INTEGRATION_IMAGES = {
  // 'azure-devops': 'azure.png',
  // 'circle-ci': 'circleci.png',
  // 'pivotal-tracker': 'pivotal.png',
  // 'robot-framework': 'robot.png',
  // assemblia: 'assemblia.png',
  // asana: 'asana.png',
  // bugzilla: 'bugzilla.png',
  // clickup: 'clickup.png',
  // cucumber: 'cucumber.png',
  // cypress: 'cypress.png',
  // fogbugz: 'fogbugz.png',
  github: 'github.png',
  // jenkins: 'jenkins.png',
  jira: 'jira.png',
  // junit: 'junit.png',
  // linear: 'linear.png',
  // mantis: 'mantis.png',
  // monday: 'monday.png',
  // redmine: 'redmine.png',
  // selenium: 'selenium.png',
  // slack: 'slack.png',
  // testng: 'testng.png',
  // trac: 'trac.png',
  // trello: 'trello.png',
  // youtrack: 'youtrack.png',
  testrail: 'testrail.png',
} as const;

// Descriptions dictionary
export const INTEGRATION_DESCRIPTIONS = {
  // 'azure-devops': 'Azure DevOps delivers a feature-rich, end-to-end solution...',
  // 'circle-ci': 'CircleCI is a leading cloud-based Continuous Integration and Continuous Delivery...',
  // 'pivotal-tracker': 'Pivotal Tracker is an agile project management tool designed to help teams...',
  // 'robot-framework': 'Robot Framework stands out as an open-source, keyword-driven...',
  // assemblia: 'Assemblia is a versatile platform that seamlessly connects agile...',
  // asana: 'Asana is the ultimate work management platform that empowers teams...',
  // bugzilla: 'Bugzilla stands as a stalwart in the world of bug tracking and issue management...',
  // clickup: 'ClickUp is the ultimate project management platform designed...',
  // cucumber: 'Cucumber is a powerful Behavior-Driven Development (BDD) testing framework...',
  // cypress: 'Cypress is a cutting-edge, open-source test automation framework...',
  // fogbugz: 'FogBugz is a comprehensive and agile-focused issue tracking and project management tool...',
  github: 'GitHub stands as the cornerstone of modern software development...',
  // jenkins: 'Jenkins stands as the cornerstone of modern software development...',
  jira: 'Jira is the go-to project management and issue tracking tool trusted by teams worldwide...',
  // junit: 'JUnit stands as the cornerstone of robust software testing...',
  // linear: 'Linear is a modern and intuitive issue tracking and software project management tool...',
  // mantis: 'Mantis is a flexible and user-friendly open-source bug tracking and issue management solution...',
  // monday: 'Monday is a versatile work operating system that streamlines the workflow...',
  // redmine: 'Redmine is a versatile and open-source project management and issue tracking platform...',
  // selenium: 'Selenium stands as the industry-standard open-source automation framework...',
  // slack: 'Boosts communication and collaboration by seamlessly integrating...',
  // testng: 'TestNG stands at the forefront of Java testing frameworks...',
  // trac: 'Trac provides a lightweight and open-source platform optimal for agile software development...',
  // trello: 'Trello is a user-friendly and visually appealing project management tool that enables teams...',
  // youtrack: 'YouTrack is a robust and customizable agile issue tracking and project management tool...',
  testrail: 'TestRail is a leading test management tool that helps teams manage, track, and report test results...',
} as const;

export const JIRA_SCOPES = [
  'read:jira-work',
  'write:jira-work',
  'read:board-scope:jira-software',
  'read:project:jira',
  'read:sprint:jira-software',
  // 'manage:jira-project',
  'read:jira-user',
  // 'manage:jira-webhook',
  'read:me',
  'read:account',
  'offline_access',
] as const;

export const GITHUB_SCOPES = [
  'repo',
  'read:user',
  'user:email',
] as const;

// Testrail Types map
export const TESTRAIL_TYPES = {
  1: 'text', // string in testrail
  2: 'integer',
  3: 'text',
  4: 'link', // url in testrail
  5: 'checkbox',
  6: 'dropdown',
  7: 'integer', // user in testrail
  8: 'date',
  9: 'integer', // milestone in testrail
  10: 'step', // steps in testrail
  11: 'step', // steps in testrail
  12: 'multi', // multi-select in testrail
  13: 'text', // adding BDD as a text field in testfiesta
  14: 'text', // adding BDD as a text field in testfiesta
};

// Add service-specific configurations
export const SERVICE_CONFIGS = {
  jira: {
    required: ['projectConfigurations'],
    authTypes: ['oauth2'],
    apiLimit: 100,
    cronSchedule: '*/10 * * * *', // 10 minutes
    entityTypes: ['projects', 'defects'],
  },
  github: {
    required: ['projectConfigurations'],
    authTypes: ['oauth2'],
    apiLimit: 100,
    entityTypes: ['repositories', 'defects'],
    cronSchedule: '*/10 * * * *', // 10 minutes
  },
  testrail: {
    required: ['projectConfigurations'],
    authTypes: ['basic'],
    apiLimit: 250,
    cronSchedule: '*/15 * * * *', // 15 minutes
    entityTypes: ['projects', 'sharedSteps', 'customFields', 'templates', 'configurations', 'folders', 'milestones', 'plans', 'runs'],
  },
} as const;

export const TESTRAIL_SYSTEM_FIELDS = [
  {
    is_active: true,
    type_id: 1,
    name: 'estimate',
    system_name: 'estimate',
    label: 'Estimate',
    configs: [
      {
        context: {
          is_global: true,
          project_ids: null,
        },
        options: {
          is_required: false,
          default_value: '',
        },
      },
    ],
    include_all: true,
  },
  {
    is_active: true,
    type_id: 1,
    name: 'refs',
    system_name: 'refs',
    label: 'References',
    configs: [
      {
        context: {
          is_global: true,
          project_ids: null,
        },
        options: {
          is_required: false,
          default_value: '',
        },
      },
    ],
    include_all: true,
  },
] as const;

export const AVAILBLE_INTEGRATIONS = [
  {
    category: 'Issue Tracking & Project Management',
    items: [
      {
        name: 'Jira',
        description: INTEGRATION_DESCRIPTIONS.jira,
        image: INTEGRATION_IMAGES.jira,
        authTypes: SERVICE_CONFIGS.jira.authTypes,
      },
      //     {
      //       name: 'YouTrack',
      //       description: INTEGRATION_DESCRIPTIONS.youtrack,
      //       image: INTEGRATION_IMAGES.youtrack,
      //     },
      //     {
      //       name: 'Redmine',
      //       description: INTEGRATION_DESCRIPTIONS.redmine,
      //       image: INTEGRATION_IMAGES.redmine,
      //     },
      //     {
      //       name: 'Trello',
      //       description: INTEGRATION_DESCRIPTIONS.trello,
      //       image: INTEGRATION_IMAGES.trello,
      //     },
      //     {
      //       name: 'Monday',
      //       description: INTEGRATION_DESCRIPTIONS.monday,
      //       image: INTEGRATION_IMAGES.monday,
      //     },
      //     {
      //       name: 'Pivotal Tracker',
      //       description: INTEGRATION_DESCRIPTIONS['pivotal-tracker'],
      //       image: INTEGRATION_IMAGES['pivotal-tracker'],
      //     },
      //     {
      //       name: 'Trac',
      //       description: INTEGRATION_DESCRIPTIONS.trac,
      //       image: INTEGRATION_IMAGES.trac,
      //     },
      //     {
      //       name: 'FogBugz',
      //       description: INTEGRATION_DESCRIPTIONS.fogbugz,
      //       image: INTEGRATION_IMAGES.fogbugz,
      //     },
      //     {
      //       name: 'Linear',
      //       description: INTEGRATION_DESCRIPTIONS.linear,
      //       image: INTEGRATION_IMAGES.linear,
      //     },
      //     {
      //       name: 'Bugzilla',
      //       description: INTEGRATION_DESCRIPTIONS.bugzilla,
      //       image: INTEGRATION_IMAGES.bugzilla,
      //     },
      //     {
      //       name: 'Mantis',
      //       description: INTEGRATION_DESCRIPTIONS.mantis,
      //       image: INTEGRATION_IMAGES.mantis,
      //     },
      //     {
      //       name: 'Asana',
      //       description: INTEGRATION_DESCRIPTIONS.asana,
      //       image: INTEGRATION_IMAGES.asana,
      //     },
      //     {
      //       name: 'Selenium',
      //       description: INTEGRATION_DESCRIPTIONS.selenium,
      //       image: INTEGRATION_IMAGES.selenium,
      //     },
      //   ],
      // },
    ],
  },
  // {
  //   category: 'Communication and Collaboration',
  //   items: [
  //     {
  //       name: 'Slack',
  //       description: INTEGRATION_DESCRIPTIONS.slack,
  //       image: INTEGRATION_IMAGES.slack,
  //     },
  //     {
  //       name: 'ClickUp',
  //       description: INTEGRATION_DESCRIPTIONS.clickup,
  //       image: INTEGRATION_IMAGES.clickup,
  //     },
  //     {
  //       name: 'Assemblia',
  //       description: INTEGRATION_DESCRIPTIONS.assemblia,
  //       image: INTEGRATION_IMAGES.assemblia,
  //     },
  //   ],
  // },
  // {
  //   category: 'Performance Testing',
  //   items: [
  //     {
  //       name: 'JUnit',
  //       description: INTEGRATION_DESCRIPTIONS.junit,
  //       image: INTEGRATION_IMAGES.junit,
  //     },
  //   ],
  // },
  // {
  //   category: 'Continuous Integration and Delivery (CI/CD)',
  //   items: [
  //     {
  //       name: 'Azure DevOps',
  //       description: INTEGRATION_DESCRIPTIONS['azure-devops'],
  //       image: INTEGRATION_IMAGES['azure-devops'],
  //     },
  //     {
  //       name: 'Jenkins',
  //       description: INTEGRATION_DESCRIPTIONS.jenkins,
  //       image: INTEGRATION_IMAGES.jenkins,
  //     },
  //     {
  //       name: 'Circle CI',
  //       description: INTEGRATION_DESCRIPTIONS['circle-ci'],
  //       image: INTEGRATION_IMAGES['circle-ci'],
  //     },
  //   ],
  // },
  {
    category: 'Test Management',
    items: [
      {
        name: 'TestRail',
        description: INTEGRATION_DESCRIPTIONS.testrail,
        image: INTEGRATION_IMAGES.testrail,
        authTypes: SERVICE_CONFIGS.testrail.authTypes,
      },
    ],
  },
  {
    category: 'Source Code Management',
    items: [
      {
        name: 'GitHub',
        description: INTEGRATION_DESCRIPTIONS.github,
        image: INTEGRATION_IMAGES.github,
        authTypes: SERVICE_CONFIGS.github.authTypes,
      },
    ],
  },
  // {
  //   category: 'Test Automation',
  //   items: [
  //     {
  //       name: 'Cucumber',
  //       description: INTEGRATION_DESCRIPTIONS.cucumber,
  //       image: INTEGRATION_IMAGES.cucumber,
  //     },
  //     {
  //       name: 'Robot Framework',
  //       description: INTEGRATION_DESCRIPTIONS['robot-framework'],
  //       image: INTEGRATION_IMAGES['robot-framework'],
  //     },
  //     {
  //       name: 'Cypress',
  //       description: INTEGRATION_DESCRIPTIONS.cypress,
  //       image: INTEGRATION_IMAGES.cypress,
  //     },
  //   ],
  // },
];
