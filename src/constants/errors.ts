const errorConstants = {
  ORGANIZATION_NAME_EXIST: 'The name of this organization is already taken.',
  INVALID_SERVICE: 'Integration service not found',
  INVALID_INVITE_LINK: 'This invite link is not invalid',
  INVITATION_NOT_EXIST: 'This invitation is not exist.',
  INVITATION_LINK_EXPIRED: 'This invitation link is invalid or expired',
  INVITATION_LINK_USED: 'This invitation link was used before',
  INVITATION_EMAIL_NO_MATCH:
    'This invitation was sent to a different email than the logged in user',
  TOO_MANY_ATTEMPTS: 'Too many attempts. Try again after 1 day',
  USER_EMAIL_NOT_EXIST: 'User with given email doesn\'t exist',
  RESET_LINK_INVALID: 'This password reset link is invalid',
  RESET_LINK_EXPIRED: 'This password reset link is expired',
  NO_AUTHENTICATED_USER: 'No authenticated user provided',
  SEARCH_QUERY_MUST_BE_NUMBERS: 'Search query must be a string',
  EMAIL_IN_USE: 'Email already in use',
  NAME_FIELD_REQUIRED: '"name" field is required',
  USERUIDS_MUST_BE_STRING: 'User ids must be strings',
  USERUIDS_MUST_BE_ARRAY: 'User ids must be an array',
  PRIORITIES_MUST_BE_NUMBERS: 'Priorities must be numbers',
  PRIORITIES_MUST_BE_ARRAY: 'Priorities must be an array',
  STATUS_MUST_BE_NUMBERS: 'Status must be numbers',
  STATUS_MUST_BE_ARRAY: 'Status must be an array',
  TAGS_MUST_BE_NUMBERS: 'Tags must be numbers',
  TAGS_MUST_BE_ARRAY: 'Tags must be an array',
  ASSIGN_DATE_START_MUST_BE_DATE: 'Assign date start must be a date',
  ASSIGN_DATE_END_MUST_BE_DATE: 'Assign date end must be a date',
  DUE_DATE_START_MUST_BE_DATE: 'Due date start must be a date',
  DUE_DATE_END_MUST_BE_DATE: 'Due date end must be a date',
  PROVIDE_DATA_TO_UPDATE: 'Provide data to update',
  STATUS_FIELD_REQUIRED: '"status" field is required',
  DESCRIPTION_FIELD_REQUIRED: '"description" field is required',
  PRIORITY_FIELD_REQUIRED: '"priority" field is required',
  PROBLEM_UPDATING_TEST_CASE: 'We are having problem updating test-case',
  REQUIRE_SIGN_UP: 'You should signup first',
  INVALID_TYPE: 'Invalid type',
  INVALID_USER_UID: 'Invalid user uid',
  INVALID_ORG_UID: 'Invalid organization uid',
  TOKEN_REQUIRED: 'Token is required',
  REQUIRE_CORRECT_LOGIN: 'This email is invalid',
  USER_ALREADY_INVITED: 'User has already been invited',
  CURRENT_PASSWORD_INCORRECT: 'Your current password is not correct',
  NOT_ADMIN: 'You are not an admin user',
  NO_ACCESS_TO_ORG: 'You don\'t have access to this organization',
  PROBLEM_FETCHING_USERS: 'We are having problem fetching users',
  PROBLEM_FETCHING_ROLES: 'We are having problem fetching roles',
  PROBLEM_FETCHING_REPO_BRANCHES: 'We are having problem fetching branches',
  PROBLEM_FETCHING_HANDLE: 'We are having problem fetching usernames',
  NO_ORG: 'Organization does not exist',
  CAN_NOT_UPDATE_ROLE: 'Unable to update role',
  MISSING_FIELDS: 'User ID or organization ID is missing.',
  MISSING_PERMISSION: 'You don\'t have permission to perform this action',
  UNABLE_TO_REASSIGN_ALL_OWNERS: 'At least one owner must remain in the organization',
  USER_NOT_FOUND: 'User with given ID doesn\'t exist',
  ORG_NOT_FOUND: 'Organization with given ID doesn\'t exist',
  MISSING_X_VALUE: 'Chart missing x value',
  MISSING_Y_VALUE: 'Chart missing y value',
  MISSING_W_VALUE: 'Chart missing w value',
  MISSING_H_VALUE: 'Chart missing h value',
  EDITABLE_BOOLEAN: 'Editable should be a boolean value',
  DEFAULT_BOOLEAN: 'Default should be a boolean value',
  DEFAULT_DASHBOARD_RESTRICT: 'You should have at least one default dashboard',
  DASHBOARD_NOT_FOUNT: 'Dashboard not found',
  DASHBOARD_NOT_EDITABLE: 'Dashboard is not editable.',
  DASHBOARD_NAME_RESTRICT: 'Dashboard name must be an string',
  TOKEN_NOT_FOUND: 'Token with given ID doesn\'t exist',
  SOURCE_KEY_REQUIRED: '"source" key is required',
  TYPE_KEY_REQUIRED: '"type" key is required',
  HANDLE_DUPLICATED: 'Handle is already in use, please choose another',
  HANDLE_UNAVAILABLE: 'Handle not available',
  KEY_UNAVAILABLE: 'Project key not available',
  HANDLE_NOT_FOUND: 'Handle not found, please use a registered handle',
  HANDLE_FOUND: 'Handle found, please select another',
  KEY_FOUND: 'Project key found, please select another',
  PROJECT_NOT_FOUND: 'Project not found',
  PROJECT_IDS_NOT_FOUND: 'The following project IDs were not found: ',
  STEP_UID_REQUIRED: '"step_uid" key is required',
  TYPES_KEY_REQUIRED: '"types" key is required',
  RUNS_FIELD_REQUIRED: '"runs" field is required',
  END_MUST_BE_DATE: '"endRange" must be a valid date',
  START_MUST_BE_DATE: '"startRange" must be a valid date',
  ID_MUST_BE_NUMBER: '"ID" must be a number',
  ENTRIES_KEY_REQUIRED: '"entries" key is required',
  INVALID_SOURCE_KEY: 'Invalid "source" key',
  INVALID_TYPE_KEY: 'Invalid "type" key',
  INVALID_REQUEST: 'Invalid request',
  INVALID_VIEW: 'Invalid view',
  PROJECTS_NOT_ARRAY: '"projects" query must be an array',
  NO_PERMISSION_FOR_PROJECT: 'You don\'t have permission for one of the provided projects.',
  ENTRIES_KEY_NOT_ARRAY: '"entries" key provided is not an object',
  ENTITY_TYPES_FIELD_NOT_ARRAY: '"entityTypes" key provided is not an array',
  RUNS_KEY_NOT_ARRAY: '"runs" key provided is not an array',
  OPTIONS_KEY_NOT_ARRAY: '"options" key provided is not an array',
  CONFIGURATIONS_KEY_NOT_ARRAY: '"configurations" key provided is not an array',
  TAGS_KEY_NOT_ARRAY: '"tags" key provided is not an array',
  USER_NOT_MEMBER_OF_ORG: 'User not member of organization',
  PROBLEM_PROCESSING_REQUEST: 'We\'re having a problem processing that request',
  ATTACHMENT_NOT_FOUND: '"attachment" not found',
  ORG_OWNER_CANNOT_BE_REASSIGNED: 'The owner of the organization cannot be reassigned',
  TYPE_FIELD_REQUIRED: '"type" field is required',
  SIZE_FIELD_REQUIRED: '"size" field is required',
  OWNER_ID_REQUIRED: 'Owner is required',
  OWNER_TYPE_REQUIRED: 'Owner type is required',
  FILE_NAME_FIELD_REQUIRED: '"file_name" field is required',
  INVALID_MIME_TYPE: 'invalid "type" provided',
  MEDIA_TYPE_FIELD_REQUIRED: '"media_type" field is required',
  HANDLE_LENGTH: 'Username must be between 2 and 30 characters',
  HANDLE_MATCH:
    'Handle can only contain numbers, letters, dashes, and underscores.',
  PROBLEM_FETCHING_TEST_EXECUTIONS:
    'We are having problems fetching test-excutions',
  PROBLEM_FETCHING_TEST_RUNS: 'We are having problems fetching test-runs',
  PROBLEM_FETCHING_TEST_PLANS: 'We are having problems fetching test-plans',
  PROBLEM_FETCHING_TEST_FOLDERS: 'We are having problems fetching test-folders',
  PROBLEM_FETCHING_TEST_CASES: 'We are having problems fetching test-cases',
  PROBLEM_FETCHING_TEST_SUITES: 'We are having problems fetching test-suites',
  PROBLEM_FETCHING_TEST_MILESTONES:
    'We are having problems fetching test-milestones',
  PROBLEM_FETCHING_TEST_PROJECTS:
    'We are having problems fetching test-projects',
  PROBLEM_FETCHING_TEST_REPOSITORIES:
    'We are having problems fetching repositories',
  PROBLEM_FETCHING_TEST_BRANCHES: 'We are having problems fetching branches',
  IMAGE_UPLOAD_OVER_SIZE_LIMIT: 'Image size cannot be larger than 2MB!',
  FIRST_NAME_FIELD_REQUIRED: '"firstName" field is required',
  LAST_NAME_FIELD_REQUIRED: '"lastName" field is required',
  AVATAR_MUST_BE_URL: '"avatarUrl" field must be a URL',
  ORG_FIELD_REQUIRED: '"org" field is required',
  EXTERNALID_FIELD_REQUIRED: '"externalId" field is required',
  PLANIDS_FIELD_REQUIRED: '"planIds" field is required',
  PLANS_FIELD_REQUIRED: '"plans" field is required',
  PLANS_ID_FIELD_REQUIRED: 'Each "planId" must be integer',
  INVALID_ARCHIVED_TYPE: 'Each "archived" must be (true/false)',
  PLANIDS_FIELD_NOT_ARRAY: '"planIds" field is not an array',
  PLANS_FIELD_NOT_ARRAY: '"planIds" field is not an array',
  SOURCE_FIELD_REQUIRED: '"source" field is required',
  REPO_FIELD_REQUIRED: '"repoUID" field is required',
  testRunUid_REQUIRED: '"uid" field is required for test run',
  testPlanUid_REQUIRED: '"uid" field is required for test plan',
  TEST_PROJECT_UID_REQUIRED: '"projectId" field is required for test plan',
  TAG_UID_REQUIRED: '"tagId" field is required',
  STEP_NOT_FOUND: 'Step not found',
  STEP_RELATION_NOT_FOUND: 'Step it not related to the execution',
  NON_SHARED_STEPS_REQUIRED_FILEDS:
    'Non shared steps must have all required fields',
  EXECUTION_UID_IS_REQUIRED: 'test execution id is required',
  TEST_CASE_UID_IS_REQUIRED: 'test case id is required',
  // Active step not found
  ACTIVE_STEP_NOT_FOUND: 'Active step not found',
  FAILED_TO_PROCESS_PINATA_DATA: 'Failed to process PINATA data',
  INVALID_ROLE: 'Invalid role',
  USER_NOT_SPECIFIED: 'User not specified',
  EXISTING_MEMBER: 'User already a member of the organization',
  EMAIL_OR_UID_REQUIRED: 'User email or ID must be provided',
  PERMISSIONS_REQUIRED: 'Permissions list is required',
  INVALID_PERMISSION: 'Invalid permission provided',
  DUPLICATE_ROLE: 'Role already exists in organisation',
  ROLE_NOT_FOUND: 'Role not found',
  ROLE_UID_REQUIRED: 'Role id is required',
  INVALID_ATTACHMENT_UID: 'Invalid attachment uid',
  INVALID_CASE_UID: 'Invalid case uid',
  INVALID_STEP_UID: 'Invalid step uid',
  INVALID_SHARED_STEP_UID: 'Invalid shared step uid',
  INVALID_TEMPLATE_UID: 'Invalid template uid',
  INVALID_ORG_NAME_LENGTH:
    'The org name must be longer than 2 and shorter than 64 characters',
  INVALID_CREDENTIALS: 'Invalid user credentials',
  CONFIG_NOT_FOUND: 'Config not found',
  DUPLICATE_CONFIG: 'Config name already in use',
  DASHBOARD_NOT_FOUND: 'Dashboard not found',
  DASHBOARD_DATE_RANGE_EXCEEDED: 'Dashboard date range exceeded',
  CANNOT_DELETE_ROOT_FOLDER: 'Cannot delete project root folder',
  FOLDER_NOT_FOUND: 'Folder not found',
  RESOURCE_NOT_FOUND: 'Resource not found',
  INTERNAL_SERVER_ERROR: 'Internal server error',
  INVALID_TEST_MILESTONE_NAME_LENGTH:
    'The test milestone name must be longer than 2 and shorter than 64 characters',
  INVALID_ORG_NAME_CHARACTERS:
    'The org name must only contain alpha numeric characters, spaces, hyphens, and underscores.',
  MILESTONE_ID_IS_REQUIRED: '"milestoneId" is required',
  RUN_IDS_ARE_REQUIRED: '"runIds" are required and must be array',
  RUN_IDS_MUST_BE_ARRAY: '"runIds" must be an array',
  PLANS_ARE_REQUIRED: '"plans" are required and must be array',
  MILESTONES_ARE_REQUIRED: '"milestones" are required and must be array',
  CASES_IDS_ARE_REQUIRED: '"ids" are required and must be array',
  CUSTOM_FIELDS_ARE_REQUIRED: '"customFields" are required and must be object',
  EACH_RUN_ID_MUST_BE_STRING: 'Each "runId" must be an string',
  EACH_ENTITY_TYPE_MUST_BE_STRING: 'Each "entityType" must be an string',
  PROJECT_UID_IS_REQUIRED: 'Each "projectUid" is required',
  RESULT_NOT_FOUND: 'Result not found',
  EACH_PLAN_ID_MUST_BE_STRING: 'Each "planId" must be an string',
  EACH_MILESTONE_ID_MUST_BE_INTEGER: 'Each "milestoneId" must be integer',
  EACH_LINK_MUST_BE_STRING: 'Each "link" must be an string',
  FORBIDDEN: 'You are not allowed to access this resource',
  HANDLE_IS_NOT_ORG: 'Handle doesn\'t belong to an Org',
  STEPS_MUST_BE_ARRAY: '"steps" must be an array',
  INVALID_FOLDER_UID: 'Invalid folder uid',
  SHARED_STEP_IDS_MUST_BE_AN_ARRAY: '\'sharedStepIds\' must be an array',
  SHARED_STEP_IDS_IS_REQUIRED: '\'sharedStepIds\' is required',
  SHARED_STEPS_MUST_BE_AN_ARRAY: '\'sharedSteps\' must be an array',
  SHARED_STEPS_IS_REQUIRED: '\'sharedSteps\' is required',
  ARCHIVED_PROJECT_CANNOT_BE_UPDATED: 'The archived project cannot be updated',
  PROJECT_ALREADY_ARCHIVED: 'The project has already been archived',
  PROJECT_KEY_REQUIRED: '"key" field is required for a project',
  PROJECT_KEY_LENGTH: '"key" must be between 2 and 10 characters long',
  PROJECT_KEY_PATTERN:
    '"key" can only contain letters, numbers, hyphens, and underscores',
  REPO_NOT_FOUND: 'repo not found',
  INVALID_DUE_DATE: 'invalid due date',
  MILESTONE_NOT_FOUND: 'milestone not found',
  PROJECT_KEY_EXISTS: 'A project with this key already exists',
  CASES_FIELD_REQUIRED: 'Cases must contain at least one item',
  INVALID_TESTRUN_UID:
    'The test run ID provided is not valid. Please check and try again.',
  ROLE_IDS_IS_REQUIRED: '\'roleIds\' is required',
  ROLE_IDS_MUST_BE_AN_ARRAY: '\'roleIds\' must be an array',
  MEMBERS_IS_REQUIRED: '"members" is required',
  MEMBERS_MUST_BE_AN_ARRAY: '"members" must be an array',
  TAG_IDS_MUST_BE_ARRAY: '"tagIds" must be an array',
  PASSWORD_FIELD_REQUIRED: '"password" field is required',
  CUSTOM_FIELD_INVALID_CUSTOM_FIELD_ID: 'Invalid custom field id',
  CUSTOM_FIELD_NAME_REQUIRED: 'The name field is required.',
  CUSTOM_FIELD_EXISTS: 'Custom field name is already taken',
  CUSTOM_FIELD_TYPE_REQUIRED: 'The type field is required.',
  INVALID_CUSTOM_FIELD_TYPE: 'The type field is required.',
  CUSTOM_FIELD_INVALID_OPTIONS_ARRAY: 'Options must be an array.',
  CUSTOM_FIELD_INVALID_SOURCE: 'Invalid source value.',
  FOLDER_EXISTS: 'Folder already exists',
  PARENT_FOLDER_NOT_EXISTS: 'Parent folder does not exists',
  MULTIPLE_FOLDERS_FOR_CASE: 'Case must belong to one folder only',
  CASE_NOT_FOUND: 'Case not found',
  AUTH_TOKEN_NOT_FOUND: 'Service is not authenticated',
  REFRESH_TOKEN_EXPIRED: 'Refresh token expired, please authenticate again',
  INVALID_ENTITY_TYPE: 'Invalid entity type',
  INVALID_CHART_TYPE: 'Invalid chart type',
  INVALID_CHART_ID: 'Invalid chart ID',
  CANNOT_REMOVE_OWNER: 'Owner cannot be removed',
  // eslint-disable-next-line @typescript-eslint/quotes
  USER_IS_NOT_OWNER: "You're not an owner of this account",
  UNABLE_TO_REMOVE_ALL_OWNERS: 'You cannot remove all owners',
  CANNOT_UPDATE_SYSTEM_ROLE: 'You cannot update a system role',
  INTEGRATION_NOT_FOUND: 'You dont have this service integrated',
  INTEGRATION_CONFIGURED_FOR_PROJECTS: 'Integration is configured for projects',
  UNSUPPORTED_INTEGRATION: 'Unsupported integration service',
  UNABLE_TO_REMOVE_INTEGRATION: 'Unable to remove integration',
  DEFECT_NOT_FOUND: 'Defect not found',
  MESSAGE_CONTENT_REQUIRED:
    'Message content is required and must be a valid string.',
  FIELD_VALUE_REQUIRED: 'Text assist field value is required.',
  PROMPT_CONTENT_REQUIRED:
    'Prompt content is required and must be a valid string.',
  TEXT_FIELD_REQUIRED: 'At least one text field is required.',
  FIELD_NAME_REQUIRED: 'Text assist field name is required.',
  NO_RESPONSE_FOUND: 'No response found.',
  INVALID_TAG_UID: 'Invalid tag uid(s)',
  PLAN_NOT_FOUND: 'Plan not found',
  TEST_EXECUTION_NOT_FOUND: 'Test execution not found',
  INTEGRATION_USER_NOT_FOUND: 'Integration user not found',
  RUN_NOT_FOUND: 'Run not found',
  CUSTOM_FIELDS_MUST_BE_ARRAY: '"customFields" must be an array',
  CASE_IMPROVEMENT_CUSTOM_FIELD_REQUIRED:
    'Please set at least one custom field to generate accurate improvement.',
  CASE_IMPROVEMENT_STEPS_REQUIRED:
    'Please set at least one step to generate accurate improvement.',
  EXTERNAL_API_ERROR: 'Error occured during api request',
  INTEGRATION_AUTHENTICATION_REQUIRED: 'Integration authentication is required',
  INTEGRATION_DELETED: 'This integration is removed',
  ACCESS_TOKEN_NOT_FOUND: 'Access token not found',
  MILESTONEUIDS_MUST_BE_ARRAY: 'Milestone ids must be an array',
  RUNUIDS_MUST_BE_ARRAY: 'Run ids must be an array',
  PLANUIDS_MUST_BE_ARRAY: 'Plan ids must be an array',
  PROJECTUIDS_MUST_BE_ARRAY: 'Project ids must be an array',
  MILESTONEUIDS_MUST_BE_NUMBERS: 'Milestone ids must be numbers',
  RUNUIDS_MUST_BE_NUMBERS: 'Run ids must be numbers',
  PLANUIDS_MUST_BE_NUMBERS: 'Plan ids must be numbers',
  PROJECTUIDS_MUST_BE_NUMBERS: 'Project ids must be numbers',
  LIMIT_MUST_BE_NUMBER: 'Limit must be a number',
  OFFSET_MUST_BE_NUMBER: 'Offset must be a number',
  SSO_CONFIG_NOT_ALLOWED: 'User cannot create or delete SSO config. Please make sure you provide handle of an organization',
  UNSUPPORTED_SSO_PROVIDER: 'Unsupported SSO provider',
  INVALID_FOLDER_QUERY_ENTITY_TYPE: 'entityType must be either \'case\' or \'execution\'',
  TEST_RUN_ID_REQUIRED_FOLDER_QUERY: 'testRunId is required when entityType is execution',
  SCHEDULED_TASK_NOT_FOUND: 'Scheduled task not found',
  SCHEDULED_TASK_ALREADY_RUNNING: 'Scheduled task already running or completed',
};

export default errorConstants;
