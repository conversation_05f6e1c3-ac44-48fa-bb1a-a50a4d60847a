const errorConstants = {
  // Organization errors
  ORGANIZATION_NAME_EXIST: 'errors.organization.nameExist',
  INVALID_ORG_UID: 'errors.organization.invalidOrgUid',
  NO_ORG: 'errors.organization.noOrg',
  ORG_NOT_FOUND: 'errors.organization.orgNotFound',
  ORG_FIELD_REQUIRED: 'errors.organization.orgFieldRequired',
  INVALID_ORG_NAME_LENGTH: 'errors.organization.invalidOrgNameLength',
  INVALID_ORG_NAME_CHARACTERS: 'errors.organization.invalidOrgNameCharacters',
  ORG_OWNER_CANNOT_BE_REASSIGNED: 'errors.organization.orgOwnerCannotBeReassigned',
  USER_NOT_MEMBER_OF_ORG: 'errors.organization.userNotMemberOfOrg',
  NO_ACCESS_TO_ORG: 'errors.organization.noAccessToOrg',
  UNABLE_TO_REASSIGN_ALL_OWNERS: 'errors.organization.unableToReassignAllOwners',
  CANNOT_REMOVE_OWNER: 'errors.organization.cannotRemoveOwner',
  USER_IS_NOT_OWNER: 'errors.organization.userIsNotOwner',
  UNABLE_TO_REMOVE_ALL_OWNERS: 'errors.organization.unableToRemoveAllOwners',

  // Authentication errors
  INVALID_SERVICE: 'errors.authentication.invalidService',
  INVALID_INVITE_LINK: 'errors.authentication.invalidInviteLink',
  INVITATION_NOT_EXIST: 'errors.authentication.invitationNotExist',
  INVITATION_LINK_EXPIRED: 'errors.authentication.invitationLinkExpired',
  INVITATION_LINK_USED: 'errors.authentication.invitationLinkUsed',
  INVITATION_EMAIL_NO_MATCH: 'errors.authentication.invitationEmailNoMatch',
  TOO_MANY_ATTEMPTS: 'errors.authentication.tooManyAttempts',
  USER_EMAIL_NOT_EXIST: 'errors.authentication.userEmailNotExist',
  RESET_LINK_INVALID: 'errors.authentication.resetLinkInvalid',
  RESET_LINK_EXPIRED: 'errors.authentication.resetLinkExpired',
  NO_AUTHENTICATED_USER: 'errors.authentication.noAuthenticatedUser',
  EMAIL_IN_USE: 'errors.authentication.emailInUse',
  REQUIRE_CORRECT_LOGIN: 'errors.authentication.requireCorrectLogin',
  USER_ALREADY_INVITED: 'errors.authentication.userAlreadyInvited',
  CURRENT_PASSWORD_INCORRECT: 'errors.authentication.currentPasswordIncorrect',
  REQUIRE_SIGN_UP: 'errors.authentication.requireSignUp',
  INVALID_CREDENTIALS: 'errors.authentication.invalidCredentials',
  AUTH_TOKEN_NOT_FOUND: 'errors.authentication.authTokenNotFound',
  REFRESH_TOKEN_EXPIRED: 'errors.authentication.refreshTokenExpired',
  ACCESS_TOKEN_NOT_FOUND: 'errors.authentication.accessTokenNotFound',

  // User errors
  INVALID_USER_UID: 'errors.users.invalidUserUid',
  USER_NOT_FOUND: 'errors.users.userNotFound',
  USER_NOT_SPECIFIED: 'errors.users.userNotSpecified',
  EXISTING_MEMBER: 'errors.users.existingMember',
  EMAIL_OR_UID_REQUIRED: 'errors.users.emailOrUidRequired',
  PROBLEM_FETCHING_USERS: 'errors.users.problemFetchingUsers',
  USERUIDS_MUST_BE_STRING: 'errors.users.useruidsMustBeString',
  USERUIDS_MUST_BE_ARRAY: 'errors.users.useruidsMustBeArray',
  NOT_ADMIN: 'errors.users.notAdmin',
  MISSING_FIELDS: 'errors.users.missingFields',
  FIRST_NAME_FIELD_REQUIRED: 'errors.users.firstNameFieldRequired',
  LAST_NAME_FIELD_REQUIRED: 'errors.users.lastNameFieldRequired',
  AVATAR_MUST_BE_URL: 'errors.users.avatarMustBeUrl',
  PASSWORD_FIELD_REQUIRED: 'errors.users.passwordFieldRequired',

  // Validation errors
  NAME_FIELD_REQUIRED: 'errors.validation.nameFieldRequired',
  PRIORITIES_MUST_BE_NUMBERS: 'errors.validation.prioritiesMustBeNumbers',
  PRIORITIES_MUST_BE_ARRAY: 'errors.validation.prioritiesMustBeArray',
  STATUS_MUST_BE_NUMBERS: 'errors.validation.statusMustBeNumbers',
  STATUS_MUST_BE_ARRAY: 'errors.validation.statusMustBeArray',
  TAGS_MUST_BE_NUMBERS: 'errors.validation.tagsMustBeNumbers',
  TAGS_MUST_BE_ARRAY: 'errors.validation.tagsMustBeArray',
  QUERY_MUST_BE_STRING: 'errors.validation.queryMustBeString',
  SEARCH_QUERY_MUST_BE_NUMBERS: 'errors.validation.searchQueryMustBeNumbers',
  PROVIDE_DATA_TO_UPDATE: 'errors.validation.provideDataToUpdate',
  STATUS_FIELD_REQUIRED: 'errors.validation.statusFieldRequired',
  DESCRIPTION_FIELD_REQUIRED: 'errors.validation.descriptionFieldRequired',
  PRIORITY_FIELD_REQUIRED: 'errors.validation.priorityFieldRequired',
  INVALID_TYPE: 'errors.validation.invalidType',
  TOKEN_REQUIRED: 'errors.validation.tokenRequired',
  MISSING_PERMISSION: 'errors.validation.missingPermission',
  TYPE_FIELD_REQUIRED: 'errors.validation.typeFieldRequired',
  SIZE_FIELD_REQUIRED: 'errors.validation.sizeFieldRequired',
  SOURCE_FIELD_REQUIRED: 'errors.validation.sourceFieldRequired',
  SOURCE_KEY_REQUIRED: 'errors.validation.sourceKeyRequired',
  TYPE_KEY_REQUIRED: 'errors.validation.typeKeyRequired',
  STEP_UID_REQUIRED: 'errors.validation.stepUidRequired',
  TYPES_KEY_REQUIRED: 'errors.validation.typesKeyRequired',
  END_MUST_BE_DATE: 'errors.validation.endMustBeDate',
  START_MUST_BE_DATE: 'errors.validation.startMustBeDate',
  ID_MUST_BE_NUMBER: 'errors.validation.idMustBeNumber',
  ENTRIES_KEY_REQUIRED: 'errors.validation.entriesKeyRequired',
  INVALID_SOURCE_KEY: 'errors.validation.invalidSourceKey',
  INVALID_TYPE_KEY: 'errors.validation.invalidTypeKey',
  INVALID_REQUEST: 'errors.validation.invalidRequest',
  INVALID_VIEW: 'errors.validation.invalidView',
  PROJECTS_NOT_ARRAY: 'errors.validation.projectsNotArray',
  ENTRIES_KEY_NOT_ARRAY: 'errors.validation.entriesKeyNotArray',
  ENTITY_TYPES_FIELD_NOT_ARRAY: 'errors.validation.entityTypesFieldNotArray',
  RUNS_KEY_NOT_ARRAY: 'errors.validation.runsKeyNotArray',
  OPTIONS_KEY_NOT_ARRAY: 'errors.validation.optionsKeyNotArray',
  CONFIGURATIONS_KEY_NOT_ARRAY: 'errors.validation.configurationsKeyNotArray',
  TAGS_KEY_NOT_ARRAY: 'errors.validation.tagsKeyNotArray',
  LIMIT_MUST_BE_NUMBER: 'errors.validation.limitMustBeNumber',
  OFFSET_MUST_BE_NUMBER: 'errors.validation.offsetMustBeNumber',
  CUSTOM_FIELDS_MUST_BE_ARRAY: 'errors.validation.customFieldsMustBeArray',

  // Plans errors
  SELECTED_PLANS_MUST_BE_NUMBERS: 'errors.plans.selectedPlansMustBeNumbers',
  PLANIDS_FIELD_REQUIRED: 'errors.plans.planidsFieldRequired',
  PLANS_FIELD_REQUIRED: 'errors.plans.plansFieldRequired',
  PLANS_ID_FIELD_REQUIRED: 'errors.plans.plansIdFieldRequired',
  INVALID_ARCHIVED_TYPE: 'errors.plans.invalidArchivedType',
  PLANIDS_FIELD_NOT_ARRAY: 'errors.plans.planidsFieldNotArray',
  PLANS_FIELD_NOT_ARRAY: 'errors.plans.plansFieldNotArray',
  testPlanUid_REQUIRED: 'errors.plans.testPlanUidRequired',
  EACH_PLAN_ID_MUST_BE_STRING: 'errors.plans.eachPlanIdMustBeString',
  PLANS_ARE_REQUIRED: 'errors.plans.plansAreRequired',
  PLAN_NOT_FOUND: 'errors.plans.planNotFound',
  PLANUIDS_MUST_BE_ARRAY: 'errors.plans.planuidsMustBeArray',
  PLANUIDS_MUST_BE_NUMBERS: 'errors.plans.planuidsMustBeNumbers',
  PROBLEM_FETCHING_TEST_PLANS: 'errors.plans.problemFetchingTestPlans',
  // Milestones errors
  SELECTED_MILESTONES_MUST_BE_NUMBERS: 'errors.milestones.selectedMilestonesMustBeNumbers',
  INVALID_TEST_MILESTONE_NAME_LENGTH: 'errors.milestones.invalidTestMilestoneNameLength',
  MILESTONE_ID_IS_REQUIRED: 'errors.milestones.milestoneIdIsRequired',
  MILESTONES_ARE_REQUIRED: 'errors.milestones.milestonesAreRequired',
  EACH_MILESTONE_ID_MUST_BE_INTEGER: 'errors.milestones.eachMilestoneIdMustBeInteger',
  INVALID_DUE_DATE: 'errors.milestones.invalidDueDate',
  MILESTONE_NOT_FOUND: 'errors.milestones.milestoneNotFound',
  MILESTONEUIDS_MUST_BE_ARRAY: 'errors.milestones.milestoneuidsMustBeArray',
  MILESTONEUIDS_MUST_BE_NUMBERS: 'errors.milestones.milestoneuidsMustBeNumbers',
  PROBLEM_FETCHING_TEST_MILESTONES: 'errors.milestones.problemFetchingTestMilestones',

  // Runs errors
  SELECTED_RUNS_MUST_BE_NUMBERS: 'errors.runs.selectedRunsMustBeNumbers',
  ASSIGN_DATE_START_MUST_BE_DATE: 'errors.runs.assignDateStartMustBeDate',
  ASSIGN_DATE_END_MUST_BE_DATE: 'errors.runs.assignDateEndMustBeDate',
  DUE_DATE_START_MUST_BE_DATE: 'errors.runs.dueDateStartMustBeDate',
  DUE_DATE_END_MUST_BE_DATE: 'errors.runs.dueDateEndMustBeDate',
  RUNS_FIELD_REQUIRED: 'errors.runs.runsFieldRequired',
  RUN_IDS_ARE_REQUIRED: 'errors.runs.runIdsAreRequired',
  RUN_IDS_MUST_BE_ARRAY: 'errors.runs.runIdsMustBeArray',
  EACH_RUN_ID_MUST_BE_STRING: 'errors.runs.eachRunIdMustBeString',
  testRunUid_REQUIRED: 'errors.runs.testRunUidRequired',
  INVALID_TESTRUN_UID: 'errors.runs.invalidTestrunUid',
  RUN_NOT_FOUND: 'errors.runs.runNotFound',
  RUNUIDS_MUST_BE_ARRAY: 'errors.runs.runuidsMustBeArray',
  RUNUIDS_MUST_BE_NUMBERS: 'errors.runs.runuidsMustBeNumbers',
  PROBLEM_FETCHING_TEST_RUNS: 'errors.runs.problemFetchingTestRuns',

  // Cases errors
  PROBLEM_UPDATING_TEST_CASE: 'errors.cases.problemUpdatingTestCase',
  CASES_IDS_ARE_REQUIRED: 'errors.cases.casesIdsAreRequired',
  TEST_CASE_UID_IS_REQUIRED: 'errors.cases.testCaseUidIsRequired',
  INVALID_CASE_UID: 'errors.cases.invalidCaseUid',
  CASE_NOT_FOUND: 'errors.cases.caseNotFound',
  CASES_FIELD_REQUIRED: 'errors.cases.casesFieldRequired',
  CASE_IMPROVEMENT_CUSTOM_FIELD_REQUIRED: 'errors.cases.caseImprovementCustomFieldRequired',
  CASE_IMPROVEMENT_STEPS_REQUIRED: 'errors.cases.caseImprovementStepsRequired',
  PROBLEM_FETCHING_TEST_CASES: 'errors.cases.problemFetchingTestCases',

  // Executions errors
  EXECUTION_UID_IS_REQUIRED: 'errors.executions.executionUidIsRequired',
  TEST_EXECUTION_NOT_FOUND: 'errors.executions.testExecutionNotFound',
  PROBLEM_FETCHING_TEST_EXECUTIONS: 'errors.executions.problemFetchingTestExecutions',

  // Steps errors
  STEP_NOT_FOUND: 'errors.steps.stepNotFound',
  STEP_RELATION_NOT_FOUND: 'errors.steps.stepRelationNotFound',
  NON_SHARED_STEPS_REQUIRED_FILEDS: 'errors.steps.nonSharedStepsRequiredFileds',
  ACTIVE_STEP_NOT_FOUND: 'errors.steps.activeStepNotFound',
  INVALID_STEP_UID: 'errors.steps.invalidStepUid',
  STEPS_MUST_BE_ARRAY: 'errors.steps.stepsMustBeArray',
  SHARED_STEP_IDS_MUST_BE_AN_ARRAY: 'errors.steps.sharedStepIdsMustBeAnArray',
  SHARED_STEP_IDS_IS_REQUIRED: 'errors.steps.sharedStepIdsIsRequired',
  SHARED_STEPS_MUST_BE_AN_ARRAY: 'errors.steps.sharedStepsMustBeAnArray',
  SHARED_STEPS_IS_REQUIRED: 'errors.steps.sharedStepsIsRequired',
  INVALID_SHARED_STEP_UID: 'errors.steps.invalidSharedStepUid',

  // Projects errors
  PROJECT_NOT_FOUND: 'errors.projects.projectNotFound',
  PROJECT_IDS_NOT_FOUND: 'errors.projects.projectIdsNotFound',
  NO_PERMISSION_FOR_PROJECT: 'errors.projects.noPermissionForProject',
  TEST_PROJECT_UID_REQUIRED: 'errors.projects.testProjectUidRequired',
  PROJECT_UID_IS_REQUIRED: 'errors.projects.projectUidIsRequired',
  ARCHIVED_PROJECT_CANNOT_BE_UPDATED: 'errors.projects.archivedProjectCannotBeUpdated',
  PROJECT_ALREADY_ARCHIVED: 'errors.projects.projectAlreadyArchived',
  PROJECT_KEY_REQUIRED: 'errors.projects.projectKeyRequired',
  PROJECT_KEY_LENGTH: 'errors.projects.projectKeyLength',
  PROJECT_KEY_PATTERN: 'errors.projects.projectKeyPattern',
  PROJECT_KEY_EXISTS: 'errors.projects.projectKeyExists',
  PROJECTUIDS_MUST_BE_ARRAY: 'errors.projects.projectuidsMustBeArray',
  PROJECTUIDS_MUST_BE_NUMBERS: 'errors.projects.projectuidsMustBeNumbers',
  PROBLEM_FETCHING_TEST_PROJECTS: 'errors.projects.problemFetchingTestProjects',

  // Tags errors
  TAG_UID_REQUIRED: 'errors.tags.tagUidRequired',
  INVALID_TAG_UID: 'errors.tags.invalidTagUid',
  TAG_IDS_MUST_BE_ARRAY: 'errors.tags.tagIdsMustBeArray',
  EACH_ENTITY_TYPE_MUST_BE_STRING: 'errors.tags.eachEntityTypeMustBeString',

  // Roles errors
  INVALID_ROLE: 'errors.roles.invalidRole',
  PERMISSIONS_REQUIRED: 'errors.roles.permissionsRequired',
  INVALID_PERMISSION: 'errors.roles.invalidPermission',
  DUPLICATE_ROLE: 'errors.roles.duplicateRole',
  ROLE_NOT_FOUND: 'errors.roles.roleNotFound',
  ROLE_UID_REQUIRED: 'errors.roles.roleUidRequired',
  CAN_NOT_UPDATE_ROLE: 'errors.roles.canNotUpdateRole',
  PROBLEM_FETCHING_ROLES: 'errors.roles.problemFetchingRoles',
  ROLE_IDS_IS_REQUIRED: 'errors.roles.roleIdsIsRequired',
  ROLE_IDS_MUST_BE_AN_ARRAY: 'errors.roles.roleIdsMustBeAnArray',
  MEMBERS_IS_REQUIRED: 'errors.roles.membersIsRequired',
  MEMBERS_MUST_BE_AN_ARRAY: 'errors.roles.membersMustBeAnArray',
  CANNOT_UPDATE_SYSTEM_ROLE: 'errors.roles.cannotUpdateSystemRole',

  // Attachments errors
  ATTACHMENT_NOT_FOUND: 'errors.attachments.attachmentNotFound',
  OWNER_ID_REQUIRED: 'errors.attachments.ownerIdRequired',
  OWNER_TYPE_REQUIRED: 'errors.attachments.ownerTypeRequired',
  FILE_NAME_FIELD_REQUIRED: 'errors.attachments.fileNameFieldRequired',
  INVALID_MIME_TYPE: 'errors.attachments.invalidMimeType',
  MEDIA_TYPE_FIELD_REQUIRED: 'errors.attachments.mediaTypeFieldRequired',
  IMAGE_UPLOAD_OVER_SIZE_LIMIT: 'errors.attachments.imageUploadOverSizeLimit',
  INVALID_ATTACHMENT_UID: 'errors.attachments.invalidAttachmentUid',

  // Handles errors
  HANDLE_DUPLICATED: 'errors.handles.handleDuplicated',
  HANDLE_UNAVAILABLE: 'errors.handles.handleUnavailable',
  HANDLE_NOT_FOUND: 'errors.handles.handleNotFound',
  HANDLE_FOUND: 'errors.handles.handleFound',
  HANDLE_LENGTH: 'errors.handles.handleLength',
  HANDLE_MATCH: 'errors.handles.handleMatch',
  PROBLEM_FETCHING_HANDLE: 'errors.handles.problemFetchingHandle',
  HANDLE_IS_NOT_ORG: 'errors.handles.handleIsNotOrg',

  // Keys errors
  KEY_UNAVAILABLE: 'errors.keys.keyUnavailable',
  KEY_FOUND: 'errors.keys.keyFound',

  // Dashboards errors
  MISSING_X_VALUE: 'errors.dashboards.missingXValue',
  MISSING_Y_VALUE: 'errors.dashboards.missingYValue',
  MISSING_W_VALUE: 'errors.dashboards.missingWValue',
  MISSING_H_VALUE: 'errors.dashboards.missingHValue',
  EDITABLE_BOOLEAN: 'errors.dashboards.editableBoolean',
  DEFAULT_BOOLEAN: 'errors.dashboards.defaultBoolean',
  DEFAULT_DASHBOARD_RESTRICT: 'errors.dashboards.defaultDashboardRestrict',
  DASHBOARD_NOT_FOUNT: 'errors.dashboards.dashboardNotFount',
  DASHBOARD_NOT_EDITABLE: 'errors.dashboards.dashboardNotEditable',
  DASHBOARD_NAME_RESTRICT: 'errors.dashboards.dashboardNameRestrict',
  DASHBOARD_NOT_FOUND: 'errors.dashboards.dashboardNotFound',
  DASHBOARD_DATE_RANGE_EXCEEDED: 'errors.dashboards.dashboardDateRangeExceeded',

  // Tokens errors
  TOKEN_NOT_FOUND: 'errors.tokens.tokenNotFound',

  // Folders errors
  CANNOT_DELETE_ROOT_FOLDER: 'errors.folders.cannotDeleteRootFolder',
  FOLDER_NOT_FOUND: 'errors.folders.folderNotFound',
  INVALID_FOLDER_UID: 'errors.folders.invalidFolderUid',
  FOLDER_EXISTS: 'errors.folders.folderExists',
  PARENT_FOLDER_NOT_EXISTS: 'errors.folders.parentFolderNotExists',
  MULTIPLE_FOLDERS_FOR_CASE: 'errors.folders.multipleFoldersForCase',
  INVALID_FOLDER_QUERY_ENTITY_TYPE: 'errors.folders.invalidFolderQueryEntityType',
  TEST_RUN_ID_REQUIRED_FOLDER_QUERY: 'errors.folders.testRunIdRequiredFolderQuery',
  PROBLEM_FETCHING_TEST_FOLDERS: 'errors.folders.problemFetchingTestFolders',

  // Repositories errors
  REPO_FIELD_REQUIRED: 'errors.repositories.repoFieldRequired',
  REPO_NOT_FOUND: 'errors.repositories.repoNotFound',
  PROBLEM_FETCHING_REPO_BRANCHES: 'errors.repositories.problemFetchingRepoBranches',
  PROBLEM_FETCHING_TEST_REPOSITORIES: 'errors.repositories.problemFetchingTestRepositories',
  PROBLEM_FETCHING_TEST_BRANCHES: 'errors.repositories.problemFetchingTestBranches',

  // Custom Fields errors
  CUSTOM_FIELDS_ARE_REQUIRED: 'errors.customFields.customFieldsAreRequired',
  CUSTOM_FIELD_INVALID_CUSTOM_FIELD_ID: 'errors.customFields.customFieldInvalidCustomFieldId',
  CUSTOM_FIELD_NAME_REQUIRED: 'errors.customFields.customFieldNameRequired',
  CUSTOM_FIELD_EXISTS: 'errors.customFields.customFieldExists',
  CUSTOM_FIELD_TYPE_REQUIRED: 'errors.customFields.customFieldTypeRequired',
  INVALID_CUSTOM_FIELD_TYPE: 'errors.customFields.invalidCustomFieldType',
  CUSTOM_FIELD_INVALID_OPTIONS_ARRAY: 'errors.customFields.customFieldInvalidOptionsArray',
  CUSTOM_FIELD_INVALID_SOURCE: 'errors.customFields.customFieldInvalidSource',

  // Templates errors
  INVALID_TEMPLATE_UID: 'errors.templates.invalidTemplateUid',

  // Configs errors
  CONFIG_NOT_FOUND: 'errors.configs.configNotFound',
  DUPLICATE_CONFIG: 'errors.configs.duplicateConfig',

  // Suites errors
  PROBLEM_FETCHING_TEST_SUITES: 'errors.suites.problemFetchingTestSuites',

  // Integrations errors
  INTEGRATION_NOT_FOUND: 'errors.integrations.integrationNotFound',
  INTEGRATION_CONFIGURED_FOR_PROJECTS: 'errors.integrations.integrationConfiguredForProjects',
  UNSUPPORTED_INTEGRATION: 'errors.integrations.unsupportedIntegration',
  UNABLE_TO_REMOVE_INTEGRATION: 'errors.integrations.unableToRemoveIntegration',
  INTEGRATION_USER_NOT_FOUND: 'errors.integrations.integrationUserNotFound',
  EXTERNAL_API_ERROR: 'errors.integrations.externalApiError',
  INTEGRATION_AUTHENTICATION_REQUIRED: 'errors.integrations.integrationAuthenticationRequired',
  INTEGRATION_DELETED: 'errors.integrations.integrationDeleted',

  // Defects errors
  DEFECT_NOT_FOUND: 'errors.defects.defectNotFound',

  // Assist errors
  MESSAGE_CONTENT_REQUIRED: 'errors.assist.messageContentRequired',
  FIELD_VALUE_REQUIRED: 'errors.assist.fieldValueRequired',
  PROMPT_CONTENT_REQUIRED: 'errors.assist.promptContentRequired',
  TEXT_FIELD_REQUIRED: 'errors.assist.textFieldRequired',
  FIELD_NAME_REQUIRED: 'errors.assist.fieldNameRequired',
  NO_RESPONSE_FOUND: 'errors.assist.noResponseFound',

  // SSO errors
  SSO_CONFIG_NOT_ALLOWED: 'errors.sso.ssoConfigNotAllowed',
  UNSUPPORTED_SSO_PROVIDER: 'errors.sso.unsupportedSsoProvider',

  // Scheduled Tasks errors
  SCHEDULED_TASK_NOT_FOUND: 'errors.scheduledTasks.scheduledTaskNotFound',
  SCHEDULED_TASK_ALREADY_RUNNING: 'errors.scheduledTasks.scheduledTaskAlreadyRunning',

  // Charts errors
  INVALID_CHART_TYPE: 'errors.charts.invalidChartType',
  INVALID_CHART_ID: 'errors.charts.invalidChartId',

  // Entities errors
  INVALID_ENTITY_TYPE: 'errors.entities.invalidEntityType',
  EACH_LINK_MUST_BE_STRING: 'errors.entities.eachLinkMustBeString',

  // Pinata errors
  FAILED_TO_PROCESS_PINATA_DATA: 'errors.pinata.failedToProcessPinataData',

  // Generic errors
  PROBLEM_PROCESSING_REQUEST: 'errors.generic.problemProcessingRequest',
  RESOURCE_NOT_FOUND: 'errors.generic.resourceNotFound',
  INTERNAL_SERVER_ERROR: 'errors.generic.internalServerError',
  RESULT_NOT_FOUND: 'errors.generic.resultNotFound',
  FORBIDDEN: 'errors.generic.forbidden',
};

export default errorConstants;
