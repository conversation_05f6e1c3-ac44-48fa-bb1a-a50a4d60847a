const errorConstants = {
  // Organization errors
  ORGANIZATION_NAME_EXIST: 'errors.organization.nameExist',
  INVALID_ORG_UID: 'errors.organization.invalidOrgUid',
  NO_ORG: 'errors.organization.noOrg',
  ORG_NOT_FOUND: 'errors.organization.orgNotFound',
  ORG_FIELD_REQUIRED: 'errors.organization.orgFieldRequired',
  INVALID_ORG_NAME_LENGTH: 'errors.organization.invalidOrgNameLength',
  INVALID_ORG_NAME_CHARACTERS: 'errors.organization.invalidOrgNameCharacters',
  ORG_OWNER_CANNOT_BE_REASSIGNED: 'errors.organization.orgOwnerCannotBeReassigned',
  USER_NOT_MEMBER_OF_ORG: 'errors.organization.userNotMemberOfOrg',
  NO_ACCESS_TO_ORG: 'errors.organization.noAccessToOrg',
  UNABLE_TO_REASSIGN_ALL_OWNERS: 'errors.organization.unableToReassignAllOwners',
  CANNOT_REMOVE_OWNER: 'errors.organization.cannotRemoveOwner',
  USER_IS_NOT_OWNER: 'errors.organization.userIsNotOwner',
  UNABLE_TO_REMOVE_ALL_OWNERS: 'errors.organization.unableToRemoveAllOwners',

  // Authentication errors
  INVALID_SERVICE: 'errors.authentication.invalidService',
  INVALID_INVITE_LINK: 'errors.authentication.invalidInviteLink',
  INVITATION_NOT_EXIST: 'errors.authentication.invitationNotExist',
  INVITATION_LINK_EXPIRED: 'errors.authentication.invitationLinkExpired',
  INVITATION_LINK_USED: 'errors.authentication.invitationLinkUsed',
  INVITATION_EMAIL_NO_MATCH: 'errors.authentication.invitationEmailNoMatch',
  TOO_MANY_ATTEMPTS: 'errors.authentication.tooManyAttempts',
  USER_EMAIL_NOT_EXIST: 'errors.authentication.userEmailNotExist',
  RESET_LINK_INVALID: 'errors.authentication.resetLinkInvalid',
  RESET_LINK_EXPIRED: 'errors.authentication.resetLinkExpired',
  NO_AUTHENTICATED_USER: 'errors.authentication.noAuthenticatedUser',
  EMAIL_IN_USE: 'errors.authentication.emailInUse',
  REQUIRE_CORRECT_LOGIN: 'errors.authentication.requireCorrectLogin',
  USER_ALREADY_INVITED: 'errors.authentication.userAlreadyInvited',
  CURRENT_PASSWORD_INCORRECT: 'errors.authentication.currentPasswordIncorrect',
  REQUIRE_SIGN_UP: 'errors.authentication.requireSignUp',
  INVALID_CREDENTIALS: 'errors.authentication.invalidCredentials',
  AUTH_TOKEN_NOT_FOUND: 'errors.authentication.authTokenNotFound',
  REFRESH_TOKEN_EXPIRED: 'errors.authentication.refreshTokenExpired',
  ACCESS_TOKEN_NOT_FOUND: 'errors.authentication.accessTokenNotFound',

  // User errors
  INVALID_USER_UID: 'errors.users.invalidUserUid',
  USER_NOT_FOUND: 'errors.users.userNotFound',
  USER_NOT_SPECIFIED: 'errors.users.userNotSpecified',
  EXISTING_MEMBER: 'errors.users.existingMember',
  EMAIL_OR_UID_REQUIRED: 'errors.users.emailOrUidRequired',
  PROBLEM_FETCHING_USERS: 'errors.users.problemFetchingUsers',
  USERUIDS_MUST_BE_STRING: 'errors.users.useruidsMustBeString',
  USERUIDS_MUST_BE_ARRAY: 'errors.users.useruidsMustBeArray',
  NOT_ADMIN: 'errors.users.notAdmin',
  MISSING_FIELDS: 'errors.users.missingFields',
  FIRST_NAME_FIELD_REQUIRED: 'errors.users.firstNameFieldRequired',
  LAST_NAME_FIELD_REQUIRED: 'errors.users.lastNameFieldRequired',
  AVATAR_MUST_BE_URL: 'errors.users.avatarMustBeUrl',
  PASSWORD_FIELD_REQUIRED: 'errors.users.passwordFieldRequired',

  // Validation errors
  NAME_FIELD_REQUIRED: 'errors.validation.nameFieldRequired',
  PRIORITIES_MUST_BE_NUMBERS: 'errors.validation.prioritiesMustBeNumbers',
  PRIORITIES_MUST_BE_ARRAY: 'errors.validation.prioritiesMustBeArray',
  STATUS_MUST_BE_NUMBERS: 'errors.validation.statusMustBeNumbers',
  STATUS_MUST_BE_ARRAY: 'errors.validation.statusMustBeArray',
  TAGS_MUST_BE_NUMBERS: 'errors.validation.tagsMustBeNumbers',
  TAGS_MUST_BE_ARRAY: 'errors.validation.tagsMustBeArray',
  QUERY_MUST_BE_STRING: 'errors.validation.queryMustBeString',
  SEARCH_QUERY_MUST_BE_NUMBERS: 'errors.validation.searchQueryMustBeNumbers',
  PROVIDE_DATA_TO_UPDATE: 'errors.validation.provideDataToUpdate',
  STATUS_FIELD_REQUIRED: 'errors.validation.statusFieldRequired',
  DESCRIPTION_FIELD_REQUIRED: 'errors.validation.descriptionFieldRequired',
  PRIORITY_FIELD_REQUIRED: 'errors.validation.priorityFieldRequired',
  INVALID_TYPE: 'errors.validation.invalidType',
  TOKEN_REQUIRED: 'errors.validation.tokenRequired',
  MISSING_PERMISSION: 'errors.validation.missingPermission',
  TYPE_FIELD_REQUIRED: 'errors.validation.typeFieldRequired',
  SIZE_FIELD_REQUIRED: 'errors.validation.sizeFieldRequired',
  SOURCE_FIELD_REQUIRED: 'errors.validation.sourceFieldRequired',
  SOURCE_KEY_REQUIRED: 'errors.validation.sourceKeyRequired',
  TYPE_KEY_REQUIRED: 'errors.validation.typeKeyRequired',
  STEP_UID_REQUIRED: 'errors.validation.stepUidRequired',
  TYPES_KEY_REQUIRED: 'errors.validation.typesKeyRequired',
  END_MUST_BE_DATE: 'errors.validation.endMustBeDate',
  START_MUST_BE_DATE: 'errors.validation.startMustBeDate',
  ID_MUST_BE_NUMBER: 'errors.validation.idMustBeNumber',
  ENTRIES_KEY_REQUIRED: 'errors.validation.entriesKeyRequired',
  INVALID_SOURCE_KEY: 'errors.validation.invalidSourceKey',
  INVALID_TYPE_KEY: 'errors.validation.invalidTypeKey',
  INVALID_REQUEST: 'errors.validation.invalidRequest',
  INVALID_VIEW: 'errors.validation.invalidView',
  PROJECTS_NOT_ARRAY: 'errors.validation.projectsNotArray',
  ENTRIES_KEY_NOT_ARRAY: 'errors.validation.entriesKeyNotArray',
  ENTITY_TYPES_FIELD_NOT_ARRAY: 'errors.validation.entityTypesFieldNotArray',
  RUNS_KEY_NOT_ARRAY: 'errors.validation.runsKeyNotArray',
  OPTIONS_KEY_NOT_ARRAY: 'errors.validation.optionsKeyNotArray',
  CONFIGURATIONS_KEY_NOT_ARRAY: 'errors.validation.configurationsKeyNotArray',
  TAGS_KEY_NOT_ARRAY: 'errors.validation.tagsKeyNotArray',
  LIMIT_MUST_BE_NUMBER: 'errors.validation.limitMustBeNumber',
  OFFSET_MUST_BE_NUMBER: 'errors.validation.offsetMustBeNumber',
  CUSTOM_FIELDS_MUST_BE_ARRAY: 'errors.validation.customFieldsMustBeArray',

  // Plans errors
  SELECTED_PLANS_MUST_BE_NUMBERS: 'errors.plans.selectedPlansMustBeNumbers',
  PLANIDS_FIELD_REQUIRED: 'errors.plans.planidsFieldRequired',
  PLANS_FIELD_REQUIRED: 'errors.plans.plansFieldRequired',
  PLANS_ID_FIELD_REQUIRED: 'errors.plans.plansIdFieldRequired',
  INVALID_ARCHIVED_TYPE: 'errors.plans.invalidArchivedType',
  PLANIDS_FIELD_NOT_ARRAY: 'errors.plans.planidsFieldNotArray',
  PLANS_FIELD_NOT_ARRAY: 'errors.plans.plansFieldNotArray',
  testPlanUid_REQUIRED: 'errors.plans.testPlanUidRequired',
  EACH_PLAN_ID_MUST_BE_STRING: 'errors.plans.eachPlanIdMustBeString',
  PLANS_ARE_REQUIRED: 'errors.plans.plansAreRequired',
  PLAN_NOT_FOUND: 'errors.plans.planNotFound',
  PLANUIDS_MUST_BE_ARRAY: 'errors.plans.planuidsMustBeArray',
  PLANUIDS_MUST_BE_NUMBERS: 'errors.plans.planuidsMustBeNumbers',
  PROBLEM_FETCHING_TEST_PLANS: 'errors.plans.problemFetchingTestPlans',
  MISSING_X_VALUE: 'errors.missingXValue',
  MISSING_Y_VALUE: 'errors.missingYValue',
  MISSING_W_VALUE: 'errors.missingWValue',
  MISSING_H_VALUE: 'errors.missingHValue',
  EDITABLE_BOOLEAN: 'errors.editableBoolean',
  DEFAULT_BOOLEAN: 'errors.defaultBoolean',
  DEFAULT_DASHBOARD_RESTRICT: 'errors.defaultDashboardRestrict',
  DASHBOARD_NOT_FOUNT: 'errors.dashboardNotFount',
  DASHBOARD_NOT_EDITABLE: 'errors.dashboardNotEditable',
  DASHBOARD_NAME_RESTRICT: 'errors.dashboardNameRestrict',
  TOKEN_NOT_FOUND: 'errors.tokenNotFound',
  SOURCE_KEY_REQUIRED: 'errors.sourceKeyRequired',
  TYPE_KEY_REQUIRED: 'errors.typeKeyRequired',
  HANDLE_DUPLICATED: 'errors.handleDuplicated',
  HANDLE_UNAVAILABLE: 'errors.handleUnavailable',
  KEY_UNAVAILABLE: 'errors.keyUnavailable',
  HANDLE_NOT_FOUND: 'errors.handleNotFound',
  HANDLE_FOUND: 'errors.handleFound',
  KEY_FOUND: 'errors.keyFound',
  PROJECT_NOT_FOUND: 'errors.projectNotFound',
  PROJECT_IDS_NOT_FOUND: 'errors.projectIdsNotFound',
  STEP_UID_REQUIRED: 'errors.stepUidRequired',
  TYPES_KEY_REQUIRED: 'errors.typesKeyRequired',
  RUNS_FIELD_REQUIRED: 'errors.runsFieldRequired',
  END_MUST_BE_DATE: 'errors.endMustBeDate',
  START_MUST_BE_DATE: 'errors.startMustBeDate',
  ID_MUST_BE_NUMBER: 'errors.idMustBeNumber',
  ENTRIES_KEY_REQUIRED: 'errors.entriesKeyRequired',
  INVALID_SOURCE_KEY: 'errors.invalidSourceKey',
  INVALID_TYPE_KEY: 'errors.invalidTypeKey',
  INVALID_REQUEST: 'errors.invalidRequest',
  INVALID_VIEW: 'errors.invalidView',
  PROJECTS_NOT_ARRAY: 'errors.projectsNotArray',
  NO_PERMISSION_FOR_PROJECT: 'errors.noPermissionForProject',
  ENTRIES_KEY_NOT_ARRAY: 'errors.entriesKeyNotArray',
  ENTITY_TYPES_FIELD_NOT_ARRAY: 'errors.entityTypesFieldNotArray',
  RUNS_KEY_NOT_ARRAY: 'errors.runsKeyNotArray',
  OPTIONS_KEY_NOT_ARRAY: 'errors.optionsKeyNotArray',
  CONFIGURATIONS_KEY_NOT_ARRAY: 'errors.configurationsKeyNotArray',
  TAGS_KEY_NOT_ARRAY: 'errors.tagsKeyNotArray',
  USER_NOT_MEMBER_OF_ORG: 'errors.userNotMemberOfOrg',
  PROBLEM_PROCESSING_REQUEST: 'errors.problemProcessingRequest',
  ATTACHMENT_NOT_FOUND: 'errors.attachmentNotFound',
  ORG_OWNER_CANNOT_BE_REASSIGNED: 'errors.orgOwnerCannotBeReassigned',
  TYPE_FIELD_REQUIRED: 'errors.typeFieldRequired',
  SIZE_FIELD_REQUIRED: 'errors.sizeFieldRequired',
  OWNER_ID_REQUIRED: 'errors.ownerIdRequired',
  OWNER_TYPE_REQUIRED: 'errors.ownerTypeRequired',
  FILE_NAME_FIELD_REQUIRED: 'errors.fileNameFieldRequired',
  INVALID_MIME_TYPE: 'errors.invalidMimeType',
  MEDIA_TYPE_FIELD_REQUIRED: 'errors.mediaTypeFieldRequired',
  HANDLE_LENGTH: 'errors.handleLength',
  HANDLE_MATCH: 'errors.handleMatch',
  PROBLEM_FETCHING_TEST_EXECUTIONS: 'errors.problemFetchingTestExecutions',
  PROBLEM_FETCHING_TEST_RUNS: 'errors.problemFetchingTestRuns',
  PROBLEM_FETCHING_TEST_PLANS: 'errors.problemFetchingTestPlans',
  PROBLEM_FETCHING_TEST_FOLDERS: 'errors.problemFetchingTestFolders',
  PROBLEM_FETCHING_TEST_CASES: 'errors.problemFetchingTestCases',
  PROBLEM_FETCHING_TEST_SUITES: 'errors.problemFetchingTestSuites',
  PROBLEM_FETCHING_TEST_MILESTONES: 'errors.problemFetchingTestMilestones',
  PROBLEM_FETCHING_TEST_PROJECTS: 'errors.problemFetchingTestProjects',
  PROBLEM_FETCHING_TEST_REPOSITORIES: 'errors.problemFetchingTestRepositories',
  PROBLEM_FETCHING_TEST_BRANCHES: 'errors.problemFetchingTestBranches',
  IMAGE_UPLOAD_OVER_SIZE_LIMIT: 'errors.imageUploadOverSizeLimit',
  FIRST_NAME_FIELD_REQUIRED: 'errors.firstNameFieldRequired',
  LAST_NAME_FIELD_REQUIRED: 'errors.lastNameFieldRequired',
  AVATAR_MUST_BE_URL: 'errors.avatarMustBeUrl',
  ORG_FIELD_REQUIRED: 'errors.orgFieldRequired',
  EXTERNALID_FIELD_REQUIRED: 'errors.externalidFieldRequired',
  PLANIDS_FIELD_REQUIRED: 'errors.planidsFieldRequired',
  PLANS_FIELD_REQUIRED: 'errors.plansFieldRequired',
  PLANS_ID_FIELD_REQUIRED: 'errors.plansIdFieldRequired',
  INVALID_ARCHIVED_TYPE: 'errors.invalidArchivedType',
  PLANIDS_FIELD_NOT_ARRAY: 'errors.planidsFieldNotArray',
  PLANS_FIELD_NOT_ARRAY: 'errors.plansFieldNotArray',
  SOURCE_FIELD_REQUIRED: 'errors.sourceFieldRequired',
  REPO_FIELD_REQUIRED: 'errors.repoFieldRequired',
  testRunUid_REQUIRED: 'errors.testRunUidRequired',
  testPlanUid_REQUIRED: 'errors.testPlanUidRequired',
  TEST_PROJECT_UID_REQUIRED: 'errors.testProjectUidRequired',
  TAG_UID_REQUIRED: 'errors.tagUidRequired',
  STEP_NOT_FOUND: 'errors.stepNotFound',
  STEP_RELATION_NOT_FOUND: 'errors.stepRelationNotFound',
  NON_SHARED_STEPS_REQUIRED_FILEDS: 'errors.nonSharedStepsRequiredFileds',
  EXECUTION_UID_IS_REQUIRED: 'errors.executionUidIsRequired',
  TEST_CASE_UID_IS_REQUIRED: 'errors.testCaseUidIsRequired',
  ACTIVE_STEP_NOT_FOUND: 'errors.activeStepNotFound',
  FAILED_TO_PROCESS_PINATA_DATA: 'errors.failedToProcessPinataData',
  INVALID_ROLE: 'errors.invalidRole',
  USER_NOT_SPECIFIED: 'errors.userNotSpecified',
  EXISTING_MEMBER: 'errors.existingMember',
  EMAIL_OR_UID_REQUIRED: 'errors.emailOrUidRequired',
  PERMISSIONS_REQUIRED: 'errors.permissionsRequired',
  INVALID_PERMISSION: 'errors.invalidPermission',
  DUPLICATE_ROLE: 'errors.duplicateRole',
  ROLE_NOT_FOUND: 'errors.roleNotFound',
  ROLE_UID_REQUIRED: 'errors.roleUidRequired',
  INVALID_ATTACHMENT_UID: 'errors.invalidAttachmentUid',
  INVALID_CASE_UID: 'errors.invalidCaseUid',
  INVALID_STEP_UID: 'errors.invalidStepUid',
  INVALID_SHARED_STEP_UID: 'errors.invalidSharedStepUid',
  INVALID_TEMPLATE_UID: 'errors.invalidTemplateUid',
  INVALID_ORG_NAME_LENGTH: 'errors.invalidOrgNameLength',
  INVALID_CREDENTIALS: 'errors.invalidCredentials',
  CONFIG_NOT_FOUND: 'errors.configNotFound',
  DUPLICATE_CONFIG: 'errors.duplicateConfig',
  DASHBOARD_NOT_FOUND: 'errors.dashboardNotFound',
  DASHBOARD_DATE_RANGE_EXCEEDED: 'errors.dashboardDateRangeExceeded',
  CANNOT_DELETE_ROOT_FOLDER: 'errors.cannotDeleteRootFolder',
  FOLDER_NOT_FOUND: 'errors.folderNotFound',
  RESOURCE_NOT_FOUND: 'errors.resourceNotFound',
  INTERNAL_SERVER_ERROR: 'errors.internalServerError',
  INVALID_TEST_MILESTONE_NAME_LENGTH: 'errors.invalidTestMilestoneNameLength',
  INVALID_ORG_NAME_CHARACTERS: 'errors.invalidOrgNameCharacters',
  MILESTONE_ID_IS_REQUIRED: 'errors.milestoneIdIsRequired',
  RUN_IDS_ARE_REQUIRED: 'errors.runIdsAreRequired',
  RUN_IDS_MUST_BE_ARRAY: 'errors.runIdsMustBeArray',
  PLANS_ARE_REQUIRED: 'errors.plansAreRequired',
  MILESTONES_ARE_REQUIRED: 'errors.milestonesAreRequired',
  CASES_IDS_ARE_REQUIRED: 'errors.casesIdsAreRequired',
  CUSTOM_FIELDS_ARE_REQUIRED: 'errors.customFieldsAreRequired',
  EACH_RUN_ID_MUST_BE_STRING: 'errors.eachRunIdMustBeString',
  EACH_ENTITY_TYPE_MUST_BE_STRING: 'errors.eachEntityTypeMustBeString',
  PROJECT_UID_IS_REQUIRED: 'errors.projectUidIsRequired',
  RESULT_NOT_FOUND: 'errors.resultNotFound',
  EACH_PLAN_ID_MUST_BE_STRING: 'errors.eachPlanIdMustBeString',
  EACH_MILESTONE_ID_MUST_BE_INTEGER: 'errors.eachMilestoneIdMustBeInteger',
  EACH_LINK_MUST_BE_STRING: 'errors.eachLinkMustBeString',
  FORBIDDEN: 'errors.forbidden',
  HANDLE_IS_NOT_ORG: 'errors.handleIsNotOrg',
  STEPS_MUST_BE_ARRAY: 'errors.stepsMustBeArray',
  INVALID_FOLDER_UID: 'errors.invalidFolderUid',
  SHARED_STEP_IDS_MUST_BE_AN_ARRAY: 'errors.sharedStepIdsMustBeAnArray',
  SHARED_STEP_IDS_IS_REQUIRED: 'errors.sharedStepIdsIsRequired',
  SHARED_STEPS_MUST_BE_AN_ARRAY: 'errors.sharedStepsMustBeAnArray',
  SHARED_STEPS_IS_REQUIRED: 'errors.sharedStepsIsRequired',
  ARCHIVED_PROJECT_CANNOT_BE_UPDATED: 'errors.archivedProjectCannotBeUpdated',
  PROJECT_ALREADY_ARCHIVED: 'errors.projectAlreadyArchived',
  PROJECT_KEY_REQUIRED: 'errors.projectKeyRequired',
  PROJECT_KEY_LENGTH: 'errors.projectKeyLength',
  PROJECT_KEY_PATTERN: 'errors.projectKeyPattern',
  REPO_NOT_FOUND: 'errors.repoNotFound',
  INVALID_DUE_DATE: 'errors.invalidDueDate',
  MILESTONE_NOT_FOUND: 'errors.milestoneNotFound',
  PROJECT_KEY_EXISTS: 'errors.projectKeyExists',
  CASES_FIELD_REQUIRED: 'errors.casesFieldRequired',
  INVALID_TESTRUN_UID: 'errors.invalidTestrunUid',
  ROLE_IDS_IS_REQUIRED: 'errors.roleIdsIsRequired',
  ROLE_IDS_MUST_BE_AN_ARRAY: 'errors.roleIdsMustBeAnArray',
  MEMBERS_IS_REQUIRED: 'errors.membersIsRequired',
  MEMBERS_MUST_BE_AN_ARRAY: 'errors.membersMustBeAnArray',
  TAG_IDS_MUST_BE_ARRAY: 'errors.tagIdsMustBeArray',
  PASSWORD_FIELD_REQUIRED: 'errors.passwordFieldRequired',
  CUSTOM_FIELD_INVALID_CUSTOM_FIELD_ID:
    'errors.customFieldInvalidCustomFieldId',
  CUSTOM_FIELD_NAME_REQUIRED: 'errors.customFieldNameRequired',
  CUSTOM_FIELD_EXISTS: 'errors.customFieldExists',
  CUSTOM_FIELD_TYPE_REQUIRED: 'errors.customFieldTypeRequired',
  INVALID_CUSTOM_FIELD_TYPE: 'errors.invalidCustomFieldType',
  CUSTOM_FIELD_INVALID_OPTIONS_ARRAY: 'errors.customFieldInvalidOptionsArray',
  CUSTOM_FIELD_INVALID_SOURCE: 'errors.customFieldInvalidSource',
  FOLDER_EXISTS: 'errors.folderExists',
  PARENT_FOLDER_NOT_EXISTS: 'errors.parentFolderNotExists',
  MULTIPLE_FOLDERS_FOR_CASE: 'errors.multipleFoldersForCase',
  CASE_NOT_FOUND: 'errors.caseNotFound',
  AUTH_TOKEN_NOT_FOUND: 'errors.authTokenNotFound',
  REFRESH_TOKEN_EXPIRED: 'errors.refreshTokenExpired',
  INVALID_ENTITY_TYPE: 'errors.invalidEntityType',
  INVALID_CHART_TYPE: 'errors.invalidChartType',
  INVALID_CHART_ID: 'errors.invalidChartId',
  CANNOT_REMOVE_OWNER: 'errors.cannotRemoveOwner',
  USER_IS_NOT_OWNER: 'errors.userIsNotOwner',
  UNABLE_TO_REMOVE_ALL_OWNERS: 'errors.unableToRemoveAllOwners',
  CANNOT_UPDATE_SYSTEM_ROLE: 'errors.cannotUpdateSystemRole',
  INTEGRATION_NOT_FOUND: 'errors.integrationNotFound',
  INTEGRATION_CONFIGURED_FOR_PROJECTS:
    'errors.integrationConfiguredForProjects',
  UNSUPPORTED_INTEGRATION: 'errors.unsupportedIntegration',
  UNABLE_TO_REMOVE_INTEGRATION: 'errors.unableToRemoveIntegration',
  DEFECT_NOT_FOUND: 'errors.defectNotFound',
  MESSAGE_CONTENT_REQUIRED: 'errors.messageContentRequired',
  FIELD_VALUE_REQUIRED: 'errors.fieldValueRequired',
  PROMPT_CONTENT_REQUIRED: 'errors.promptContentRequired',
  TEXT_FIELD_REQUIRED: 'errors.textFieldRequired',
  FIELD_NAME_REQUIRED: 'errors.fieldNameRequired',
  NO_RESPONSE_FOUND: 'errors.noResponseFound',
  INVALID_TAG_UID: 'errors.invalidTagUid',
  PLAN_NOT_FOUND: 'errors.planNotFound',
  TEST_EXECUTION_NOT_FOUND: 'errors.testExecutionNotFound',
  INTEGRATION_USER_NOT_FOUND: 'errors.integrationUserNotFound',
  RUN_NOT_FOUND: 'errors.runNotFound',
  CUSTOM_FIELDS_MUST_BE_ARRAY: 'errors.customFieldsMustBeArray',
  CASE_IMPROVEMENT_CUSTOM_FIELD_REQUIRED:
    'errors.caseImprovementCustomFieldRequired',
  CASE_IMPROVEMENT_STEPS_REQUIRED: 'errors.caseImprovementStepsRequired',
  EXTERNAL_API_ERROR: 'errors.externalApiError',
  INTEGRATION_AUTHENTICATION_REQUIRED:
    'errors.integrationAuthenticationRequired',
  INTEGRATION_DELETED: 'errors.integrationDeleted',
  ACCESS_TOKEN_NOT_FOUND: 'errors.accessTokenNotFound',
  MILESTONEUIDS_MUST_BE_ARRAY: 'errors.milestoneuidsMustBeArray',
  RUNUIDS_MUST_BE_ARRAY: 'errors.runuidsMustBeArray',
  PLANUIDS_MUST_BE_ARRAY: 'errors.planuidsMustBeArray',
  PROJECTUIDS_MUST_BE_ARRAY: 'errors.projectuidsMustBeArray',
  MILESTONEUIDS_MUST_BE_NUMBERS: 'errors.milestoneuidsMustBeNumbers',
  RUNUIDS_MUST_BE_NUMBERS: 'errors.runuidsMustBeNumbers',
  PLANUIDS_MUST_BE_NUMBERS: 'errors.planuidsMustBeNumbers',
  PROJECTUIDS_MUST_BE_NUMBERS: 'errors.projectuidsMustBeNumbers',
  LIMIT_MUST_BE_NUMBER: 'errors.limitMustBeNumber',
  OFFSET_MUST_BE_NUMBER: 'errors.offsetMustBeNumber',
  SSO_CONFIG_NOT_ALLOWED: 'errors.ssoConfigNotAllowed',
  UNSUPPORTED_SSO_PROVIDER: 'errors.unsupportedSsoProvider',
  INVALID_FOLDER_QUERY_ENTITY_TYPE: 'errors.invalidFolderQueryEntityType',
  TEST_RUN_ID_REQUIRED_FOLDER_QUERY: 'errors.testRunIdRequiredFolderQuery',
  SCHEDULED_TASK_NOT_FOUND: 'errors.scheduledTaskNotFound',
  SCHEDULED_TASK_ALREADY_RUNNING: 'errors.scheduledTaskAlreadyRunning',
};

export default errorConstants;
