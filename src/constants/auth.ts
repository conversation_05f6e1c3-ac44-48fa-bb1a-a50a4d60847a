import { auth, FgaService } from '@ss-libs/ss-component-auth';

import { NextFunction, Request } from 'express';

import { Tenant } from '@app/models/tenant';
import { tenantManager } from '@app/lib/tenants';
import { Membership } from '@app/models/membership';
import { Handle } from '@app/models/handle';
import permissionsList from './permissions';

/**
 * Authenticates a users
 * @param {*} req
 * @param {*} res
 * @param {*} next
 * @returns
 */
export function authenticateUser(req, res, next) {
  // @TODO - Using authenticateHybrid here precludes allowing management via API
  //         with bearer tokens.
  return auth().authenticateHybrid(req, res, next);
}

/**
 * Authenticates the user making the request and then checks if the user is an admin of the org
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
export function authenticateOrg(req: Request, res, next: NextFunction) {
  auth().authenticateHybrid(req, res, async () => {
    const orgId = <string>req.query.uid;
    if (!orgId) return res.status(403).send('query.uid is required');

    const tenant = await Tenant.query(req.sharedKnexDB)
      .where({
        tenantType: 'org',
        tenantUid: orgId,
      })
      .withGraphFetched('dbServer')
      .first();
    const handle = await Handle.query(req.sharedKnexDB)
      .where({
        ownerType: 'org',
        ownerUid: orgId,
        current: true,
      })
      .first();

    const conn = await tenantManager.loadTenant(tenant);
    const fga = new FgaService(conn.fga);

    const subject = `user:${req.locals.user.uid}`;
    const object = `org:${orgId}`;

    const [members] = (await Membership.query(req.sharedKnexDB)
      .where({ accountType: 'org', accountUid: orgId, deletedAt: null })
      .select({ count: req.sharedKnexDB.raw('count("userUid")') })) as any[];

    const allowed = await fga.check(subject, object, 'limited_billing', { current_project: '' });

    if (!allowed) return res.status(403).send('Forbidden');
    req.locals.org = <any>{
      uid: orgId,
      // @TODO: split this between allowed size and current size
      size: +members.count,
    };

    req.locals.ssCustomer = {
      handle: handle.name,
      uid: orgId,
      name: `org_${orgId}`,
    };
    return next();
  });
}

export const ROOT_PERMISSION = 'root';
// eslint-disable-next-line @typescript-eslint/naming-convention
const _permissions = [
  'owner', // super permission
];

const retrievePermission = (list) => {
  list.forEach((action) => {
    if (action.value) _permissions.push(action.value);
    if (action.actions) retrievePermission(action.actions);
  });
};

permissionsList.forEach((permission) => {
  retrievePermission(permission.actions);
});

// TODO - enforce on pages where these aren't the primary resource
export type Permission = (typeof _permissions)[number];

export const permissions: Record<Permission, string> = _permissions.reduce(
  (obj, p) => {
    obj[p] = p;
    return obj;
  },
  <any>{},
);
Object.freeze(permissions); // ensure the permissions object is immutable

export const excludedProjectPermissions = [
  'no_billing',
  'limited_billing',
  'full_billing',
];

export interface Role {
  name: string;
  slug: string;
  description: string;
  permissions: Permission[];
  system?: boolean;
}

export const presetRoles: Role[] = [
  {
    name: 'Owner',
    slug: 'owner',
    description: 'Controls everything that can be controllers by a user',
    permissions: ['owner'],
    system: true,
  },
  {
    name: 'No Access',
    slug: 'no_access',
    description: 'This role has no access to any resources.',
    permissions: [],
    system: true,
  },
  {
    name: 'Account Admin',
    slug: 'account_admin',
    description: 'Controls platform settings, user roles, and projects',
    permissions: _permissions.filter(
      (p) => !['limited_billing', 'no_billing', 'owner'].includes(p),
    ),
  },
  {
    name: 'QA Lead',
    slug: 'qa_lead',
    description: 'Leads specific projects, manages test plans',
    permissions: [
      'read_member',
      'read_role',
      'write_role',
      'read_dashboard',
      'write_dashboard',
      'delete_dashboard',
      'read_report',
      'write_report',
      'delete_report',
      'write_tag',
      'delete_tag',
      'write_custom_field',
      'delete_custom_field',
      'write_step',
      'delete_step',
      'no_billing',
      'write_template',
      'delete_template',
      'read_integration',
      'write_integration',
      'delete_integration',
      'read_entity',
      'write_entity',
      'delete_entity',
      'read_activity',
      'write_activity',
      'delete_activity',
      'write_defect',
      'read_defect',
      'delete_defect',
    ],
  },
  {
    name: 'Tester',
    slug: 'tester',
    description: 'Executes tests on assigned projects',
    permissions: [
      'read_member',
      'read_dashboard',
      'write_dashboard',
      'read_report',
      'read_entity',
      'read_activity',
      'write_activity',
      'write_entity',
      'write_step',
      'no_billing',
      'read_integration',
      'write_defect',
      'read_defect',
      'delete_defect',
    ],
  },
  {
    name: 'Client',
    slug: 'client',
    description: 'Tracks project progress with limited access',
    permissions: [
      'read_dashboard',
      'write_dashboard',
      'read_report',
      'read_entity',
      'read_activity',
      'write_step',
      'no_billing',
    ],
  },
  {
    name: 'Biling',
    slug: 'billing',
    description: 'manages billing subscription and financial details',
    permissions: ['read_report', 'read_role', 'write_role', 'full_billing'],
  },
];

export const projectRoles = presetRoles.filter((role) => role.slug !== 'owner' && role.slug !== 'account_admin' && role.slug !== 'billing')
  .map((role) => ({
    ...role,
    permissions: role
      .permissions
      .filter((permission) => !excludedProjectPermissions.includes(permission)),
  }));
