import dayjs from 'dayjs';

// project Id and folderId need to be decided on
const cases = [
  {
    name: 'User Login Functionality',
    customFields: {
      expectedResultByStep: false,
      expectedResult:
        'User is redirected to the dashboard. A welcome message is displayed.',
    },
    steps: [
      {
        id: 1,
        description: 'Navigate to the login page',
        children: [],
        shared: false,
        title: 'Page Load',
      },
      {
        id: 2,
        description: 'Enter a valid username',
        children: [],
        shared: false,
        title: 'Add Username',
      },
      {
        id: 3,
        description: 'Enter the correct password',
        children: [],
        shared: false,
        title: 'Add Pasword',
      },
      {
        id: 4,
        description: 'Click the "Login" button',
        children: [],
        shared: false,
        title: 'Attempt Login',
      },
    ],
  },
  {
    name: 'Forgot Password Functionality',
    customFields: {
      expectedResultByStep: false,
      expectedResult:
        'User receives forgot password email formatted appropriately. CTA redirects to the reset password page',
    },
    steps: [
      {
        id: 1,
        description: 'Navigate to the forgot password page',
        children: [],
        shared: false,
        title: 'Page Load',
      },
      {
        id: 2,
        description: 'Enter a valid email address',
        children: [],
        shared: false,
        title: 'Add Email',
      },
      {
        id: 3,
        description: 'Click the "SEND EMAIL" button',
        children: [],
        shared: false,
        title: 'Request Reset Password Mail',
      },
    ],
  },
];

const run = {
  name: 'Test Run for Release v1.0.0',
  dueAt: () => dayjs().add(2, 'days').toDate(),
};

const milestone = {
  name: 'Sprint 1 - Backend Feature Completion',
  dueAt: () => dayjs().add(2, 'days').endOf('week').toDate(),
};

const projectDemo = {
  cases,
  run,
  milestone,
};

export default projectDemo;
