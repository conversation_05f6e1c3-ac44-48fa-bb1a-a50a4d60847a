import logger from '@app/config/logger';
import { TESTRAIL_SYSTEM_FIELDS, TESTRAIL_TYPES } from '@app/constants/integration';
import { decryptData } from '@app/utils/decryptData';
import axios from 'axios';
import { IntegrationService } from 'integrations';
import { Knex } from 'knex';
import crypto from 'crypto';
import Bottleneck from 'bottleneck';
import { transformError } from '@app/utils/transformIntegrationError';

const limiter = new Bottleneck({
  maxConcurrent: 5, // maximum 5 concurrent requests
  reservoir: 180, // maximum 180 requests per minute
  reservoirRefreshAmount: 180,
  reservoirRefreshInterval: 60 * 1000,
});

// Directly schedule requests with Bottleneck
const rateLimitedRequest = async (config) => limiter.schedule(() => axios(config));

export class TestRailService implements IntegrationService {
  private async fetchProject(params:any, authHeader: any) {
    try {
      const response = await rateLimitedRequest({
        method: 'get',
        url: `https://${params.url}/index.php?/api/v2/get_project/${params.projectId}`,
        headers: authHeader.headers,
      });
      const projectData = {
        source: params.service,
        sourceId: response.data.id.toString(),
        entityType: 'integration',
        entityUid: params.uid,
        customFields: {
          sourceType: 'project',
          name: response.data.name,
          url: response.data.url,
        },
      };
      return {
        success: true,
        data: projectData,
        // Dummy pagination to prevent infinite scroll as we only fetch one project
        pagination: {
          hasMoreData: false,
          startAt: 0,
          maxResults: 1,
          page: 1,
        },
      };
    } catch (error) {
      logger.error('Error fetching project from TestRail:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  private async fetchTemplates(params:any, authHeader:any) {
    try {
      const templatesResponse = await rateLimitedRequest({
        method: 'get',
        url: `https://${params.url}/index.php?/api/v2/get_templates/${params.projectId}`,
        headers: authHeader.headers,
      });
      const templates = templatesResponse.data;
      // Map template IDs to their corresponding custom fields
      const processedTemplates = templates.map((template) => {
        const fieldsForTemplate = params.caseFields.filter((field) => field.include_all || field.template_ids.includes(template.id));

        // Map custom fields to a structured format
        const customFieldsMapped = fieldsForTemplate.map((field) => {
          const { options } = field.configs[0];
          const items = options.items ? options.items.split('\n').map((item) => {
            const [key, value] = item.split(',');
            return { key, value };
          }) : [];
          return {
            id: crypto.randomUUID(),
            name: field.label,
            dataType: TESTRAIL_TYPES[field.type_id],
            options: items.map((item) => item.value),
            isRequired: options.is_required,
            defaultValue: items.find((item) => item.key === options.default_value)?.value || options.default_value,
          };
        });

        return {
          id: template.id,
          name: template.name,
          isDefault: template.is_default,
          customFields: customFieldsMapped,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
      });
      return {
        success: true,
        data: processedTemplates,
        // Dummy pagination to prevent infinite scroll as there is no way to paginate through templates
        pagination: {
          hasMoreData: false,
          startAt: 0,
          maxResults: processedTemplates.length,
          page: 1,
        },
      };
    } catch (error) {
      logger.error('Error fetching templates from TestRail:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  private mapStepsField = (stepsSeparated: any[]) => {
    const steps = stepsSeparated.map((step, index) => {
      const mappedStep = {
        id: index + 1,
        title: `Step ${index + 1}`,
        shared: Boolean(step.shared_step_id),
        children: [] as any[],
        description: step.content,
        expectedResult: step.expected,
      };
      if (step.shared_step_id) {
        return {
          ...mappedStep,
          sharedStepUid: step.shared_step_id,
        };
      }
      return mappedStep;
    });

    return steps;
  };

  private async mapTestCaseCustomFields(testCase:any, allCaseFields:any, projectId:any) {
    const fieldsForTemplate = allCaseFields.filter(
      (field) => field.include_all || field.template_ids.includes(testCase.template_id) || field.configs[0].project_id === projectId,
    );

    return fieldsForTemplate.map((field) => {
      const config = field.configs.find((config) => config.context.is_global === true || config.context.project_ids.includes(Number(projectId)));
      if (!config) return null;
      // skip steps field as its processed separately
      if (field.type_id === 10) return null;
      const { options } = config;

      const items = options.items
        ? options.items.split('\n').map((line) => {
          const [key, val] = line.split(',');
          return { key: key?.trim(), value: val?.trim() };
        })
        : [];

      const rawValue = testCase[field.system_name];

      // Handle BDD scenarios specially
      let mappedValue = rawValue;
      if (field.system_name === 'custom_testrail_bdd_scenario' && rawValue) {
        mappedValue = this.formatBddScenarios(rawValue);
      } else if (field.type_id === 12 && rawValue) {
        mappedValue = rawValue.map((keyValue) => items.find((item) => item.key === String(keyValue))?.value ?? keyValue);
      } else {
        mappedValue = items.find((item) => item.key === String(rawValue))?.value ?? rawValue;
      }
      const mappedDefault = items.find((item) => item.key === options.default_value)?.value
          ?? options.default_value
          ?? '';

      return {
        id: crypto.randomUUID(),
        name: field.label,
        dataType: TESTRAIL_TYPES[field.type_id],
        options: items.map((item) => item.value),
        isRequired: options.is_required,
        defaultValue: mappedDefault,
        ...(TESTRAIL_TYPES[field.type_id] === 'date' && { default_date: options.default_date }),
        value: mappedValue,
      };
    }).filter((field) => field !== null);
  }

  private formatBddScenarios = (bddScenarioString: string): string => {
    try {
      // Parse the JSON string into an array of scenarios
      const scenarios = JSON.parse(bddScenarioString);

      // Transform into numbered list
      return scenarios
        .map((scenario: { content: string }, index: number) => `${index + 1}. ${scenario.content.trim()}`)
        .join('\n\n');
    } catch (error) {
      // Return original string if parsing fails
      return bddScenarioString;
    }
  };

  private async fetchConfigurations(params: any, authHeader: any) {
    try {
      const response = await rateLimitedRequest({
        method: 'get',
        url: `https://${params.url}/index.php?/api/v2/get_configs/${params.projectId}`,
        headers: authHeader.headers,
      });
      const configurations = response.data;
      const configurationsData = configurations.map((config) => ({
        name: config.name,
        source: params.service,
        externalId: config.id.toString(),
        integrationUid: params.uid,
        options: config.configs.map((option) => ({
          name: option.name,
          externalId: option.id.toString(),
          source: params.service,
          integrationUid: params.uid,
        })),
      }));
      return {
        success: true,
        data: configurationsData,
        // Dummy pagination to prevent infinite scroll as there is no way to paginate through configurations
        pagination: {
          hasMoreData: false,
          startAt: 0,
          maxResults: configurationsData.length,
          page: 1,
        },
      };
    } catch (error) {
      logger.error('Error fetching configurations from TestRail:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  private async fetchCases(params: any, authHeader: any, pagination: any, tagMappings: any) {
    try {
      const { suiteId } = params;
      const offset = pagination.startAt;
      const limit = pagination.maxResults; // TestRail's maximum limit
      let urlString = `https://${params.url}/index.php?/api/v2/get_cases/${params.projectId}&suite_id=${suiteId}&offset=${offset}&limit=${limit}`;
      if (params.syncedAt) {
        const unixTimestamp = Math.floor(new Date(params.syncedAt).getTime() / 1000);
        urlString += `&updated_after=${unixTimestamp}`;
      }
      const response = await rateLimitedRequest({
        method: 'get',
        url: urlString,
        headers: authHeader.headers,
      });
      const { data } = response;
      const casesData = await Promise.all(
        data.cases.map(async (testCase) => {
          const templateFields = await this.mapTestCaseCustomFields(testCase, params.caseFields, params.projectId);
          const steps = testCase.custom_steps_separated
            ? this.mapStepsField(testCase.custom_steps_separated)
            : [];

          return {
            externalId: testCase.id.toString(),
            source: params.service,
            name: testCase.title,
            link: `https://${params.url}/index.php?api/v2/get_case/${testCase.id}`,
            steps,
            customFields: {
              templateFields,
              raw: testCase,
              integrationUid: params.uid,
              tags: testCase.type_id
                ? [tagMappings.tagMappings.case[testCase.type_id].tagUid]
                : [],
            },
            priority: tagMappings.priorityMappings[testCase.priority_id] || undefined,
            status: tagMappings.statusMappings[testCase.status_id] || undefined,
            version: 1,
            testTemplateUid: testCase.template_id,
            active: testCase.is_deleted === 0,
            externalCreatedAt: testCase.created_on
              ? new Date(testCase.created_on * 1000).toISOString()
              : new Date().toISOString(),
            externalUpdatedAt: testCase.updated_on
              ? new Date(testCase.updated_on * 1000).toISOString()
              : new Date().toISOString(),
            syncedAt: new Date().toISOString(),
          };
        }),
      );
      return {
        success: true,
        data: casesData,
        pagination: {
          hasMoreData: data._links?.next !== null && data.size === limit,
          startAt: offset + limit,
          maxResults: limit,
          total: data.size,
        },
      };
    } catch (error) {
      logger.error('Error fetching cases from TestRail:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  private async fetchPlans(params:any, authHeader: any, pagination:any) {
    try {
      const offset = pagination.startAt;
      const limit = pagination.maxResults; // TestRail's maximum limit
      const urlString = `https://${params.url}/index.php?/api/v2/get_plans/${params.projectId}&offset=${offset}&limit=${limit}`;
      const response = await rateLimitedRequest({
        method: 'get',
        url: urlString,
        headers: authHeader.headers,
      });
      const { data } = response;
      let stopPagination = false;
      const plans = data.plans.map((plan) => {
        if (params.syncedAt) {
          const syncTime = calculateSyncTime(params.syncDuration);
          if (new Date(plan.created_on) <= syncTime) {
            stopPagination = true;
          }
        }
        return {
          externalId: plan.id.toString(),
          source: 'testrail',
          name: plan.name,
          description: String(plan.description),
          integrationUid: params.uid,
          customFields: {
            link: `https://${params.url}/index.php?api/v2/get_plan/${plan.id}`,
            raw: plan,
            milestoneId: plan.milestone_id,
            tags: [],
            archived: false,
            configurations: 'Browsers',
          },
          externalCreatedAt: plan.created_on ? new Date(plan.created_on * 1000).toISOString() : null,
          externalUpdatedAt: plan.updated_on ? new Date(plan.updated_on * 1000).toISOString() : null,
          syncedAt: new Date().toISOString(),
        };
      });
      return {
        success: true,
        data: plans,
        pagination: {
          hasMoreData: stopPagination ? false : (data._links?.next !== null && data.size === limit),
          startAt: pagination.startAt + data.size,
          maxResults: pagination.maxResults,
          page: pagination.page + 1,
        },
      };
    } catch (error) {
      logger.error(`Error fetching TestRail plans for project ${params.projectId}:`, error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  private async fetchPlanEntries(params:any, authHeader: any) {
    try {
      const response = await rateLimitedRequest({
        method: 'get',
        url: `https://${params.url}/index.php?/api/v2/get_plan/${params.planId}`,
        headers: authHeader.headers,
      });
      const { entries } = response.data;
      let runs = [];
      entries.forEach((entry:any) => {
        runs = [...runs, ...entry.runs];
      });
      const runsData = runs.map((run) => ({
        name: run.name,
        externalCreatedAt: run.created_on ? new Date(run.created_on * 1000).toISOString() : null,
        externalUpdatedAt: run.updated_on ? new Date(run.updated_on * 1000).toISOString() : null,
        source: 'testrail',
        integrationUid: params.uid,
        externalId: run.id.toString(),
        customFields: {
          externalMilestoneId: run.milestone_id,
          externalPlanId: run.plan_id,
          link: `https://${params.url}/index.php?api/v2/get_run/${run.id}`,
          syncedAt: new Date().toISOString(),
          entryId: run.entry_id,
          configs: run.config_ids,
        },
      }));
      return {
        success: true,
        data: runsData,
        // Dummy pagination to prevent infinite scroll as there is no way to paginate through enteries inside a plan
        pagination: {
          hasMoreData: false,
          startAt: 0,
          maxResults: runsData.length,
          page: 1,
        },
      };
    } catch (error) {
      logger.error(`Error fetching TestRail plans for project ${params.projectId}:`, error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  private async fetchRuns(params:any, authHeader: any, pagination:any) {
    try {
      const offset = pagination.startAt;
      const limit = pagination.maxResults;
      const urlString = `https://${params.url}/index.php?/api/v2/get_runs/${params.projectId}&offset=${offset}&limit=${limit}`;
      const response = await rateLimitedRequest({
        method: 'get',
        url: urlString,
        headers: authHeader.headers,
      });
      const { data } = response;
      let stopPagination = false;
      const runs = data.runs.map((run) => {
        if (params.syncedAt) {
          const syncTime = calculateSyncTime(params.syncDuration);
          if (new Date(run.created_on) <= syncTime) {
            stopPagination = true;
          }
        }
        return {
          name: run.name,
          externalCreatedAt: run.created_on ? new Date(run.created_on * 1000).toISOString() : null,
          externalUpdatedAt: run.updated_on ? new Date(run.updated_on * 1000).toISOString() : null,
          source: 'testrail',
          integrationUid: params.uid,
          externalId: run.id.toString(),
          customFields: {
            link: `https://${params.url}/index.php?api/v2/get_run/${run.id}`,
            externalMilestoneId: run.milestone_id,
            externalPlanId: run.plan_id,
            configs: run.config_ids,
          },
        };
      });
      return {
        success: true,
        data: runs,
        pagination: {
          hasMoreData: stopPagination ? false : (data._links?.next !== null && data.size === limit),
          startAt: pagination.startAt + data.size,
          maxResults: pagination.maxResults,
          page: pagination.page + 1,
        },
      };
    } catch (error) {
      logger.error(`Error fetching TestRail runs for project ${params.projectId}:`, error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  private async fetchMilestones(params:any, authHeader: any, pagination:any) {
    try {
      const offset = pagination.startAt;
      const limit = pagination.maxResults;
      const urlString = `https://${params.url}/index.php?/api/v2/get_milestones/${params.projectId}&offset=${offset}&limit=${limit}`;
      const response = await rateLimitedRequest({
        method: 'get',
        url: urlString,
        headers: authHeader.headers,
      });
      const { data } = response;
      const transformMilestone = (milestone: any, parentId: number | null = null) => ({
        projectUid: Number(params.projectUid),
        externalId: milestone.id.toString(),
        source: 'testrail',
        integrationUid: params.uid,
        name: milestone.name || '',
        parentUid: parentId,
        customFields: {
          dueAt: milestone.due_on ? new Date(milestone.due_on * 1000).toISOString() : null,
          startDate: milestone.start_on ? new Date(milestone.start_on * 1000).toISOString() : null,
          progress: 0,
          description: milestone.description,
          tagUids: [],
          syncedAt: new Date().toISOString(),
          link: milestone.url || `https://${params.url}/index.php?api/v2/get_milestone/${milestone.id}`,
        },
        completedAt: milestone.completed_on ? new Date(milestone.completed_on * 1000).toISOString() : null,
        archivedAt: milestone.is_completed ? new Date(milestone.completed_on * 1000).toISOString() : undefined,
        syncedAt: new Date().toISOString(),
      });
      const allMilestones = [];
      data.milestones.forEach((parentMilestone) => {
        allMilestones.push(transformMilestone(parentMilestone));
        if (parentMilestone.milestones && Array.isArray(parentMilestone.milestones)) {
          parentMilestone.milestones.forEach((submilestone) => {
            allMilestones.push(transformMilestone(submilestone, parentMilestone.id));
          });
        }
      });
      return {
        success: true,
        data: allMilestones,
        pagination: {
          hasMoreData: data._links?.next !== null && data.size === limit,
          startAt: pagination.startAt + data.size,
          maxResults: pagination.maxResults,
          page: pagination.page + 1,
        },
      };
    } catch (error) {
      logger.error(`Error fetching TestRail milestones for project ${params.projectId}:`, error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  private async fetchTestExecutions(params:any, authHeader: any, pagination:any, tagMappings: any) {
    try {
      const offset = pagination.startAt;
      const limit = pagination.maxResults;
      const urlString = `https://${params.url}/index.php?/api/v2/get_tests/${params.runId}&offset=${offset}&limit=${limit}`;
      const response = await rateLimitedRequest({
        method: 'get',
        url: urlString,
        headers: authHeader.headers,
      });
      const { data } = response;
      const tests = data.tests.map((test) => ({
        externalId: test.id.toString(),
        source: 'testrail',
        link: `https://${params.url}/index.php?api/v2/get_case/${test.id}`,
        priority: tagMappings.priorityMappings[test.priority_id] || undefined,
        status: tagMappings.statusMappings[test.status_id] || undefined,
        caseId: test.case_id,
        customFields: {
          raw: test,
          integrationUid: params.uid,
        },
      }));
      return {
        success: true,
        data: tests,
        pagination: {
          hasMoreData: data._links?.next !== null && data.size === limit,
          startAt: pagination.startAt + data.size,
          maxResults: pagination.maxResults,
          page: pagination.page + 1,
        },
      };
    } catch (error) {
      logger.error('Error fetching TestRail test executions:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  private async fetchTestResults(params:any, authHeader: any, pagination:any, tagMappings: any) {
    try {
      const offset = pagination.startAt;
      const limit = pagination.maxResults;
      const urlString = `https://${params.url}/index.php?/api/v2/get_results/${params.testId}&offset=${offset}&limit=${limit}`;
      const response = await rateLimitedRequest({
        method: 'get',
        url: urlString,
        headers: authHeader.headers,
      });
      const { data } = response;
      let stopPagination = false;
      const results = data.results.map((result) => {
        if (params.syncedAt) {
        // if the result was created before the sync time, stop pagination as results are not updated so no need to fetch more
          if (new Date(result.created_on) <= new Date(params.syncedAt)) {
            stopPagination = true;
          }
        }
        return {
          externalId: result.id.toString(),
          status: tagMappings.statusMappings[result.status_id] || undefined,
          comment: result.comment ? `<p>${result.comment}</p>` : undefined,
          source: 'testrail',
          name: result.title,
          link: `https://${params.url}/index.php?api/v2/get_result/${result.id}`,
          customFields: {
            raw: result,
            integrationUid: params.uid,
          },
        };
      });
      return {
        success: true,
        data: results,
        pagination: {
          hasMoreData: stopPagination ? false : (data._links?.next !== null && data.size === limit),
          startAt: pagination.startAt + data.size,
          maxResults: pagination.maxResults,
          page: pagination.page + 1,
        },
      };
    } catch (error) {
      logger.error('Error fetching TestRail test results:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  private async getCustomFields(params:any, authHeader:any) {
    try {
      const fieldsResponse = await rateLimitedRequest({
        method: 'get',
        url: `https://${params.url}/index.php?/api/v2/get_case_fields`,
        headers: authHeader.headers,
      });
      // filter out fields that are not global or belong to the project
      const customFields = [...fieldsResponse.data, ...TESTRAIL_SYSTEM_FIELDS]
        .map((field:any) => {
          const config = field.configs.find((config) => config.context.is_global === true || config.context.project_ids.includes(Number(params.projectId)));
          if (!config) return null;
          // skip steps field as its processed separately
          if (field.type_id === 10) return null;
          const { options } = config;

          const items = options.items
            ? options.items.split('\n').map((line) => {
              const [key, val] = line.split(',');
              return { key: key?.trim(), value: val?.trim() };
            })
            : [];

          return {
            name: field.name,
            dataType: TESTRAIL_TYPES[field.type_id],
            options: items.map((item) => item.value),
            source: 'testrail',
            externalId: field.id,
          };
        }).filter((field:any) => field !== null);
      return {
        success: true,
        data: customFields,
        // Dummy pagination to prevent infinite scroll as there is no way to paginate through custom fields
        pagination: {
          hasMoreData: false,
          startAt: 0,
          maxResults: customFields.length,
          page: 1,
        },
      };
    } catch (error) {
      logger.error('Error fetching custom fields from TestRail:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  private async getFolders(params: any, authHeader: any) {
    try {
      // Fetch suites
      const suitesResponse = await rateLimitedRequest({
        method: 'get',
        url: `https://${params.url}/index.php?/api/v2/get_suites/${params.projectId}`,
        headers: authHeader.headers,
      });

      // Map suites data
      const suites = suitesResponse.data.map((suite) => ({
        name: suite.name === 'Master'
          ? `testrail-${suite.id}-${params.projectId}`
          : `${suite.name}-${suite.id}-${params.projectId}`,
        description: String(suite.description),
        systemType: 'folder',
        slug: suite.name.toLowerCase().replace(/ /g, '-'),
        externalId: suite.id.toString(),
        source: 'testrail',
        integrationUid: params.uid,
        customFields: {
          type: 'suite',
          link: `https://${params.url}/index.php?api/v2/get_suite/${suite.id}`,
        },
      }));
      return {
        success: true,
        data: suites,
        // Dummy pagination to prevent infinite scroll as there is no way to paginate through suites
        pagination: {
          hasMoreData: false,
          startAt: 0,
          maxResults: 0,
          page: 1,
        },
      };
    } catch (error) {
      logger.error('Error fetching TestRail folders:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  private async getSubFolders(params: any, authHeader: any, pagination: any) {
    try {
      const offset = pagination.startAt;
      const limit = pagination.maxResults;
      const sectionsResponse = await rateLimitedRequest({
        method: 'get',
        url: `https://${params.url}/index.php?/api/v2/get_sections/${params.projectId}&suite_id=${params.suiteId}&offset=${offset}&limit=${limit}`,
        headers: authHeader.headers,
      });

      const sortedSections = [...sectionsResponse.data.sections].sort((a, b) => a.depth - b.depth);

      // Create a map of sections by their IDs
      const sections = sortedSections.map((section) => ({
        name: section.name,
        description: String(section.description),
        systemType: 'folder',
        slug: section.name.toLowerCase().replace(/ /g, '-'),
        externalId: section.id.toString(),
        source: 'testrail',
        integrationUid: params.uid,
        customFields: {
          type: 'section',
          link: `https://${params.url}/index.php?api/v2/get_section/${section.id}`,
          parentId: section.parent_id,
          depth: section.depth,
          suiteId: params.suiteId,
        },
      }));

      return {
        success: true,
        data: sections,
        pagination: {
          hasMoreData: sectionsResponse.data._links?.next !== null && sectionsResponse.data.size === limit,
          startAt: pagination.startAt + sectionsResponse.data.size,
          maxResults: pagination.maxResults,
          page: pagination.page + 1,
        },
      };
    } catch (error) {
      logger.error('Error fetching TestRail subfolders:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  private async getSharedSteps(params:any, authHeader:any, pagination:any) {
    try {
      const offset = pagination.startAt;
      const limit = pagination.maxResults;
      const response = await rateLimitedRequest({
        method: 'get',
        url: `https://${params.url}/index.php?/api/v2/get_shared_steps/${params.projectId}&offset=${offset}&limit=${limit}`,
        headers: authHeader.headers,
      });
      let stopPagination = false;
      const sharedSteps = response.data.shared_steps.map((step:any) => {
        if (params.syncedAt) {
          const syncTime = calculateSyncTime(params.syncDuration);
          if (new Date(step.created_on) <= syncTime) {
            stopPagination = true;
          }
        }
        return {
          name: step.title,
          externalId: step.id.toString(),
          source: 'testrail',
          link: `https://${params.url}/index.php?api/v2/get_shared_step/${step.id}`,
          version: 1,
          active: true,
          customFields: {
            raw: step,
            integrationUid: params.uid,
          },
          steps: step.custom_steps_separated.map((step:any, index:number) => ({
            title: `${'Step'}-${index + 1}`,
            children: [],
            description: step.content,
            expectedResult: step.expected,
          })),
          externalCreatedAt: new Date(step.created_on * 1000).toISOString(),
          externalUpdatedAt: new Date(step.updated_on * 1000).toISOString(),
        };
      });
      return {
        success: true,
        data: sharedSteps,
        pagination: {
          hasMoreData: stopPagination ? false : (response.data._links?.next !== null && response.data.size === limit),
          startAt: pagination.startAt + response.data.size,
          maxResults: pagination.maxResults,
          page: pagination.page + 1,
        },
      };
    } catch (error) {
      logger.error('Error fetching TestRail shared steps:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  private async fetchTestResultsByRun(params:any, authHeader:any, pagination:any, tagMappings:any) {
    try {
      const offset = pagination.startAt;
      const limit = pagination.maxResults;
      const urlString = `https://${params.url}/index.php?/api/v2/get_results_for_run/${params.runId}&offset=${offset}&limit=${limit}`;
      const response = await rateLimitedRequest({
        method: 'get',
        url: urlString,
        headers: authHeader.headers,
      });
      const { data } = response;
      const results = data.results.map((result) => ({
        externalId: result.id.toString(),
        status: tagMappings.statusMappings[result.status_id] || undefined,
        comment: result.comment ? `<p>${result.comment}</p>` : undefined,
        source: 'testrail',
        name: result.title,
        link: `https://${params.url}/index.php?api/v2/get_result/${result.id}`,
        customFields: {
          raw: result,
          integrationUid: params.uid,
          testId: result.test_id,
        },
      }));
      return {
        success: true,
        data: results,
        pagination: {
          hasMoreData: data._links?.next !== null && data.size === limit,
          startAt: pagination.startAt + data.size,
          maxResults: pagination.maxResults,
          page: pagination.page + 1,
        },
      };
    } catch (error) {
      logger.error('Error fetching TestRail test results by run:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  private async fetchCaseFields(params:any, authHeader:any) {
    try {
      const response = await rateLimitedRequest({
        method: 'get',
        url: `https://${params.url}/index.php?/api/v2/get_case_fields`,
        headers: authHeader.headers,
      });
      return {
        success: true,
        data: [...response.data, ...TESTRAIL_SYSTEM_FIELDS],
        // Dummy pagination to prevent infinite scroll as there is no way to paginate through case fields
        pagination: {
          hasMoreData: false,
          startAt: 0,
          maxResults: response.data.length,
          page: 1,
        },
      };
    } catch (error) {
      logger.error('Error fetching TestRail case fields:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  prepareAuthHeader = async (integrationToken, tenantDb: Knex) => {
    const accessToken = await decryptData('integration', integrationToken.accessToken, 'accessToken', integrationToken.uid, tenantDb);
    return {
      headers: {
        Authorization: `Basic ${accessToken}`,
        'Content-Type': 'application/json',
      },
    };
  };

  fetchUserData = async (authHeader: any, url?: string): Promise<any> => {
    const apiUrl = `https://${url}/index.php?/api/v2/get_current_user`;
    try {
      const response = await rateLimitedRequest({
        method: 'get',
        url: apiUrl,
        headers: authHeader.headers,
      });
      // Transform the TestRail response to match expected format
      return {
        success: true,
        data: {
          source: 'testrail',
          sourceId: response.data.id.toString(),
          entityType: 'org',
          customFields: {
            sourceType: 'user',
          },
        },
      };
    } catch (error) {
      logger.error('Error fetching projects from testrail:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  };

  fetchTags = async (params, authHeader) => {
    try {
      const numberToHex = (color:number) => `#${color.toString(16).padStart(6, '0')}`;
      const priorityResponse = await rateLimitedRequest({
        method: 'get',
        url: `https://${params.url}/index.php?/api/v2/get_priorities`,
        headers: authHeader.headers,
      });
      const statusResponse = await rateLimitedRequest({
        method: 'get',
        url: `https://${params.url}/index.php?/api/v2/get_statuses`,
        headers: authHeader.headers,
      });
      const tagsResponse = await rateLimitedRequest({
        method: 'get',
        url: `https://${params.url}/index.php?/api/v2//get_case_types`,
        headers: authHeader.headers,
      });
      return {
        success: true,
        data: {
          priority: priorityResponse.data.map((priority) => ({
            id: priority.id,
            name: priority.name,
            color: priority.color || '#000000', // testrail does not return color for priorities, using black as default
          })),
          status: statusResponse.data.map((status) => ({
            id: status.id,
            name: status.name,
            color: numberToHex(status.color_medium),
            isFinal: status.is_final,
          })),
          tags: tagsResponse.data.map((tag) => ({
            id: tag.id,
            name: tag.name,
          })),
        },
      };
    } catch (error) {
      logger.error('Error fetching TestRail tags:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  };

  getProjects = async (params, authHeader) => {
    try {
      let allProjects = [];
      let offset = 0;
      const limit = 250; // TestRail's default limit
      let hasMore = true;

      while (hasMore) {
        const response = await rateLimitedRequest({
          method: 'get',
          url: `https://${params.url}/index.php?/api/v2/get_projects&offset=${offset}&limit=${limit}`,
          headers: authHeader.headers,
        });

        const { projects, size, links } = response.data;

        allProjects = allProjects.concat(
          projects.map((project) => ({
            projectId: project.id.toString(),
            projectKey: project.name,
            projectName: project.name,
          })),
        );
        if (links) {
          // Check if there are more projects to fetch
          hasMore = links.next !== null;
          offset += size;
        } else {
          hasMore = false;
        }
      }

      return {
        success: true,
        data: allProjects,
        // Dummy pagination to prevent infinite scroll
        pagination: {
          hasMoreData: false,
          startAt: 0,
          maxResults: allProjects.length,
          page: 1,
        },
      };
    } catch (error) {
      logger.error('Error fetching projects from TestRail:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  };

  fetchEntities = async (
    entityTypes:string,
    authHeader:string,
    params:any,
    pagination:any,
    tagMappings?: any,
  ) => {
    switch (entityTypes) {
      case 'projects':
        return this.fetchProject(params, authHeader);
      case 'milestones':
        return this.fetchMilestones(params, authHeader, pagination);
      case 'plans':
        return this.fetchPlans(params, authHeader, pagination);
      case 'runs':
        return this.fetchRuns(params, authHeader, pagination);
      case 'cases':
        return this.fetchCases(params, authHeader, pagination, tagMappings);
      case 'testResults':
        return this.fetchTestResults(params, authHeader, pagination, tagMappings);
      case 'testExecutions':
        return this.fetchTestExecutions(params, authHeader, pagination, tagMappings);
      case 'templates':
        return this.fetchTemplates(params, authHeader);
      case 'customFields':
        return this.getCustomFields(params, authHeader);
      case 'folders':
        return this.getFolders(params, authHeader);
      case 'subFolders':
        return this.getSubFolders(params, authHeader, pagination);
      case 'sharedSteps':
        return this.getSharedSteps(params, authHeader, pagination);
      case 'planEntries':
        return this.fetchPlanEntries(params, authHeader);
      case 'configurations':
        return this.fetchConfigurations(params, authHeader);
      case 'testResultsByRun':
        return this.fetchTestResultsByRun(params, authHeader, pagination, tagMappings);
      case 'caseFields':
        return this.fetchCaseFields(params, authHeader);
      default:
        return null;
    }
  };

  createEntity = async (): Promise<any> => {
    throw new Error('Not implemented');
  };

  // private async updateCase(url: string, authHeader: any, body: any) {
  //   try {
  //     const updateUrl = url.replace('get_case', 'update_case');
  //     const response = await rateLimitedAxios.post(updateUrl, body, authHeader);
  //     return {
  //       success: true,
  //       data: response.data,
  //     };
  //   } catch (error) {
  //     logger.error(`Error updating case ${body.externalId}:`, error);
  //     return {
  //       success: false,
  //       error: transformError(error),
  //     };
  //   }
  // }

  // private async updatePlan(url: string, authHeader: any, body: any) {
  //   try {
  //     const updateUrl = url.replace('get_plan', 'update_plan');
  //     const planData = {
  //       name: body.name,
  //       description: body.description,
  //     };
  //     const response = await rateLimitedAxios.post(updateUrl, planData, authHeader);
  //     return {
  //       success: true,
  //       data: response.data,
  //     };
  //   } catch (error) {
  //     logger.error(`Error updating plan ${body.externalId}:`, error);
  //     return {
  //       success: false,
  //       error: transformError(error),
  //     };
  //   }
  // }

  // private async updateMilestone(url: string, authHeader: any, body: any) {
  //   try {
  //     const updateUrl = url.replace('get_milestone', 'update_milestone');
  //     const response = await rateLimitedAxios.post(updateUrl, body, authHeader);
  //     return {
  //       success: true,
  //       data: response.data,
  //     };
  //   } catch (error) {
  //     logger.error(`Error updating milestone ${body.externalId}:`, error);
  //     return {
  //       success: false,
  //       error: transformError(error),
  //     };
  //   }
  // }

  // private async updateRun(url: string, authHeader: any, body: any) {
  //   try {
  //     let updateUrl;
  //     if (body.planId) {
  //       const baseUrl = url.split('index.php')[0];
  //       updateUrl = `${baseUrl}index.php?/api/v2/update_plan_entry/${body.planId}/${body.entryId}`;
  //     } else {
  //       updateUrl = url.replace('get_run', 'update_run');
  //     }
  //     const runData = {
  //       name: body.name,
  //       description: body.description,
  //     };
  //     const response = await rateLimitedAxios.post(updateUrl, runData, authHeader);
  //     return {
  //       success: true,
  //       data: response.data,
  //     };
  //   } catch (error) {
  //     logger.error(`Error updating run ${body.externalId}:`, error);
  //     return {
  //       success: false,
  //       error: transformError(error),
  //     };
  //   }
  // }

  updateEntity = async (url: string, authHeader: any, entityType: string, body: any): Promise<any> => {
    logger.info(`Updating entity ${entityType} with body: ${JSON.stringify(body)}`);

    // TODO: Implement this to handle all entity types more robustly
    // switch (entityType) {
    //   case 'case':
    //     return this.updateCase(url, authHeader, body);
    //   case 'plan':
    //     return this.updatePlan(url, authHeader, body);
    //   case 'milestone':
    //     return this.updateMilestone(url, authHeader, body);
    //   case 'run':
    //     return this.updateRun(url, authHeader, body);
    //   default:
    //     throw new Error(`Unsupported entity type: ${entityType}`);
    // }
  };
}

function calculateSyncTime(syncDuration: string): Date {
  const match = syncDuration.match(/^(\d+)([dmy])$/);
  if (!match) {
    throw new Error('Invalid syncDuration format. Use formats like "90d", "3m", or "1y".');
  }

  const value = parseInt(match[1], 10);
  const unit = match[2];

  const now = new Date();

  switch (unit) {
    case 'd':
      return new Date(now.setDate(now.getDate() - value));
    case 'm':
      return new Date(now.setMonth(now.getMonth() - value));
    case 'y':
      return new Date(now.setFullYear(now.getFullYear() - value));
    default:
      throw new Error('Unsupported time unit. Use "d", "m", or "y".');
  }
}
