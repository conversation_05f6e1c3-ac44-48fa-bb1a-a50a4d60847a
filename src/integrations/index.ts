import { IntegrationToken } from '@app/models/integrationToken';
import { Knex } from 'knex';
import { TestRailService } from './testrail';
import { GitHubService } from './github';
import { JiraService } from './jira';

export interface OAuthResponseParams {
  oauthBaseURL: string;
  code: string;
  state: string;
  clientId: string;
  clientSecret: string;
  redirectUri: string;
}

export interface OAuthResponseResult {
  accessToken: string;
  refreshToken?: string;
  expiresIn: number;
  tokenData?: any;
  url?: string;
}

export interface IntegrationService {
  prepareAuthHeader(integrationToken: IntegrationToken, tenantDb: Knex): Promise<any>;
  fetchUserData(authHeader: any, url?: string): Promise<any>;
  getProjects(params: any, authHeader: any): Promise<any>;
  fetchEntities(entityType:string, authHeader:any, params:any, pagination:any, tagMappings?:any): Promise<any>;
  createEntity(authHeader: any, entityType: string, body: any): Promise<any>;
  updateEntity(url: string, authHeader: any, entityType: string, body: any): Promise<any>;
  getDefect?(defectUrl: string, authHeader: any): Promise<any>;
  fetchTags(integration: any, authHeader: any): Promise<any>;
  handleOAuthResponse?: (params: OAuthResponseParams) => Promise<OAuthResponseResult>;
  createRemoteLink?(authHeader: any, params: any): Promise<any>;
}

export class IntegrationServiceFactory {
  private static services: { [key: string]: IntegrationService } = {
    jira: new JiraService(),
    github: new GitHubService(),
    testrail: new TestRailService(),
  };

  static getService(serviceName: string): IntegrationService {
    const service = this.services[serviceName];
    if (!service) {
      throw new Error(`Unsupported integration service: ${serviceName}`);
    }
    return service;
  }
}
