import axios from 'axios';
import { IntegrationService, OAuthResponseParams, OAuthResponseResult } from 'integrations';
import logger from '@app/config/logger';
import { Knex } from 'knex';
import { decryptData } from '@app/utils/decryptData';
import { transformError } from '@app/utils/transformIntegrationError';

export class GitHubService implements IntegrationService {
  private async fetchRepo(params: any, authHeader: any) {
    try {
      const [owner, repo] = params.projectName.split('/');
      const url = `https://api.github.com/repos/${owner}/${repo}`;
      const response = await axios.get(url, authHeader);

      // Transform project data to match expected format
      const transformedProject = {
        source: params.service,
        sourceId: String(response.data.id),
        entityType: 'integration',
        entityUid: params.uid,
        customFields: {
          sourceType: 'project',
          name: response.data.name,
          url: response.data.url,
        },
      };

      return {
        success: true,
        data: transformedProject,
        // Dummy pagination to prevent infinite scroll as we only fetch one repo
        pagination: {
          hasMoreData: false, // TO stop infinite scroll
          startAt: 0,
          maxResults: 1,
          page: 1,
        },
      };
    } catch (error) {
      logger.error(`Error fetching GitHub repository ${params.projectName}:`, error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  private getTagMappings = (tagMappings: any, type: string, id: string) => {
    try {
      return tagMappings[type]?.[id].tagUid || null;
    } catch (error) {
      logger.error(`Error in getTagMappings: ${error.message}`);
      return null;
    }
  };

  private async getDefects(params: any, authHeader: any, pagination:any, tagMappings: any) {
    try {
      const [owner, repo] = params.projectName.split('/');
      const perPage = pagination.maxResults;
      const { page } = pagination;
      let url = `https://api.github.com/repos/${owner}/${repo}/issues?state=all&per_page=${perPage}&page=${page}`;

      // Add since parameter if syncedAt is provided
      if (params.syncedAt) {
        url += `&since=${new Date(params.syncedAt).toISOString()}`;
      }

      const response = await axios.get(url, authHeader);
      const issues = response.data;

      // Break if no more issues
      if (issues.length === 0) {
        return {
          success: true,
          data: [],
          // In case of no more issues, return a pagination object to prevent infinite scroll and end loop
          pagination: {
            hasMoreData: false,
            startAt: page + perPage,
            maxResults: perPage,
            page: page + 1,
          },
        };
      }

      const defects = issues.filter((issue) => !issue.pull_request)
        .map((issue) => ({
          name: issue.title,
          creator: issue.user.login,
          priority: null,
          status: null,
          externalId: issue.id.toString(),
          integrationSourceUid: String(params.uid),
          archivedAt: issue.state === 'closed' ? new Date(issue.closed_at).toISOString() : null,
          customFields: {
            ...issue,
            apiUrl: issue.url,
            webUrl: issue.html_url,
            projectScope: `${owner}/${repo}`,
            tags: issue.labels.map((label: any) => this.getTagMappings(tagMappings, 'tag', label.id)).filter(Boolean),
          },
        }));

      return {
        success: true,
        data: defects,
        pagination: {
          hasMoreData: true, // GH API does not return total count or next page token
          startAt: page + perPage,
          maxResults: perPage,
          page: page + 1,
        },
      };
    } catch (error) {
      logger.error(`Error fetching defects for GitHub repository ${params.projectName}:`, error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  prepareAuthHeader = async (integrationToken, tenantDb: Knex) => {
    const accessToken = await decryptData('integration', integrationToken.accessToken, 'accessToken', integrationToken.uid, tenantDb);
    return {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: 'application/json',
      },
    };
  };

  getProjects = async (params, authHeader) => {
    let hasMoreData = true;
    const perPage = 100;
    let page = 1;
    const projects = [];
    try {
      while (hasMoreData) {
        const response = await axios.get(
          'https://api.github.com/user/repos',
          {
            ...authHeader,
            params: {
              per_page: perPage,
              page,
            },
          },
        );

        projects.push(...response.data);
        if (response.data.length < perPage) {
          hasMoreData = false;
        }
        page++;
      }

      return {
        success: true,
        data: projects.map((repo) => ({
          projectId: repo.id.toString(),
          projectKey: repo.name,
          projectName: repo.full_name,
        })),
        pagination: {
          hasMoreData: false, // GH API does not return total count or next page token
          startAt: 0,
          maxResults: 100,
          page: 1,
        },
      };
    } catch (error) {
      logger.error('Error fetching repositories from GitHub:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  };

  fetchUserData = async (authHeader) => {
    try {
      const response = await axios.get(
        'https://api.github.com/user',
        authHeader,
      );

      const userData = {
        source: 'github',
        sourceId: response.data.id.toString(),
        entityType: 'org',
        customFields: {
          sourceType: 'user',
        },
      };
      return {
        success: true,
        data: userData,
        // Dummy pagination to prevent infinite scroll as we only fetch one user
        pagination: {
          hasMoreData: false, // GH API does not return total count or next page token
          startAt: 0,
          maxResults: 100,
          page: 1,
        },
      };
    } catch (error) {
      logger.error('Error fetching GitHub user data:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  };

  fetchTags = async (params: any, authHeader: any) => {
    try {
      const allTags = [];
      const [owner, repo] = params.projectName.split('/');
      const labelsUrl = `https://api.github.com/repos/${owner}/${repo}/labels`;

      const response = await axios.get(labelsUrl, authHeader);
      const labels = response.data;

      // Convert GitHub labels to tags
      for (const label of labels) {
        allTags.push({
          name: label.name,
          type: 'tag',
          description: 'tags internal to Testfiesta',
          sourceService: 'github',
          id: label.id.toString(),
          color: `#${label.color}`,
          isDefault: false,
          customFields: {
            projectScope: `${params.projectName}`,
          },
        });
      }
      return {
        success: true,
        data: allTags,
      };
    } catch (error) {
      logger.error('Error fetching GitHub tags:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  };

  getDefect = async (defectUrl, authHeader) => {
    try {
      const response = await axios.get(defectUrl, {
        headers: authHeader.headers,
      });

      // Transform the data before returning
      return {
        success: true,
        data: {
          name: response.data.title,
          creator: response.data.user.login,
          status: response.data.state,
          externalId: response.data.id.toString(),
          archivedAt: response.data.state === 'closed' ? new Date(response.data.closed_at).toISOString() : null,
          customFields: {
            ...response.data,
            apiUrl: response.data.url,
          },
        },
      };
    } catch (error) {
      logger.error('Error fetching GitHub defect:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  };

  createEntity = async (authHeader: any, entityType: string, body: any): Promise<any> => {
    switch (entityType) {
      case 'defect':
        return this.createDefect(authHeader, body);
      default:
        throw new Error(`Unsupported entity type: ${entityType}`);
    }
  };

  updateEntity = async (url: string, authHeader: any, entityType: string, body: any): Promise<any> => {
    switch (entityType) {
      case 'defect':
        return this.updateDefect(url, authHeader, body);
      default:
        throw new Error(`Unsupported entity type: ${entityType}`);
    }
  };

  private async createDefect(authHeader, body) {
    try {
      const [owner, repo] = body.projectName.split('/');
      const url = `https://api.github.com/repos/${owner}/${repo}/issues`;

      const payload = {
        title: body.name,
        body: body.description,
        labels: body.tags || [],
      };

      const response = await axios.post(url, payload, authHeader);

      return {
        success: true,
        data: {
          name: response.data.title,
          status: response.data.state,
          externalId: response.data.id.toString(),
          archivedAt: response.data.state === 'closed' ? new Date(response.data.closed_at).toISOString() : null,
          customFields: {
            ...response.data,
            apiUrl: response.data.url,
            webUrl: response.data.html_url,
            projectScope: `${owner}/${repo}`,
            tags: response.data.labels.map((tag) => tag.id),
          },
        },
      };
    } catch (error) {
      logger.error('Error creating GitHub defect:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  private async updateDefect(defectUrl, authHeader, body) {
    try {
      const payload: any = {};

      if (body.name) payload.title = body.name;
      if (body.description) payload.body = body.description;
      if (body.state) payload.state = body.state;
      if (body.labels && body.labels.length > 0) payload.labels = body.labels;

      const response = await axios.patch(defectUrl, payload, authHeader);
      return {
        success: true,
        data: {
          name: response.data.title,
          status: response.data.state,
          externalId: response.data.id.toString(),
          archivedAt: response.data.state === 'closed' ? new Date(response.data.closed_at).toISOString() : null,
          customFields: {
            ...response.data,
            apiUrl: response.data.url,
            webUrl: response.data.html_url,
            projectScope: response.data.repository_url.split('/').slice(-2).join('/'),
            tags: response.data.labels.map((tag) => tag.name),
          },
        },
      };
    } catch (error) {
      logger.error('Error updating GitHub defect:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  createRemoteLink = async (authHeader, params) => {
    // GitHub doesn't have a direct remote link API, so we update the issue body instead
    try {
      const issue = await axios.get(params.defectUrl, authHeader);
      const currentBody = issue.data.body || '';
      const updatedBody = `${currentBody}\n---\nLinked Testfiesta Execution: [View Execution](${params.link})\n---\nTestFiesta Defect: [View Defect](${params.webUrl})`;

      await axios.patch(params.defectUrl, {
        body: updatedBody,
      }, authHeader);

      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  };

  handleOAuthResponse = async (params: OAuthResponseParams): Promise<OAuthResponseResult> => {
    const oauthURL = `${params.oauthBaseURL}/login/oauth/access_token`;

    const response = await axios.post(
      oauthURL,
      {
        client_id: params.clientId,
        client_secret: params.clientSecret,
        code: params.code,
      },
      {
        headers: {
          Accept: 'application/json',
          'Accept-Encoding': 'application/json',
        },
      },
    );

    return {
      accessToken: response.data.access_token,
      refreshToken: response.data.refresh_token || '',
      expiresIn: 31536000, // 1 year in seconds for GitHub
      tokenData: response.data,
      url: `${params.oauthBaseURL}/login/oauth/access_token`,
    };
  };

  fetchEntities = async (
    entityType: string,
    authHeader: any,
    params: any,
    pagination: any,
    tagMappings?: any,
  ) => {
    switch (entityType) {
      case 'repositories':
        return this.fetchRepo(params, authHeader);
      case 'defects':
        return this.getDefects(params, authHeader, pagination, tagMappings);
      default:
        throw new Error(`Unsupported entity type: ${entityType}`);
    }
  };
}
