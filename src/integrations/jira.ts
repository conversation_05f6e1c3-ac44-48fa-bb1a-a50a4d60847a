import { IntegrationService, OAuthResponseParams, OAuthResponseResult } from 'integrations';
import axios from 'axios';
import logger from '@app/config/logger';
import dayjs from 'dayjs';
import { Knex } from 'knex';
import { getCreateMeta, transformFields } from '@app/utils/jiraHelper';
import { decryptData } from '@app/utils/decryptData';
import appConstants from '@app/constants/app';
import { IntegrationToken } from '@app/models/integrationToken';
import { setupDB } from '@app/config/db';
import { KEY_MANAGER } from '@app/config/keyManagerLoader';
import { transformError } from '@app/utils/transformIntegrationError';

export class JiraService implements IntegrationService {
  private async fetchProject(params: any, authHeader: any) {
    try {
      const url = `https://api.atlassian.com/ex/jira/${params.resourceId}/rest/api/3/project/${params.projectId}`;
      const response = await axios.get(url, authHeader);

      const transformedProject = {
        source: params.service,
        sourceId: String(response.data.id),
        entityType: 'integration',
        entityUid: params.uid,
        customFields: {
          sourceType: 'project',
          url: response.data.self,
          name: response.data.name,
        },
      };

      return {
        success: true,
        data: transformedProject,
        // Dummy pagination to prevent infinite scroll as we only fetch one project
        pagination: {
          hasMoreData: false,
          startAt: 0,
          maxResults: 1,
          page: 1,
        },
      };
    } catch (error) {
      logger.error('Error fetching project from Jira:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  private getStatusColor(colorName: string): string {
    const colorMap: { [key: string]: string } = {
      'blue-gray': '#4A6785',
      yellow: '#F5CD47',
      green: '#14892C',
      // Add more color mappings as needed
      default: '#666666',
    };

    return colorMap[colorName] || colorMap.default;
  }

  private getTagMappings = (tagMappings: any, type: string, id: string) => {
    try {
      return Number(tagMappings[type]?.[id].tagUid) || null;
    } catch (error) {
      logger.error(`Error in getTagMappings: ${error.message}`);
      return null;
    }
  };

  private async getDefects(params: any, authHeader: any, pagination:any, tagMappings: any) {
    try {
      const url = `https://api.atlassian.com/ex/jira/${params.resourceId}/rest/api/3/search`;
      let jql = `project=${params.projectId}`;

      // Add date filter if syncedAt is provided
      if (params.syncedAt) {
        const formattedDate = dayjs(params.syncedAt).format('YYYY-MM-DD HH:mm');
        jql += ` AND updated >= '${formattedDate}'`;
      }

      // Add any additional JQL filters from params
      if (params.additionalJql) {
        jql += ` AND ${params.additionalJql}`;
      }

      const { startAt } = pagination;
      const { maxResults } = pagination;
      const urlString = `${url}?jql=${encodeURIComponent(jql)}&maxResults=${maxResults}&startAt=${startAt}`;
      const response = await axios.get(urlString, authHeader);
      const defects = response.data.issues.map((issue: any) => ({
        name: issue.fields.summary,
        priority: this.getTagMappings(tagMappings, 'priority', issue.fields.priority?.id),
        creator: issue.fields.creator.displayName,
        status: this.getTagMappings(tagMappings, 'status', issue.fields.status?.id),
        externalId: issue.id,
        integrationSourceUid: String(params.uid),
        archivedAt: issue.fields.status?.statusCategory?.key === 'done' ? new Date(issue.fields.updated).toISOString() : null,
        customFields: {
          ...issue.fields,
          apiUrl: issue.self,
          webUrl: `${params.webUrl}/browse/${issue.key}`,
          key: issue.key,
          resourceId: params.resourceId,
          projectScope: params.projectId,
        },
      }));
      const totalIssues = response.data.total;
      return {
        success: true,
        data: defects,
        pagination: {
          startAt: startAt + maxResults,
          maxResults,
          hasMoreData: startAt + maxResults < totalIssues,
        },
      };
    } catch (error) {
      logger.error(`Error fetching defects for project ${params.projectId}:`, error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  prepareAuthHeader = async (integrationToken, tenantDb: Knex) => {
    const expiresAt = dayjs(integrationToken.expiresAt);
    const currentTime = dayjs();
    const timeLeft = expiresAt.diff(currentTime, 'second'); // Get remaining time in seconds
    const keyManager = KEY_MANAGER;
    const db = setupDB();

    // Refresh token if it has 500 seconds or less remaining
    if (timeLeft <= 500) {
      const refreshToken = await decryptData(
        'integration',
        integrationToken.refreshToken,
        'refreshToken',
        integrationToken.uid,
        tenantDb,
      );

      try {
        const postResponse = await axios.post(
          integrationToken.url,
          {
            client_id: appConstants.OAUTH_JIRA_CLIENT_ID,
            client_secret: appConstants.OAUTH_JIRA_CLIENT_SECRET,
            refresh_token: refreshToken,
            grant_type: 'refresh_token',
          },
          {
            headers: {
              Accept: 'application/json',
            },
          },
        );

        const newIntegrationToken = {
          ...integrationToken,
          accessToken: postResponse.data.access_token,
          refreshToken: postResponse.data.refresh_token,
        };

        const { encryptedIntegrationToken, symmetricKeyData } = await IntegrationToken.getEncryptedIntegrationToken(db, newIntegrationToken);

        // Update access token with new expiration time
        const newExpiresAt = dayjs().add(postResponse.data.expires_in, 'second').toISOString();
        await integrationToken.$query().patch({
          accessToken: encryptedIntegrationToken.accessToken,
          refreshToken: encryptedIntegrationToken.refreshToken,
          expiresAt: newExpiresAt,
        });

        symmetricKeyData.entityId = integrationToken.uid;
        await keyManager.createSymmeticKey(tenantDb, symmetricKeyData);

        logger.info('Jira token refreshed successfully');
        return {
          headers: {
            Authorization: `Bearer ${postResponse.data.access_token}`,
            Accept: 'application/json',
          },
        };
      } catch (error) {
        logger.error(`Error refreshing Jira token: ${error}`);
        return null;
      }
    }

    // Return existing token if it's still valid
    const accessToken = await decryptData(
      'integration',
      integrationToken.accessToken,
      'accessToken',
      integrationToken.uid,
      tenantDb,
    );
    return {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: 'application/json',
      },
    };
  };

  fetchUserData = async (authHeader) => {
    try {
      const response = await axios.get(
        'https://api.atlassian.com/me',
        authHeader,
      );
      const userData = {
        sourceId: response.data.account_id,
        source: 'jira',
        entityType: 'org',
        customFields: {
          sourceType: 'user',
        },
      };
      return {
        success: true,
        data: userData,
        // Dummy pagination to prevent infinite scroll as we only fetch one user
        pagination: {
          hasMoreData: false,
          startAt: 0,
          maxResults: 1,
          page: 1,
        },
      };
    } catch (error) {
      logger.error('Error fetching Jira user data:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  };

  getProjects = async (params, authHeader) => {
    try {
      const url = `https://api.atlassian.com/ex/jira/${params.resourceId}/rest/api/3/project/search`;
      let startAt = 0;
      const maxResults = 50;
      const urlParams = new URLSearchParams({
        maxResults: maxResults.toString(),
        startAt: startAt.toString(),
      });
      let loop = true;
      const projects = [];
      while (loop) {
        const response = await axios.get(
          `${url}?${urlParams.toString()}`,
          authHeader,
        );
        const { values } = response.data;
        for (const project of values) {
          projects.push({
            projectId: project.id,
            projectKey: project.key,
            projectName: project.name,
          });
        }
        // check if there are more projects to fetch
        if (startAt + maxResults >= response.data.total) {
          loop = false;
        }
        startAt += maxResults;
        urlParams.set('startAt', startAt.toString());
      }
      return {
        success: true,
        data: projects,
        // Dummy pagination to prevent infinite scroll
        pagination: {
          hasMoreData: false,
          startAt: 0,
          maxResults: 1,
          page: 1,
        },
      };
    } catch (error) {
      logger.error('Error fetching projects from Jira:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  };

  fetchEntities = async (
    entityType: string,
    authHeader: any,
    params: any,
    pagination: any,
    tagMappings?: any,
  ) => {
    switch (entityType) {
      case 'projects':
        return this.fetchProject(params, authHeader);
      case 'defects':
        return this.getDefects(params, authHeader, pagination, tagMappings);
      default:
        throw new Error(`Unsupported entity type: ${entityType}`);
    }
  };

  fetchTags = async (params: any, authHeader: any) => {
    try {
      let allTags = [];
      const includedStatusIds = new Set(); // Track added status IDs

      // Fetch global priorities first
      const priorityResponse = await axios.get(
        `https://api.atlassian.com/ex/jira/${params.resourceId}/rest/api/3/priority`,
        authHeader,
      );

      // Add priorities as global tags (not project-scoped)
      const priorities = priorityResponse.data.map((priority) => ({
        id: priority.id,
        name: priority.name,
        color: priority.statusColor || '#666666',
        type: 'priority',
        sourceService: 'jira',
        customFields: {
          resourceId: params.resourceId,
        },
      }));

      // Add global priorities to tags
      allTags = [...priorities];
      const globalStatusResponse = await axios.get(
        `https://api.atlassian.com/ex/jira/${params.resourceId}/rest/api/3/status`,
        authHeader,
      );

      // Add global statuses
      const globalStatuses = globalStatusResponse.data
        .filter((status) => !status.scope) // Only include global statuses
        .map((status) => {
          includedStatusIds.add(status.id); // Track global status ID
          return {
            id: status.id,
            name: status.name,
            color: this.getStatusColor(status.statusCategory?.colorName),
            type: 'status',
            sourceService: 'jira',
            customFields: {
              resourceId: params.resourceId,
              isClosed: status.statusCategory?.key === 'done',
            },
          };
        });

      allTags = [...allTags, ...globalStatuses];
      // Filter project-specific statuses
      const projectStatuses = globalStatusResponse.data
        .filter(
          (status) => status.scope?.type === 'PROJECT'
            && status.scope.project.id === params.projectId
            && !includedStatusIds.has(status.id), // Skip if already included as global
        )
        .map((status) => {
          includedStatusIds.add(status.id); // Track project-specific status ID
          return {
            id: status.id,
            name: status.name,
            color: this.getStatusColor(status.statusCategory?.colorName),
            type: 'status',
            sourceService: 'jira',
            customFields: {
              projectScope: params.projectId,
              resourceId: params.resourceId,
              isClosed: status.statusCategory?.key === 'done',
            },
          };
        });

      // Add project-specific statuses
      allTags = [...allTags, ...projectStatuses];
      return {
        success: true,
        data: allTags,
      };
    } catch (error) {
      logger.error('Error fetching Jira tags:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  };

  getDefect = async (defectUrl, authHeader) => {
    try {
      const url = `${defectUrl}?expand=editmeta`;
      const response = await axios.get(url, {
        headers: authHeader.headers,
      });

      // Transform the data before returning

      return {
        success: true,
        data: {
          name: response.data.fields.summary,
          priority: response.data.fields.priority?.id,
          creator: response.data.fields.creator.displayName,
          externalId: response.data.id,
          archivedAt: response.data.fields.status?.statusCategory?.key === 'done' ? new Date(response.data.fields.updated).toISOString() : null,
          customFields: {
            ...response.data.fields,
            apiUrl: response.data.self,
            key: response.data.key,
            editmeta: response.data.editmeta,
          },
        },
      };
    } catch (error) {
      logger.error('Error fetching Jira defect:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  };

  updateEntity = async (url: string, authHeader: any, entityType: string, body: any): Promise<any> => {
    switch (entityType) {
      case 'defect':
        return this.updateDefect(url, authHeader, body);
      default:
        throw new Error(`Unsupported entity type: ${entityType}`);
    }
  };

  createEntity = async (authHeader: any, entityType: string, body: any): Promise<any> => {
    switch (entityType) {
      case 'defect':
        return this.createDefect(authHeader, body);
      default:
        throw new Error(`Unsupported entity type: ${entityType}`);
    }
  };

  private async updateDefect(defectUrl, authHeader, body) {
    // Fetch editmeta for the issue
    const defectData = await axios.get(`${defectUrl}?expand=editmeta`, {
      headers: authHeader.headers,
    });
    const { editmeta } = defectData.data;
    // Transform the fields
    const transformedFields = await transformFields(
      {
        ...body,
        summary: body.name,
        priority: body.priority,
      },
      editmeta.fields,
      authHeader,
    );
    try {
      await axios.put(
        defectUrl,
        { fields: transformedFields.fields },
        { headers: authHeader.headers },
      );
      if (body.status) {
        const status = await axios.get(`${defectUrl}/transitions`, authHeader);
        const transition = status.data.transitions.find((transition: any) => Number(transition.to.id) === Number(body.status));
        if (transition) {
          await axios.post(`${defectUrl}/transitions`, {
            transition: { id: transition.id },
          }, authHeader);
        }
      }
      const response = await axios.get(defectUrl, authHeader);
      return {
        success: true,
        data: {
          name: response.data.fields.summary,
          priority: response.data.fields.priority?.id,
          status: response.data.fields.status?.id,
          archivedAt: response.data.fields.status?.statusCategory?.key === 'done' ? new Date(response.data.fields.updated).toISOString() : null,
          customFields: {
            ...response.data.fields,
            apiUrl: response.data.self,
            key: response.data.key,
            projectScope: response.data.fields.project.id,
          },
        },
      };
    } catch (error) {
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  createRemoteLink = async (authHeader: any, params: any) => {
    try {
      const body = {
        object: {
          url: params.link,
          title: `TF Exec ${params.projectKey}-${params.executionUid}`,
        },
      };
      const response = await axios.post(`${params.defectUrl}/remotelink`, body, authHeader);
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      logger.error('Error creating remote link:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  };

  private async createDefect(authHeader, body) {
    try {
      const url = `https://api.atlassian.com/ex/jira/${body.resourceId}/rest/api/3/issue`;

      // Fetch create metadata internally
      const createMeta = await getCreateMeta(
        {
          resourceId: body.resourceId,
          projectId: body.projectId,
          issueTypeId: body.issueTypeId,
        },
        authHeader,
      );

      if (!createMeta.success) {
        throw new Error('Failed to fetch create metadata');
      }

      // Transform fields based on create metadata
      const transformedFields = await transformFields(
        {
          summary: body.name,
          description: body.description,
        },
        createMeta.data[body.projectId][body.typeId],
        authHeader,
      );

      const payload = {
        fields: {
          ...transformedFields.fields,
          project: { id: body.projectId },
          issuetype: { id: body.typeId },
          ...body.fields,
        },
      };
      const webUrlResponse = await axios.get(
        `https://api.atlassian.com/ex/jira/${body.resourceId}/rest/api/3/serverInfo`,
        authHeader,
      );
      const response = await axios.post(url, payload, authHeader);
      const defect = await axios.get(`${response.data.self}`, authHeader);
      return {
        success: true,
        data: {
          name: defect.data.fields?.summary,
          externalId: defect.data.id,
          priority: defect.data.fields.priority?.id,
          status: defect.data.fields.status?.id,
          archivedAt: defect.data.fields.status?.statusCategory?.key === 'done' ? new Date(defect.data.fields.updated).toISOString() : null,
          customFields: {
            ...defect.data.fields,
            apiUrl: defect.data.self,
            webUrl: `${webUrlResponse.data.baseUrl}/browse/${defect.data.key}`,
            key: defect.data.key,
            projectScope: body.projectId,

          },
        },
      };
    } catch (error) {
      logger.error('Error creating Jira defect:', error);
      return {
        success: false,
        error: transformError(error),
        status: error.response?.data?.status,
      };
    }
  }

  handleOAuthResponse = async (params: OAuthResponseParams): Promise<OAuthResponseResult> => {
    const oauthURL = `${params.oauthBaseURL}/oauth/token`;

    const response = await axios.post(
      oauthURL,
      {
        client_id: params.clientId,
        client_secret: params.clientSecret,
        code: params.code,
        grant_type: 'authorization_code',
        redirect_uri: params.redirectUri,
      },
      { headers: { Accept: 'application/json' } },
    );

    return {
      accessToken: response.data.access_token,
      refreshToken: response.data.refresh_token,
      expiresIn: response.data.expires_in,
      tokenData: response.data,
      url: `${params.oauthBaseURL}/oauth/token`,
    };
  };
}
