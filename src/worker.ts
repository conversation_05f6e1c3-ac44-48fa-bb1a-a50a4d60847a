import 'module-alias/register';
import * as notifications from '@ss-libs/ss-component-notifications';
import path from 'path';

import { createTemporalWorker, stopTemporalWorker } from './temporal/worker';
import { freeHandlesCron } from './temporal/activities/handle';
import { stopWorkers } from './lib/queue';
import env from './config/env';
import logger from './config/logger';
import { registerNamespaces } from './temporal/namespace';

async function main() {
  logger.info('Loading notifications');

  notifications.init({
    emails: {
      credentials: { apiKey: env.SENDGRID_KEY },
      from: env.MAIL_USER,
      provider: 'sendgrid',
      templatesDir: path.join(process.cwd(), 'dist/src/mail'),
    },
  });

  await registerNamespaces();

  logger.info('starting email worker');
  // startWorker(emailWorker);
  createTemporalWorker({
    taskQueue: 'email-queue',
    workflowsPath: './workflows',
  });

  logger.info('starting ingress worker');
  // startWorker(ingressWorker);
  createTemporalWorker({
    taskQueue: 'ingress-queue',
    workflowsPath: './workflows',
  });

  logger.info('starting attachment worker');
  // startWorker(attachmentWorker);
  createTemporalWorker({
    taskQueue: 'attachment-queue',
    workflowsPath: './workflows',
  });

  logger.info('starting free handles worker');
  // startWorker(freeHandlesWorker(db));
  createTemporalWorker({
    taskQueue: 'free-handles-queue',
    workflowsPath: './workflows',
  });

  logger.info('starting template worker');
  // startWorker(templateWorker);
  createTemporalWorker({
    taskQueue: 'template-queue',
    workflowsPath: './workflows',
  });

  logger.info('starting dashboard worker');
  // startWorker(dashboardWorker);
  createTemporalWorker({
    taskQueue: 'dashboard-queue',
    workflowsPath: './workflows',
  });

  logger.info('starting softDeleteProjectRelatedRecords worker');
  // startWorker(softDeleteProjectRelatedRecordsWorker);
  createTemporalWorker({
    taskQueue: 'soft-delete-project-records-queue',
    workflowsPath: './workflows',
  });

  logger.info('starting storageMigration worker');
  createTemporalWorker({
    taskQueue: 'storage-migration-queue',
    workflowsPath: './workflows',
  });

  logger.info('starting free handles cron job');
  freeHandlesCron();

  logger.info('starting setup account worker');
  // startWorker(setupAccountWorker(db));
  createTemporalWorker({
    taskQueue: 'setup-account-queue',
    workflowsPath: './workflows',
  });

  // Start Integration Worker Temporal worker
  logger.info('starting integration worker');
  createTemporalWorker({
    taskQueue: 'integration-queue',
    workflowsPath: './workflows',
  });

  // Start Integration Sync Temporal worker
  logger.info('Starting Integration Sync Temporal worker...');
  createTemporalWorker({
    taskQueue: 'integration-sync-queue',
    workflowsPath: './workflows',
  });

  // Start Integration Entities Temporal worker
  logger.info('Starting Integration Entities Temporal worker...');
  createTemporalWorker({
    taskQueue: 'integration-entities-queue',
    workflowsPath: './workflows',
  });

  // Start Integration Entities Temporal worker
  logger.info('Starting Email Events Temporal worker...');
  createTemporalWorker({
    taskQueue: 'email-events-queue',
    workflowsPath: './workflows',
  });

  // Start Roles worker
  logger.info('Starting Roles Temporal worker...');
  createTemporalWorker({
    taskQueue: 'role-queue',
    workflowsPath: './workflows',
  });

  logger.info('starting update milestone worker');
  // startWorker(updateMilestoneWorker(db));
  createTemporalWorker({
    taskQueue: 'update-milestone-queue',
    workflowsPath: './workflows',
  });

  logger.info('starting update plan worker');
  // startWorker(updatePlanWorker(db));
  createTemporalWorker({
    taskQueue: 'update-plan-queue',
    workflowsPath: './workflows',
  });

  logger.info('starting update run worker');
  // startWorker(updateRunWorker(db));
  createTemporalWorker({
    taskQueue: 'update-run-queue',
    workflowsPath: './workflows',
  });

  logger.info('starting create demo project worker');
  createTemporalWorker({
    taskQueue: 'create-project-demo-queue',
    workflowsPath: './workflows',
  });

  process.on('SIGTERM', async () => {
    await stopWorkers();
    await stopTemporalWorker();
  });
}
main();
