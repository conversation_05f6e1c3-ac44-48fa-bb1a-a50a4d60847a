import './config/instrumentation';

import * as Sentry from '@sentry/node';

import express, { Router } from 'express';
import morgan, { StreamOptions } from 'morgan';

import { Knex } from 'knex';
import compression from 'compression';
import cors from 'cors';
import fs from 'node:fs';
import helmet from 'helmet';
import logger from '@app/config/logger';
import passport from 'passport';
import cookieParser from 'cookie-parser';
import fileUpload from 'express-fileupload';
import { configureI18n } from '@app/middlewares/i18n';
import env from './config/env';
import { createLocalStorage } from './middlewares/storage';
import { RegisterRoutes } from './routes/v1/routes';
import { errorHandler, notFound } from './middlewares/error';
import requestTraceId from './middlewares/requestTraceId';
import serverVersion from './config/app';
import { setupPlugins } from './plugins';
import trv2Router from './routes/trv2';

/**
 * prepares the express appliation for exposure via HTTP
 * @param db
 * @param openfga
 * @param internalRoutes
 * @param loadExternalRoutes
 * @returns
 */
export function buildApp(
  db: Knex,
  internalRoutes: Router[] = [],
  loadExternalRoutes = false,
) {
  const app = express();
  app.disable('x-powered-by');

  const stream: StreamOptions = {
    write: (str: string) => logger.info(str.trim()),
  };

  app.use(configureI18n);

  app.use(morgan('combined', { stream }));

  app.use(fileUpload());

  app.use(
    cors({
      origin: env.CORS_ORIGINS,
      methods: ['POST', 'PUT', 'PATCH', 'GET', 'DELETE'],
      credentials: true,
      exposedHeaders: [
        'x-tf-backend-version',
        'x-tf-frontend-version',
        'x-tf-backend-redirect',
      ],
    }),
  );

  app.use(
    helmet({
      crossOriginResourcePolicy: { policy: 'same-site' },
    }),
  );
  app.use(compression());
  app.use(createLocalStorage);
  app.use(requestTraceId);
  app.use(setupPlugins(db));
  app.use(express.urlencoded({ limit: '10mb', extended: true }));
  app.use(express.json({ limit: '10mb' }));

  app.use(passport.initialize());
  app.use(express.static('public')); // TODO - remove when Oauth file is moved to CDN
  app.use(cookieParser(env.JWT_SIGNING_SECRET));

  logger.info('Starting request processing....');

  const appInfo = (req, res) => res.json({ version: serverVersion });
  app.get('/healthz', appInfo);
  app.get('/version', appInfo);

  if (loadExternalRoutes) {
    logger.info('Loading external routes....');
    app.use(['/v1'], (req, res, next) => {
      req.sharedKnexDB = db;
      next();
    });
    RegisterRoutes(app);

    logger.info('Loading TRV2 routes....');
    app.use(['/trv2'], (req, res, next) => {
      req.sharedKnexDB = db;
      next();
    });
    app.use('/trv2', trv2Router);
  }

  logger.info('Loading internal routes....');
  for (const route of internalRoutes) {
    app.use(`${env.API_INTERNAL_ROUTE}`, route);
  }

  logger.info('Loading docs....');
  app.get('/v1/spec', (_req, res) => {
    const openApiSpec = JSON.parse(
      fs.readFileSync('dist/swagger.json', 'utf8'),
    );
    return res.json(openApiSpec);
  });

  // lazy load sentry
  if (env.SENTRY_ENABLED && env.SENTRY_ENABLED === 'true') {
    Sentry.setupExpressErrorHandler(app);
  }

  app.use(notFound, errorHandler);

  return app;
}
