/**
 * Calculate the Cartesian product of an array of arrays
 * @param sets - An array of arrays containing all the options from all configurations
 * @returns An array of arrays containing all possible combinations of the options
 * @example
 * const options = [['A', 'B'], ['X', 'Y'], ['1', '2']];
 * const result = cartesianProductOptions(options);
 * Output:
 * [
 *  ['A', 'X', '1'], ['A', 'X', '2'], ['A', 'Y', '1'],
 *  ['A', 'Y', '2'], ['B', 'X', '1'], ['B', 'X', '2'],
 *  ['B', 'Y', '1'], ['B', 'Y', '2']
 * ]
 */
export function cartesianProduct<T>(sets: T[][]): T[][] {
  if (sets.length === 0) {
    return [[]];
  }

  const firstArray = sets[0];

  // Recursively calculate the Cartesian product of the rest of the arrays
  const restArrays = cartesianProduct(sets.slice(1));

  // Result array to store all combinations
  const result = [];

  // Iterate over elements in the first array
  for (const element of firstArray) {
    // For each combination in the rest arrays, concatenate it with the current element
    for (const combination of restArrays) {
      result.push([element, ...combination]);
    }
  }

  return result;
}

/**
 * Returns a list of unique sets
 * @param sets list of sets
 * @returns a list of unique sets
 * @example
 * const sets = [['chrome','linux'],['linux','chrome'],['linux'],['chrome']]
 * const result = getDistinct(sets)
 * [
 *  ['chrome','linux'],['linux'],['chrome']
 * ]
 */
export function getDistinct<T>(sets: T[][]): T[][] {
  const map: Record<string, T[]> = {};

  for (const s of sets) {
    map[s.sort().join('')] = s;
  }

  return Object.values(map);
}

/**
 * Returns a list of namespaced sets. a namespaced sets is simply a set of items having the same namespace
 * const sets = [['browser::chrome','os::linux'],['os::linux','browser::chrome'],['os::linux'],['browser::chrome']]
 * const result = mergeAndGroup(sets)
 * [
 *  ['browser::chrome'], ['os::linux']
 * ]
 */
export function mergeAndGroup(sets: string[][]): string[][] {
  const merged: Record<string, Set<string>> = {};

  for (const set of sets) {
    for (const item of set) {
      const [k, v] = item.split('::');
      if (!k || !v) continue;

      if (merged[k]) merged[k].add(v);
      else merged[k] = new Set([v]);
    }
  }

  return Object.keys(merged).map((m) => Array.from(merged[m]).map((a) => `${m}::${a}`));
}
