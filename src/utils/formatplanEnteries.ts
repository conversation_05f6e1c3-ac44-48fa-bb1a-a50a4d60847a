import { v5 as uuidv5 } from 'uuid';
import { formatConfigs, formatCaseCount } from './formatRunsFields';

const NAMESPACE = '5e514862-79be-47c0-8163-927b8d5b6053';

export const generateEntryId = (planId, projectId, suffix = '') => {
  // Add suffix to ensure different but consistent UUIDs
  const uniqueId = `${planId}-${projectId}${suffix}`;
  return uuidv5(uniqueId, NAMESPACE);
};

export const transformRunEnteries = (
  name,
  runs,
  projectUid,
  planId,
  suiteUid,
  statusColors,
  baseURL,
) => {
  if (!runs || runs.length === 0) {
    return [];
  }

  // Separate runs by configs presence
  const runsWithConfig = runs.filter(
    (r) => r.customFields?.configs && r.customFields.configs.length > 0,
  );
  const runsWithoutConfig = runs.filter(
    (r) => !r.customFields?.configs || r.customFields.configs.length === 0,
  );

  // Helper to format runs array and assign entry_id later
  const formatRuns = (runsSubset, entryId) => runsSubset.map((r, index) => ({
    id: r.uid,
    suite_id: suiteUid,
    name: r.name,
    description: r.description,
    milestone_id: Array.isArray(r.testMilestones) && r.testMilestones.length > 0 ? r.testMilestones[0].uid : null,
    assignedto_id: null,
    include_all: false,
    is_completed: !!r.archivedAt,
    completed_on: r.archivedAt ? new Date(r.archivedAt).getTime() : null,
    ...formatConfigs(r.customFields.configs, r.configs),
    ...formatCaseCount(r.customFields.frequency, statusColors),
    project_id: projectUid,
    plan_id: planId,
    entery_index: index,
    entry_id: entryId,
    updated_on: new Date(r.updatedAt).getTime(),
    refs: null,
    dataset_id: null,
    created_by: null,
    url: `${baseURL}/${projectUid}/runs/${r.uid}`,
  }));

  // Generate two distinct but consistent entry IDs
  const entryIdWithConfig = generateEntryId(planId, projectUid, '-withConfig');
  const entryIdWithoutConfig = generateEntryId(planId, projectUid, '-noConfig');

  // Format runs with assigned entry IDs
  const runsWithConfigData = formatRuns(runsWithConfig, entryIdWithConfig);
  const runsWithoutConfigData = formatRuns(runsWithoutConfig, entryIdWithoutConfig);

  // Prepare entries array
  const entries = [];

  if (runsWithConfigData.length > 0) {
    entries.push({
      id: entryIdWithConfig,
      suite_id: suiteUid,
      name,
      refs: null,
      description: null,
      include_all: false,
      dataset_id: null,
      runs: runsWithConfigData,
    });
  }

  if (runsWithoutConfigData.length > 0) {
    entries.push({
      id: entryIdWithoutConfig,
      suite_id: suiteUid,
      name,
      refs: null,
      description: null,
      include_all: false,
      dataset_id: null,
      runs: runsWithoutConfigData,
    });
  }

  return entries;
};
