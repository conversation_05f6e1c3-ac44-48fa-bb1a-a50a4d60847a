import { Knex } from 'knex';
import crypto from 'crypto';
import dayjs from 'dayjs';
import formatDate from './formatDate';
import { hashPassword } from './hashPassword';

const createAccessToken = async (
  db: Knex,
  name: string,
  ownerUid: string,
  ownerType: string,
  expirationDate: any,
) => {
  const { expiresAt, accessTokenHash, accessTokenKey } = await generateAccessTokenInfo(expirationDate);

  await db('accessTokens').insert({
    name,
    accessTokenHash,
    ownerUid,
    ownerType,
    expiresAt,
  });
  const result = await db('accessTokens')
    .where({
      name,
      accessTokenHash,
      ownerUid,
      ownerType,
      expiresAt,
    })
    .first();
  if (result) {
    const accessTokenFinal = `testfiesta_${result.uid.replaceAll('-', '')}.${accessTokenKey}`;
    return { ...result, accessTokenFinal };
  }
};

const generateAccessTokenInfo = async (expirationDate: any) => {
  const expiresAt = expirationDate || formatDate(dayjs().add(90, 'day').toDate());
  const accessTokenKey = crypto.randomBytes(16).toString('hex');
  const accessTokenHash = await hashPassword(accessTokenKey);

  return {
    expiresAt,
    accessTokenKey,
    accessTokenHash,
  };
};

export { createAccessToken, generateAccessTokenInfo };
