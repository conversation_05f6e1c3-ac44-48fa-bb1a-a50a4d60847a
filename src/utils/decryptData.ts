import { DecryptionService } from '@ss-libs/ss-component-encryption';
import * as forge from 'node-forge';
import { KEY_MANAGER } from '@app/config/keyManagerLoader';
import { Knex } from 'knex';
import { setupDB } from '../config/db';

export async function decryptData(
  service: string,
  encrypted: string,
  field: string,
  entityId: string,
  tenantDb: Knex,
) {
  const db = setupDB();
  const keyManager = KEY_MANAGER;
  const latestPublicKey = await keyManager.getLatestPublicKey(
    db,
    'integration',
  );
  keyManager.loadPrivateKey(
    `integration-${latestPublicKey.version}-key`,
    process.env[`INTEGRATION_${latestPublicKey.version}_KEY`],
  );
  try {
    const decrypter = new DecryptionService({ keyManager });
    const decryptedData = await decrypter.decryptHybrid(service, {
      db: tenantDb,
      encryptedData: {
        data: encrypted,
      },
      field,
      entityId,
    });
    return decryptedData;
  } catch (err) {
    throw new Error(err);
  }
}

export function decipherDecrypt(
  data: string,
  decryptedSymmetricKey: string,
  tag: string,
  iv: string,
): string {
  const decipher = forge.cipher.createDecipher(
    'AES-GCM',
    forge.util.createBuffer(decryptedSymmetricKey),
  );
  decipher.start({
    iv: forge.util.decode64(iv),
    tag: forge.util.createBuffer(forge.util.decode64(tag)),
  });

  decipher.update(forge.util.createBuffer(forge.util.decode64(data)));

  const pass = decipher.finish();

  if (!pass) {
    throw new Error('Decryption failed: authentication error');
  }

  return decipher.output.toString();
}
