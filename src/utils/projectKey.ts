const shuffleString = (str: string): string => {
  const arr = str.split('');
  for (let i = arr.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [arr[i], arr[j]] = [arr[j], arr[i]];
  }
  return arr.join('');
};

export const generateKey = (name: string): string => {
  const cleanedName = name.trim().toUpperCase().replace(/[^A-Z0-9\s]/g, ' ');
  const words = cleanedName.split(/\s+/);
  let key = '';
  words.forEach((word) => {
    if (word.length > 0) {
      const numCharacters = Math.floor(Math.random() * (word.length - 1)) + 1;
      let selectedChars = word.slice(0, numCharacters);
      selectedChars = shuffleString(selectedChars);
      key += selectedChars;
    }
  });
  const randomLength = Math.floor(Math.random() * (4)) + 2;
  return key.slice(0, randomLength);
};
