export const transformTemplateFields = (templateFields) => {
  const transformedObject = {};

  templateFields.forEach((field) => {
    const key = `custom_case_${field.name.toLowerCase().replace(/\s+/g, '_')}`;
    transformedObject[key] = field.value;
  });

  return transformedObject;
};

export const transformCustomSteps = (steps) => {
  const transformedSteps = steps.map((step) => {
    const transformedStep:any = {
      content: step.description || '',
      expected: step.expectedResult || '',
    };
    if (step.sharedStepUid) {
      transformedStep.shared_step_id = step.sharedStepUid;
    }

    if (!step.shared) {
      transformedStep.additional_info = '';
      transformedStep.refs = '';
    }

    return transformedStep;
  });

  return transformedSteps.length > 0 ? transformedSteps : null;
};

export const getCaseCustomFields = (templateFields, fields, body) => {
  const customFields = [];
  const fieldMap = {};
  fields.forEach((field) => {
    fieldMap[field.name.toLowerCase().replace(/\s+/g, '_')] = field.value;
  });
  templateFields.forEach((field) => {
    const key = `custom_case_${field.name.toLowerCase().replace(/\s+/g, '_')}`;
    let value = body[key];
    if (!value) {
      value = fieldMap[field.name.toLowerCase().replace(/\s+/g, '_')];
    }
    if (value) {
      customFields.push({
        ...field,
        value,
      });
    } else {
      customFields.push({
        ...field,
        value: field.defaultValue,
      });
    }
  });
  return {
    templateFields: customFields,
  };
};

export const getCaseSteps = (custom_steps_separated, sharedSteps) => {
  const steps = [];
  let stepIndex = 1;
  if (custom_steps_separated) {
    custom_steps_separated.forEach((step) => {
      if (step.shared_step_id) {
        const sharedStep = sharedSteps[step.shared_step_id];
        sharedStep.steps.forEach((sharedStep) => {
          steps.push({
            id: stepIndex,
            shared: true,
            sharedStepUid: step.shared_step_id,
            title: sharedStep.title,
            children: [],
            description: sharedStep.description,
            expectedResult: sharedStep.expectedResult,
          });
          stepIndex++;
        });
      } else {
        steps.push({
          id: stepIndex,
          title: `Step ${stepIndex}`,
          children: [],
          shared: false,
          description: step.content,
          expectedResult: step.expected,
        });
        stepIndex++;
      }
    });
  }
  return steps;
};
