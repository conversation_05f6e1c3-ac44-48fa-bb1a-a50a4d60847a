import DEFAULT_PREFERENCE from '@app/constants/preference';
import { Org } from '@app/models/org';
import { User } from '@app/models/user';
import _ from 'lodash';

export const handlePreferences = async (
  sharedKnexDB,
  ownerType: 'org' | 'user',
  ownerUid,
) => {
  let preferences: any = {};
  const transaction = await sharedKnexDB.transaction();

  if (ownerType === 'org' && ownerUid) {
    preferences = (
      await Org.query(transaction)
        .findOne('uid', ownerUid)
        .select('preferences')
    ).preferences;
  } else if (ownerType === 'user' && ownerUid) {
    preferences = (
      await User.query(transaction)
        .findOne('uid', ownerUid)
        .select('preferences')
    ).preferences;
  }

  await transaction.commit();
  return _.isEmpty(preferences) ? DEFAULT_PREFERENCE : preferences;
};
