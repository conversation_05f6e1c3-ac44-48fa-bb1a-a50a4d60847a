//  1 time run script to setup integration sync flows for existing integrations

import 'module-alias/register';
import { setupDB } from '../config/db';
import logger from '../config/logger';
import { startWorkflow, terminateWorkflow } from '../temporal/client';
import { sharedModels, tenantModels } from '../models';

async function setupIntegrationSyncFlows(handleName: string, cronSchedule: string, integrationUid?: string) {
  const db = setupDB();
  logger.info('Starting integration sync flows migration');
  try {
    const Handle = sharedModels.Handle.bindKnex(db);
    logger.info('Fetching handles');
    const handle = await Handle.query()
      .where({
        ownerType: 'org',
        current: true,
        name: handleName,
      }).first();

    if (!handle) {
      throw new Error(`Handle ${handleName} not found`);
    }

    logger.info(`Processing handle: ${handle.name}`);
    const tenantDb = setupDB(handle.ownerUid);
    const integrationsList = [];
    try {
      const Integration = tenantModels.Integration.bindKnex(tenantDb);
      if (integrationUid) {
        const integration = await Integration.query()
          .whereNull('deletedAt')
          .whereNull('archivedAt')
          .where('uid', integrationUid)
          .first();
        integrationsList.push(integration);
      } else {
        const integrations = await Integration.query()
          .whereNull('deletedAt')
          .whereNull('archivedAt')
          .where('service', 'testrail');
        integrationsList.push(...integrations);
      }
      logger.info(`Found ${integrationsList.length} integrations to process`);
      for (const integration of integrationsList) {
        try {
          const workflowId = `${handle.name}:integration:sync:${integration.service}:${integration.uid}`;
          try {
            await terminateWorkflow('integration-sync-queue', workflowId);
          } catch (error) {
            logger.error(`Error terminating workflow for handle: ${handle.name} integration: ${integration.uid}:`, error);
          }
          await startWorkflow('integrationSyncWorkflow', {
            taskQueue: 'integration-sync-queue',
            workflowId,
            args: [handle.ownerUid, integration.uid],
            cronSchedule,
          });
          logger.info(`Started sync workflow for handle: ${handle.name} integration: ${integration.uid}`);
        } catch (error) {
          logger.error(`Error starting workflow for handle: ${handle.name} integration: ${integration.uid}:`, error);
        }
      }
    } catch (handleError) {
      logger.error(`Error processing handle ${handle.name}:`, handleError);
    } finally {
      await tenantDb.destroy();
    }
  } catch (error) {
    logger.error('Script failed:', error);
  } finally {
    // Close main DB connection
    if (db) {
      await db.destroy();
    }
  }
}
if (require.main === module) {
  const args = process.argv.slice(2);
  const handleName = args[0];
  const cronSchedule = args[1];
  const integrationUid = args[2];
  setupIntegrationSyncFlows(handleName, cronSchedule, integrationUid)
    .then(() => {
      logger.info('Successfully setup integration sync flows');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Error:', error);
      process.exit(1);
    });
}
