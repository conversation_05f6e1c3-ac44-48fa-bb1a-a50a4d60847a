export const transformError = (error: any) => {
  let errorString = '';
  if (error?.response?.data) {
    if (Array.isArray(error.response.data.errorMessages)) {
      errorString += error.response.data.errorMessages.join('\n');
    }
    if (error.response.data.errors) {
      errorString += Object.values(error.response.data.errors).join('\n');
    }
    if (error.response.data.error) {
      errorString += error.response.data.error;
    }
    if (error.response.data.message) {
      errorString += error.response.data.message;
    }
  } else if (error?.message) {
    errorString += error.message;
  }

  return errorString;
};
