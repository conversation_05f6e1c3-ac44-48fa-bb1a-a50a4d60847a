import logger from '@app/config/logger';
import env from '@app/config/env';
import fs from 'fs';
import * as media from '@ss-libs/ss-component-media';
import { authorizeUpload } from '@app/middlewares/storage';
import { Knex } from 'knex';
import { KEY_MANAGER } from '@app/config/keyManagerLoader';

export function initializeStorage(db: Knex) {
  const isNonUpstream = ['development', 'test'].includes(env.NODE_ENV);
  const gcpCredsVar = env.GC_SERVICE_KEY_FILE;
  let gcpCredentials;

  if (isNonUpstream) {
    if (fs.existsSync(gcpCredsVar)) {
      gcpCredentials = {
        ...JSON.parse(fs.readFileSync(env.GC_SERVICE_KEY_FILE).toString()),
        bucket: env.GCS_BUCKET_NAME,
      };
    } else {
      logger.warn('Failed to init shared cloud storage!');
    }
  } else {
    gcpCredentials = {
      ...JSON.parse(env.GC_SERVICE_KEY_FILE),
      bucket: env.GCS_BUCKET_NAME,
    };
  }

  media.init({
    db,
    default: {
      provider: 'gcp',
      credential: { ...gcpCredentials },
    },
    getOwner: (req, res) => authorizeUpload(req, res),
    types: {
      attachment: { byos: 'always' },
      'profile-picture': { byos: 'never', publicRead: true },
    },
    encryptionConfig: {
      keyManager: KEY_MANAGER,
    },
    migrationTaskConfig: {
      workflowName: 'storageMigrationWorkflow',
      queueName: 'storage-migration-queue',
    },
  });
}
