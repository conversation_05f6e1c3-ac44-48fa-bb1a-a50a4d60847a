import { FgaService } from '@ss-libs/ss-component-auth';
import { Knex } from 'knex';
import crypto from 'crypto';
import dayjs from 'dayjs';
import { createAccessToken } from './createAccessToken';

const generateAppKeyAndUserData = async (
  db: Knex,
  user,
  openfga: FgaService,
) => {
  const newToken = await createAccessToken(
    db,
    `app_${crypto.randomBytes(8).toString('hex')}`,
    user.uid,
    'user',
    dayjs().add(90, 'day').format('YYYY-MM-DD HH:mm:ss'),
  );
  const relatedOrgs = await openfga.getRelatedOrgOfUser(user.uid);
  const orgs = await db('orgs')
    .join('handles', 'handles.ownerUid', '=', 'orgs.uid')
    .select({
      uid: 'orgs.uid',
      handle: 'handles.name',
      avatarUrl: 'orgs.avatarUrl',
      name: 'orgs.name',
    })
    .whereIn(
      'orgs.uid',
      relatedOrgs.map((t) => t.key.object.split(':')[1]),
    )
    .where('orgs.deletedAt', null);
  return {
    accessToken: newToken.accessTokenFinal,
    expiresAt: newToken.expiresAt,
    user,
    orgs,
  };
};

export { generateAppKeyAndUserData };
