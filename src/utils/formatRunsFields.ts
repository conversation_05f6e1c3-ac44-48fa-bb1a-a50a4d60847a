export const formatCaseCount = (frequency, statusColors) => {
  const runStatus = statusColors.filter((status) => status.entityType === 'testCase');
  const caseCount:any = {};
  runStatus.forEach((status) => {
    const statusCount = frequency && frequency[status.id] ? frequency[status.id] : 0;
    caseCount[`custom_${status.name.toLowerCase()}_count`] = statusCount;
  });
  return caseCount;
};

export const formatConfigs = (runConfigs, configs) => {
  const configIds = runConfigs.map((config) => Number(config.split('::')[1]));
  const configNames = configs.map((config) => config.option);
  return {
    config_ids: configIds,
    config: configNames.length > 0 ? configNames.join(',') : null,
  };
};
