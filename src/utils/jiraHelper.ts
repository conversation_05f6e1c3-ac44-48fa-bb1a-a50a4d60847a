import {
  a,
  b,
  blockQuote,
  code,
  codeBlock,
  doc,
  em,
  heading,
  li,
  mention,
  ol,
  p,
  table,
  tableCell,
  tableHeader,
  tableRow,
  text,
  textColor,
  u,
  ul,
} from '@atlaskit/adf-utils/builders';

import axios from 'axios';
import logger from '@app/config/logger';
import { parse } from 'node-html-parser';

// Helper function to get project assignable users
export async function getProjectAssignableUsers(params, authHeader) {
  try {
    const url = `https://api.atlassian.com/ex/jira/${params.resourceId}/rest/api/3/user/assignable/search?project=${params.projectId}`;
    const response = await axios.get(url, authHeader);
    const users = response.data
      .filter((user: any) => user.accountType === 'atlassian' && user.active)
      .map((user: any) => ({
        id: user.accountId,
        name: user.displayName,
      }));
    return {
      success: true,
      data: users,
    };
  } catch (error) {
    logger.error('Error fetching project assignable users:', error);
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status,
    };
  }
}

// Helper function to get Jira users for reporters list
export async function getJiraUsers(params:any, authHeader:any) {
  try {
    const url = `https://api.atlassian.com/ex/jira/${params.resourceId}/rest/api/3/user/search?query=`;
    const response = await axios.get(url, authHeader);
    const users = response.data.filter((user: any) => user.accountType === 'atlassian' && user.active)
      .map((user: any) => ({
        id: user.accountId,
        name: user.displayName,
      }));
    return {
      success: true,
      data: users,
    };
  } catch (error) {
    logger.error('Error fetching Jira users:', error);
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status,
    };
  }
}

export async function getJiraBoards(params, authHeader) {
  try {
    const url = `https://api.atlassian.com/ex/jira/${params.resourceId}/rest/agile/1.0/board`;
    const response = await axios.get(url, authHeader);
    const boards = response.data.values
      .map((board: any) => ({
        id: board.id,
        name: board.name,
      }));
    return {
      success: true,
      data: boards,
    };
  } catch (error) {
    logger.error('Error fetching Jira boards:', error);
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status,
    };
  }
}

export async function getJiraSprints(params, authHeader) {
  try {
    const url = `https://api.atlassian.com/ex/jira/${params.resourceId}/rest/agile/1.0/board/${params.boardId}/sprint`;
    const response = await axios.get(url, authHeader);
    const sprints = response.data.values.map((sprint: any) => ({
      id: sprint.id,
      name: sprint.name,
    }));
    return {
      success: true,
      data: sprints,
    };
  } catch (error) {
    logger.error('Error fetching Jira sprints:', error);
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status,
    };
  }
}

export async function IssueTypes(params, authHeader) {
  try {
    const url = `https://api.atlassian.com/ex/jira/${params.resourceId}/rest/api/3/issuetype`;
    const response = await axios.get(url, authHeader);
    // Filter issue types based on project scope or global types
    const filteredTypes = response.data.filter((issueType) => !issueType.scope
             || (issueType.scope.type === 'PROJECT'
              && issueType.scope.project.id === params.projectId));

    // Map to only include id and name
    const simplifiedTypes = filteredTypes.map((issueType) => ({
      id: issueType.id,
      name: issueType.name,
    }));

    return {
      success: true,
      data: simplifiedTypes,
    };
  } catch (error) {
    logger.error('Error fetching issue types:', error);
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status,
    };
  }
}

export async function getCreateMeta(params, authHeader) {
  try {
    const url = `https://api.atlassian.com/ex/jira/${params.resourceId}/rest/api/3/issue/createmeta`;
    const response = await axios.get(url, {
      ...authHeader,
      params: {
        expand: 'projects.issuetypes.fields',
      },
    });

    // Map the response data
    const projectMap: Record<string, any> = {};
    response.data.projects.forEach((project: any) => {
      const issueTypesMap: Record<string, any> = {};

      project.issuetypes.forEach((issuetype: any) => {
        issueTypesMap[issuetype.id] = issuetype.fields;
      });

      projectMap[project.id] = issueTypesMap;
    });

    return {
      success: true,
      data: projectMap,
    };
  } catch (error) {
    logger.error('Error fetching create metadata:', error);
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status,
    };
  }
}

export async function transformFields(
  fieldsToUpdate: any,
  metaFields: any,
  authHeader: any,
): Promise<{ fields: any }> {
  const transformedFields: any = {};
  let adfContent: any;

  for (const [fieldKey, fieldValue] of Object.entries(fieldsToUpdate)) {
    const fieldMeta = metaFields[fieldKey];

    if (!fieldMeta) {
      continue;
    }

    const fieldSchema = fieldMeta.schema;

    try {
      switch (fieldSchema.type) {
        case 'string':
          if (fieldSchema.system === 'description') {
            adfContent = htmlToADF(fieldValue as string);
            transformedFields[fieldKey] = adfContent;
          } else {
            transformedFields[fieldKey] = fieldValue;
          }
          break;
        case 'number':
          transformedFields[fieldKey] = Number(fieldValue);
          break;
        case 'array':
          if (fieldSchema.items === 'option') {
            transformedFields[fieldKey] = mapOptionsArray(
              fieldValue as string[],
              fieldMeta.allowedValues,
            );
          } else if (fieldSchema.items === 'string') {
            transformedFields[fieldKey] = fieldValue as string[];
          } else if (fieldSchema.items === 'user') {
            transformedFields[fieldKey] = await mapUsersArray(
              fieldValue as string[],
              authHeader,
            );
          } else {
            transformedFields[fieldKey] = fieldValue;
          }
          break;
        case 'option':
          transformedFields[fieldKey] = mapOptionValue(
            fieldValue as string,
            fieldMeta.allowedValues,
          );
          break;
        case 'user':
          transformedFields[fieldKey] = await mapUserValue(
            fieldValue as string,
            authHeader,
          );
          break;
        case 'datetime':
          transformedFields[fieldKey] = new Date(
            fieldValue as string | number | Date,
          ).toISOString();
          break;
        case 'issuetype':
          transformedFields[fieldKey] = mapOptionValue(
            fieldValue as string,
            fieldMeta.allowedValues,
          );
          break;
        case 'priority':
          transformedFields[fieldKey] = mapOptionValue(
            fieldValue as string,
            fieldMeta.allowedValues,
          );
          break;
        default:
          transformedFields[fieldKey] = fieldValue;
          break;
      }
    } catch (error) {
      throw new Error(`Error transforming field ${fieldKey}: ${error.message}`);
    }
  }

  return {
    fields: transformedFields,
  };
}

export function htmlToADF(htmlContent: string): any {
  const root = parse(htmlContent);
  let style: string;
  let colorMatch: RegExpMatchArray | null;
  let color: string;
  let hexColor: string;

  function traverse(node: any): any {
    // Handle text nodes
    if (node.nodeType === 3) {
      const trimmedText = node.rawText.trim();
      return trimmedText ? text(trimmedText) : null;
    }

    // Recursively process child nodes
    const children = node.childNodes.map(traverse).filter(Boolean); // Remove null/undefined values

    switch (node.tagName?.toLowerCase()) {
      case 'p':
        return p(...children);

      case 'a': {
        const href = node.getAttribute('href');
        const title = node.getAttribute('title');
        if (href?.startsWith('mention://')) {
          // Handle mentions
          const userId = href.replace('mention://', '');
          return mention({ id: userId, text: node.rawText });
        }
        return a({ href, title })(children);
      }

      case 'b':
      case 'strong':
        return b(children);
      case 'u':
        return u(children);

      case 'i':
      case 'em':
        return em(children);

      case 'blockquote':
        return blockQuote(...children);

      case 'pre':
        return codeBlock({
          language:
            node.getAttribute('class')?.replace('language-', '') || 'plain',
        })(...children);
      case 'code':
        return code(children);

      case 'ol':
        return ol(children);
      case 'ul':
        return ul(...children);

      case 'li':
        return li(children);

      case 'h1':
        return heading({ level: 1 })(...children);

      case 'h2':
        return heading({ level: 2 })(...children);

      case 'h3':
        return heading({ level: 3 })(...children);

      case 'h4':
        return heading({ level: 4 })(...children);

      case 'h5':
        return heading({ level: 5 })(...children);

      case 'h6':
        return heading({ level: 6 })(...children);

      case 'table':
        return table(...children);
      case 'tr':
        return tableRow(children);

      case 'td':
        return tableCell(children);
      case 'th':
        return tableHeader(...children);

      case 'span': {
        style = node.getAttribute('style');
        if (style?.includes('color:')) {
          colorMatch = style.match(/color:\s*(#[0-9a-f]{6}|rgb\([^)]+\))/i);
          if (colorMatch) {
            [color] = colorMatch;
            hexColor = color.startsWith('rgb') ? rgbToHex(color) : color;
            return textColor({ color: hexColor })(children);
          }
        }
        return text(children);
      }

      default: {
        return children.length ? p(...children) : null;
      }
    }
  }

  const content = traverse(root);
  return doc(content);
}

function rgbToHex(rgbString: string): string {
  const rgb = rgbString
    .replace(/[^\d,]/g, '')
    .split(',')
    .map(Number);

  const toHex = (n: number): string => {
    const hex = n.toString(16);
    return hex.length === 1 ? `0${hex}` : hex;
  };

  return `#${toHex(rgb[0])}${toHex(rgb[1])}${toHex(rgb[2])}`;
}

function mapOptionValue(fieldValue: string, allowedValues: any[]): any {
  const matchedOption = allowedValues.find((option: any) => Number(option.id) === Number(fieldValue));

  if (matchedOption) {
    return { id: matchedOption.id };
  }
  throw new Error(`Invalid option value: ${fieldValue}`);
}

function mapOptionsArray(fieldValues: string[], allowedValues: any[]): any[] {
  return fieldValues.map((value) => {
    const matchedOption = allowedValues.find(
      (option: any) => option.value.toLowerCase() === value.toLowerCase(),
    );

    if (matchedOption) {
      return { id: matchedOption.id };
    }
    throw new Error(`Invalid option value: ${value}`);
  });
}

async function mapUserValue(fieldValue: string, authHeader: any): Promise<any> {
  const users = await searchUsers(fieldValue, authHeader);

  const matchedUser = users.find(
    (user: any) => user.displayName.toLowerCase() === fieldValue.toLowerCase()
      || user.emailAddress.toLowerCase() === fieldValue.toLowerCase(),
  );

  if (matchedUser) {
    return { accountId: matchedUser.accountId };
  }
  throw new Error(`User not found: ${fieldValue}`);
}

async function searchUsers(query: string, authHeader: any): Promise<any[]> {
  const url = 'https://api.atlassian.com/ex/jira/{cloudId}/rest/api/3/user/search';
  const response = await axios.get(url, {
    params: { query },
    headers: authHeader.headers,
  });
  return response.data;
}

async function mapUsersArray(
  fieldValues: string[],
  authHeader: any,
): Promise<any[]> {
  return Promise.all(
    fieldValues.map(async (value) => mapUserValue(value, authHeader)),
  );
}
