import { TestExecution } from '@app/models/testExecution';
import { handlePreferences } from './handlePreferences';

let defaultStatus = null;
const isCompleted = [];

// DEPRECATED
export const getExecutionsProgress = async (sharedKnexDB, ownerType : 'user' | 'org', ownerUid, executions: { list: TestExecution[], runStatus?: number | null }[]) => {
  let completedExecutions = 0;
  let totalExecutions = 0;
  const userPreferences = await handlePreferences(sharedKnexDB, ownerType, ownerUid);
  const testCaseStatuses = userPreferences.statusColors
    .filter((status) => status.entityType === 'testCase');
  const completedStatusIds = userPreferences.statusColors
    .filter((status) => status.entityType === 'testRun' && status.isCompleted).map((item) => item.id);

  const executionsProgress = testCaseStatuses
    .reduce((acc, status) => {
      const key = status.name.toLowerCase().replace(/\s+/g, '');
      if (status.isDefault) defaultStatus = key;

      if (status.isCompleted) isCompleted.push(key);

      acc[key] = {
        id: status.id,
        name: status.name,
        count: 0,
      };
      return acc;
    }, {});

  const statusMapping = testCaseStatuses.reduce((acc, item) => {
    acc[item.id] = item;
    return acc;
  }, {});

  if (defaultStatus && executions.length) {
    executions.forEach((item) => {
      item.list?.forEach((element) => {
        const statusName = statusMapping[element.status]?.name.toLowerCase();
        if (statusName && statusName !== defaultStatus && executionsProgress[statusName.replace(/\s+/g, '')]) {
          completedExecutions += 1;
        }

        if (statusName) { executionsProgress[statusName?.replace(/\s+/g, '')].count += 1; }
      });

      const isCompleted = completedStatusIds.includes(item.runStatus);

      if (isCompleted) {
        totalExecutions += item.list.filter((element) => {
          const statusName = statusMapping[element.status]?.name.toLowerCase();
          return statusName && statusName !== defaultStatus;
        }).length;
      } else { totalExecutions += item.list?.length ?? 0; }
    });
  }

  const percentage = completedExecutions ? parseInt((
    (completedExecutions / totalExecutions)
    * 100
  ).toFixed(0)) : 0;

  return {
    executionsProgress,
    percentage,
  };
};
