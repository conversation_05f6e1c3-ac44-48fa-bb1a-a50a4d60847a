import winston, { Logger, transports } from 'winston';

// import appRoot from 'app-root-path';
import { asyncLocalStorage } from '@app/middlewares/requestTraceId';

const options = {
  // file: {
  //  level: 'info',
  //  filename: `${appRoot}/logs/app.log`,
  //  handleExceptions: true,
  //  json: true,
  //  maxsize: 5242880, // 5MB
  //  maxFiles: 5,
  //  colorize: false,
  // },
  console: {
    level: 'debug',
    handleExceptions: true,
    json: true,
    colorize: true,
  },
};

const uuidFormat = winston.format((info) => ({
  ...info,
  traceId: asyncLocalStorage.getStore(),
}));

const logger: Logger = winston.createLogger({
  transports: [
    // new transports.File(options.file),
    new transports.Console(options.console),
  ],
  format: winston.format.combine(
    uuidFormat(),
    winston.format.errors({ stack: true }),
    winston.format.json(),
  ),
  exitOnError: false, // do not exit on handled exceptions
});

export default logger;
