import { KeyManager } from '@ss-libs/ss-component-encryption';
import { Knex } from 'knex';
import logger from '@app/config/logger';

const keyManager = KeyManager.getInstance();

const SERVICES = ['integration', 'auth', 'media'];

export async function loadAllPrivateKeys(db: Knex) {
  for (const service of SERVICES) {
    try {
      const latestPublicKey = await keyManager.getLatestPublicKey(db, service);
      const keyName = `${service}-${latestPublicKey.version}-key`;
      const envKey = process.env[`${service.toUpperCase()}_${latestPublicKey.version}_KEY`];
      logger.info(`Loaded private key for ${service}: ${keyName}`);
      if (!envKey) {
        logger.error(`❌ Missing environment variable: ${service.toUpperCase()}_${latestPublicKey.version}_KEY`);
        continue;
      }

      keyManager.loadPrivateKey(keyName, envKey);
      logger.info(`✅ Loaded private key for ${service}: ${keyName}`);
    } catch (err) {
      logger.error(`❌ Failed to load private key for ${service}: ${err.message}`);
    }
  }
}

export const KEY_MANAGER = keyManager;
