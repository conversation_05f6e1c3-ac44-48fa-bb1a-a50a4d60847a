import { OpenFgaClient, Store } from '@openfga/sdk';

import fs from 'fs';
import path from 'path';
import env from './env';

interface SetupConfig {
  storeName?: string;
  storeId?: string;
  authModelId?: string;
}

async function createStore(client: OpenFgaClient, name: string) {
  let store: Store;
  let continuationToken: string;

  while (!store) {
    const storeQuery = await client.listStores({
      pageSize: 100,
      continuationToken,
    });
    store = storeQuery.stores.find((s) => s.name === name);
    continuationToken = storeQuery.continuation_token;
    if (!continuationToken) break;
  }

  return store || (await client.createStore({ name }));
}

export async function setupOpenfga(opts?: SetupConfig) {
  if (!opts) opts = { storeName: env.OPENFGA_DATASTORE_NAME };

  const client = new OpenFgaClient({
    apiScheme: env.OPENFGA_DATASTORE_API_SCHEME,
    apiHost: env.OPENFGA_DATASTORE_HOST,
  });

  if (opts.storeId) {
    client.storeId = opts.storeId;
  } else {
    const store = await createStore(client, opts.storeName);
    client.storeId = store.id;
  }

  const migrationsPath = path.join(process.cwd(), 'dist/db/fgaMigrations');

  const files = fs
    .readdirSync(migrationsPath)
    .filter((element) => element.endsWith('js'));

  // eslint-disable-next-line import/no-dynamic-require, global-require, @typescript-eslint/no-var-requires
  const { authModel } = require(
    path.join(migrationsPath, files[files.length - 1]),
  );

  if (opts.authModelId) {
    client.authorizationModelId = opts.authModelId;
  } else {
    const model = await client.writeAuthorizationModel(authModel);
    client.authorizationModelId = model.authorization_model_id;
  }

  return client;
}
