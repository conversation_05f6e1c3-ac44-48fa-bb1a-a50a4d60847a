import joi from 'joi';
import dotenv from 'dotenv';
import dotenvExpand from 'dotenv-expand';
import { DataValidationError, joiCSV, validate } from '@app/lib/joi';

const environments = <const>['staging', 'development', 'production', 'test'];
export interface ApplicationEnv {
  NODE_ENV: (typeof environments)[number];
  PORT: number;
  FRONTEND_URL: string;
  BACKEND_URL: string;
  MAIL_USER: string;
  API_VERSION_1_ROUTE: string;
  API_INTERNAL_ROUTE: string;
  CORS_ORIGINS: string[]; // allowed origins for CORS reasons

  SENDGRID_KEY: string;
  SENDGRID_SIGNING_SECRET: string;
  JWT_EXPIRATION_TIME: string;
  JWT_SIGNING_SECRET: string;
  JWT_SECRET: string;

  DB_HOST: string;
  DB_DATABASE: string;
  DB_USERNAME: string;
  DB_PASSWORD: string;
  DB_PORT: number;

  OPENFGA_DATASTORE_HOST: string;
  OPENFGA_DATASTORE_API_SCHEME: string;
  OPENFGA_DATASTORE_NAME: string;
  OPENFGA_DATASTORE_STORE_ID?: string;
  OPENFGA_DATASTORE_AUTH_MODEL?: string;

  REDIS_HOST: string;
  REDIS_PORT: number;
  REDIS_PASSWORD: string;
  REDIS_CA?: string;

  STRIPE_SECRET_KEY: string;
  STRIPE_SIGNING_SECRET: string;
  STRIPE_DEFAULT_USER_PRICE_ID: string;
  STRIPE_DEFAULT_ORG_PRICE_ID: string;

  GC_SERVICE_KEY_FILE: string;
  GCS_BUCKET_NAME: string;

  OAUTH_JIRA_CLIENT_ID?: string;
  OAUTH_JIRA_CLIENT_SECRET?: string;

  OAUTH_GITHUB_CLIENT_ID?: string;
  OAUTH_GITHUB_CLIENT_SECRET?: string;
  SENTRY_ENABLED: string;
  SENTRY_DSN?: string;

  TEMPORAL_HOST: string;
  TEMPORAL_PORT: number;

  OAUTH_GOOGLE_CLIENT_ID?: string;
  OAUTH_GOOGLE_CLIENT_SECRET?: string;
  OAUTH_GOOGLE_CALLBACK_URL?: string;
  OAUTH_GOOGLE_REDIRECT_SIGN_IN_CALLBACK_URL?: string;
  OAUTH_GOOGLE_REDIRECT_SIGN_UP_CALLBACK_URL?: string;

  OIDC_REDIRECT_CALLBACK_URL?: string;
  OIDC_CALLBACK_URL?: string;
}

const requiredString = () => joi.string().required();

const schema: Record<keyof ApplicationEnv, joi.Schema> = {
  NODE_ENV: requiredString().valid(...environments),
  PORT: joi.number().required(),
  SENDGRID_KEY: requiredString(),
  SENDGRID_SIGNING_SECRET: requiredString(),
  FRONTEND_URL: requiredString().uri(),
  BACKEND_URL: requiredString().uri(),
  MAIL_USER: requiredString().email(),
  API_VERSION_1_ROUTE: joi.string().default('/v1'),
  API_INTERNAL_ROUTE: joi.string().default('/core'),

  CORS_ORIGINS: joiCSV(
    joi
      .string()
      .uri({ scheme: ['http', 'https'] })
      .required(),
  ).required(),

  JWT_EXPIRATION_TIME: requiredString(),
  JWT_SIGNING_SECRET: requiredString(),
  JWT_SECRET: requiredString(),

  DB_HOST: requiredString(),
  DB_DATABASE: requiredString(),
  DB_USERNAME: requiredString(),
  DB_PASSWORD: requiredString(),
  DB_PORT: joi.number().required(),

  OPENFGA_DATASTORE_HOST: requiredString(),
  OPENFGA_DATASTORE_API_SCHEME: requiredString(),
  OPENFGA_DATASTORE_NAME: requiredString(),
  OPENFGA_DATASTORE_STORE_ID: joi.string().optional(),
  OPENFGA_DATASTORE_AUTH_MODEL: joi.string().optional(),

  REDIS_HOST: requiredString(),
  REDIS_PORT: joi.number().required(),
  REDIS_PASSWORD: joi.string().optional(),
  REDIS_CA: joi.string().optional(),

  STRIPE_SECRET_KEY: requiredString(),
  STRIPE_SIGNING_SECRET: requiredString(),
  STRIPE_DEFAULT_USER_PRICE_ID: requiredString(),
  STRIPE_DEFAULT_ORG_PRICE_ID: requiredString(),

  GC_SERVICE_KEY_FILE: requiredString(),
  GCS_BUCKET_NAME: requiredString(),

  OAUTH_JIRA_CLIENT_ID: joi.string().optional(), // requiredString(),
  OAUTH_JIRA_CLIENT_SECRET: joi.string().optional(), // requiredString(),

  OAUTH_GITHUB_CLIENT_ID: joi.string().optional(), // requiredString(),
  OAUTH_GITHUB_CLIENT_SECRET: joi.string().optional(), // requiredString(),
  SENTRY_ENABLED: joi.string().default('false'),
  SENTRY_DSN: joi.string().optional(),

  TEMPORAL_HOST: requiredString(),
  TEMPORAL_PORT: joi.number().required(),

  OAUTH_GOOGLE_CLIENT_ID: joi.string().optional(),
  OAUTH_GOOGLE_CLIENT_SECRET: joi.string().optional(),
  OAUTH_GOOGLE_CALLBACK_URL: joi.string().optional(),
  OAUTH_GOOGLE_REDIRECT_SIGN_IN_CALLBACK_URL: joi.string().optional(),
  OAUTH_GOOGLE_REDIRECT_SIGN_UP_CALLBACK_URL: joi.string().optional(),

  OIDC_REDIRECT_CALLBACK_URL: joi.string().optional(),
  OIDC_CALLBACK_URL: joi.string().optional(),

};

function loadEnv() {
  try {
    return validate(process.env, schema);
  } catch (err) {
    if (err instanceof DataValidationError) {
      throw new Error(
        `Unable to load environment:\n${JSON.stringify(err.messages, null, 2)}`,
      );
    }
  }
}

dotenvExpand.expand(dotenv.config());

const env: ApplicationEnv = loadEnv();

export default env;
