import knex, { Knex } from 'knex';

import path from 'path';
import pg from 'pg';
import { DBServer } from '@app/models/dbServer';
import env from './env';

// ensure numeric types are parsed properly
pg.types.setTypeParser(pg.types.builtins.NUMERIC, parseFloat);

const config: knex.Knex.StaticConnectionConfig = {
  multipleStatements: true,
  host: env.DB_HOST,
  port: env.DB_PORT,
  user: env.DB_USERNAME,
  password: env.DB_PASSWORD,
  database: env.DB_DATABASE,
};

const tenantMigrationsPath = path.join(process.cwd(), 'dist/db/migrations');

let sharedDB: Knex;

/**
 * returns a knex DB client. if a tenantDb name isn't passed,
 * it returns a knex connection to the shared db instance, this shared db instance is
 * also cached in global state
 * @param tenantDb
 * @returns
 */
export function setupDB(tenantDb?: string, dbServer?: DBServer) {
  if (!tenantDb) {
    sharedDB = sharedDB ?? knex({ client: 'pg', connection: { ...config } });
    return sharedDB;
  }

  if (!dbServer) {
    return knex({ client: 'pg', connection: { ...config, database: tenantDb } });
  }

  return knex({
    client: 'pg',
    connection: {
      multipleStatements: true,
      host: dbServer.host,
      user: dbServer.username,
      password: dbServer.password,
      port: dbServer.port,
      database: tenantDb,
    },
  });
}

export function setupDBFromConfig(dbServer: DBServer) {
  return knex({
    client: 'pg',
    connection: {
      multipleStatements: true,
      host: dbServer.host,
      user: dbServer.username,
      password: dbServer.password,
      port: dbServer.port,
      database: 'postgres',
    },
  });
}

export function migrateTenantDB(trx: Knex) {
  return trx.migrate.latest({
    directory: tenantMigrationsPath,
    loadExtensions: ['.js'],
  });
}
