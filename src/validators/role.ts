import { joiValidate } from '@app/middlewares/validator';
import joi from 'joi';
import errorConstants from '@app/constants/errors';
import { permissions } from '@app/constants/auth';

const validateCreateRole = joiValidate(joi.object().keys({
  name: joi.string().required(),
  description: joi.string().allow('').optional(),
  permissions: joi
    .array()
    .items(
      joi.string().custom((value, helpers) => {
        if (!permissions[value]) {
          return helpers.error('any.invalid', { value });
        }
        return value;
      }),
    )
    .min(1)
    .messages({
      'array.min': errorConstants.PERMISSIONS_REQUIRED,
      'any.invalid': errorConstants.INVALID_PERMISSION,
    }),
  tagUids: joi
    .array()
    .items(joi.number().optional())
    .optional()
    .default([]),
}));

const validateFindRole = joiValidate(joi.object().keys({
  id: joi.string().required().uuid(),
}), 'params');

const validateGetMemberRoles = joiValidate(joi.object().keys({
  userUid: joi.string().required().uuid(),
}), 'params');

const validateRoleIds = joiValidate(joi.object().keys({
  roleIds: joi
    .array()
    .items(joi.string().required().uuid())
    .min(1),
}));

const validateReAssignMembers = joiValidate(joi.object().keys({
  members: joi
    .array()
    .min(1)
    .items(
      joi.object().keys({
        userId: joi.string().required().uuid(),
      }),
    )
    .required(),
  overriddenRoles: joi
    .array()
    .items(
      joi.object().keys({
        roleUid: joi.string().optional().uuid(),
        projectUid: joi.number().optional(),
      }),
    )
    .optional(),
}));

const validateUpdateRole = joiValidate(joi.object().keys({
  name: joi.string().optional(),
  description: joi.string().optional(),
  permissions: joi
    .array()
    .items(
      joi.string().custom((value, helpers) => {
        if (!permissions[value]) {
          return helpers.error('any.invalid', { value });
        }
        return value;
      }),
    )
    .min(1)
    .messages({
      'array.min': errorConstants.PERMISSIONS_REQUIRED,
      'any.invalid': errorConstants.INVALID_PERMISSION,
    }),
  tagUids: joi
    .array()
    .items(joi.number())
    .optional()
    .default([]),
}));

const validateAddMembers = joiValidate(joi.object().keys({
  userIds: joi
    .array()
    .items(joi.string().required().uuid())
    .min(1),
}));

const validateGetRoles = joiValidate(joi.object().keys({
  includeUsers: joi
    .boolean()
    .optional()
    .default(false),
  per_page: joi
    .number()
    .optional()
    .default(10)
    .min(1)
    .max(250),
  q: joi
    .string()
    .allow('')
    .optional()
    .default(''),
  includeProjects: joi
    .array()
    .optional()
    .items(joi.number().required())
    .default([]),
  includeRoleCounts: joi
    .boolean()
    .optional()
    .default(false),
  includePermissions: joi
    .boolean()
    .optional()
    .default(false),
  includeOrgRoles: joi
    .boolean()
    .optional()
    .default(false),
  includeProjectLevelRoles: joi
    .boolean()
    .optional()
    .default(false),
  current_page: joi
    .number()
    .optional()
    .default(1)
    .min(1),
}), 'query');

export {
  validateCreateRole,
  validateFindRole,
  validateUpdateRole,
  validateAddMembers,
  validateGetMemberRoles,
  validateRoleIds,
  validateReAssignMembers,
  validateGetRoles,
};
