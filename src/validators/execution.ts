import {
  isPaginatedQuery,
  joiValidate,
  validate,
} from '@app/middlewares/validator';

import { body } from 'express-validator';
import errorConstants from '@app/constants/errors';
import joi from 'joi';
import { joiCSV } from '@app/lib/joi';
import { executionRelations } from '@app/types/execution';

const validateUpdateExecutions = validate([
  body('executionIds').notEmpty().isArray({ min: 1 }),
  body('executionIds.*')
    .isNumeric()
    .withMessage(errorConstants.ID_MUST_BE_NUMBER),
  body('assignedTo').optional({ nullable: true }).notEmpty().isString(),
]);

const tagUids = joi.array().items(joi.number()).default([]);
const executionUpdateBaseSchema = {
  status: joi.number().integer(),
  priority: joi.number().integer(),
  dueAt: joi.date(),
  assignedTo: joi.string().uuid({ version: 'uuidv4' }),
  tagUids,
  tagReplacements: joi
    .array()
    .items(
      joi.object().keys({
        existingTagUids: tagUids,
        newTagUids: tagUids,
      }),
    )
    .default([]),
};

const validateUpdateExecution = joiValidate(
  joi.object().keys({
    name: joi.string(),
    expectedResult: joi.string(),
    steps: joi
      .array()
      .items(
        joi.object({
          uid: joi.string().uuid({ version: 'uuidv4' }),
          position: joi.number().required(),
          title: joi.string(),
          description: joi.string(),
          expectedResult: joi.string(),
          sharedStepUid: joi.number(),
          children: joi.array(),
          status: joi.number(),
        }),
      )
      .default([]),
    templateFields: joi.any(),
    ...executionUpdateBaseSchema,
  }),
);

const validateBulkUpdateExecutions = joiValidate(
  joi.object().keys({
    executionUids: joi.array().min(1).items(joi.number().required()).required(),
    ...executionUpdateBaseSchema,
  }),
);

const validateDeleteSteps = joiValidate(
  {
    stepUids: joiCSV(joi.string().uuid({ version: 'uuidv4' })),
  },
  'query',
);

const validateExecutionUid = joiValidate(
  {
    id: joi.number().integer().required(),
  },
  'params',
);

const validateGetExecutions = joiValidate(
  {
    runUid: joi.number(),
    ...isPaginatedQuery,
  },
  'query',
);

const validateGetMilestonesQuery = joiValidate(
  joi.object({
    projectUids: joi
      .array()
      .items(
        joi.number().integer().messages({
          'number.base': errorConstants.PROJECTUIDS_MUST_BE_NUMBERS,
        }),
      )
      .default([])
      .messages({ 'array.base': errorConstants.PROJECTUIDS_MUST_BE_ARRAY }),
  }),
  'query',
);

const validateGetTestPlansQuery = joiValidate(
  joi.object({
    milestoneUids: joi
      .array()
      .items(
        joi.number().integer().messages({
          'number.base': errorConstants.MILESTONEUIDS_MUST_BE_NUMBERS,
        }),
      )
      .default([])
      .messages({ 'array.base': errorConstants.MILESTONEUIDS_MUST_BE_ARRAY }),
  }),
  'query',
);

const validateGetTestRunsQuery = joiValidate(
  joi.object({
    planUids: joi
      .array()
      .items(
        joi
          .number()
          .integer()
          .messages({ 'number.base': errorConstants.PLANUIDS_MUST_BE_NUMBERS }),
      )
      .default([])
      .messages({ 'array.base': errorConstants.PLANUIDS_MUST_BE_ARRAY }),
  }),
  'query',
);

const validateGetExecutionCountQuery = joiValidate(
  joi.object({
    status: joi
      .array()
      .items(
        joi
          .number()
          .integer()
          .messages({ 'number.base': errorConstants.STATUS_MUST_BE_NUMBERS }),
      )
      .default([])
      .messages({ 'array.base': errorConstants.STATUS_MUST_BE_ARRAY }),
  }),
  'query',
);

const validateGetExecutionsRelationsQuery = joiValidate(
  joi.object({
    relation: joi
      .string()
      .valid(...executionRelations)
      .required(),
    executionUids: joiCSV(joi.number()).required(),
  }),
  'query',
);

export {
  validateUpdateExecutions,
  validateUpdateExecution,
  validateBulkUpdateExecutions,
  validateDeleteSteps,
  validateExecutionUid,
  validateGetExecutions,
  validateGetMilestonesQuery,
  validateGetTestPlansQuery,
  validateGetTestRunsQuery,
  validateGetExecutionCountQuery,
  validateGetExecutionsRelationsQuery,
};
