import joi from 'joi';
import { joiValidate } from '@app/middlewares/validator';
import { SSO_PROVIDERS } from '@app/models/ssoConfig';

const ssoConfigValue = joi.object({
  url: joi.string().required(),
  key: joi.string().required(),
  secret: joi.string().required(),
  allowedOrigins: joi.array().items(joi.string()).optional(),
  groupMappings: joi.object().pattern(joi.string(), joi.string()).optional(),
  defaultRole: joi.string().optional(),
  allowOnlyInvitedAccounts: joi.boolean().optional(),
});

const createSSOConfig = joiValidate(
  joi.object({
    config: joi.object()
      .pattern(joi.string().valid(...SSO_PROVIDERS), ssoConfigValue)
      .unknown(false)
      .required(),
    isActive: joi.boolean().optional(),
    symmetricKeyData: joi.object().required(),
  }),
);

const updateSSOConfig = joiValidate(
  joi.object({
    config: joi.object()
      .pattern(joi.string().valid(...SSO_PROVIDERS), ssoConfigValue)
      .unknown(false)
      .optional(),
    isActive: joi.boolean().optional(),
    symmetricKeyData: joi.object().optional(),
  }),
);

const ssoConfigValidator = {
  createSSOConfig,
  updateSSOConfig,
};

export default ssoConfigValidator;
