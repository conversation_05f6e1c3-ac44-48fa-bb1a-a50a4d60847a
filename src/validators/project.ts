import { body } from 'express-validator';
import joi from 'joi';
import { BLACKLISTED_TOKENS } from '@app/constants/blacklist';
import errorConstants from '@app/constants/errors';
import {
  isPaginatedQuery,
  joiValidate,
  validate,
} from '@app/middlewares/validator';
import { countProjectEntityTypes } from '@app/types/project';

const createProject = joiValidate(
  joi
    .object({
      externalId: joi.string(),
      source: joi.string(),
      name: joi
        .string()
        .when('createDemo', { is: joi.valid(false), then: joi.required() }),
      createDemo: joi.boolean().default(false),
      key: joi
        .string()
        .trim()
        .uppercase()
        .min(2)
        .max(10)
        .disallow(...BLACKLISTED_TOKENS.map((t) => t.toUpperCase()))
        .regex(/^[A-Z0-9_-]+$/)
        .when('createDemo', { is: joi.valid(false), then: joi.required() }),
    })
    .messages({
      'any.required': errorConstants.NAME_FIELD_REQUIRED,
      'string.max': errorConstants.PROJECT_KEY_LENGTH,
      'string.min': errorConstants.PROJECT_KEY_LENGTH,
      'any.invalid': errorConstants.KEY_UNAVAILABLE,
    }),
);

const validateUpdateProject = validate([
  body('externalId').optional(),
  body('source').optional(),
  body('name')
    .optional()
    .not()
    .isEmpty()
    .withMessage(errorConstants.NAME_FIELD_REQUIRED),
]);

const validatePaginatedProjectsQuery = joiValidate(
  {
    ...isPaginatedQuery,
    includeCount: joi.boolean().optional(),
  },
  'query',
);

const projectValidator = {
  createProject,
};

const validateCountProjectEntity = joiValidate(
  {
    entityType: joi
      .string()
      .required()
      .valid(...countProjectEntityTypes),
  },
  'query',
);

export {
  validateUpdateProject,
  validatePaginatedProjectsQuery,
  validateCountProjectEntity,
};

export default projectValidator;
