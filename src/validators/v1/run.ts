import joi from 'joi';

export const createTestRunSchema = joi.object({
  name: joi.string().required(),
  description: joi.string().optional(),
  caseUids: joi.array().items(joi.number()).required(),
  externalId: joi.string().optional(),
  source: joi.string().optional(),
  priority: joi.number().optional(),
  status: joi.number().optional(),
  milestoneUids: joi.array().items(joi.number()).optional(),
});

export const updateTestRunSchema = joi.object({
  name: joi.string().optional(),
  caseUids: joi.array().items(joi.number()).optional(),
  externalId: joi.string().optional(),
  source: joi.string().optional(),
  priority: joi.number().optional(),
  status: joi.number().optional(),
  milestoneUids: joi.array().items(joi.number()).optional(),
  archived: joi.boolean().optional(),
});
