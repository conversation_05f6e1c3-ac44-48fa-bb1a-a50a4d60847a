import joi from 'joi';
import { types } from '@app/models/customField';

export const createCustomFieldSchema = joi.object({
  name: joi.string().required(),
  type: joi.string().valid(...types).optional(),
  source: joi.string().optional(),
  options: joi.array().items(joi.string()),
});

export const updateCustomFieldSchema = joi.object({
  name: joi.string().optional(),
  type: joi.string().valid(...types).optional(),
  source: joi.string().optional(),
  options: joi.array().items(joi.string()).optional(),
});
