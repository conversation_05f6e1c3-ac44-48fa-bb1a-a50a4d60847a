import joi from 'joi';

export const createFolderSchema = joi.object({
  name: joi.string().required(),
  externalId: joi.string().optional(),
  source: joi.string().optional(),
  customField: joi.object().optional(),
  parentId: joi.number().required(),
  status: joi.number().optional(),
});

export const updateFolderSchema = joi.object({
  name: joi.string().optional(),
  source: joi.string().optional(),
  customField: joi.object().optional(),
  parentId: joi.number().optional(),
});
