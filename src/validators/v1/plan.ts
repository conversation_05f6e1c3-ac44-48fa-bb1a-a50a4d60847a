import joi from 'joi';
import { planConfigValidator } from '../plan';

export const createPlanSchema = joi.object({
  name: joi.string().required(),
  description: joi.string(),
  milestoneUids: joi.array().items(joi.number().required()).default([]),
  status: joi.number(),
  priority: joi.number(),
  testRuns: joi
    .array()
    .items(
      joi.object({
        uid: joi.number().required(),
        configuration: planConfigValidator,
      }),
    )
    .default([]),
  configuration: planConfigValidator.optional(),
  externalId: joi.string().optional(),
  source: joi.string().optional(),
  tagUids: joi.array().items(joi.number()).optional(),
});

export const updatePlanSchema = joi.object({
  name: joi.string(),
  description: joi.string(),
  milestoneUids: joi.array().items(joi.number()),
  tagUids: joi.array().items(joi.number()),
  runUids: joi.array().items(joi.number().required()),
  status: joi.number(),
  priority: joi.number(),
});
