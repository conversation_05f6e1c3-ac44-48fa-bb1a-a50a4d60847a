import joi from 'joi';
import { entityTypes } from '@app/models/tag';

export const createTagSchema = joi.object({
  name: joi.string().required(),
  description: joi.string().optional(),
  entityTypes: joi.array().items(joi.string().valid(...entityTypes)).required(),
});

export const updateTagSchema = joi.object({
  name: joi.string().optional(),
  description: joi.string().optional(),
  entityTypes: joi.array().items(joi.string().valid(...entityTypes)).optional(),
  archived: joi.boolean().optional(),
});
