import joi from 'joi';

const stepSchema = joi.object({
  id: joi.string().required(),
  title: joi.string().optional(),
  description: joi.string().optional(),
  expectedResult: joi.string().optional(),
  children: joi.array().optional(),
  shared: joi.boolean().optional(),
  sharedStepUid: joi.number().optional(),
});

export const createSharedStepSchema = joi.object({
  name: joi.string().required(),
  steps: joi.array().items(stepSchema).required(),
});

export const updateSharedStepSchema = joi.object({
  name: joi.string().optional(),
  steps: joi.array().items(stepSchema).optional(),
  archived: joi.boolean().optional(),
});
