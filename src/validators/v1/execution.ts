import joi from 'joi';

export const executionStepSchema = joi.object({
  testStepUid: joi.number().optional(),
  description: joi.string().optional(),
  customFields: joi.object().optional(),
});

export const createExecutionSchema = joi.object({
  externalId: joi.string().optional(),
  source: joi.string().optional(),
  testCaseUid: joi.number().optional(),
  testCaseRef: joi.number().optional(),
  testRunUid: joi.number().optional(),
  status: joi.number().optional(),
  customFields: joi.object().optional(),
  steps: joi.array().items(executionStepSchema).default([]),
});

const tagUids = joi.array().items(joi.number()).default([]);

export const updateExecutionSchema = {
  name: joi.string().optional(),
  steps: joi.any().optional(),
  templateFields: joi.any(),
  status: joi.number().integer().optional(),
  priority: joi.number().integer().optional(),
  dueAt: joi.string().optional(),
  assignedTo: joi.string().uuid({ version: 'uuidv4' }).optional(),
  tagUids,
  tagReplacements: joi
    .array()
    .items(
      joi.object().keys({
        existingTagUids: tagUids,
        newTagUids: tagUids,
      }),
    )
    .default([]),
};
