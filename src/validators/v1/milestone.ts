import joi from 'joi';

export const createMilestoneSchema = joi.object({
  name: joi.string().required(),
  description: joi.string().allow('').optional(),
  startDate: joi.string().required(),
  dueAt: joi.string().required().custom((value, helpers) => {
    const { startDate } = helpers.state.ancestors[0];
    if (new Date(value) < new Date(startDate)) {
      return helpers.error('date.min', { limit: startDate });
    }
    return value;
  }),
  status: joi.number(),
  planIds: joi.array().items(joi.number()).default([]).optional(),
  runIds: joi.array().items(joi.number()).default([]).optional(),
  tagUids: joi.array().items(joi.number()).default([]).optional(),
});

export const updateMilestoneSchema = joi.object({
  name: joi.string().optional(),
  description: joi.string().allow('').optional(),
  startDate: joi.string().optional(),
  dueAt: joi.string().optional().custom((value, helpers) => {
    if (!value) return value;
    const { startDate } = helpers.state.ancestors[0];
    if (new Date(value) < new Date(startDate)) {
      return helpers.error('date.min', { limit: startDate });
    }
    return value;
  }),
  status: joi.number(),
  planIds: joi.array().items(joi.number()).default([]).optional(),
  runIds: joi.array().items(joi.number()).default([]).optional(),
  tagUids: joi.array().items(joi.number()).default([]).optional(),
  archived: joi.boolean().optional(),
});
