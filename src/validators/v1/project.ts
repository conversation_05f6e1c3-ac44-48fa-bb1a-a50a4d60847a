import joi from 'joi';
import { BLACKLISTED_TOKENS } from '@app/constants/blacklist';
import errorConstants from '@app/constants/errors';

export const createProjectSchema = joi.object({
  name: joi.string().required().trim().min(1),
  externalId: joi.string().optional(),
  source: joi.string().optional(),
  key: joi
    .string()
    .trim()
    .required()
    .messages({
      'any.required': errorConstants.PROJECT_KEY_REQUIRED,
      'string.empty': errorConstants.PROJECT_KEY_REQUIRED,
    })
    .min(2)
    .max(10)
    .messages({
      'string.min': errorConstants.PROJECT_KEY_LENGTH,
      'string.max': errorConstants.PROJECT_KEY_LENGTH,
    })
    .uppercase()
    .pattern(/^[A-Z0-9_-]+$/)
    .messages({
      'string.pattern.base': errorConstants.PROJECT_KEY_PATTERN,
    })
    .invalid(...BLACKLISTED_TOKENS.map((token) => token.toUpperCase()))
    .messages({
      'any.invalid': errorConstants.KEY_UNAVAILABLE,
    }),
  customFields: joi.object().optional(),
});
