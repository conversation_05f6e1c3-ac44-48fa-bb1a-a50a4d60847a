import joi from 'joi';

export const caseStepSchema = joi.object({
  title: joi.string().optional(),
  description: joi.string().optional(),
  expectedResult: joi.string().optional(),
  shared: joi.boolean().optional(),
  sharedStepUid: joi.string().optional(),
});

export const createCaseSchema = joi.object({
  name: joi.string().required(),
  externalId: joi.string().optional(),
  source: joi.string().optional(),
  customField: joi.object().optional(),
  templateId: joi.number().optional(),
  parentId: joi.number().optional(),
  status: joi.number().optional(),
  tagIds: joi.array().items(joi.number()).optional(),
});

export const createCasesSchema = joi.array().items(createCaseSchema);
