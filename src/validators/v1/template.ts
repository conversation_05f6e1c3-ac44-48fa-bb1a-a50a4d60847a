import joi from 'joi';

export const customFieldSchema = joi.object({
  id: joi.string().required(),
  name: joi.string().required(),
  dataType: joi.string().required(),
  defaultValue: joi.string().optional(),
  value: joi.string().optional(),
  options: joi.array().items(joi.string()),
});

export const createTemplateSchema = joi.object({
  name: joi.string().required(),
  templateFields: joi.array().items(customFieldSchema).optional(),
});

export const updateTemplateSchema = joi.object({
  name: joi.string().optional(),
  templateFields: joi.array().items(customFieldSchema).optional(),
});
