import joi from 'joi';

const FolderDataSchema = joi.object({
  name: joi.string().required(),
  externalId: joi.string().required(),
  source: joi.string().required(),
}).unknown();

const RunDataSchema = joi.object({
  name: joi.string().required(),
  externalId: joi.string().required(),
  source: joi.string().required(),
}).unknown();

const ExecutionDataSchema = joi.object({
  source: joi.string().required(),
  caseRef: joi.string().required(),
  runRef: joi.string().required(),
  externalId: joi.string(),
}).unknown();

const CaseDataSchema = joi.object({
  name: joi.string().required(),
  externalId: joi.string().required(),
  source: joi.string().required(),
  folderExternalId: joi.string(),
}).unknown();

export const DataSchema = joi.object({
  entities: joi.object().keys({
    folders: joi.object({
      entries: joi.array().max(10000).items(FolderDataSchema), // TODO - It should be configurable at the tenant level.
    }).optional(),
    cases: joi.object({
      entries: joi.array().max(10000).items(CaseDataSchema), // TODO - It should be configurable at the tenant level.
    }).optional(),
    runs: joi.object({
      entries: joi.array().max(10000).items(RunDataSchema), // TODO - It should be configurable at the tenant level.
    }).optional(),
    executions: joi.object({
      entries: joi.array().max(10000).items(ExecutionDataSchema), // TODO - It should be configurable at the tenant level.
    }).optional(),
  }),
});
