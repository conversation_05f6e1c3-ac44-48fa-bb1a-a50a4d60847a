import { query } from 'express-validator';
import { validate } from '@app/middlewares/validator';

/**
 * Validates the 'integrationService' query parameter for the integration service name
 * This will check if the provided integration is in the allowed list of valid services.
 */
const validateIntegrationService = (isRequired = false) => {
  const validator = query('integrationService'); // Change name as per your requirement

  const validServices = ['jira', 'github']; // List of valid integration services

  if (isRequired) {
    validator
      .exists()
      .withMessage('integrationService parameter is required');
  } else {
    validator.optional();
  }

  return validator
    .customSanitizer((value) => {
      // Handle undefined/null/empty cases
      if (!value || value === '') {
        return isRequired ? undefined : null;
      }

      // Since we only care about a single integration service, make sure it's a string
      if (typeof value === 'string') {
        return value.trim().toLowerCase();
      }

      return null; // If it's not a string, return null
    })
    .custom((value) => {
      // Skip validation if optional and no value provided
      if (!isRequired && (value === null || value === undefined)) {
        return true;
      }

      // Ensure the provided integration service is valid
      if (!validServices.includes(value)) {
        throw new Error(`Invalid integration service. Valid services are: ${validServices.join(', ')}`);
      }

      return true;
    });
};

/**
 * Validates the integrationService query parameter for the open defects count endpoint
 * Only allows valid integration services like 'jira', 'github', etc.
 */
const validateDefectOpenCount = validate([
  validateIntegrationService(false), // Optional - can be omitted (will query for all)
]);

/**
 * Validates the integrationService query parameter for the closed defects count endpoint
 * Only allows valid integration services like 'jira', 'github', etc.
 */
const validateDefectClosedCount = validate([
  validateIntegrationService(false), // Optional - can be omitted (will query for all)
]);

/**
 * Alternative validators if you want to make integrationService required
 */
const validateDefectOpenCountRequired = validate([
  validateIntegrationService(true), // Required - must provide a valid integration service
]);

const validateDefectClosedCountRequired = validate([
  validateIntegrationService(true), // Required - must provide a valid integration service
]);

const defectValidator = {
  validateDefectOpenCount,
  validateDefectClosedCount,
  validateDefectOpenCountRequired,
  validateDefectClosedCountRequired,
};

export default defectValidator;
