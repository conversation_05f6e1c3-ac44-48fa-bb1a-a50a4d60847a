import Joi from 'joi';
import { BLACKLISTED_TOKENS } from '@app/constants/blacklist';
import { body, param } from 'express-validator';
import errorConstants from '@app/constants/errors';
import { joiValidate, validate } from '@app/middlewares/validator';

const HANDLE_REGEX = /^[A-Za-z0-9\-_]+$/;
const validateHandle = validate([
  body('name')
    .not()
    .isEmpty()
    .isLength({ min: 2, max: 30 })
    .withMessage(errorConstants.HANDLE_LENGTH)
    .toLowerCase()
    .matches(HANDLE_REGEX)
    .withMessage(errorConstants.HANDLE_MATCH)
    .not()
    .isIn(BLACKLISTED_TOKENS)
    .withMessage(errorConstants.HANDLE_UNAVAILABLE),
]);

// TODO combine these two validators
const validatePotentialHandle = validate([
  param('handle')
    .not()
    .isEmpty()
    .isLength({ min: 2, max: 30 })
    .withMessage(errorConstants.HANDLE_LENGTH)
    .toLowerCase()
    .matches(HANDLE_REGEX)
    .withMessage(errorConstants.HANDLE_MATCH)
    .not()
    .isIn(BLACKLISTED_TOKENS)
    .withMessage(errorConstants.HANDLE_UNAVAILABLE),
]);

const statusColorSchema = Joi.object({
  id: Joi.number().required(),
  color: Joi.string().required(),
  entityType: Joi.string().required(),
  name: Joi.string().required(),
  isDefault: Joi.boolean().required(),
  isCompleted: Joi.boolean().required(),
  isSuccess: Joi.boolean().optional(),
  isFailure: Joi.boolean().optional(),
  aliases: Joi.array().items(Joi.string()).optional(),
});

const priorityColorSchema = Joi.object({
  id: Joi.number().required(),
  color: Joi.string().required(),
  entityType: Joi.string().required(),
  name: Joi.string().required(),
  isDefault: Joi.boolean().required(),
  aliases: Joi.array().items(Joi.string()).optional(),
});

const preferencesSchema = Joi.object({
  statusColors: Joi.array()
    .items(statusColorSchema)
    .custom((statusColors, helpers) => {
      const aliasMap = new Map();

      for (const status of statusColors) {
        const { entityType, aliases = [], name } = status;

        for (const alias of aliases) {
          const key = `${entityType.trim().toLowerCase()}-${alias.trim().toLowerCase()}`;
          if (aliasMap.has(key)) {
            return helpers.error('statusColors.duplicateAlias', {
              alias,
              original: aliasMap.get(key),
              entityType,
            });
          }
          aliasMap.set(key, name);
        }
      }

      return statusColors;
    }, 'Unique alias per entityType')
    .messages({
      'statusColors.duplicateAlias': 'Alias \'{#alias}\' is already used by \'{#original}\' in entityType \'{#entityType}\'',
    }),

  priorityColors: Joi.array().items(priorityColorSchema),
});

const validatePreferenceUpdate = joiValidate(Joi.object({
  preferences: preferencesSchema.required(),
}));

export { validateHandle, validatePotentialHandle, validatePreferenceUpdate };
