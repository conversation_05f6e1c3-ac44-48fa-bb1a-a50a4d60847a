import { body, param, validationResult } from 'express-validator';

import errorConstants from '@app/constants/errors';
import joi from 'joi';
import { isPaginatedQuery, joiValidate } from '@app/middlewares/validator';
import { milestoneRelations } from '@app/types/milestone';
import { joiCSV } from '@app/lib/joi';

/**
 * Validates the request body for creating a test milestone
 * @param req - The request object
 * @param res - The response object
 */
// externalId, customFields, source, name, description, dueDate
const validateCreateMilestone = joiValidate(
  joi.object({
    name: joi.string().required(),
    description: joi.string().allow(''),
    startDate: joi.date().required(),
    dueAt: joi.date().required().min(joi.ref('startDate')),
    status: joi.number(),
    planIds: joi.array().items(joi.number()).default([]),
    runIds: joi.array().items(joi.number()).default([]),
    tagUids: joi.array().items(joi.number()).default([]),
  }),
);

/**
 * Validates the request body for updating a test milestone
 * @param req - The request object
 * @param res - The response object
 */
const validateUpdateMilestone = joiValidate(
  joi.object({
    name: joi.string(),
    description: joi.string(),
    startDate: joi.date(),
    dueAt: joi.date(),
    archived: joi.boolean(),
    status: joi.number(),
    planIds: joi.array().items(joi.number()).default([]),
    runIds: joi.array().items(joi.number()).default([]),
    tagUids: joi.array().items(joi.number()).default([]),
  }),
);

const validateMilestoneQuery = joiValidate(
  joi.object({
    status: joi.number().optional(),
    tagUids: joi.array().items(joi.number()).default([]),
    archived: joi.boolean().default(false),
    ...isPaginatedQuery,
  }),
  'query',
);

const validateModifyMilestoneRuns = [
  body('runIds')
    .isArray({ min: 1 })
    .withMessage(errorConstants.RUN_IDS_ARE_REQUIRED),
  body('runIds.*')
    .isInt()
    .withMessage(errorConstants.EACH_RUN_ID_MUST_BE_STRING),
  param('id').notEmpty().withMessage(errorConstants.MILESTONE_ID_IS_REQUIRED),
  (req: any, res: any, next: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json(errors.array());
    }
    next();
  },
];

const validateModifyMilestonePlans = [
  body('planUids')
    .isArray({ min: 1 })
    .withMessage(errorConstants.PLANS_ARE_REQUIRED),
  body('milestoneUids')
    .isArray({ min: 1 })
    .withMessage(errorConstants.MILESTONES_ARE_REQUIRED),
  body('milestoneUids.*')
    .isInt()
    .withMessage(errorConstants.EACH_MILESTONE_ID_MUST_BE_INTEGER),
  (req: any, res: any, next: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json(errors.array());
    }
    next();
  },
];

const validateGetMilestoneRelations = joiValidate(
  {
    relation: joi
      .string()
      .required()
      .valid(...milestoneRelations),
    milestoneUids: joiCSV(joi.number().required()).required(),
  },
  'query',
);

export {
  validateCreateMilestone,
  validateUpdateMilestone,
  validateModifyMilestonePlans,
  validateModifyMilestoneRuns,
  validateMilestoneQuery,
  validateGetMilestoneRelations,
};
