import joi from 'joi';
import { param, body } from 'express-validator';
import errorConstants from '@app/constants/errors';
import mimeType from 'mime-types';
import { joiValidate, validate } from '@app/middlewares/validator';

import config from '@app/constants/config';

const validateCreateAttachment = validate([
  body('size').not().isEmpty().withMessage(errorConstants.SIZE_FIELD_REQUIRED),
  body('fileName')
    .not()
    .isEmpty()
    .withMessage(errorConstants.FILE_NAME_FIELD_REQUIRED),
  body('fileType')
    .not()
    .isEmpty()
    .withMessage(errorConstants.TYPE_FIELD_REQUIRED)
    .custom((value, { req }) => {
      if (mimeType.contentType(value) === false) {
        throw new Error(errorConstants.INVALID_MIME_TYPE);
      }
      if (
        config.IMAGE_TYPES.test(value)
        && +req.body.size > config.IMAGE_SIZE_LIMIT
      ) {
        throw new Error(errorConstants.IMAGE_UPLOAD_OVER_SIZE_LIMIT);
      }
      return true;
    }),
]);

const validateCleanupUploaded = validate([
  param('type')
    .isIn(['cases', 'results', 'projects', 'executions']),
]);

const validateGetAttachment = joiValidate(
  joi.object({
    id: joi.string().required().messages({
      'any.required': errorConstants.INVALID_ATTACHMENT_UID,
    }),
    type: joi.string().valid('cases', 'results', 'projects', 'executions', 'integrations', 'defects').required(),
  }),
  'params',
);

const validateGetAttachmentQuery = joiValidate(
  joi.object({
    external: joi.boolean().optional(),
    source: joi.string().optional(),
    download: joi.boolean().optional(),
  }),
  'query',
);
export {
  validateCreateAttachment,
  validateGetAttachment,
  validateGetAttachmentQuery,
  validateCleanupUploaded,
};
