import { body, query } from 'express-validator';
import { ApplicationError } from '@app/lib/http';
import { SERVICE_CONFIGS } from '@app/constants/integration';
import { StatusCodes } from 'http-status-codes';
import { validate } from '@app/middlewares/validator';

const validateOauthSetup = validate([
  // Validate the 'state' query parameter
  query('state')
    .notEmpty()
    .withMessage('Invalid state format.')
    .custom((value) => {
      const parts = value.split(';');

      if (parts.length < 2) {
        throw new Error('State must contain exactly 2 parts separated by ";"');
      }

      const oauthURL = parts[0];

      // Optionally, validate oauthURL format if necessary
      try {
        // eslint-disable-next-line no-new
        new URL(oauthURL);
      } catch (err) {
        throw new Error('Invalid oauthURL format in state.');
      }

      return true; // Indicates the validation passed
    }),

  // Validate the 'code' query parameter
  query('code')
    .notEmpty()
    .withMessage('Missing code.')
    .isString()
    .withMessage('Code must be a string.'),
]);

const validateServiceConfig = (service: string, config: any) => {
  const serviceConfig = SERVICE_CONFIGS[service];
  if (!serviceConfig) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      `Invalid service: ${service}`,
    );
  }
  const missingRequired = serviceConfig.required.filter(
    (field) => !config[field],
  );
  if (missingRequired.length > 0) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      `Missing required fields for ${service}: ${missingRequired.join(', ')}`,
    );
  }

  // Additional validation for the 'projects' field
  if (!('projectConfigurations' in config)) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      '\'projectConfigurations\' in configuration must be a non-empty object with valid keys',
    );
  }

  // Validate every index in projectConfigurations is a non-empty object
  if (
    !Array.isArray(config.projectConfigurations)
    || config.projectConfigurations.length === 0
  ) {
    throw new ApplicationError(
      StatusCodes.BAD_REQUEST,
      '\'projectConfigurations\' must be a non-empty array',
    );
  }

  return true;
};

const validateCreateIntegration = validate([
  body('type')
    .notEmpty()
    .withMessage('Integration type is required')
    .trim()
    .toLowerCase()
    .custom((type, { req }) => {
      const service = req.body.service?.toLowerCase();
      if (!service || !type) return true;

      const { authTypes } = SERVICE_CONFIGS[service];
      if (!authTypes.includes(type)) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid auth type',
        );
      }
      return true;
    }),

  body('service')
    .notEmpty()
    .withMessage('Service is required')
    .trim()
    .toLowerCase()
    .isIn(Object.keys(SERVICE_CONFIGS))
    .withMessage(
      `Integration type must be one of: ${Object.keys(SERVICE_CONFIGS).join(', ')}`,
    ),

]);

const validateUpdateIntegration = validate([
  body('service')
    .notEmpty()
    .withMessage('Service is required')
    .trim()
    .toLowerCase()
    .isIn(Object.keys(SERVICE_CONFIGS))
    .withMessage(
      `Integration type must be one of: ${Object.keys(SERVICE_CONFIGS).join(', ')}`,
    ),

  body('configuration')
    .notEmpty()
    .withMessage('Configuration is required')
    .isObject()
    .withMessage('Configuration must be an object')
    .custom((configuration, { req }) => {
      validateServiceConfig(req.body.service, configuration);
      return true;
    }),
]);

const validateRemoveIntegrationErrors = validate([
  body()
    .custom((_, { req }) => {
      if (
        req.body.index === undefined
        && req.body.removeAll === undefined
      ) {
        throw new Error('Either index or removeAll is required');
      }
      return true;
    }),

  // Validate index only if present
  body('index')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Index must be a positive integer'),

  // Validate removeAll only if present
  body('removeAll')
    .optional()
    .isBoolean()
    .withMessage('Remove all must be a boolean'),
]);

export {
  validateOauthSetup,
  validateCreateIntegration,
  validateUpdateIntegration,
  validateRemoveIntegrationErrors,
};
