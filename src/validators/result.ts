import { body, param } from 'express-validator';
import { joiValidate, validate } from '@app/middlewares/validator';

import joi from 'joi';

// Validators for creating a test result
export const validateCreateResult = joiValidate(
  joi.object({
    comment: joi.string(),
    status: joi.number().required(),
    tagUids: joi.array().items(joi.number()).default([]),
    stepUid: joi.string().uuid({ version: 'uuidv4' }),
  }),
);

export const validateExecUidParam = joiValidate(
  joi.object({ id: joi.number().min(1).required() }),
  'params',
);

// Validators for retrieving results by execution
export const getResultByExecutionValidators = validate([
  param('executionUid')
    .notEmpty()
    .isInt()
    .withMessage('A valid execution UID is required.'),
]);

// Validators for updating a test result
export const updateResultValidators = validate([
  param('id').notEmpty().isInt().withMessage('A valid result UID is required.'),
  body('comment')
    .optional()
    .isString()
    .trim()
    .withMessage('Comment must be a string.'),
]);

// Validators for deleting a test result
export const deleteResultValidators = validate([
  param('id').notEmpty().isInt().withMessage('A valid result UID is required.'),
]);
