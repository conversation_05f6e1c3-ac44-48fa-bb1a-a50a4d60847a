import { body, check } from 'express-validator';
import { validate } from '@app/middlewares/validator';
import errorConstants from '@app/constants/errors';
import { PROMPT_TYPES } from '@app/constants/aiAssist';

const validateCaseCreation = validate([
  body('type')
    .isString()
    .isIn(PROMPT_TYPES),
  body('userPrompt')
    .if(body('type').isIn(['caseCreation', 'fieldImprovement']))
    .isString()
    .notEmpty()
    .withMessage(errorConstants.PROMPT_CONTENT_REQUIRED),
  body('inputFields')
    .if(body('type').equals('caseCreation'))
    .notEmpty()
    .isArray({ min: 1 })
    .withMessage(errorConstants.TEXT_FIELD_REQUIRED),
  body('files')
    .if(body('type').equals('caseCreation'))
    .optional()
    .isArray()
    .notEmpty(),
  check('files.*.name')
    .notEmpty()
    .isString(),
  check('files.*.content')
    .notEmpty()
    .isString(),
  body('inputFields')
    .if(body('type').equals('fieldImprovement'))
    .isArray({ min: 1, max: 1 }),
  body('inputFields[0].fieldName')
    .if(body('type').equals('fieldImprovement'))
    .notEmpty()
    .isString(),
  body('inputFields[0].fieldValue')
    .if(body('type').equals('fieldImprovement'))
    .notEmpty()
    .isString(),
  body('inputFields')
    .if(body('type').equals('caseImprovement'))
    .optional()
    .isArray({ min: 1 })
    .withMessage(errorConstants.CUSTOM_FIELDS_MUST_BE_ARRAY),
  check('inputFields.*.fieldName')
    .notEmpty()
    .isString(),
  check('inputFields.*.fieldValue')
    .isString()
    .if(body('type').equals('caseImprovement'))
    .notEmpty(),
  body('steps')
    .if(body('type').equals('caseImprovement'))
    .isArray({ min: 1 })
    .withMessage(errorConstants.CASE_IMPROVEMENT_STEPS_REQUIRED),
  check('steps.*.description')
    .notEmpty()
    .isString(),
  check('steps.*.expectedResult')
    .isString(),
  check('steps.*.children')
    .optional()
    .isArray({ min: 1 }),
  body('caseTitle')
    .if(body('type').equals('caseImprovement'))
    .isString()
    .optional(),
  body('casePriority')
    .if(body('type').equals('caseImprovement'))
    .isString()
    .optional(),
]);

export { validateCaseCreation };
