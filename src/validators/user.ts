import { check, query } from 'express-validator';

import errorConstants from '@app/constants/errors';
import { validate } from '@app/middlewares/validator';

const emailUserNameRegex = /^(?:[A-Z\d][A-Z\d_-]{2,20}|[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4})$/i;

const validateUserSignUp = validate([
  check('firstName')
    .isLength({ min: 2, max: 30 })
    .withMessage('First name must be between 2 and 30 characters'),
  check('lastName')
    .isLength({ min: 2, max: 30 })
    .withMessage('Last name must be between 2 and 30 characters'),
  check('password')
    .isLength({ min: 8, max: 30 })
    .withMessage('Password must be between 8 and 30 characters'),
  check('email').isEmail().withMessage('Email is invalid'),
]);

const validateUserSignIn = validate([
  check('email')
    .notEmpty()
    .withMessage('Email field is required')
    .matches(emailUserNameRegex)
    .withMessage('Email is invalid'),
  check('password').notEmpty().withMessage('Password field is required'),
]);

const validateUserUpdateProfile = validate([
  check('firstName')
    .notEmpty()
    .withMessage(errorConstants.FIRST_NAME_FIELD_REQUIRED),
  check('lastName')
    .notEmpty()
    .withMessage(errorConstants.LAST_NAME_FIELD_REQUIRED),
  check('avatarUrl')
    .optional()
    .isURL()
    .withMessage(errorConstants.AVATAR_MUST_BE_URL),
]);

const validateUserSearch = validate([
  query('query')
    .toLowerCase()
    .notEmpty()
    .withMessage('user query cannot be empty')
    .escape(),
]);

export {
  validateUserSignUp,
  validateUserSignIn,
  validateUserUpdateProfile,
  validateUserSearch,
};
