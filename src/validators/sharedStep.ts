import { body, param } from 'express-validator';

import errorConstants from '@app/constants/errors';
import { validate } from '@app/middlewares/validator';

const validateId = (err: string) => validate([param('id').isInt().notEmpty().withMessage(err)]);

const validateSharedStepId = validateId(errorConstants.INVALID_SHARED_STEP_UID);

const validateSteps = (required: boolean) => [
  required
    ? body('steps')
      .notEmpty()
      .isArray()
      .withMessage(errorConstants.STEPS_MUST_BE_ARRAY)
    : body('steps')
      .optional()
      .isArray()
      .withMessage(errorConstants.STEPS_MUST_BE_ARRAY),
  body('steps.*.title')
    .if(body('steps.*.shared').not().exists({ values: 'falsy' }))
    .optional()
    .isString(),
  body('steps.*.description')
    .if(body('steps.*.shared').not().exists({ values: 'falsy' }))
    .notEmpty()
    .isString(),
  body('steps.*.expectedResult')
    .if(body('steps.*.shared').not().exists({ values: 'falsy' }))
    .optional()
    .isString(),
  body('steps.*.children')
    .if(body('steps.*.shared').not().exists({ values: 'falsy' }))
    .optional()
    .isArray(),
  body('steps.*.shared').optional().isBoolean({ loose: true }),
];

const validateWriteSharedTestStep = validate([
  body('name').notEmpty(),
  body('expectedResultByStep').optional().isBoolean(),
  ...validateSteps(true),
]);

const validateUpdateSharedTestStep = validate([
  body('name').optional(),
  body('archived').isBoolean().optional(),
  ...validateSteps(false),
]);

const validateToogleArchiveSharedSteps = validate([
  body('sharedSteps')
    .notEmpty()
    .withMessage(errorConstants.SHARED_STEPS_IS_REQUIRED)
    .isArray()
    .withMessage(errorConstants.SHARED_STEPS_MUST_BE_AN_ARRAY),
  body('steps.*.id').notEmpty().isString(),
  body('steps.*.archived').isBoolean().notEmpty(),
]);

const validateSharedStepIds = validate([
  body('sharedStepIds')
    .notEmpty()
    .withMessage(errorConstants.SHARED_STEP_IDS_IS_REQUIRED)
    .isArray()
    .withMessage(errorConstants.SHARED_STEP_IDS_MUST_BE_AN_ARRAY),
]);

export {
  validateToogleArchiveSharedSteps,
  validateWriteSharedTestStep,
  validateUpdateSharedTestStep,
  validateSharedStepId,
  validateSharedStepIds,
};
