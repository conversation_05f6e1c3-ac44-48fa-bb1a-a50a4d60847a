import * as customField from '@app/models/customField';

import { body, param } from 'express-validator';

import errorConstants from '@app/constants/errors';
import { validate } from '@app/middlewares/validator';

const validateCustomFieldId = validate([
  param('id')
    .isInt()
    .notEmpty()
    .withMessage(errorConstants.CUSTOM_FIELD_INVALID_CUSTOM_FIELD_ID),
]);

const validateCustomFieldBody = (required: boolean) => validate([
  required
    ? body('name')
      .notEmpty()
      .withMessage(errorConstants.CUSTOM_FIELD_NAME_REQUIRED)
    : body('name').optional(),
  body('type')
    .notEmpty()
    .isString()
    .withMessage(errorConstants.CUSTOM_FIELD_TYPE_REQUIRED)
    .isIn(customField.types)
    .withMessage(errorConstants.INVALID_CUSTOM_FIELD_TYPE),
  body('options')
    .optional()
    .isArray()
    .withMessage(errorConstants.CUSTOM_FIELD_INVALID_OPTIONS_ARRAY),
]);

export { validateCustomFieldBody, validateCustomFieldId };
