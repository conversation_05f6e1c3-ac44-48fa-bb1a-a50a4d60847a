import { body, param } from 'express-validator';
import {
  isPaginatedQuery,
  joiValidate,
  validate,
} from '@app/middlewares/validator';

import errorConstants from '@app/constants/errors';
import joi from 'joi';
import { joiCSV } from '@app/lib/joi';
import { caseRelations } from '@app/types/case';

const validateCreateCases = validate([
  body('cases').notEmpty().isArray({ min: 1 }),
  body('cases.*.name')
    .notEmpty()
    .withMessage(errorConstants.NAME_FIELD_REQUIRED),
  body('cases.*.customFields').optional().isObject(),
  body('cases.*.parentId').optional({ nullable: true }).isInt(),
  body('cases.*.parentName')
    .if(body('cases.*.parentId').not().exists({ values: 'falsy' }))
    .notEmpty()
    .isString(),
  body('cases.*.steps')
    .optional()
    .isArray()
    .withMessage(errorConstants.STEPS_MUST_BE_ARRAY),
  body('cases.*.steps.*.title')
    .if(body('cases.*.steps.*.sharedStepUid').not().exists({ values: 'falsy' }))
    .notEmpty()
    .isString(),
  body('cases.*.steps.*.description')
    .if(body('cases.*.steps.*.sharedStepUid').not().exists({ values: 'falsy' }))
    .notEmpty()
    .isString(),
  body('cases.*.steps.*.sharedStepUid').optional().isInt(),
  body('cases.*.steps.*.expectedResult').optional().isString(),
  body('cases.*.steps.*.children')
    .if(body('cases.*.steps.*.sharedStepUid').not().exists({ values: 'falsy' }))
    .optional()
    .isArray(),
  body('cases.*.steps.*.shared').optional().isBoolean({ loose: true }),
  body('cases.*.tagIds')
    .optional()
    .isArray()
    .withMessage(errorConstants.TAG_IDS_MUST_BE_ARRAY),
  body('cases.*.tags')
    .optional()
    .isArray()
    .withMessage(errorConstants.TAG_IDS_MUST_BE_ARRAY),
  body('externalCreatedAt').optional(),
  body('externalUpdatedAt').optional(),
  body('priorityText').optional().isString(),
  body('statusText').optional().isString(),
]);

const validateCreateCase = validate([
  body('name').notEmpty().withMessage(errorConstants.NAME_FIELD_REQUIRED),
  body('customFields').optional().isObject(),
  body('parentId').notEmpty().isInt(),
  body('steps')
    .optional()
    .isArray()
    .withMessage(errorConstants.STEPS_MUST_BE_ARRAY),
  body('steps.*.title')
    .if(body('steps.*.sharedStepUid').not().exists({ values: 'falsy' }))
    .notEmpty()
    .isString(),
  body('steps.*.description')
    .if(body('steps.*.sharedStepUid').not().exists({ values: 'falsy' }))
    .notEmpty()
    .isString(),
  body('steps.*.sharedStepUid').optional().isInt(),
  body('steps.*.expectedResult').optional().isString(),
  body('steps.*.children')
    .if(body('steps.*.sharedStepUid').not().exists({ values: 'falsy' }))
    .optional()
    .isArray(),
  body('steps.*.shared').optional().isBoolean({ loose: true }),
]);

const validateWriteCase = validate([
  body('name').optional(),
  body('customFields').optional().isObject(),
  body('parentId').optional().isInt(),
  body('steps')
    .optional()
    .isArray()
    .withMessage(errorConstants.STEPS_MUST_BE_ARRAY),
  body('cases.*.steps.*.title')
    .if(body('cases.*.steps.*.sharedStepUid').not().exists({ values: 'falsy' }))
    .notEmpty()
    .isString(),
  body('cases.*.steps.*.description')
    .if(body('cases.*.steps.*.sharedStepUid').not().exists({ values: 'falsy' }))
    .notEmpty()
    .isString(),
  body('cases.*.steps.*.expectedResult').optional().isString(),
  body('cases.*.steps.*.children')
    .if(body('cases.*.steps.*.sharedStepUid').not().exists({ values: 'falsy' }))
    .optional()
    .isArray(),
  body('cases.*.tagIds')
    .optional()
    .isArray()
    .withMessage(errorConstants.TAG_IDS_MUST_BE_ARRAY),
  body('cases.*.steps.*.sharedStepUid').optional().isBoolean({ loose: true }),
]);

const validateDeleteCases = validate([
  body('ids')
    .isArray()
    .notEmpty()
    .withMessage(errorConstants.CASES_IDS_ARE_REQUIRED),
  body('ids.*').isInt().withMessage(errorConstants.INVALID_CASE_UID),
]);

const validateCaseID = validate([
  param('id').isInt().notEmpty().withMessage(errorConstants.INVALID_CASE_UID),
]);

const validateCaseQuery = joiValidate(
  joi.object({
    q: joi.string(),
    priority: joi.number(),
    status: joi.number(),
    tagUids: joiCSV(joi.number()),
    parentUid: joi.number(),
    ...isPaginatedQuery,
  }),
  'query',
);

const validateCaseRelationQuery = joiValidate(
  joi.object({
    relation: joi
      .string()
      .required()
      .valid(...caseRelations),
    caseUids: joiCSV(joi.number().required()).required(),
  }),
  'query',
);

export {
  validateWriteCase,
  validateCaseID,
  validateCaseQuery,
  validateCreateCases,
  validateCreateCase,
  validateDeleteCases,
  validateCaseRelationQuery,
};
