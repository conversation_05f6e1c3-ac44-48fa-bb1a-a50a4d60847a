import * as errorsConstants from '@app/constants/errors';
import * as name from '../accessToken';

import sinon, { SinonSandbox } from 'sinon';

import { expect } from 'chai';

describe('accessToken.js', () => {
  let sandbox: SinonSandbox;
  let req: any;
  let res: any;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    req = {
      body: sandbox.stub(),
      name: sandbox.stub(),
      errors: sandbox.stub(),
    };
    req.body.accessToken = name;

    res = {
      status: sandbox.spy(),
    };
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should return 422 error if name is less than 2 characters', () => {
    const name = 'A';
    expect(name).to.equal(res.status(422).json(errorsConstants));
  });

  it('should return 422 error if name is more than 64 characters.', () => {
    const name =
      'AAAAAAAABBBBBBBBCCCCCCCCDDDDDDDDEEEEEEEEFFFFFFFFGGGGGGGGHHHHHHHHI';
    expect(name).to.equal(res.status(422).json(errorsConstants));
  });

  it('should pass if name is in between 2 and 64 characters.', () => {
    const name = 'Between';
    expect(name).not.to.equal(res.status(422).json(errorsConstants));
  });

  it('should only contain appropriate characters', () => {
    // should not contain: ! ? @ # $ % ^ & * ( ) / | [ ] { } , . ` ` < > " " ' ' : ; = +
    const name = 'Adam';
    expect(name).not.to.include([
      '!',
      '?',
      '@',
      '#',
      '$',
      '%',
      '^',
      '&',
      '*',
      '(',
      ')',
      '/',
      '|',
      '[',
      ']',
      '{',
      '}',
      ',',
      '.',
      '`',
      '`',
      '<',
      '>',
      '"',
      '"',
      ':',
      ';',
      '=',
      '+',
    ]);
  });

  it('should return 422 error if name contains inappropriate characters', () => {
    const name = 'Adam?';
    expect(name).to.equal(res.status(422).json(errorsConstants));
  });

  it('should return a 422 error if the error list is not empty', () => {
    const errors = { 1: 'test' };
    expect(errors).not.to.be.empty;
  });
});
