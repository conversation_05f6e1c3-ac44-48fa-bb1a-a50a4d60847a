import { body, check } from 'express-validator';

import errorConstants from '@app/constants/errors';
import { validate } from '@app/middlewares/validator';

const validateCreateRepo = validate([
  check('externalId')
    .not()
    .isEmpty()
    .withMessage(errorConstants.EXTERNALID_FIELD_REQUIRED),
  check('source')
    .not()
    .isEmpty()
    .withMessage(errorConstants.SOURCE_FIELD_REQUIRED),
]);

const validateCreateBranch = validate([
  body('externalId').optional(),
  body('customField').optional(),
  body('name').notEmpty().withMessage(errorConstants.NAME_FIELD_REQUIRED),
]);

export { validateCreateRepo, validateCreateBranch };
