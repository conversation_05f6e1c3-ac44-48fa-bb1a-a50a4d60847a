import { isPaginatedQ<PERSON>y, joiValidate } from '@app/middlewares/validator';
import { isValidName, joiCSV } from '@app/lib/joi';

import { bulkUpdateActions, runRelations } from '@app/types/run';
import joi from 'joi';

const validateCreateTestRun = joiValidate({
  name: isValidName().required(),
  description: joi.string(),
  milestoneUids: joi.array().items(joi.number()).allow(null).default([]),
  priority: joi.number().allow(null),
  status: joi.number().allow(null),
  tagUids: joi.array().items(joi.number()).allow(null).default([]),
  dueAt: joi.date(),
  caseUids: joi.array().items(joi.number()).allow(null).default([]),
});

const runConfigValidator = joi
  .object({
    type: joi.string().valid('simple', 'matrix'),
    sets: joi.array().items(
      joi
        .array()
        .required()
        .min(1)
        .items(
          joi
            .string()
            .required()
            .regex(/^.+::.+$/),
        ),
    ),
  })
  .with('type', 'sets')
  .default({});

const validateDuplicateRuns = joiValidate({
  testRuns: joi
    .array()
    .required()
    .min(1)
    .items(
      joi.object({
        uid: joi.number().required(),
        configuration: runConfigValidator,
      }),
    ),
  configuration: runConfigValidator,
});

const validateBulkUpdateRuns = joiValidate({
  uids: joiCSV(joi.number().required()).min(1).required(),
  action: joi
    .string()
    .valid(...bulkUpdateActions)
    .required(),
  dueAt: joi
    .date()
    .when('action', { is: joi.valid('updateDueDate'), then: joi.required() }),
  milestoneUids: joi
    .array()
    .items(joi.number())
    .when('action', {
      is: joi.valid('addMilestones', 'removeMilestones'),
      then: joi.array().min(1).required(),
    }),
  planUids: joi
    .array()
    .items(joi.number())
    .when('action', {
      is: joi.valid('addPlans'),
      then: joi.array().min(1).required(),
    }),
});

const numericArray = () => joi.array().items(joi.number().required()).optional().allow(null)
  .default([]);

const validateUpdateTestRun = joiValidate({
  name: joi.string(),
  description: joi.string(),
  status: joi.number().allow(null),
  priority: joi.number().allow(null),
  dueAt: joi.date(),
  addMilestoneUids: numericArray(),
  removeMilestoneUids: numericArray(),
  addTagUids: numericArray(),
  removeTagUids: numericArray(),
  addPlanUids: numericArray(),
  removePlanUids: numericArray(),
  archive: joi.boolean(),
  configs: joi.array().items(joi.string().regex(/^.+::.+$/)),
});

const validateListRunsQuery = joiValidate(
  {
    ...isPaginatedQuery,
    planUid: joi.alternatives([
      joi.allow(null).custom((v) => (v === 'null' ? null : +v)),
      joi.number().integer().optional().min(1),
    ]),
    priorityUids: joiCSV(joi.number()),
    statusUids: joiCSV(joi.number()),
    configs: joiCSV(joi.string()),
    minTestCaseCount: joi.number().integer().min(0),
    maxTestCaseCount: joi.number().integer().min(0),
    fromDueDate: joi.date(),
    toDueDate: joi.date(),
    fromCreatedAt: joi.date(),
    toCreatedAt: joi.date(),
    minProgress: joi.number().integer(),
    maxProgress: joi.number().integer(),
    tagUids: joiCSV(joi.number()),
    milestoneUids: joiCSV(joi.number()),
  },
  'query',
);

const validateGetRunRelations = joiValidate(
  {
    relation: joi
      .string()
      .required()
      .valid(...runRelations),
    runUids: joiCSV(joi.number().required()).required(),
  },
  'query',
);

const validateTestRunUid = joiValidate(
  { id: joi.number().min(1).required() },
  'params',
);

const validateDeleteRuns = joiValidate({
  runUids: joiCSV(joi.number().integer().required()).required().min(1),
});

const validateListRunsCasesQuery = joiValidate(
  {
    ...isPaginatedQuery,
  },
  'query',
);

const validateUpdateTestRunCases = joiValidate({
  addCaseUids: joi.array().items(joi.number()).default([]),
  removeExecUids: joi.array().items(joi.number()).default([]),
});

export {
  validateCreateTestRun,
  validateDuplicateRuns,
  validateBulkUpdateRuns,
  validateUpdateTestRun,
  validateListRunsQuery,
  validateDeleteRuns,
  validateTestRunUid,
  validateListRunsCasesQuery,
  validateUpdateTestRunCases,
  validateGetRunRelations,
};
