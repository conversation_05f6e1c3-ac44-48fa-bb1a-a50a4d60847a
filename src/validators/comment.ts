import { body, param } from 'express-validator';
import { joiValidate, validate } from '@app/middlewares/validator';

import joi from 'joi';

// Validators for creating a comment
export const createCommentValidator = joiValidate(
  joi.object({
    entityType: joi
      .string()
      .required()
      .lowercase()
      .valid('case', 'milestone', 'execution', 'result', 'run'),
    entityUid: joi.number().integer().required(),
    body: joi.string().required(),
  }),
);

// Validators for updating a comment
export const updateCommentValidators = validate([
  param('id').isUUID().withMessage('Invalid comment ID'),
  body('body').notEmpty().withMessage('Comment body is required'),
]);

// Validators for retrieving comments for a specific entity
export const getCommentsValidators = validate([
  param('entityType')
    .isIn(['case', 'milestone', 'execution', 'result', 'run'])
    .withMessage('Invalid entity type'),
  param('entityUid').notEmpty().withMessage('Entity UID is required'),
]);

// Validators for deleting a comment
export const deleteCommentValidators = validate([
  param('id').isUUID().withMessage('Invalid comment ID'),
]);
