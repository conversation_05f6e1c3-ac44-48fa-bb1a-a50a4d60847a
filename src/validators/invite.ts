import { joiValidate } from '@app/middlewares/validator';
import joi from 'joi';

const findOne = joiValidate(joi.object().keys({
  token: joi.string().required(),
  handle: joi.string().required(),
}), 'params');

const bulkResend = joiValidate(joi.object().keys({
  emails: joi
    .array()
    .items(joi.string().email().required())
    .min(1)
    .required(),
}));

const bulkUpdateInvites = joiValidate(joi.object().keys({
  updates: joi
    .array()
    .items(
      joi.object().keys({
        email: joi.string().email().required(),
        roleUid: joi.string().uuid().required(),
        tagUids: joi.array().items(joi.number()).optional(),
      }),
    )
    .min(1)
    .required(),
}));

const validateInvite = joiValidate(joi.object().keys({
  roleUid: joi.string().required().uuid(),
  userUid: joi.string().uuid().optional(),
  tagUids: joi.array().items(joi.number()).optional(),
  userEmail: joi.string().email().optional(),
  roles: joi
    .array()
    .items(
      joi.object().keys({
        projectUid: joi.number().when('roles', {
          is: joi.array().min(1),
          then: joi.required(),
          otherwise: joi.optional(),
        }),
        roleUid: joi.string().uuid().when('roles', {
          is: joi.array().min(1),
          then: joi.required(),
          otherwise: joi.optional(),
        }),
      }),
    )
    .optional(),
}));

const findAll = joiValidate(joi.object().keys({
  status: joi
    .string()
    .required()
    .valid('pending', 'expired'),
}), 'query');

export default {
  findOne,
  bulkResend,
  bulkUpdateInvites,
  findAll,
  validateInvite,
};
