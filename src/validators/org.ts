import { NextFunction, Response } from 'express';
import { isEmpty } from 'lodash';
import {
  body,
  check,
  matchedData,
  param,
  validationResult,
} from 'express-validator';

import { BLACKLISTED_TOKENS } from '@app/constants/blacklist';
import { OpenFgaClient } from '@openfga/sdk';
import env from '@app/config/env';
import errorConstants from '@app/constants/errors';
import { validate } from '@app/middlewares/validator';

const openFgaApi = new OpenFgaClient({
  apiScheme: env.OPENFGA_DATASTORE_API_SCHEME,
  apiHost: env.OPENFGA_DATASTORE_HOST,
  authorizationModelId: env.OPENFGA_DATASTORE_AUTH_MODEL,
  storeId: env.OPENFGA_DATASTORE_STORE_ID,
});

const validateOrgProvidedAndAccess = async (
  req: any,
  res: Response,
  next: NextFunction,
): Promise<Response | void> => {
  const orgId: string = req.query.org as string;
  if (!orgId && !req.locals.user) {
    return res.status(404).send();
  }

  const { allowed } = await openFgaApi.check({
    user: `user:${req.locals.user.uid}`,
    relation: 'member',
    object: `org:${orgId}`,
  });
  if (!allowed) {
    return res.status(404).send();
  }
  req.locals.orgId = orgId;
  next();
};

const validateNewOrg = validate([
  body('name')
    .whitelist('A-Za-z0-9 -_')
    .notEmpty()
    .withMessage(errorConstants.NAME_FIELD_REQUIRED)
    .isLength({ min: 2, max: 63 })
    .withMessage(errorConstants.INVALID_ORG_NAME_LENGTH),
  body('avatarUrl')
    .optional()
    .isURL()
    .withMessage(errorConstants.AVATAR_MUST_BE_URL),
  body('handle')
    .not()
    .isEmpty()
    .withMessage(errorConstants.HANDLE_DUPLICATED)
    .isLength({ min: 2, max: 30 })
    .withMessage(errorConstants.HANDLE_LENGTH)
    .toLowerCase()
    .not()
    .isIn(BLACKLISTED_TOKENS)
    .withMessage(errorConstants.HANDLE_UNAVAILABLE),
]);

const validateTokenWithID = [
  check('userUid')
    .optional()
    .isUUID()
    .withMessage(errorConstants.INVALID_USER_UID),
  check('org_uid')
    .optional()
    .isUUID()
    .withMessage(errorConstants.INVALID_ORG_UID),
  async (req: any, res: Response, next: NextFunction) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json(errors.array());
    }

    let type = '';
    let ownerUid = '';
    if (req.params.userUid) {
      type = 'user';
      ownerUid = req.params.userUid;
    } else if (req.params.org_uid) {
      type = 'org';
      ownerUid = req.params.org_uid;
    } else {
      return res.status(409).json(errorConstants.MISSING_FIELDS);
    }

    if (type === 'user') {
      const user = await req.models.User.query().findById(ownerUid);
      if (!user) {
        return res.status(409).json(errorConstants.USER_NOT_FOUND);
      }
    } else if (type === 'org') {
      const org = await req.models.Org.query().where('uid', ownerUid).first();
      if (!org) {
        return res.status(409).json(errorConstants.ORG_NOT_FOUND);
      }
    }
    req.locals = { type, ownerUid };

    next();
  },
];

const validateUpdateOrg = [
  body('avatarUrl')
    .optional()
    .isURL()
    .withMessage(errorConstants.AVATAR_MUST_BE_URL),
  body('name').optional(),
  body('preferences').optional().isObject(),
  (req: any, res: any, next: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json(errors.array());
    }
    req.body = matchedData(req, { locations: ['body'] });
    if (isEmpty(req.body)) {
      return res
        .status(422)
        .json({ message: errorConstants.PROVIDE_DATA_TO_UPDATE });
    }
    next();
  },
];

const validateRemoveMember = validate([
  param('userId')
    .notEmpty()
    .isUUID()
    .withMessage(errorConstants.INVALID_USER_UID),
]);

const validateDeleteOrg = validate([
  body('password')
    .notEmpty()
    .withMessage(errorConstants.PASSWORD_FIELD_REQUIRED),
]);

const validateBulkMemberUpdate = validate([
  body('roleUid')
    .notEmpty()
    .withMessage(errorConstants.ROLE_IDS_IS_REQUIRED)
    .isUUID(4),
  body('tagUids')
    .optional()
    .isArray()
    .withMessage(errorConstants.TAG_IDS_MUST_BE_ARRAY)
    .default([]),
  body('userUids').notEmpty().isArray({ min: 1 }),
  body('userUids.*')
    .notEmpty()
    .isUUID(4)
    .withMessage(errorConstants.INVALID_USER_UID),
  body('tagReplacements').optional(),
  body('tagReplacements.existingTagUids').optional().isArray({ min: 1 }),
  body('tagReplacements.existingTagUids.*').notEmpty().isInt(),
  body('tagReplacements.newTagUids').optional().isArray({ min: 1 }),
  body('tagReplacements.newTagUids.*').notEmpty().isInt(),
]);

export {
  validateOrgProvidedAndAccess,
  validateNewOrg,
  validateTokenWithID,
  validateUpdateOrg,
  validateRemoveMember,
  validateDeleteOrg,
  validateBulkMemberUpdate,
};
