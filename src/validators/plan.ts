import { isPaginatedQuery, joiValidate } from '@app/middlewares/validator';

import { bulkUpdateActions, planRelations } from '@app/types/plan';
import joi from 'joi';
import { joiCSV } from '@app/lib/joi';

export const planConfigValidator = joi
  .object({
    type: joi.string().valid('simple', 'matrix'),
    sets: joi
      .array()
      .min(1)
      .items(
        joi
          .array()
          .required()
          .min(1)
          .items(
            joi
              .string()
              .required()
              .regex(/.+::.+/),
          ),
      ),
  })
  .with('type', 'sets')
  .pattern(/./, joi.array().items(joi.string().required()).min(1))
  .default({});

const createPlan = joiValidate(
  joi.object().keys({
    name: joi.string().required(),
    description: joi.string(),
    milestoneUids: joi.array().items(joi.number().required()).default([]),
    status: joi.number(),
    priority: joi.number(),
    testRuns: joi
      .array()
      .items(
        joi.object({
          uid: joi.number().required(),
          configuration: planConfigValidator,
        }),
      )
      .default([]),
    configuration: planConfigValidator.optional(),
    externalId: joi.string().optional(),
    source: joi.string().optional(),
    tagUids: joi.array().items(joi.number()),
  }),
);

const duplicatePlan = joiValidate(
  joi.object().keys({
    plans: joi
      .array()
      .items(
        joi
          .object()
          .keys({
            uid: joi.number().required(),
            runUids: joi.array().items(joi.number()).optional(),
          })
          .required(),
      )
      .required()
      .min(1),
  }),
);

const getPlans = joiValidate(
  joi.object({
    status: joi.number(),
    priority: joi.number(),
    archived: joi.boolean(),
    minRunCount: joi.number().min(0),
    maxRunCount: joi.number().when('minRunCount', {
      is: joi.exist(),
      then: joi.number().min(joi.ref('minRunCount')),
    }),
    minCreatedAt: joi.date(),
    maxCreatedAt: joi.date().when('minCreatedAt', {
      is: joi.exist(),
      then: joi.date().min(joi.ref('minCreatedAt')),
    }),
    minProgress: joi.number(),
    maxProgress: joi.number().when('minProgress', {
      is: joi.exist(),
      then: joi.number().min(joi.ref('minProgress')),
    }),
    statusUids: joiCSV(joi.number()),
    priorityUids: joiCSV(joi.number()),
    milestoneUids: joiCSV(joi.number()),
    tagUids: joiCSV(joi.number()),
    ...isPaginatedQuery,
  }),
  'query',
);

const deletePlan = joiValidate(
  joi.object({
    planUids: joiCSV(joi.number().required()).min(1).required(),
    cascade: joi.boolean().default(false),
  }),
  'query',
);

const bulkUpdatePlans = joiValidate(
  joi.object({
    uids: joiCSV(joi.number().required()).min(1).required(),
    action: joi.string().valid(...bulkUpdateActions),
    milestoneUids: joi
      .array()
      .items(joi.number())
      .when('action', {
        is: joi.valid('addMilestones', 'removeMilestones'),
        then: joi.required(),
      }),
    runUids: joi
      .array()
      .items(joi.number())
      .when('action', {
        is: joi.valid('addRuns', 'removeRuns'),
        then: joi.required(),
      }),
    cascade: joi
      .boolean()
      .when('action', { is: joi.valid('delete'), then: joi.required() }),
  }),
);

const getPlan = joiValidate(
  joi.object({ id: joi.number().min(1).required() }),
  'params',
);

const updatePlan = joiValidate(
  joi.object({
    name: joi.string(),
    description: joi.string(),
    milestoneUids: joi.array().items(joi.number()),
    tagUids: joi.array().items(joi.number()),
    runUids: joi.array().items(joi.number().required()),
    status: joi.number(),
    priority: joi.number(),
  }),
);

const getPlanRelations = joiValidate(
  {
    planUids: joiCSV(joi.number().required()).required(),
    relation: joi
      .string()
      .required()
      .valid(...planRelations),
  },
  'query',
);

const plansValidator = {
  createPlan,
  duplicatePlan,
  getPlans,
  deletePlan,
  bulkUpdatePlans,
  getPlan,
  updatePlan,
  getPlanRelations,
};
export default plansValidator;
