import errorConstants from '@app/constants/errors';
import joi from 'joi';
import { joiValidate } from '@app/middlewares/validator';
import { permissions } from '@app/constants/auth';

const validateCreateAccessToken = joiValidate({
  name: joi
    .string()
    .required()
    .regex(/^[a-zA-Z0-9_-~]+$/),
  days: joi.number().integer().positive(),
  permissions: joi
    .array()
    .items(joi.valid(...Object.keys(permissions)))
    .default([])
    .messages({ 'any.only': errorConstants.INVALID_PERMISSION }),
  projects: joi.array().items(joi.number()).default([]),
});

const validateUpdateAccessToken = joiValidate({
  name: joi.string().required(),
});

const validateAccessTokenId = joiValidate(
  {
    id: joi.string().uuid({ version: 'uuidv4' }).required(),
  },
  'params',
);

export {
  validateUpdateAccessToken,
  validateCreateAccessToken,
  validateAccessTokenId,
};
