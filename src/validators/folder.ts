import { body, query } from 'express-validator';
import errorConstants from '@app/constants/errors';
import { validate } from '@app/middlewares/validator';

const validateCreateFolder = validate([
  body('name').not().isEmpty().withMessage(errorConstants.NAME_FIELD_REQUIRED),
  body('externalId').optional(),
  body('source').optional(),
  body('customFields').optional(),
  body('parentId')
    .not()
    .isEmpty()
    .isInt()
    .withMessage(errorConstants.INVALID_FOLDER_UID),
]);

const validateUpdateFolder = validate([
  body('source').optional(),
  body('name').optional(),
  body('customFields').optional(),
  body('parentId')
    .optional()
    .isInt()
    .withMessage(errorConstants.INVALID_FOLDER_UID),
]);

const validateQueryFolder = validate([
  query('entityType')
    .optional()
    .isIn(['case', 'execution'])
    .withMessage(errorConstants.INVALID_FOLDER_QUERY_ENTITY_TYPE),

  query('testRunId')
    .if(query('entityType').equals('execution'))
    .notEmpty()
    .withMessage(errorConstants.TEST_RUN_ID_REQUIRED_FOLDER_QUERY)
    .isInt()
    .withMessage(errorConstants.ID_MUST_BE_NUMBER),
]);

export { validateCreateFolder, validateUpdateFolder, validateQueryFolder };
