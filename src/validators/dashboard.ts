import { query, body, param } from 'express-validator';
import errorsConstants from '@app/constants/errors';
import { validate } from '@app/middlewares/validator';
import dayjs from 'dayjs';

const dashboardValidator = validate([
  query('startRange')
    .optional()
    .isISO8601()
    .toDate()
    .withMessage(errorsConstants.START_MUST_BE_DATE),
  query('endRange')
    .optional()
    .isISO8601()
    .toDate()
    .withMessage(errorsConstants.END_MUST_BE_DATE),
  query('view')
    .optional()
    .isString()
    .isIn(['testRun', 'testPlan', 'milestone'])
    .withMessage(errorsConstants.INVALID_VIEW),
  param('key')
    .custom((value, { req }) => {
      if (req.query.view && !value) {
        throw new Error(errorsConstants.PROJECT_NOT_FOUND);
      }
      return true;
    }),
]);

const validateUpdateCharts = validate([
  body('charts')
    .optional()
    .isArray(),
  body('charts.*.x')
    .isNumeric()
    .withMessage(errorsConstants.MISSING_X_VALUE),
  body('charts.*.y')
    .isNumeric()
    .withMessage(errorsConstants.MISSING_Y_VALUE),
  body('charts.*.w')
    .isNumeric()
    .withMessage(errorsConstants.MISSING_W_VALUE),
  body('charts.*.h')
    .isNumeric()
    .withMessage(errorsConstants.MISSING_H_VALUE),
  body('period')
    .optional()
    .custom((value) => {
      const enums = ['last7Days', 'last14Days', 'lastMonth'];

      if (typeof value === 'string' && enums.includes(value)) { return true; }

      const isValidDate = (date : any) => dayjs(date, 'YYYY-MM-DD', true).isValid();
      if (Array.isArray(value) && value.length === 2 && isValidDate(value[0]) && isValidDate(value[1])) { return true; }

      throw new Error('Period must be either a valid enum or an array of two dates');
    }),
]);

const validateUpdateDashboard = validate([
  body('name')
    .isString()
    .withMessage(errorsConstants.DASHBOARD_NAME_RESTRICT),
  body('isDefault')
    .isBoolean()
    .withMessage(errorsConstants.DEFAULT_BOOLEAN),
  body('editable')
    .isBoolean()
    .withMessage(errorsConstants.EDITABLE_BOOLEAN),
]);
const createValidator = validate([
  body('name')
    .isString()
    .notEmpty()
    .withMessage(errorsConstants.NAME_FIELD_REQUIRED),
  body('isDefault')
    .optional()
    .isBoolean({ loose: true }),
]);

const chartValidator = validate([
  param('chartId')
    .isNumeric()
    .withMessage(errorsConstants.INVALID_CHART_ID),

]);

export {
  dashboardValidator, validateUpdateDashboard, chartValidator, createValidator, validateUpdateCharts,
};
