import Joi from 'joi';
import { joiValidate } from '@app/middlewares/validator';

const validateGetProjects = joiValidate(
  Joi.object({
    is_completed: Joi.boolean().optional(),
    limit: Joi.number().optional(),
    offset: Joi.number().optional(),
  }).unknown(true),
  'query',
);
const validateCreateProject = joiValidate(
  Joi.object({
    name: Joi.string().required(),
    announcement: Joi.string().optional(),
    showAnnouncement: Joi.boolean().optional(),
    suite: Joi.string().optional(),
  }).unknown(true),
  'body',
);

const validateUpdateProject = joiValidate(
  Joi.object({
    name: Joi.string().required(),
    announcement: Joi.string().optional(),
    showAnnouncement: Joi.boolean().optional(),
    suite: Joi.string().optional(),
  }).unknown(true),
  'body',
);

export { validateCreateProject, validateUpdateProject, validateGetProjects };
