import { joiValidate } from '@app/middlewares/validator';
import <PERSON><PERSON> from 'joi';

const validateGetCases = joiValidate(
  Joi.object({
    suite_id: Joi.number().optional(),
    created_after: Joi.number().optional(),
    created_before: Joi.number().optional(),
    created_by: Joi.string().optional(),
    filter: Joi.string().optional(),
    priority_id: Joi.number().optional(),
    section_id: Joi.number().optional(),
    template_id: Joi.number().optional(),
    updated_after: Joi.number().optional(),
    updated_before: Joi.number().optional(),
    offset: Joi.number().optional(),
    limit: Joi.number().optional(),
  }).unknown(true),
  'query',
);

const validateCreateCase = joiValidate(
  Joi.object({
    title: Joi.string().required(),
    template_id: Joi.number().optional(),
    type_id: Joi.number().optional(),
    priority_id: Joi.number().optional(),
  }).unknown(true),
  'body',
);

const validateUpdateCase = joiValidate(
  Joi.object({
    section_id: Joi.number().optional(),
    title: Joi.string().optional(),
    template_id: Joi.number().optional(),
    type_id: Joi.number().optional(),
    priority_id: Joi.number().optional(),
  }).unknown(true),
  'body',
);

const validateCopyCasesToSection = joiValidate(
  Joi.object({
    case_ids: Joi.array().items(Joi.number()).required(),
  }).unknown(true),
  'body',
);
const validateUpdateCases = joiValidate(
  Joi.object({
    case_ids: Joi.array().items(Joi.number()).required(),
    section_id: Joi.number().optional(),
    title: Joi.string().optional(),
    template_id: Joi.number().optional(),
    type_id: Joi.number().optional(),
    priority_id: Joi.number().optional(),
  }).unknown(true),
  'body',
);

const validateMoveCasesToSection = joiValidate(
  Joi.object({
    case_ids: Joi.array().items(Joi.number()).required(),
  }).unknown(true),
  'body',
);

export {
  validateGetCases, validateCreateCase, validateUpdateCase, validateCopyCasesToSection, validateUpdateCases, validateMoveCasesToSection,
};
