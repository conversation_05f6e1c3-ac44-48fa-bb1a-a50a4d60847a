import { joiValidate } from '@app/middlewares/validator';
import <PERSON><PERSON> from 'joi';

const validateGetPlans = joiValidate(
  Joi.object({
    created_after: Joi.number().optional(),
    created_before: Joi.number().optional(),
    is_completed: Joi.boolean().optional(),
    milestone_id: Joi.number().optional(),
    offset: Joi.number().optional(),
    limit: Joi.number().optional(),
  }).unknown(true),
  'query',
);

const validateCreatePlan = joiValidate(
  Joi.object({
    name: Joi.string().required(),
    description: Joi.string().optional(),
    milestone_id: Joi.number().optional(),
    entries: Joi.array().optional(),
  }).unknown(true),
  'body',
);

const validateAddPlanEntry = joiValidate(
  Joi.object({
    suite_id: Joi.number().optional(),
    name: Joi.string().optional(),
    description: Joi.string().optional(),
    include_all: Joi.boolean().optional(),
    case_ids: Joi.array().optional(),
    config_ids: Joi.array().optional(),
    runs: Joi.array().optional(),
  }).unknown(true),
  'body',
);

const validateAddRunToPlanEntry = joiValidate(
  Joi.object({
    config_ids: Joi.array().required(),
    description: Joi.string().optional(),
    include_all: Joi.boolean().optional(),
    case_ids: Joi.array().optional(),
  }).unknown(true),
  'body',
);

const validateUpdatePlan = joiValidate(
  Joi.object({
    name: Joi.string().optional(),
    description: Joi.string().optional(),
    milestone_id: Joi.number().optional(),
  }).unknown(true),
  'body',
);

const validateUpdatePlanEntry = joiValidate(
  Joi.object({
    name: Joi.string().optional(),
    description: Joi.string().optional(),
  }).unknown(true),
  'body',
);

const validateUpdateRunInPlanEntry = joiValidate(
  Joi.object({
    name: Joi.string().optional(),
    description: Joi.string().optional(),
    include_all: Joi.boolean().optional(),
    case_ids: Joi.array().optional(),
  }).unknown(true),
  'body',
);

export {
  validateGetPlans, validateCreatePlan, validateAddPlanEntry, validateAddRunToPlanEntry, validateUpdatePlan, validateUpdatePlanEntry, validateUpdateRunInPlanEntry,
};
