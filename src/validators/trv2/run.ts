import { joiValidate } from '@app/middlewares/validator';
import <PERSON><PERSON> from 'joi';

const validateGetRuns = joiValidate(
  Joi.object({
    created_after: Joi.number().optional(),
    created_before: Joi.number().optional(),
    created_by: Joi.string().optional(),
    is_completed: Joi.boolean().optional(),
    milestone_id: Joi.number().optional(),
    offset: Joi.number().optional(),
    limit: Joi.number().optional(),
  }).unknown(true),
  'query',
);

const validateCreateRun = joiValidate(
  Joi.object({
    name: Joi.string().required(),
    description: Joi.string().optional(),
    milestone_id: Joi.number().optional(),
    include_all: Joi.boolean().optional(),
    case_ids: Joi.array().optional(),
  }).unknown(true),
  'body',
);

const validateUpdateRun = joiValidate(
  Joi.object({
    name: Joi.string().optional(),
    description: Joi.string().optional(),
    milestone_id: Joi.number().optional(),
    include_all: Joi.boolean().optional(),
    case_ids: Joi.array().optional(),
  }).unknown(true),
  'body',
);

export {
  validateGetRuns,
  validateCreateRun,
  validateUpdateRun,
};
