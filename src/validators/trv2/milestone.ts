import { joiValidate } from '@app/middlewares/validator';
import <PERSON><PERSON> from 'joi';

const validateGetMilestones = joiValidate(
  Joi.object({
    is_completed: Joi.boolean().optional(),
    is_started: Joi.boolean().optional(),
    offset: Joi.number().optional(),
    limit: Joi.number().optional(),
  }).unknown(true),
  'query',
);

const validateCreateMilestone = joiValidate(
  Joi.object({
    name: Joi.string().required(),
    description: Joi.string().optional(),
    due_on: Joi.number().optional(),
    start_on: Joi.number().optional(),
  }).unknown(true),
  'body',
);

const validateUpdateMilestone = joiValidate(
  Joi.object({
    name: Joi.string().optional(),
    description: Joi.string().optional(),
    due_on: Joi.number().optional(),
    start_on: Joi.number().optional(),
  }).unknown(true),
  'body',
);

export { validateGetMilestones, validateCreateMilestone, validateUpdateMilestone };
