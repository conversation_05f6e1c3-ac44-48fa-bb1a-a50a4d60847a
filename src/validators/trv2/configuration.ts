import { joiValidate } from '@app/middlewares/validator';
import <PERSON><PERSON> from 'joi';

const validateAddConfigGroup = joiValidate(
  Joi.object({
    name: Joi.string().required(),
  }).unknown(true),
  'body',
);

const validateAddConfigOption = joiValidate(
  Joi.object({
    name: Joi.string().required(),
  }).unknown(true),
  'body',
);

const validateUpdateConfigGroup = joiValidate(
  Joi.object({
    name: Joi.string().optional(),
  }).unknown(true),
  'body',
);
const validateUpdateConfigOption = joiValidate(
  Joi.object({
    name: Joi.string().optional(),
  }).unknown(true),
  'body',
);

export {
  validateAddConfigGroup, validateAddConfigOption, validateUpdateConfigGroup, validateUpdateConfigOption,
};
