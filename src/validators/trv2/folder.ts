import { joiValidate } from '@app/middlewares/validator';
import <PERSON><PERSON> from 'joi';

const validateGetSections = joiValidate(
  Joi.object({
    limit: Joi.number().optional(),
    offset: Joi.number().optional(),
  }).unknown(true),
  'query',
);

const validateCreateSection = joiValidate(
  Joi.object({
    name: Joi.string().required(),
    parent_id: Joi.number().optional(),
    description: Joi.string().optional(),
  }).unknown(true),
  'body',
);

const validateUpdateSection = joiValidate(
  Joi.object({
    name: Joi.string().optional(),
    parent_id: Joi.number().optional(),
    description: Joi.string().optional(),
  }).unknown(true),
  'body',
);

const validateMoveSection = joiValidate(
  Joi.object({
    parent_id: Joi.number().optional(),
    after_id: Joi.number().optional(),
  }).unknown(true),
  'body',
);

export {
  validateCreateSection, validateUpdateSection, validateGetSections, validateMoveSection,
};
