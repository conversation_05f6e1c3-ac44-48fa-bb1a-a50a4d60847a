import { joiValidate } from '@app/middlewares/validator';
import Jo<PERSON> from 'joi';

const validateGetResults = joiValidate(
  Joi.object({
    status_id: Joi.string().optional(),
    limit: Joi.number().optional(),
    offset: Joi.number().optional(),
  }).unknown(true),
  'query',
);

const validateGetResultsByRun = joiValidate(
  Joi.object({
    created_after: Joi.number().optional(),
    created_before: Joi.number().optional(),
    created_by: Joi.string().optional(),
    status_id: Joi.string().optional(),
    limit: Joi.number().optional(),
    offset: Joi.number().optional(),
  }).unknown(true),
  'query',
);

const validateCreateResult = joiValidate(
  Joi.object({
    status_id: Joi.string().required(),
    comment: Joi.string().optional(),
  }).unknown(true),
  'body',
);

const validateAddResults = joiValidate(
  Joi.object({
    results: Joi.array().items(Joi.object({
      test_case_id: Joi.number().required(),
      status_id: Joi.string().required(),
      comment: Joi.string().optional(),
    })).required(),
  }).unknown(true),
  'body',
);

const validateAddResultForCases = joiValidate(
  Joi.object({
    results: Joi.array().items(Joi.object({
      case_id: Joi.number().required(),
      status_id: Joi.string().required(),
      comment: Joi.string().optional(),
    })).required(),
  }).unknown(true),
  'body',
);

export {
  validateGetResults, validateGetResultsByRun, validateCreateResult, validateAddResults, validateAddResultForCases,
};
