import { body } from 'express-validator';
import errorConstants from '@app/constants/errors';
import { validate } from '@app/middlewares/validator';

const validateCreateBranch = validate([
  body('externalId')
    .not()
    .isEmpty()
    .withMessage(errorConstants.EXTERNALID_FIELD_REQUIRED),
  body('source')
    .not()
    .isEmpty()
    .withMessage(errorConstants.SOURCE_FIELD_REQUIRED),
  body('repoUID')
    .isUUID('4')
    .not()
    .isEmpty()
    .withMessage(errorConstants.REPO_FIELD_REQUIRED),
]);

export { validateCreateBranch };
