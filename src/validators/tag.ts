import { check, param } from 'express-validator';
import { joiValidate, validate } from '@app/middlewares/validator';

import { entityTypes } from '@app/models/tag';
import errorConstants from '@app/constants/errors';
import joi from 'joi';

const validateCreateTag = validate([
  check('name')
    .not()
    .isEmpty()
    .isString()
    .withMessage(errorConstants.NAME_FIELD_REQUIRED),
  check('description')
    .optional()
    .isString()
    .withMessage(errorConstants.DESCRIPTION_FIELD_REQUIRED),
  check('entityTypes')
    .isArray()
    .withMessage(errorConstants.ENTITY_TYPES_FIELD_NOT_ARRAY),
  check('entityTypes.*')
    .isString()
    .withMessage(errorConstants.EACH_ENTITY_TYPE_MUST_BE_STRING),
]);

const validateUpdateTag = validate([
  param('id').notEmpty().withMessage(errorConstants.TAG_UID_REQUIRED),
  check('name')
    .optional()
    .isString()
    .withMessage(errorConstants.NAME_FIELD_REQUIRED),
  check('entityTypes')
    .optional()
    .isArray()
    .withMessage(errorConstants.ENTITY_TYPES_FIELD_NOT_ARRAY),
  check('entityTypes.*')
    .isString()
    .withMessage(errorConstants.EACH_ENTITY_TYPE_MUST_BE_STRING),
]);

const validateListTags = joiValidate(
  {
    entityType: joi
      .string()
      .optional()
      .valid(...entityTypes),
    includeArchived: joi.bool().default(false),
  },
  'query',
);

export { validateCreateTag, validateUpdateTag, validateListTags };
