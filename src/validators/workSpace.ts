import Joi from 'joi';
import { joiValidate } from '@app/middlewares/validator';
import errorConstants from '@app/constants/errors';

const validateGetWorkspaceExecutionQuery = joiValidate(
  Joi.object({
    milestoneUids: Joi.array()
      .items(
        Joi.number().integer().messages({
          'number.base': errorConstants.MILESTONEUIDS_MUST_BE_NUMBERS,
        }),
      )
      .default([])
      .messages({ 'array.base': errorConstants.MILESTONEUIDS_MUST_BE_ARRAY }),
    searchTerm: Joi.string()
      .messages({ 'string.base': errorConstants.SEARCH_QUERY_MUST_BE_NUMBERS })
      .allow('')
      .default(''),
    runUids: Joi.array()
      .items(
        Joi.number()
          .integer()
          .messages({ 'number.base': errorConstants.RUNUIDS_MUST_BE_NUMBERS }),
      )
      .default([])
      .messages({ 'array.base': errorConstants.RUNUIDS_MUST_BE_ARRAY }),

    userUids: Joi.array()
      .items(
        Joi.string().messages({
          'string.base': errorConstants.USERUIDS_MUST_BE_STRING,
        }),
      )
      .default([])
      .messages({ 'array.base': errorConstants.USERUIDS_MUST_BE_ARRAY }),

    planUids: Joi.array()
      .items(
        Joi.number()
          .integer()
          .messages({ 'number.base': errorConstants.PLANUIDS_MUST_BE_NUMBERS }),
      )
      .default([])
      .messages({ 'array.base': errorConstants.PLANUIDS_MUST_BE_ARRAY }),

    projectUids: Joi.array()
      .items(
        Joi.number().integer().messages({
          'number.base': errorConstants.PROJECTUIDS_MUST_BE_NUMBERS,
        }),
      )
      .default([])
      .messages({ 'array.base': errorConstants.PROJECTUIDS_MUST_BE_ARRAY }),

    priorities: Joi.array()
      .items(
        Joi.number().integer().messages({
          'number.base': errorConstants.PRIORITIES_MUST_BE_NUMBERS,
        }),
      )
      .default([])
      .messages({ 'array.base': errorConstants.PRIORITIES_MUST_BE_ARRAY }),

    status: Joi.array()
      .items(
        Joi.number()
          .integer()
          .messages({ 'number.base': errorConstants.STATUS_MUST_BE_NUMBERS }),
      )
      .default([])
      .messages({ 'array.base': errorConstants.STATUS_MUST_BE_ARRAY }),

    tags: Joi.array()
      .items(
        Joi.number().messages({
          'string.base': errorConstants.TAGS_MUST_BE_NUMBERS,
        }),
      )
      .default([])
      .messages({ 'array.base': errorConstants.TAGS_MUST_BE_ARRAY }),

    assignDateStart: Joi.date()
      .iso()
      .messages({ 'date.base': errorConstants.ASSIGN_DATE_START_MUST_BE_DATE }),

    assignDateEnd: Joi.date()
      .iso()
      .messages({ 'date.base': errorConstants.ASSIGN_DATE_END_MUST_BE_DATE }),

    dueDateStart: Joi.date()
      .iso()
      .messages({ 'date.base': errorConstants.DUE_DATE_START_MUST_BE_DATE }),

    dueDateEnd: Joi.date()
      .iso()
      .messages({ 'date.base': errorConstants.DUE_DATE_END_MUST_BE_DATE }),

    limit: Joi.number()
      .integer()
      .min(1)
      .default(10)
      .messages({ 'number.base': errorConstants.LIMIT_MUST_BE_NUMBER }),

    offset: Joi.number()
      .integer()
      .min(0)
      .default(0)
      .messages({ 'number.base': errorConstants.OFFSET_MUST_BE_NUMBER }),
  }),
  'query',
);

const validateWorkSpace = {
  validateGetWorkspaceExecutionQuery,
};

export default validateWorkSpace;
