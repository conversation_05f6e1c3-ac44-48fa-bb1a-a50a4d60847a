import { param } from 'express-validator';
import joi from 'joi';

import errorConstants from '@app/constants/errors';
import { joiValidate, validate } from '@app/middlewares/validator';

const validateId = (err: string) => validate([param('id').isInt().notEmpty().withMessage(err)]);

const validateTemplateId = validateId(errorConstants.INVALID_TEMPLATE_UID);

const optionals = {
  isDefault: joi.boolean().optional(),
  templateFields: joi.array().optional(),
};
const validateWriteTemplate = joiValidate({
  name: joi.string().required(),
  ...optionals,
});

const validateUpdateTemplate = joiValidate({
  name: joi.string().optional(),
  ...optionals,
});

export { validateWriteTemplate, validateTemplateId, validateUpdateTemplate };
