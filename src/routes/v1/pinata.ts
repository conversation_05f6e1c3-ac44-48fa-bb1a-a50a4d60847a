import * as pinataController from '@app/controllers/pinata';

import express, { Router } from 'express';

import { auth } from '@ss-libs/ss-component-auth';

const router: Router = express.Router();

// patch endpoint for pushing pinata data
router.patch(
  '/pinata/executions',
  auth().authenticateBearerWithoutPassthrough,
  pinataController.pushPinataData,
);

router.get('/pinata/latest', pinataController.pinataLatest);

export default router;
