/* tslint:disable */
/* eslint-disable */
// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
import type { TsoaRoute } from '@tsoa/runtime';
import {  fetchMiddlewares, ExpressTemplateService } from '@tsoa/runtime';
// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
import { TemplateController } from './../../controllers/v1/templateController';
// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
import { TagController } from './../../controllers/v1/tagController';
// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
import { SharedStepController } from './../../controllers/v1/sharedStepController';
// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
import { RunController } from './../../controllers/v1/runController';
// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
import { ProjectController } from './../../controllers/v1/projectController';
// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
import { PlanController } from './../../controllers/v1/planController';
// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
import { MilestoneController } from './../../controllers/v1/milestoneController';
// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
import { FolderController } from './../../controllers/v1/folderController';
// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
import { ExecutionController } from './../../controllers/v1/executionController';
// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
import { DataController } from './../../controllers/v1/dataController';
// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
import { CustomFieldController } from './../../controllers/v1/customFieldController';
// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
import { CaseController } from './../../controllers/v1/caseController';
import type { Request as ExRequest, Response as ExResponse, RequestHandler, Router } from 'express';



// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

const models: TsoaRoute.Models = {
    "TemplateQueryParams": {
        "dataType": "refObject",
        "properties": {
            "limit": {"dataType":"double","required":true},
            "offset": {"dataType":"double","required":true},
            "q": {"dataType":"string"},
            "name": {"dataType":"string"},
            "createdByIds": {"dataType":"array","array":{"dataType":"string"}},
            "creationStartDate": {"dataType":"string"},
            "creationEndDate": {"dataType":"string"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "CustomFieldDataType": {
        "dataType": "refAlias",
        "type": {"dataType":"union","subSchemas":[{"dataType":"enum","enums":["text"]},{"dataType":"enum","enums":["radio"]},{"dataType":"enum","enums":["date"]},{"dataType":"enum","enums":["step"]},{"dataType":"enum","enums":["attachment"]},{"dataType":"enum","enums":["link"]},{"dataType":"enum","enums":["checkbox"]},{"dataType":"enum","enums":["dropdown"]},{"dataType":"enum","enums":["none"]}],"validators":{}},
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "TemplateField": {
        "dataType": "refObject",
        "properties": {
            "id": {"dataType":"string","required":true},
            "name": {"dataType":"string","required":true},
            "dataType": {"ref":"CustomFieldDataType","required":true},
            "defaultValue": {"dataType":"string"},
            "value": {"dataType":"string"},
            "options": {"dataType":"array","array":{"dataType":"string"}},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "CreateTemplateDto": {
        "dataType": "refObject",
        "properties": {
            "name": {"dataType":"string","required":true},
            "templateFields": {"dataType":"array","array":{"dataType":"refObject","ref":"TemplateField"}},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "UpdateTemplateDto": {
        "dataType": "refObject",
        "properties": {
            "name": {"dataType":"string"},
            "templateFields": {"dataType":"array","array":{"dataType":"refObject","ref":"TemplateField"}},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "TagQueryParams": {
        "dataType": "refObject",
        "properties": {
            "limit": {"dataType":"double","required":true},
            "offset": {"dataType":"double","required":true},
            "q": {"dataType":"string"},
            "entityType": {"dataType":"string"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "EntityType": {
        "dataType": "refAlias",
        "type": {"dataType":"union","subSchemas":[{"dataType":"enum","enums":["cases"]},{"dataType":"enum","enums":["milestones"]},{"dataType":"enum","enums":["executions"]},{"dataType":"enum","enums":["results"]},{"dataType":"enum","enums":["runs"]},{"dataType":"enum","enums":["plans"]},{"dataType":"enum","enums":["defects"]},{"dataType":"enum","enums":["users"]},{"dataType":"enum","enums":["roles"]}],"validators":{}},
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "CreateTagDto": {
        "dataType": "refObject",
        "properties": {
            "name": {"dataType":"string","required":true},
            "description": {"dataType":"string"},
            "entityTypes": {"dataType":"array","array":{"dataType":"refAlias","ref":"EntityType"},"required":true},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "UpdateTagDto": {
        "dataType": "refObject",
        "properties": {
            "name": {"dataType":"string"},
            "description": {"dataType":"string"},
            "entityTypes": {"dataType":"array","array":{"dataType":"refAlias","ref":"EntityType"}},
            "archived": {"dataType":"boolean"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "PaginatedQuery": {
        "dataType": "refObject",
        "properties": {
            "limit": {"dataType":"double","required":true},
            "offset": {"dataType":"double","required":true},
            "q": {"dataType":"string"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "SharedStepItem": {
        "dataType": "refObject",
        "properties": {
            "id": {"dataType":"string","required":true},
            "title": {"dataType":"string"},
            "description": {"dataType":"string"},
            "expectedResult": {"dataType":"string"},
            "children": {"dataType":"array","array":{"dataType":"refObject","ref":"SharedStepItem"}},
            "sharedStepUid": {"dataType":"double"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "CreateSharedStepDto": {
        "dataType": "refObject",
        "properties": {
            "name": {"dataType":"string","required":true},
            "steps": {"dataType":"array","array":{"dataType":"refObject","ref":"SharedStepItem"},"required":true},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "UpdateSharedStepDto": {
        "dataType": "refObject",
        "properties": {
            "name": {"dataType":"string"},
            "steps": {"dataType":"array","array":{"dataType":"refObject","ref":"SharedStepItem"}},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "CreateRunDTO": {
        "dataType": "refObject",
        "properties": {
            "externalId": {"dataType":"string"},
            "source": {"dataType":"string"},
            "link": {"dataType":"string"},
            "priority": {"dataType":"double"},
            "status": {"dataType":"double"},
            "name": {"dataType":"string","required":true},
            "description": {"dataType":"string"},
            "dueAt": {"dataType":"datetime"},
            "tagUids": {"dataType":"array","array":{"dataType":"double"}},
            "configs": {"dataType":"array","array":{"dataType":"string"}},
            "caseUids": {"dataType":"array","array":{"dataType":"double"}},
            "milestoneUids": {"dataType":"array","array":{"dataType":"double"}},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "UpdateRunDTO": {
        "dataType": "refObject",
        "properties": {
            "name": {"dataType":"string"},
            "description": {"dataType":"string"},
            "status": {"dataType":"double"},
            "priority": {"dataType":"double"},
            "dueAt": {"dataType":"datetime"},
            "addMilestoneUids": {"dataType":"array","array":{"dataType":"double"}},
            "removeMilestoneUids": {"dataType":"array","array":{"dataType":"double"}},
            "addTagUids": {"dataType":"array","array":{"dataType":"double"}},
            "removeTagUids": {"dataType":"array","array":{"dataType":"double"}},
            "addPlanUids": {"dataType":"array","array":{"dataType":"double"}},
            "removePlanUids": {"dataType":"array","array":{"dataType":"double"}},
            "archive": {"dataType":"boolean"},
            "configs": {"dataType":"array","array":{"dataType":"string"}},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "PaginatedResult_unknown_": {
        "dataType": "refObject",
        "properties": {
            "items": {"dataType":"array","array":{"dataType":"any"},"required":true},
            "count": {"dataType":"double","required":true},
            "nextOffset": {"dataType":"double","required":true},
            "metadata": {"dataType":"any"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "Record_string.any_": {
        "dataType": "refAlias",
        "type": {"dataType":"nestedObjectLiteral","nestedProperties":{},"additionalProperties":{"dataType":"any"},"validators":{}},
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "CreateProjectDto": {
        "dataType": "refObject",
        "properties": {
            "name": {"dataType":"string","required":true},
            "key": {"dataType":"string","required":true},
            "externalId": {"dataType":"string"},
            "source": {"dataType":"string"},
            "customFields": {"ref":"Record_string.any_","required":true},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "ListPlanQueryParams": {
        "dataType": "refObject",
        "properties": {
            "limit": {"dataType":"double","required":true},
            "offset": {"dataType":"double","required":true},
            "q": {"dataType":"string"},
            "status": {"dataType":"double"},
            "priority": {"dataType":"double"},
            "statusUids": {"dataType":"array","array":{"dataType":"double"}},
            "priorityUids": {"dataType":"array","array":{"dataType":"double"}},
            "archived": {"dataType":"boolean","required":true},
            "minRunCount": {"dataType":"double","required":true},
            "maxRunCount": {"dataType":"double"},
            "minCreatedAt": {"dataType":"string","required":true},
            "maxCreatedAt": {"dataType":"string"},
            "minProgress": {"dataType":"double","required":true},
            "maxProgress": {"dataType":"double"},
            "tagUids": {"dataType":"array","array":{"dataType":"double"}},
            "milestoneUids": {"dataType":"array","array":{"dataType":"double"}},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "ConfigurationDTO": {
        "dataType": "refAlias",
        "type": {"dataType":"nestedObjectLiteral","nestedProperties":{"sets":{"dataType":"array","array":{"dataType":"array","array":{"dataType":"string"}},"required":true},"type":{"dataType":"union","subSchemas":[{"dataType":"enum","enums":["simple"]},{"dataType":"enum","enums":["matrix"]}],"required":true}},"validators":{}},
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "Record_string.string_": {
        "dataType": "refAlias",
        "type": {"dataType":"nestedObjectLiteral","nestedProperties":{},"additionalProperties":{"dataType":"string"},"validators":{}},
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "CreatePlanDTO": {
        "dataType": "refObject",
        "properties": {
            "name": {"dataType":"string","required":true},
            "description": {"dataType":"string"},
            "milestoneUids": {"dataType":"array","array":{"dataType":"double"}},
            "status": {"dataType":"double"},
            "priority": {"dataType":"double"},
            "customFields": {"ref":"Record_string.string_"},
            "externalId": {"dataType":"string"},
            "source": {"dataType":"string"},
            "testRuns": {"dataType":"array","array":{"dataType":"nestedObjectLiteral","nestedProperties":{"configuration":{"ref":"ConfigurationDTO","required":true},"uid":{"dataType":"double","required":true}}}},
            "configuration": {"ref":"ConfigurationDTO"},
            "tagUids": {"dataType":"array","array":{"dataType":"double"}},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "UpdatePlanDTO": {
        "dataType": "refObject",
        "properties": {
            "name": {"dataType":"string"},
            "description": {"dataType":"string"},
            "tagUids": {"dataType":"array","array":{"dataType":"double"}},
            "milestoneUids": {"dataType":"array","array":{"dataType":"double"}},
            "runUids": {"dataType":"array","array":{"dataType":"double"}},
            "status": {"dataType":"double"},
            "priority": {"dataType":"double"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "CreateMilestoneDTO": {
        "dataType": "refObject",
        "properties": {
            "name": {"dataType":"string","required":true},
            "description": {"dataType":"string"},
            "startDate": {"dataType":"string","required":true},
            "dueAt": {"dataType":"string","required":true},
            "status": {"dataType":"double","required":true},
            "planIds": {"dataType":"array","array":{"dataType":"double"}},
            "runIds": {"dataType":"array","array":{"dataType":"double"}},
            "tagUids": {"dataType":"array","array":{"dataType":"double"}},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "UpdateMilestoneDTO": {
        "dataType": "refObject",
        "properties": {
            "name": {"dataType":"string"},
            "description": {"dataType":"string"},
            "startDate": {"dataType":"string"},
            "dueAt": {"dataType":"string"},
            "status": {"dataType":"double"},
            "planIds": {"dataType":"array","array":{"dataType":"double"}},
            "runIds": {"dataType":"array","array":{"dataType":"double"}},
            "tagUids": {"dataType":"array","array":{"dataType":"double"}},
            "archived": {"dataType":"boolean"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "CreateFolderDto": {
        "dataType": "refObject",
        "properties": {
            "name": {"dataType":"string","required":true},
            "externalId": {"dataType":"string"},
            "source": {"dataType":"string"},
            "customFields": {"ref":"Record_string.any_"},
            "parentId": {"dataType":"double","required":true},
            "status": {"dataType":"double"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "UpdateFolderDto": {
        "dataType": "refObject",
        "properties": {
            "name": {"dataType":"string"},
            "externalId": {"dataType":"string"},
            "source": {"dataType":"string"},
            "customFields": {"ref":"Record_string.any_"},
            "parentId": {"dataType":"double"},
            "status": {"dataType":"double"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "ExecutionStep": {
        "dataType": "refObject",
        "properties": {
            "testStepUid": {"dataType":"string","required":true},
            "description": {"dataType":"string","required":true},
            "customFields": {"ref":"Record_string.any_","required":true},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "CreateExecutionDTO": {
        "dataType": "refObject",
        "properties": {
            "externalId": {"dataType":"string"},
            "source": {"dataType":"string"},
            "testCaseUid": {"dataType":"double"},
            "testCaseRef": {"dataType":"double"},
            "testRunUid": {"dataType":"double"},
            "status": {"dataType":"double"},
            "customFields": {"ref":"Record_string.any_","required":true},
            "steps": {"dataType":"array","array":{"dataType":"refObject","ref":"ExecutionStep"}},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "Pick_TestCaseStepItem.Exclude_keyofTestCaseStepItem.id-or-children__": {
        "dataType": "refAlias",
        "type": {"dataType":"nestedObjectLiteral","nestedProperties":{"description":{"dataType":"string"},"sharedStepUid":{"dataType":"double"},"title":{"dataType":"string"},"expectedResult":{"dataType":"string"}},"validators":{}},
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "TestExecutionStepDTO": {
        "dataType": "refObject",
        "properties": {
            "description": {"dataType":"string"},
            "sharedStepUid": {"dataType":"double"},
            "title": {"dataType":"string"},
            "expectedResult": {"dataType":"string"},
            "uid": {"dataType":"string"},
            "position": {"dataType":"double","required":true},
            "status": {"dataType":"double"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "UpdateExecutionDTO": {
        "dataType": "refObject",
        "properties": {
            "status": {"dataType":"double"},
            "priority": {"dataType":"double"},
            "dueAt": {"dataType":"datetime"},
            "assignedTo": {"dataType":"string"},
            "tagUids": {"dataType":"array","array":{"dataType":"double"}},
            "tagReplacements": {"dataType":"array","array":{"dataType":"nestedObjectLiteral","nestedProperties":{"newTagUids":{"dataType":"array","array":{"dataType":"double"}},"existingTagUids":{"dataType":"array","array":{"dataType":"double"}}}}},
            "name": {"dataType":"string"},
            "steps": {"dataType":"array","array":{"dataType":"refObject","ref":"TestExecutionStepDTO"},"required":true},
            "templateFields": {"dataType":"any"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "CaseData": {
        "dataType": "refObject",
        "properties": {
            "name": {"dataType":"string","required":true},
            "externalId": {"dataType":"string","required":true},
            "source": {"dataType":"string","required":true},
            "folderExternalId": {"dataType":"string"},
        },
        "additionalProperties": {"dataType":"any"},
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "FolderData": {
        "dataType": "refObject",
        "properties": {
            "name": {"dataType":"string","required":true},
            "externalId": {"dataType":"string","required":true},
            "source": {"dataType":"string","required":true},
        },
        "additionalProperties": {"dataType":"any"},
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "RunData": {
        "dataType": "refObject",
        "properties": {
            "name": {"dataType":"string","required":true},
            "externalId": {"dataType":"string","required":true},
            "source": {"dataType":"string","required":true},
        },
        "additionalProperties": {"dataType":"any"},
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "ExecutionData": {
        "dataType": "refObject",
        "properties": {
            "externalId": {"dataType":"string"},
            "caseRef": {"dataType":"string","required":true},
            "runRef": {"dataType":"string","required":true},
            "source": {"dataType":"string","required":true},
        },
        "additionalProperties": {"dataType":"any"},
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "CreateDataDto": {
        "dataType": "refObject",
        "properties": {
            "entities": {"dataType":"nestedObjectLiteral","nestedProperties":{"executions":{"dataType":"nestedObjectLiteral","nestedProperties":{"entries":{"dataType":"array","array":{"dataType":"refObject","ref":"ExecutionData"},"required":true}}},"runs":{"dataType":"nestedObjectLiteral","nestedProperties":{"entries":{"dataType":"array","array":{"dataType":"refObject","ref":"RunData"},"required":true}}},"folders":{"dataType":"nestedObjectLiteral","nestedProperties":{"entries":{"dataType":"array","array":{"dataType":"refObject","ref":"FolderData"},"required":true}}},"cases":{"dataType":"nestedObjectLiteral","nestedProperties":{"entries":{"dataType":"array","array":{"dataType":"refObject","ref":"CaseData"},"required":true}}}},"required":true},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "CustomFieldTypes": {
        "dataType": "refAlias",
        "type": {"dataType":"union","subSchemas":[{"dataType":"enum","enums":["text"]},{"dataType":"enum","enums":["radio"]},{"dataType":"enum","enums":["date"]},{"dataType":"enum","enums":["step"]},{"dataType":"enum","enums":["link"]},{"dataType":"enum","enums":["checkbox"]},{"dataType":"enum","enums":["dropdown"]},{"dataType":"enum","enums":["multi"]},{"dataType":"enum","enums":["file"]},{"dataType":"enum","enums":["integer"]}],"validators":{}},
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "CreateCustomFieldDto": {
        "dataType": "refObject",
        "properties": {
            "name": {"dataType":"string","required":true},
            "type": {"ref":"CustomFieldTypes"},
            "source": {"dataType":"string"},
            "options": {"dataType":"array","array":{"dataType":"string"}},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "UpdateCustomFieldDto": {
        "dataType": "refObject",
        "properties": {
            "name": {"dataType":"string"},
            "type": {"ref":"CustomFieldTypes"},
            "source": {"dataType":"string"},
            "options": {"dataType":"array","array":{"dataType":"string"}},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "CaseQueryParams": {
        "dataType": "refObject",
        "properties": {
            "limit": {"dataType":"double","required":true},
            "offset": {"dataType":"double","required":true},
            "q": {"dataType":"string"},
            "priority": {"dataType":"string"},
            "tag": {"dataType":"string"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
    "CreateCaseDTO": {
        "dataType": "refObject",
        "properties": {
            "customFields": {"dataType":"any","required":true},
            "source": {"dataType":"string","required":true},
            "name": {"dataType":"string","required":true},
            "priority": {"dataType":"double"},
            "repoUID": {"dataType":"string","required":true},
            "parentId": {"dataType":"double","required":true},
            "steps": {"dataType":"array","array":{"dataType":"any"},"required":true},
            "externalId": {"dataType":"string","required":true},
            "projectId": {"dataType":"string","required":true},
            "templateId": {"dataType":"string"},
            "tagIds": {"dataType":"array","array":{"dataType":"double"}},
            "tags": {"dataType":"array","array":{"dataType":"string"}},
            "externalCreatedAt": {"dataType":"string"},
            "externalUpdatedAt": {"dataType":"string"},
            "statusText": {"dataType":"string"},
            "priorityText": {"dataType":"string"},
        },
        "additionalProperties": false,
    },
    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
};
const templateService = new ExpressTemplateService(models, {"noImplicitAdditionalProperties":"throw-on-extras","bodyCoercion":true});

// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa




export function RegisterRoutes(app: Router) {

    // ###########################################################################################################
    //  NOTE: If you do not see routes for all of your controllers in this file, then you might not have informed tsoa of where to look
    //      Please look into the "controllerPathGlobs" config option described in the readme: https://github.com/lukeautry/tsoa
    // ###########################################################################################################


    
        const argsTemplateController_getTemplates: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                queryParams: {"in":"queries","name":"queryParams","ref":"TemplateQueryParams"},
        };
        app.get('/v1/:handle/projects/:key/templates',
            ...(fetchMiddlewares<RequestHandler>(TemplateController)),
            ...(fetchMiddlewares<RequestHandler>(TemplateController.prototype.getTemplates)),

            async function TemplateController_getTemplates(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsTemplateController_getTemplates, request, response });

                const controller = new TemplateController();

              await templateService.apiHandler({
                methodName: 'getTemplates',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsTemplateController_getTemplate: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"string"},
        };
        app.get('/v1/:handle/projects/:key/templates/:id',
            ...(fetchMiddlewares<RequestHandler>(TemplateController)),
            ...(fetchMiddlewares<RequestHandler>(TemplateController.prototype.getTemplate)),

            async function TemplateController_getTemplate(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsTemplateController_getTemplate, request, response });

                const controller = new TemplateController();

              await templateService.apiHandler({
                methodName: 'getTemplate',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsTemplateController_createTemplate: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                templateDto: {"in":"body","name":"templateDto","required":true,"ref":"CreateTemplateDto"},
        };
        app.post('/v1/:handle/projects/:key/templates',
            ...(fetchMiddlewares<RequestHandler>(TemplateController)),
            ...(fetchMiddlewares<RequestHandler>(TemplateController.prototype.createTemplate)),

            async function TemplateController_createTemplate(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsTemplateController_createTemplate, request, response });

                const controller = new TemplateController();

              await templateService.apiHandler({
                methodName: 'createTemplate',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsTemplateController_updateTemplate: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"double"},
                templateDto: {"in":"body","name":"templateDto","required":true,"ref":"UpdateTemplateDto"},
        };
        app.patch('/v1/:handle/projects/:key/templates/:id',
            ...(fetchMiddlewares<RequestHandler>(TemplateController)),
            ...(fetchMiddlewares<RequestHandler>(TemplateController.prototype.updateTemplate)),

            async function TemplateController_updateTemplate(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsTemplateController_updateTemplate, request, response });

                const controller = new TemplateController();

              await templateService.apiHandler({
                methodName: 'updateTemplate',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsTemplateController_deleteTemplate: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"double"},
        };
        app.delete('/v1/:handle/projects/:key/templates/:id',
            ...(fetchMiddlewares<RequestHandler>(TemplateController)),
            ...(fetchMiddlewares<RequestHandler>(TemplateController.prototype.deleteTemplate)),

            async function TemplateController_deleteTemplate(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsTemplateController_deleteTemplate, request, response });

                const controller = new TemplateController();

              await templateService.apiHandler({
                methodName: 'deleteTemplate',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsTagController_getTags: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                queryParams: {"in":"queries","name":"queryParams","required":true,"ref":"TagQueryParams"},
        };
        app.get('/v1/:handle/tags',
            ...(fetchMiddlewares<RequestHandler>(TagController)),
            ...(fetchMiddlewares<RequestHandler>(TagController.prototype.getTags)),

            async function TagController_getTags(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsTagController_getTags, request, response });

                const controller = new TagController();

              await templateService.apiHandler({
                methodName: 'getTags',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsTagController_getTag: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"double"},
        };
        app.get('/v1/:handle/tags/:id',
            ...(fetchMiddlewares<RequestHandler>(TagController)),
            ...(fetchMiddlewares<RequestHandler>(TagController.prototype.getTag)),

            async function TagController_getTag(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsTagController_getTag, request, response });

                const controller = new TagController();

              await templateService.apiHandler({
                methodName: 'getTag',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsTagController_createTag: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                tagDto: {"in":"body","name":"tagDto","required":true,"ref":"CreateTagDto"},
        };
        app.post('/v1/:handle/tags',
            ...(fetchMiddlewares<RequestHandler>(TagController)),
            ...(fetchMiddlewares<RequestHandler>(TagController.prototype.createTag)),

            async function TagController_createTag(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsTagController_createTag, request, response });

                const controller = new TagController();

              await templateService.apiHandler({
                methodName: 'createTag',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsTagController_updateTag: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"double"},
                tagDto: {"in":"body","name":"tagDto","required":true,"ref":"UpdateTagDto"},
        };
        app.patch('/v1/:handle/tags/:id',
            ...(fetchMiddlewares<RequestHandler>(TagController)),
            ...(fetchMiddlewares<RequestHandler>(TagController.prototype.updateTag)),

            async function TagController_updateTag(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsTagController_updateTag, request, response });

                const controller = new TagController();

              await templateService.apiHandler({
                methodName: 'updateTag',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsTagController_deleteTag: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"double"},
        };
        app.delete('/v1/:handle/tags/:id',
            ...(fetchMiddlewares<RequestHandler>(TagController)),
            ...(fetchMiddlewares<RequestHandler>(TagController.prototype.deleteTag)),

            async function TagController_deleteTag(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsTagController_deleteTag, request, response });

                const controller = new TagController();

              await templateService.apiHandler({
                methodName: 'deleteTag',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsSharedStepController_getSharedSteps: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                queryParams: {"in":"queries","name":"queryParams","required":true,"ref":"PaginatedQuery"},
        };
        app.get('/v1/:handle/projects/:key/shared-steps',
            ...(fetchMiddlewares<RequestHandler>(SharedStepController)),
            ...(fetchMiddlewares<RequestHandler>(SharedStepController.prototype.getSharedSteps)),

            async function SharedStepController_getSharedSteps(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsSharedStepController_getSharedSteps, request, response });

                const controller = new SharedStepController();

              await templateService.apiHandler({
                methodName: 'getSharedSteps',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsSharedStepController_getSharedStep: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"double"},
        };
        app.get('/v1/:handle/projects/:key/shared-steps/:id',
            ...(fetchMiddlewares<RequestHandler>(SharedStepController)),
            ...(fetchMiddlewares<RequestHandler>(SharedStepController.prototype.getSharedStep)),

            async function SharedStepController_getSharedStep(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsSharedStepController_getSharedStep, request, response });

                const controller = new SharedStepController();

              await templateService.apiHandler({
                methodName: 'getSharedStep',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsSharedStepController_createSharedStep: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                sharedStepDto: {"in":"body","name":"sharedStepDto","required":true,"ref":"CreateSharedStepDto"},
        };
        app.post('/v1/:handle/projects/:key/shared-steps',
            ...(fetchMiddlewares<RequestHandler>(SharedStepController)),
            ...(fetchMiddlewares<RequestHandler>(SharedStepController.prototype.createSharedStep)),

            async function SharedStepController_createSharedStep(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsSharedStepController_createSharedStep, request, response });

                const controller = new SharedStepController();

              await templateService.apiHandler({
                methodName: 'createSharedStep',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsSharedStepController_updateSharedStep: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"string"},
                sharedStepDto: {"in":"body","name":"sharedStepDto","required":true,"ref":"UpdateSharedStepDto"},
        };
        app.patch('/v1/:handle/projects/:key/shared-steps/:id',
            ...(fetchMiddlewares<RequestHandler>(SharedStepController)),
            ...(fetchMiddlewares<RequestHandler>(SharedStepController.prototype.updateSharedStep)),

            async function SharedStepController_updateSharedStep(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsSharedStepController_updateSharedStep, request, response });

                const controller = new SharedStepController();

              await templateService.apiHandler({
                methodName: 'updateSharedStep',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsSharedStepController_deleteSharedStep: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"double"},
        };
        app.delete('/v1/:handle/projects/:key/shared-steps/:id',
            ...(fetchMiddlewares<RequestHandler>(SharedStepController)),
            ...(fetchMiddlewares<RequestHandler>(SharedStepController.prototype.deleteSharedStep)),

            async function SharedStepController_deleteSharedStep(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsSharedStepController_deleteSharedStep, request, response });

                const controller = new SharedStepController();

              await templateService.apiHandler({
                methodName: 'deleteSharedStep',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsRunController_getRuns: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                queryParams: {"in":"queries","name":"queryParams","ref":"PaginatedQuery"},
        };
        app.get('/v1/:handle/projects/:key/runs',
            ...(fetchMiddlewares<RequestHandler>(RunController)),
            ...(fetchMiddlewares<RequestHandler>(RunController.prototype.getRuns)),

            async function RunController_getRuns(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsRunController_getRuns, request, response });

                const controller = new RunController();

              await templateService.apiHandler({
                methodName: 'getRuns',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsRunController_getRun: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"double"},
        };
        app.get('/v1/:handle/projects/:key/runs/:id',
            ...(fetchMiddlewares<RequestHandler>(RunController)),
            ...(fetchMiddlewares<RequestHandler>(RunController.prototype.getRun)),

            async function RunController_getRun(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsRunController_getRun, request, response });

                const controller = new RunController();

              await templateService.apiHandler({
                methodName: 'getRun',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsRunController_createTestRun: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                dto: {"in":"body","name":"dto","required":true,"ref":"CreateRunDTO"},
        };
        app.post('/v1/:handle/projects/:key/runs',
            ...(fetchMiddlewares<RequestHandler>(RunController)),
            ...(fetchMiddlewares<RequestHandler>(RunController.prototype.createTestRun)),

            async function RunController_createTestRun(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsRunController_createTestRun, request, response });

                const controller = new RunController();

              await templateService.apiHandler({
                methodName: 'createTestRun',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsRunController_updateTestRun: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"double"},
                testRunDto: {"in":"body","name":"testRunDto","required":true,"ref":"UpdateRunDTO"},
        };
        app.patch('/v1/:handle/projects/:key/runs/:id',
            ...(fetchMiddlewares<RequestHandler>(RunController)),
            ...(fetchMiddlewares<RequestHandler>(RunController.prototype.updateTestRun)),

            async function RunController_updateTestRun(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsRunController_updateTestRun, request, response });

                const controller = new RunController();

              await templateService.apiHandler({
                methodName: 'updateTestRun',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsRunController_deleteTestRun: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"double"},
        };
        app.delete('/v1/:handle/projects/:key/runs/:id',
            ...(fetchMiddlewares<RequestHandler>(RunController)),
            ...(fetchMiddlewares<RequestHandler>(RunController.prototype.deleteTestRun)),

            async function RunController_deleteTestRun(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsRunController_deleteTestRun, request, response });

                const controller = new RunController();

              await templateService.apiHandler({
                methodName: 'deleteTestRun',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsProjectController_getProjects: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                queryParams: {"in":"queries","name":"queryParams","required":true,"ref":"PaginatedQuery"},
        };
        app.get('/v1/:handle/projects',
            ...(fetchMiddlewares<RequestHandler>(ProjectController)),
            ...(fetchMiddlewares<RequestHandler>(ProjectController.prototype.getProjects)),

            async function ProjectController_getProjects(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsProjectController_getProjects, request, response });

                const controller = new ProjectController();

              await templateService.apiHandler({
                methodName: 'getProjects',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsProjectController_createProject: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectDto: {"in":"body","name":"projectDto","required":true,"ref":"CreateProjectDto"},
        };
        app.post('/v1/:handle/projects',
            ...(fetchMiddlewares<RequestHandler>(ProjectController)),
            ...(fetchMiddlewares<RequestHandler>(ProjectController.prototype.createProject)),

            async function ProjectController_createProject(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsProjectController_createProject, request, response });

                const controller = new ProjectController();

              await templateService.apiHandler({
                methodName: 'createProject',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsPlanController_getPlans: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                queryParams: {"in":"queries","name":"queryParams","required":true,"ref":"ListPlanQueryParams"},
        };
        app.get('/v1/:handle/projects/:key/plans',
            ...(fetchMiddlewares<RequestHandler>(PlanController)),
            ...(fetchMiddlewares<RequestHandler>(PlanController.prototype.getPlans)),

            async function PlanController_getPlans(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsPlanController_getPlans, request, response });

                const controller = new PlanController();

              await templateService.apiHandler({
                methodName: 'getPlans',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsPlanController_getPlan: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"string"},
        };
        app.get('/v1/:handle/projects/:key/plans/:id',
            ...(fetchMiddlewares<RequestHandler>(PlanController)),
            ...(fetchMiddlewares<RequestHandler>(PlanController.prototype.getPlan)),

            async function PlanController_getPlan(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsPlanController_getPlan, request, response });

                const controller = new PlanController();

              await templateService.apiHandler({
                methodName: 'getPlan',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsPlanController_createPlan: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                planDto: {"in":"body","name":"planDto","required":true,"ref":"CreatePlanDTO"},
        };
        app.post('/v1/:handle/projects/:key/plans',
            ...(fetchMiddlewares<RequestHandler>(PlanController)),
            ...(fetchMiddlewares<RequestHandler>(PlanController.prototype.createPlan)),

            async function PlanController_createPlan(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsPlanController_createPlan, request, response });

                const controller = new PlanController();

              await templateService.apiHandler({
                methodName: 'createPlan',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsPlanController_updatePlan: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"double"},
                planDto: {"in":"body","name":"planDto","required":true,"ref":"UpdatePlanDTO"},
        };
        app.patch('/v1/:handle/projects/:key/plans/:id',
            ...(fetchMiddlewares<RequestHandler>(PlanController)),
            ...(fetchMiddlewares<RequestHandler>(PlanController.prototype.updatePlan)),

            async function PlanController_updatePlan(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsPlanController_updatePlan, request, response });

                const controller = new PlanController();

              await templateService.apiHandler({
                methodName: 'updatePlan',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsMilestoneController_getMilestones: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                queryParams: {"in":"queries","name":"queryParams","ref":"PaginatedQuery"},
        };
        app.get('/v1/:handle/projects/:key/milestones',
            ...(fetchMiddlewares<RequestHandler>(MilestoneController)),
            ...(fetchMiddlewares<RequestHandler>(MilestoneController.prototype.getMilestones)),

            async function MilestoneController_getMilestones(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsMilestoneController_getMilestones, request, response });

                const controller = new MilestoneController();

              await templateService.apiHandler({
                methodName: 'getMilestones',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsMilestoneController_getMilestone: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"double"},
        };
        app.get('/v1/:handle/projects/:key/milestones/:id',
            ...(fetchMiddlewares<RequestHandler>(MilestoneController)),
            ...(fetchMiddlewares<RequestHandler>(MilestoneController.prototype.getMilestone)),

            async function MilestoneController_getMilestone(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsMilestoneController_getMilestone, request, response });

                const controller = new MilestoneController();

              await templateService.apiHandler({
                methodName: 'getMilestone',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsMilestoneController_createMilestone: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                milestoneDto: {"in":"body","name":"milestoneDto","required":true,"ref":"CreateMilestoneDTO"},
        };
        app.post('/v1/:handle/projects/:key/milestones',
            ...(fetchMiddlewares<RequestHandler>(MilestoneController)),
            ...(fetchMiddlewares<RequestHandler>(MilestoneController.prototype.createMilestone)),

            async function MilestoneController_createMilestone(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsMilestoneController_createMilestone, request, response });

                const controller = new MilestoneController();

              await templateService.apiHandler({
                methodName: 'createMilestone',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsMilestoneController_updateMilestone: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"double"},
                milestoneDto: {"in":"body","name":"milestoneDto","required":true,"ref":"UpdateMilestoneDTO"},
        };
        app.patch('/v1/:handle/projects/:key/milestones/:id',
            ...(fetchMiddlewares<RequestHandler>(MilestoneController)),
            ...(fetchMiddlewares<RequestHandler>(MilestoneController.prototype.updateMilestone)),

            async function MilestoneController_updateMilestone(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsMilestoneController_updateMilestone, request, response });

                const controller = new MilestoneController();

              await templateService.apiHandler({
                methodName: 'updateMilestone',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsMilestoneController_deleteMilestone: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"double"},
        };
        app.delete('/v1/:handle/projects/:key/milestones/:id',
            ...(fetchMiddlewares<RequestHandler>(MilestoneController)),
            ...(fetchMiddlewares<RequestHandler>(MilestoneController.prototype.deleteMilestone)),

            async function MilestoneController_deleteMilestone(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsMilestoneController_deleteMilestone, request, response });

                const controller = new MilestoneController();

              await templateService.apiHandler({
                methodName: 'deleteMilestone',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsFolderController_getFolders: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                queryParams: {"in":"queries","name":"queryParams","required":true,"ref":"PaginatedQuery"},
        };
        app.get('/v1/:handle/projects/:key/folders',
            ...(fetchMiddlewares<RequestHandler>(FolderController)),
            ...(fetchMiddlewares<RequestHandler>(FolderController.prototype.getFolders)),

            async function FolderController_getFolders(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsFolderController_getFolders, request, response });

                const controller = new FolderController();

              await templateService.apiHandler({
                methodName: 'getFolders',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsFolderController_getFolder: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"double"},
        };
        app.get('/v1/:handle/projects/:key/folders/:id',
            ...(fetchMiddlewares<RequestHandler>(FolderController)),
            ...(fetchMiddlewares<RequestHandler>(FolderController.prototype.getFolder)),

            async function FolderController_getFolder(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsFolderController_getFolder, request, response });

                const controller = new FolderController();

              await templateService.apiHandler({
                methodName: 'getFolder',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsFolderController_createFolder: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                folderDto: {"in":"body","name":"folderDto","required":true,"ref":"CreateFolderDto"},
        };
        app.post('/v1/:handle/projects/:key/folders',
            ...(fetchMiddlewares<RequestHandler>(FolderController)),
            ...(fetchMiddlewares<RequestHandler>(FolderController.prototype.createFolder)),

            async function FolderController_createFolder(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsFolderController_createFolder, request, response });

                const controller = new FolderController();

              await templateService.apiHandler({
                methodName: 'createFolder',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsFolderController_updateFolder: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"double"},
                folderDto: {"in":"body","name":"folderDto","required":true,"ref":"UpdateFolderDto"},
        };
        app.patch('/v1/:handle/projects/:key/folders/:id',
            ...(fetchMiddlewares<RequestHandler>(FolderController)),
            ...(fetchMiddlewares<RequestHandler>(FolderController.prototype.updateFolder)),

            async function FolderController_updateFolder(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsFolderController_updateFolder, request, response });

                const controller = new FolderController();

              await templateService.apiHandler({
                methodName: 'updateFolder',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsFolderController_deleteFolder: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"double"},
        };
        app.delete('/v1/:handle/projects/:key/folders/:id',
            ...(fetchMiddlewares<RequestHandler>(FolderController)),
            ...(fetchMiddlewares<RequestHandler>(FolderController.prototype.deleteFolder)),

            async function FolderController_deleteFolder(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsFolderController_deleteFolder, request, response });

                const controller = new FolderController();

              await templateService.apiHandler({
                methodName: 'deleteFolder',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsExecutionController_getExecutions: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                queryParams: {"in":"queries","name":"queryParams","required":true,"ref":"PaginatedQuery"},
        };
        app.get('/v1/:handle/projects/:key/executions',
            ...(fetchMiddlewares<RequestHandler>(ExecutionController)),
            ...(fetchMiddlewares<RequestHandler>(ExecutionController.prototype.getExecutions)),

            async function ExecutionController_getExecutions(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsExecutionController_getExecutions, request, response });

                const controller = new ExecutionController();

              await templateService.apiHandler({
                methodName: 'getExecutions',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsExecutionController_getExecution: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"double"},
        };
        app.get('/v1/:handle/projects/:key/executions/:id',
            ...(fetchMiddlewares<RequestHandler>(ExecutionController)),
            ...(fetchMiddlewares<RequestHandler>(ExecutionController.prototype.getExecution)),

            async function ExecutionController_getExecution(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsExecutionController_getExecution, request, response });

                const controller = new ExecutionController();

              await templateService.apiHandler({
                methodName: 'getExecution',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsExecutionController_createExecution: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                executionDto: {"in":"body","name":"executionDto","required":true,"ref":"CreateExecutionDTO"},
        };
        app.post('/v1/:handle/projects/:key/executions',
            ...(fetchMiddlewares<RequestHandler>(ExecutionController)),
            ...(fetchMiddlewares<RequestHandler>(ExecutionController.prototype.createExecution)),

            async function ExecutionController_createExecution(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsExecutionController_createExecution, request, response });

                const controller = new ExecutionController();

              await templateService.apiHandler({
                methodName: 'createExecution',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsExecutionController_updateExecution: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"double"},
                executionDto: {"in":"body","name":"executionDto","required":true,"ref":"UpdateExecutionDTO"},
        };
        app.patch('/v1/:handle/projects/:key/executions/:id',
            ...(fetchMiddlewares<RequestHandler>(ExecutionController)),
            ...(fetchMiddlewares<RequestHandler>(ExecutionController.prototype.updateExecution)),

            async function ExecutionController_updateExecution(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsExecutionController_updateExecution, request, response });

                const controller = new ExecutionController();

              await templateService.apiHandler({
                methodName: 'updateExecution',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsDataController_newData: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                requestBody: {"in":"body","name":"requestBody","required":true,"ref":"CreateDataDto"},
        };
        app.post('/v1/:handle/projects/:key/data',
            ...(fetchMiddlewares<RequestHandler>(DataController)),
            ...(fetchMiddlewares<RequestHandler>(DataController.prototype.newData)),

            async function DataController_newData(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsDataController_newData, request, response });

                const controller = new DataController();

              await templateService.apiHandler({
                methodName: 'newData',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsCustomFieldController_getCustomFields: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                queryParams: {"in":"queries","name":"queryParams","required":true,"ref":"PaginatedQuery"},
        };
        app.get('/v1/:handle/projects/:key/customFields',
            ...(fetchMiddlewares<RequestHandler>(CustomFieldController)),
            ...(fetchMiddlewares<RequestHandler>(CustomFieldController.prototype.getCustomFields)),

            async function CustomFieldController_getCustomFields(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsCustomFieldController_getCustomFields, request, response });

                const controller = new CustomFieldController();

              await templateService.apiHandler({
                methodName: 'getCustomFields',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsCustomFieldController_getCustomField: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"string"},
        };
        app.get('/v1/:handle/projects/:key/customFields/:id',
            ...(fetchMiddlewares<RequestHandler>(CustomFieldController)),
            ...(fetchMiddlewares<RequestHandler>(CustomFieldController.prototype.getCustomField)),

            async function CustomFieldController_getCustomField(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsCustomFieldController_getCustomField, request, response });

                const controller = new CustomFieldController();

              await templateService.apiHandler({
                methodName: 'getCustomField',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsCustomFieldController_createCustomField: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                customFieldDto: {"in":"body","name":"customFieldDto","required":true,"ref":"CreateCustomFieldDto"},
        };
        app.post('/v1/:handle/projects/:key/customFields',
            ...(fetchMiddlewares<RequestHandler>(CustomFieldController)),
            ...(fetchMiddlewares<RequestHandler>(CustomFieldController.prototype.createCustomField)),

            async function CustomFieldController_createCustomField(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsCustomFieldController_createCustomField, request, response });

                const controller = new CustomFieldController();

              await templateService.apiHandler({
                methodName: 'createCustomField',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsCustomFieldController_updateCustomField: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"string"},
                customFieldDto: {"in":"body","name":"customFieldDto","required":true,"ref":"UpdateCustomFieldDto"},
        };
        app.patch('/v1/:handle/projects/:key/customFields/:id',
            ...(fetchMiddlewares<RequestHandler>(CustomFieldController)),
            ...(fetchMiddlewares<RequestHandler>(CustomFieldController.prototype.updateCustomField)),

            async function CustomFieldController_updateCustomField(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsCustomFieldController_updateCustomField, request, response });

                const controller = new CustomFieldController();

              await templateService.apiHandler({
                methodName: 'updateCustomField',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsCustomFieldController_deleteCustomField: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"string"},
        };
        app.delete('/v1/:handle/projects/:key/customFields/:id',
            ...(fetchMiddlewares<RequestHandler>(CustomFieldController)),
            ...(fetchMiddlewares<RequestHandler>(CustomFieldController.prototype.deleteCustomField)),

            async function CustomFieldController_deleteCustomField(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsCustomFieldController_deleteCustomField, request, response });

                const controller = new CustomFieldController();

              await templateService.apiHandler({
                methodName: 'deleteCustomField',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsCaseController_getCases: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                queryParams: {"in":"queries","name":"queryParams","required":true,"ref":"CaseQueryParams"},
        };
        app.get('/v1/:handle/projects/:key/cases',
            ...(fetchMiddlewares<RequestHandler>(CaseController)),
            ...(fetchMiddlewares<RequestHandler>(CaseController.prototype.getCases)),

            async function CaseController_getCases(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsCaseController_getCases, request, response });

                const controller = new CaseController();

              await templateService.apiHandler({
                methodName: 'getCases',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsCaseController_getCase: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"string"},
        };
        app.get('/v1/:handle/projects/:key/cases/:id',
            ...(fetchMiddlewares<RequestHandler>(CaseController)),
            ...(fetchMiddlewares<RequestHandler>(CaseController.prototype.getCase)),

            async function CaseController_getCase(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsCaseController_getCase, request, response });

                const controller = new CaseController();

              await templateService.apiHandler({
                methodName: 'getCase',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsCaseController_createCases: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                casesDto: {"in":"body","name":"casesDto","required":true,"dataType":"array","array":{"dataType":"refObject","ref":"CreateCaseDTO"}},
        };
        app.post('/v1/:handle/projects/:key/cases',
            ...(fetchMiddlewares<RequestHandler>(CaseController)),
            ...(fetchMiddlewares<RequestHandler>(CaseController.prototype.createCases)),

            async function CaseController_createCases(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsCaseController_createCases, request, response });

                const controller = new CaseController();

              await templateService.apiHandler({
                methodName: 'createCases',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
        const argsCaseController_deleteCase: Record<string, TsoaRoute.ParameterSchema> = {
                req: {"in":"request","name":"req","required":true,"dataType":"object"},
                handle: {"in":"path","name":"handle","required":true,"dataType":"string"},
                projectKey: {"in":"path","name":"key","required":true,"dataType":"string"},
                id: {"in":"path","name":"id","required":true,"dataType":"string"},
        };
        app.delete('/v1/:handle/projects/:key/cases/:id',
            ...(fetchMiddlewares<RequestHandler>(CaseController)),
            ...(fetchMiddlewares<RequestHandler>(CaseController.prototype.deleteCase)),

            async function CaseController_deleteCase(request: ExRequest, response: ExResponse, next: any) {

            // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

            let validatedArgs: any[] = [];
            try {
                validatedArgs = templateService.getValidatedArgs({ args: argsCaseController_deleteCase, request, response });

                const controller = new CaseController();

              await templateService.apiHandler({
                methodName: 'deleteCase',
                controller,
                response,
                next,
                validatedArgs,
                successStatus: undefined,
              });
            } catch (err) {
                return next(err);
            }
        });
        // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa

    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa


    // WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
}

// WARNING: This file was auto-generated with tsoa. Please do not modify it. Re-run tsoa to re-generate this file: https://github.com/lukeautry/tsoa
