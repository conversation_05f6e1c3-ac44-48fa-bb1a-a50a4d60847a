import * as userValidator from '@app/validators/user';

import express, { Router } from 'express';

import appController from '@app/controllers/app';
import { auth } from '@ss-libs/ss-component-auth';

const router: Router = express.Router();

// New anonymous token for PINATA user
router.get('/app/profile/token', appController.signUpToken);

// User signup via profile info
router.patch(
  '/app/profile',
  auth().authenticateBearerWithPassthrough,
  userValidator.validateUserSignUp,
  appController.updateProfile,
);

// User signup via profile info
router.post(
  '/app/profile/auth',
  auth().authenticateBearerWithPassthrough,
  userValidator.validateUserSignIn,
  appController.signIn,
);

router.get(
  '/app/oauth/jira/token/:tokenId',
  auth().authenticateBearerWithPassthrough,
  appController.jiraOauthToken,
);

router.get('/app/oauth/github', appController.githubOauthResponse);

router.get(
  '/app/oauth/github/token/:tokenId/new',
  appController.githubGetNewToken,
);

// TODO - auth().authenticateBearerWithPassthrough ?
router.get('/app/oauth/github/token/:tokenId', appController.githubOauthToken);

// CTODO - add validation and authz to all
router.get(
  '/app/org/:orgId/config',
  auth().authenticateBearerWithPassthrough,
  appController.getAllConfigs,
);
// CTODO - is this structure right for the URL?
router.get(
  '/app/org/:orgId/config/:configId',
  auth().authenticateBearerWithPassthrough,
  appController.getConfig,
);
router.post(
  '/app/org/:orgId/config/:configId',
  auth().authenticateBearerWithPassthrough,
  appController.newConfig,
);
router.put(
  '/app/org/:orgId/config/:configId',
  auth().authenticateBearerWithPassthrough,
  appController.upsertConfig,
);
router.delete(
  '/app/org/:orgId/config/:configId',
  auth().authenticateBearerWithPassthrough,
  appController.deleteConfig,
);

router.get(
  '/app/credentials',
  auth().authenticateBearerWithPassthrough,
  appController.getAllCredentials,
);
export default router;
