import { auth } from '@ss-libs/ss-component-auth';
import express, { Router } from 'express';
import caseController from '@app/controllers/trv2/case';
import { setResource } from '@app/middlewares/handle';
import { entityRouter } from '@app/middlewares/trv2';
import { permissions } from '@app/constants/auth';
import {
  validateCreateCase, validateUpdateCase, validateCopyCasesToSection, validateUpdateCases,
  validateMoveCasesToSection, validateGetCases,
} from '@app/validators/trv2/case';

const caseRoutesMap = {
  get_cases: {
    controller: caseController.getCases,
    middlewares: [auth().authz(permissions.read_entity), validateGetCases],
    method: 'GET',
  },
  get_case: {
    controller: caseController.getCase,
    middlewares: [auth().authz(permissions.read_entity)],
    method: 'GET',
  },
  get_history_for_case: {
    controller: caseController.getCaseHistory,
    middlewares: [auth().authz(permissions.read_entity)],
    method: 'GET',
  },
  add_case: {
    controller: caseController.createCase,
    middlewares: [auth().authz(permissions.write_entity), validateCreateCase],
    method: 'POST',
  },
  copy_cases_to_section: {
    controller: caseController.copyCasesToSection,
    middlewares: [auth().authz(permissions.write_entity), validateCopyCasesToSection],
    method: 'POST',
  },
  update_case: {
    controller: caseController.updateTestCase,
    middlewares: [auth().authz(permissions.write_entity), validateUpdateCase],
    method: 'POST',
  },
  update_cases: {
    controller: caseController.updateTestCases,
    middlewares: [auth().authz(permissions.write_entity), validateUpdateCases],
    method: 'POST',
  },
  move_cases_to_section: {
    controller: caseController.moveCasesToSection,
    middlewares: [auth().authz(permissions.write_entity), validateMoveCasesToSection],
    method: 'POST',
  },
  delete_case: {
    controller: caseController.deleteCase,
    middlewares: [auth().authz(permissions.delete_entity)],
    method: 'POST',
  },
  delete_cases: {
    controller: caseController.deleteCases,
    middlewares: [auth().authz(permissions.delete_entity)],
    method: 'POST',
  },
};

const router: Router = express.Router({ mergeParams: true });

/*
  router to handle project related queries
  GET: get_cases: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_cases
  GET: get_case: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_case/{case_id}
  POST: add_case: {Backend URL}/trv2/{handle}/index.php?/api/v2/add_case
  POST: update_case: {Backend URL}/trv2/{handle}/index.php?/api/v2/update_case/{case_id}
  POST: delete_case: {Backend URL}/trv2/{handle}/index.php?/api/v2/delete_case/{case_id}
*/

router.use(
  setResource('case'),
  entityRouter(caseRoutesMap),
);

export default router;
