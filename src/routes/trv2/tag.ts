import express, { Router } from 'express';
import { setResource } from '@app/middlewares/handle';
import { entityRouter } from '@app/middlewares/trv2';
import tagController from '@app/controllers/trv2/tag';

const tagRoutesMap = {
  get_case_types: {
    controller: tagController.getCaseTypes,
    middlewares: [],
    method: 'GET',
  },
};

const router: Router = express.Router({ mergeParams: true });

/*
  router to handle project related queries
  GET: get_case_types: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_case_types
*/

router.use(
  setResource('tag'),
  entityRouter(tagRoutesMap),
);

export default router;
