import express, { Router } from 'express';
import { entityRouter } from '@app/middlewares/trv2';
import prefrencesController from '@app/controllers/trv2/prefrences';

const prefrencesRoutesMap = {
  get_statuses: {
    controller: prefrencesController.getStatuses,
    middlewares: [],
    method: 'GET',
  },
  get_priorities: {
    controller: prefrencesController.getPriorities,
    middlewares: [],
    method: 'GET',
  },
};

const router: Router = express.Router({ mergeParams: true });

/*
  router to handle project related queries
  GET: get_case_types: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_case_types
*/

router.use(
  entityRouter(prefrencesRoutesMap),
);

export default router;
