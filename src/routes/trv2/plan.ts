import { auth } from '@ss-libs/ss-component-auth';
import express, { Router } from 'express';
import { setResource } from '@app/middlewares/handle';
import { entityRouter } from '@app/middlewares/trv2';
import { permissions } from '@app/constants/auth';
import planController from '@app/controllers/trv2/plan';
import {
  validateAddPlanEntry, validateAddRunToPlanEntry, validateCreatePlan, validateGetPlans,
  validateUpdatePlan,
  validateUpdatePlanEntry,
  validateUpdateRunInPlanEntry,
} from '@app/validators/trv2/plan';

const planRoutesMap = {
  get_plans: {
    controller: planController.getPlans,
    middlewares: [auth().authz(permissions.read_entity), validateGetPlans],
    method: 'GET',
  },
  get_plan: {
    controller: planController.getPlan,
    middlewares: [auth().authz(permissions.read_entity)],
    method: 'GET',
  },
  add_plan: {
    controller: planController.createPlan,
    middlewares: [auth().authz(permissions.write_entity), validateCreatePlan],
    method: 'POST',
  },
  add_plan_entry: {
    controller: planController.addPlanEntry,
    middlewares: [auth().authz(permissions.write_entity), validateAddPlanEntry],
    method: 'POST',
  },
  add_run_to_plan_entry: {
    controller: planController.addRunToPlanEntry,
    middlewares: [auth().authz(permissions.write_entity), validateAddRunToPlanEntry],
    method: 'POST',
  },
  update_plan: {
    controller: planController.updatePlan,
    middlewares: [auth().authz(permissions.write_entity), validateUpdatePlan],
    method: 'POST',
  },
  update_plan_entry: {
    controller: planController.updatePlanEntry,
    middlewares: [auth().authz(permissions.write_entity), validateUpdatePlanEntry],
    method: 'POST',
  },
  update_run_in_plan_entry: {
    controller: planController.updateRunInPlanEntry,
    middlewares: [auth().authz(permissions.write_entity), validateUpdateRunInPlanEntry],
    method: 'POST',
  },
  close_plan: {
    controller: planController.closePlan,
    middlewares: [auth().authz(permissions.write_entity)],
    method: 'POST',
  },
  delete_plan: {
    controller: planController.deletePlan,
    middlewares: [auth().authz(permissions.delete_entity)],
    method: 'POST',
  },
  delete_plan_entry: {
    controller: planController.deletePlanEntry,
    middlewares: [auth().authz(permissions.delete_entity)],
    method: 'POST',
  },
  delete_run_from_plan_entry: {
    controller: planController.deleteRunFromPlanEntry,
    middlewares: [auth().authz(permissions.delete_entity)],
    method: 'POST',
  },
};

const router: Router = express.Router({ mergeParams: true });

/*
  router to handle run related queries
  GET: get_plans: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_plans/{project_id}
  GET: get_plan: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_plan/{plan_id}
  POST: add_plan: {Backend URL}/trv2/{handle}/index.php?/api/v2/add_plan
  POST: update_plan: {Backend URL}/trv2/{handle}/index.php?/api/v2/update_plan/{plan_id}
  POST: close_plan: {Backend URL}/trv2/{handle}/index.php?/api/v2/close_plan/{plan_id}
  POST: delete_plan: {Backend URL}/trv2/{handle}/index.php?/api/v2/delete_plan/{plan_id}

  */

router.use(
  setResource('plan'),
  entityRouter(planRoutesMap),
);

export default router;
