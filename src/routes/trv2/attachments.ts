import express, { Router } from 'express';
import attachmentsController from '@app/controllers/trv2/attachments';
import { setResource } from '@app/middlewares/handle';
import { entityRouter } from '@app/middlewares/trv2';
import { validateGetAttachments } from '@app/validators/trv2/attachments';

const attachmentsRoutesMap = {
  get_attachments_for_case: {
    controller: attachmentsController.getAttachmentsForCase,
    middlewares: [validateGetAttachments],
    method: 'GET',
  },
  get_attachments_for_plan: {
    controller: attachmentsController.getAttachmentsForPlan,
    middlewares: [validateGetAttachments],
    method: 'GET',
  },
  get_attachments_for_plan_entry: {
    controller: attachmentsController.getAttachmentsForPlanEntry,
    middlewares: [validateGetAttachments],
    method: 'GET',
  },
  get_attachments_for_run: {
    controller: attachmentsController.getAttachmentsForRun,
    middlewares: [validateGetAttachments],
    method: 'GET',
  },
  get_attachments_for_test: {
    controller: attachmentsController.getAttachmentsForTest,
    middlewares: [validateGetAttachments],
    method: 'GET',
  },
  get_attachment: {
    controller: attachmentsController.getAttachment,
    middlewares: [validateGetAttachments],
    method: 'GET',
  },
  delete_attachment: {
    controller: attachmentsController.deleteAttachment,
    middlewares: [validateGetAttachments],
    method: 'POST',
  },
  add_attachment_to_case: {
    controller: attachmentsController.addCaseAttachment,
    middlewares: [],
    method: 'POST',
  },
  add_attachment_to_plan: {
    controller: attachmentsController.addPlanAttachment,
    middlewares: [],
    method: 'POST',
  },
  add_attachment_to_plan_entry: {
    controller: attachmentsController.addPlanEntryAttachment,
    middlewares: [],
    method: 'POST',
  },
  add_attachment_to_run: {
    controller: attachmentsController.addRunAttachment,
    middlewares: [],
    method: 'POST',
  },
  add_attachment_to_result: {
    controller: attachmentsController.addResultAttachment,
    middlewares: [],
    method: 'POST',
  },
};

const router: Router = express.Router({ mergeParams: true });

/*
  router to handle project related queries
  GET: get_attachments_for_case: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_attachments_for_case/{case_id}
  GET: get_attachments_for_plan: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_attachments_for_plan/{plan_id}
  GET: get_attachments_for_plan_entry: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_attachments_for_plan_entry/{plan_id}/{plan_entry_id}
  GET: get_attachments_for_run: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_attachments_for_run/{run_id}
  GET: get_attachments_for_test: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_attachments_for_test/{test_id}
  GET: get_attachment: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_attachment/{attachment_id}
  POST: delete_attachment: {Backend URL}/trv2/{handle}/index.php?/api/v2/delete_attachment/{attachment_id}
*/

router.use(
  setResource('attachments'),
  entityRouter(attachmentsRoutesMap),
);

export default router;
