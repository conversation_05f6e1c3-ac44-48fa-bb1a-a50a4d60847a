import { auth } from '@ss-libs/ss-component-auth';
import express, { Router } from 'express';
import { setResource } from '@app/middlewares/handle';
import { entityRouter } from '@app/middlewares/trv2';
import { permissions } from '@app/constants/auth';
import milestoneController from '@app/controllers/trv2/milestone';
import { validateCreateMilestone, validateGetMilestones, validateUpdateMilestone } from '@app/validators/trv2/milestone';

const milestoneRoutesMap = {
  get_milestones: {
    controller: milestoneController.getMilestones,
    middlewares: [auth().authz(permissions.read_entity), validateGetMilestones],
    method: 'GET',
  },
  get_milestone: {
    controller: milestoneController.getMilestone,
    middlewares: [auth().authz(permissions.read_entity)],
    method: 'GET',
  },
  add_milestone: {
    controller: milestoneController.createMilestone,
    middlewares: [auth().authz(permissions.write_entity), validateCreateMilestone],
    method: 'POST',
  },
  update_milestone: {
    controller: milestoneController.updateMilestone,
    middlewares: [auth().authz(permissions.write_entity), validateUpdateMilestone],
    method: 'POST',
  },
  delete_milestone: {
    controller: milestoneController.deleteMilestone,
    middlewares: [auth().authz(permissions.delete_entity)],
    method: 'POST',
  },
};

const router: Router = express.Router({ mergeParams: true });

/*
  router to handle project related queries
  GET: get_milestones: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_milestones/{project_id}
  GET: get_milestone: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_milestone/{milestone_id}
  POST: add_milestone: {Backend URL}/trv2/{handle}/index.php?/api/v2/add_milestone/{project_id}
  POST: update_milestone: {Backend URL}/trv2/{handle}/index.php?/api/v2/update_milestone/{milestone_id}
  POST: delete_milestone: {Backend URL}/trv2/{handle}/index.php?/api/v2/delete_milestone/{milestone_id}
*/

router.use(
  setResource('milestone'),
  entityRouter(milestoneRoutesMap),
);

export default router;
