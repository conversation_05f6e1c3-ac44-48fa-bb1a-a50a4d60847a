import { auth } from '@ss-libs/ss-component-auth';
import express, { Router } from 'express';
import folderController from '@app/controllers/trv2/folder';
import { setResource } from '@app/middlewares/handle';
import { entityRouter } from '@app/middlewares/trv2';
import { permissions } from '@app/constants/auth';
import {
  validateCreateSection, validateGetSections, validateMoveSection, validateUpdateSection,
} from '@app/validators/trv2/folder';

const folderRoutesMap = {
  get_suites: {
    controller: folderController.getProjectSuites,
    middlewares: [auth().authz(permissions.read_entity)],
    method: 'GET',
  },
  get_suite: {
    controller: folderController.getSuite,
    middlewares: [auth().authz(permissions.read_entity)],
    method: 'GET',
  },
  add_suite: {
    controller: folderController.addSuite,
    middlewares: [auth().authz(permissions.write_entity)],
    method: 'POST',
  },
  update_suite: {
    controller: folderController.updateSuite,
    middlewares: [auth().authz(permissions.write_entity)],
    method: 'POST',
  },
  delete_suite: {
    controller: folderController.deleteSuite,
    middlewares: [auth().authz(permissions.write_entity)],
    method: 'POST',
  },
  get_sections: {
    controller: folderController.getSections,
    middlewares: [auth().authz(permissions.read_entity), validateGetSections],
    method: 'GET',
  },
  get_section: {
    controller: folderController.getSection,
    middlewares: [auth().authz(permissions.read_entity)],
    method: 'GET',
  },
  add_section: {
    controller: folderController.addSection,
    middlewares: [auth().authz(permissions.write_entity), validateCreateSection],
    method: 'POST',
  },
  move_section: {
    controller: folderController.moveSection,
    middlewares: [auth().authz(permissions.write_entity), validateMoveSection],
    method: 'POST',
  },
  update_section: {
    controller: folderController.updateSection,
    middlewares: [auth().authz(permissions.write_entity), validateUpdateSection],
    method: 'POST',
  },
  delete_section: {
    controller: folderController.deleteSection,
    middlewares: [auth().authz(permissions.delete_entity)],
    method: 'POST',
  },
};

const router: Router = express.Router({ mergeParams: true });

/*
  router to handle folder related queries
  GET: get_suites: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_suites/{project_id}
  GET: get_suite: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_suite/{suite_id}
  POST: add_suite: {Backend URL}/trv2/{handle}/index.php?/api/v2/add_suite
  POST: update_suite: {Backend URL}/trv2/{handle}/index.php?/api/v2/update_suite/{suite_id}
  POST: delete_suite: {Backend URL}/trv2/{handle}/index.php?/api/v2/delete_suite/{suite_id}

  GET: get_sections: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_sections/{project_id}
  GET: get_section: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_section/{section_id}
  POST: add_section: {Backend URL}/trv2/{handle}/index.php?/api/v2/add_section
  POST: update_section: {Backend URL}/trv2/{handle}/index.php?/api/v2/update_section/{section_id}
  POST: delete_section: {Backend URL}/trv2/{handle}/index.php?/api/v2/delete_section/{section_id}
  */

router.use(
  setResource('folder'),
  entityRouter(folderRoutesMap),
);

export default router;
