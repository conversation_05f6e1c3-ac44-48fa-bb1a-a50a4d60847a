import express, { Router } from 'express';
import { setResource } from '@app/middlewares/handle';
import { entityRouter } from '@app/middlewares/trv2';
import configurationController from '@app/controllers/trv2/configuration';
import {
  validateAddConfigGroup, validateAddConfigOption, validateUpdateConfigGroup, validateUpdateConfigOption,
} from '@app/validators/trv2/configuration';
import { permissions } from '@app/constants/auth';
import { auth } from '@ss-libs/ss-component-auth';

const configurationRoutesMap = {
  get_configs: {
    controller: configurationController.getConfigurations,
    middlewares: [],
    method: 'GET',
  },
  add_config_group: {
    controller: configurationController.addConfigGroup,
    middlewares: [validateAddConfigGroup, auth().authz(permissions.write_tag)],
    method: 'POST',
  },
  add_config: {
    controller: configurationController.addConfigOption,
    middlewares: [validateAddConfigOption, auth().authz(permissions.write_tag)],
    method: 'POST',
  },
  update_config_group: {
    controller: configurationController.updateConfigGroup,
    middlewares: [validateUpdateConfigGroup, auth().authz(permissions.write_tag)],
    method: 'POST',
  },
  update_config: {
    controller: configurationController.updateConfigOption,
    middlewares: [validateUpdateConfigOption, auth().authz(permissions.write_tag)],
    method: 'POST',
  },
  delete_config_group: {
    controller: configurationController.deleteConfigGroup,
    middlewares: [auth().authz(permissions.delete_tag)],
    method: 'POST',
  },
  delete_config: {
    controller: configurationController.deleteConfigOption,
    middlewares: [auth().authz(permissions.delete_tag)],
    method: 'POST',
  },
};

const router: Router = express.Router({ mergeParams: true });

/*
  router to handle run related queries
  GET: get_configs: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_configs/{project_id}
  POST: add_config_group: {Backend URL}/trv2/{handle}/index.php?/api/v2/add_config_group
  POST: add_config: {Backend URL}/trv2/{handle}/index.php?/api/v2/add_config
  POST: update_config_group: {Backend URL}/trv2/{handle}/index.php?/api/v2/update_config_group/{config_group_id}
  POST: update_config: {Backend URL}/trv2/{handle}/index.php?/api/v2/update_config/{config_id}
  POST: delete_config_group: {Backend URL}/trv2/{handle}/index.php?/api/v2/delete_config_group/{config_group_id}
  POST: delete_config: {Backend URL}/trv2/{handle}/index.php?/api/v2/delete_config/{config_id}

  */

router.use(
  setResource('configuration'),
  entityRouter(configurationRoutesMap),
);

export default router;
