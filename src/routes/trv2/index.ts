import express, { Router } from 'express';
import { parseEndpointQuery } from '@app/middlewares/trv2';
import {
  loadDbComponent, tenantContextExternalApi, bindSharedModels, loadHandle,
} from '@app/middlewares/handle';
import { auth } from '@ss-libs/ss-component-auth';
import projectsRouter from './project';
import folderRouter from './folder';
import caseRouter from './case';
import runRouter from './run';
import planRouter from './plan';
import milestoneRouter from './milestone';
import executionRouter from './execution';
import resultRouter from './result';
import tagRouter from './tag';
import prefrencesRouter from './prefrences';
import templateRouter from './template';
import configurationRouter from './configuration';
import attachmentsRouter from './attachments';

const router: Router = express.Router();
const middlewares = () => [
  auth().authenticateBearer,
  loadHandle(),
  tenantContextExternalApi(),
  bindSharedModels,
  loadDbComponent(),
  parseEndpointQuery,
];

/*
  Example routes:
  projects: {Backend URL}/trv2/{handle}/index.php?/api/v2/projects/get_projects
  folders: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_suites/{project_id}
  runs: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_runs/{project_id}
  plans: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_plans/{project_id}
  milestones: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_milestones/{project_id}
  */

router.use(
  '/:handle/index.php',
  ...middlewares(),
  projectsRouter,
  folderRouter,
  caseRouter,
  runRouter,
  planRouter,
  milestoneRouter,
  executionRouter,
  resultRouter,
  tagRouter,
  prefrencesRouter,
  templateRouter,
  configurationRouter,
  attachmentsRouter,
);

export default router;
