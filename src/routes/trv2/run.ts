import { auth } from '@ss-libs/ss-component-auth';
import express, { Router } from 'express';
import { setResource } from '@app/middlewares/handle';
import { entityRouter } from '@app/middlewares/trv2';
import { permissions } from '@app/constants/auth';
import runController from '@app/controllers/trv2/run';
import { validateGetRuns, validateCreateRun, validateUpdateRun } from '@app/validators/trv2/run';

const runRoutesMap = {
  get_runs: {
    controller: runController.getTestRuns,
    middlewares: [auth().authz(permissions.read_entity), validateGetRuns],
    method: 'GET',
  },
  get_run: {
    controller: runController.getTestRun,
    middlewares: [auth().authz(permissions.read_entity)],
    method: 'GET',
  },
  add_run: {
    controller: runController.createTestRun,
    middlewares: [auth().authz(permissions.write_entity), validateCreateRun],
    method: 'POST',
  },
  update_run: {
    controller: runController.updateTestRun,
    middlewares: [auth().authz(permissions.write_entity), validateUpdateRun],
    method: 'POST',
  },
  close_run: {
    controller: runController.closeTestRun,
    middlewares: [auth().authz(permissions.write_entity)],
    method: 'POST',
  },
  delete_run: {
    controller: runController.deleteTestRun,
    middlewares: [auth().authz(permissions.write_entity)],
    method: 'POST',
  },
};

const router: Router = express.Router({ mergeParams: true });

/*
  router to handle run related queries
  GET: get_runs: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_runs/{project_id}
  GET: get_run: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_run/{run_id}
  POST: add_run: {Backend URL}/trv2/{handle}/index.php?/api/v2/add_run
  POST: update_run: {Backend URL}/trv2/{handle}/index.php?/api/v2/update_run/{run_id}
  POST: close_run: {Backend URL}/trv2/{handle}/index.php?/api/v2/close_run/{run_id}
  POST: delete_run: {Backend URL}/trv2/{handle}/index.php?/api/v2/delete_run/{run_id}

  */

router.use(
  setResource('run'),
  entityRouter(runRoutesMap),
);

export default router;
