import { auth } from '@ss-libs/ss-component-auth';
import express, { Router } from 'express';
import { setResource } from '@app/middlewares/handle';
import { entityRouter } from '@app/middlewares/trv2';
import { permissions } from '@app/constants/auth';
import executionController from '@app/controllers/trv2/execution';
import { validateGetExecutions } from '@app/validators/trv2/execution';

const executionRoutesMap = {
  get_tests: {
    controller: executionController.getExecutions,
    middlewares: [auth().authz(permissions.read_entity), validateGetExecutions],
    method: 'GET',
  },
  get_test: {
    controller: executionController.getExecution,
    middlewares: [auth().authz(permissions.read_entity)],
    method: 'GET',
  },
};

const router: Router = express.Router({ mergeParams: true });

/*
  router to handle project related queries
  GET: get_tests: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_tests/{project_id}
  GET: get_test: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_test/{test_id}
*/

router.use(
  setResource('execution'),
  entityRouter(executionRoutesMap),
);

export default router;
