import { auth } from '@ss-libs/ss-component-auth';
import express, { Router } from 'express';
import projectController from '@app/controllers/trv2/project';
import { setResource } from '@app/middlewares/handle';
import { entityRouter } from '@app/middlewares/trv2';
import { permissions } from '@app/constants/auth';
import {
  validateCreateProject,
  validateGetProjects,
  validateUpdateProject,
} from '@app/validators/trv2/project';

const projectRoutesMap = {
  get_projects: {
    controller: projectController.getProjects,
    middlewares: [validateGetProjects],
    method: 'GET',
  },
  get_project: {
    controller: projectController.getProject,
    middlewares: [],
    method: 'GET',
  },
  add_project: {
    controller: projectController.addProject,
    middlewares: [auth().authz(permissions.write_project), validateCreateProject],
    method: 'POST',
  },
  update_project: {
    controller: projectController.updateProject,
    middlewares: [auth().authz(permissions.write_project), validateUpdateProject],
    method: 'POST',
  },
  delete_project: {
    controller: projectController.deleteProject,
    middlewares: [auth().authz(permissions.delete_project)],
    method: 'POST',
  },
};

const router: Router = express.Router({ mergeParams: true });

/*
  router to handle project related queries
  GET: get_projects: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_projects
  GET: get_project: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_project/{project_id}
  POST: add_project: {Backend URL}/trv2/{handle}/index.php?/api/v2/add_project
  POST: update_project: {Backend URL}/trv2/{handle}/index.php?/api/v2/update_project/{project_id}
  POST: delete_project: {Backend URL}/trv2/{handle}/index.php?/api/v2/delete_project/{project_id}
*/

router.use(
  // validateCreateProjectBody,
  setResource('project'),
  entityRouter(projectRoutesMap),
);

export default router;
