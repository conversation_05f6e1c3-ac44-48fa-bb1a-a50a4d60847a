import { auth } from '@ss-libs/ss-component-auth';
import express, { Router } from 'express';
import resultController from '@app/controllers/trv2/result';
import { setResource } from '@app/middlewares/handle';
import { entityRouter } from '@app/middlewares/trv2';
import { permissions } from '@app/constants/auth';
import {
  validateAddResultForCases, validateAddResults, validateCreateResult, validateGetResults, validateGetResultsByRun,
} from '@app/validators/trv2/result';

const resultRoutesMap = {
  get_results: {
    controller: resultController.getResultsByExecution,
    middlewares: [auth().authz(permissions.read_entity), validateGetResults],
    method: 'GET',
  },
  get_results_for_case: {
    controller: resultController.getResultsByCase,
    middlewares: [auth().authz(permissions.read_entity), validateGetResults],
    method: 'GET',
  },
  get_results_for_run: {
    controller: resultController.getResultsByRun,
    middlewares: [auth().authz(permissions.read_entity), validateGetResultsByRun],
    method: 'GET',
  },
  add_result: {
    controller: resultController.createResult,
    middlewares: [auth().authz(permissions.write_entity), validateCreateResult],
    method: 'POST',
  },
  add_result_for_case: {
    controller: resultController.createResultForCase,
    middlewares: [auth().authz(permissions.write_entity), validateCreateResult],
    method: 'POST',
  },
  add_results: {
    controller: resultController.createResults,
    middlewares: [auth().authz(permissions.write_entity), validateAddResults],
    method: 'POST',
  },
  add_results_for_cases: {
    controller: resultController.createResultsForCases,
    middlewares: [auth().authz(permissions.write_entity), validateAddResultForCases],
    method: 'POST',
  },
};

const router: Router = express.Router({ mergeParams: true });

/*
  router to handle project related queries
  GET: get_results: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_results
  GET: get_results_for_case: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_results_for_case/{case_id}
  GET: get_result_for_run: {Backend URL}/trv2/{handle}/index.php?/api/v2/get_result_for_run/{run_id}
  POST: add_result: {Backend URL}/trv2/{handle}/index.php?/api/v2/add_result
  POST: add_result_for_case: {Backend URL}/trv2/{handle}/index.php?/api/v2/add_result_for_case/{case_id}
  POST: add_results: {Backend URL}/trv2/{handle}/index.php?/api/v2/add_results
  POST: add_results_for_cases: {Backend URL}/trv2/{handle}/index.php?/api/v2/add_results_for_cases
*/

router.use(
  setResource('result'),
  entityRouter(resultRoutesMap),
);

export default router;
