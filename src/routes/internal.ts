import { Router } from 'express';
import accessTokenRoutes from './internal/accessToken';
import attachmentRoutes from './internal/attachment';
import handleRoutes from './internal/handle';
import workspaceRoutes from './internal/workspace';
import integrationRoutes from './internal/integration';
import permissionsRoutes from './internal/permission';
import dashboardRoutes from './internal/dashboard';
import assistRoutes from './internal/assist';
import caseRoutes from './internal/case';
import commentRoutes from './internal/comment';
import configurationRoutes from './internal/configuration';
import customFieldRoutes from './internal/customField';
import defectRoutes from './internal/defect';
import executionRoutes from './internal/execution';
import folderRoutes from './internal/folder';
import milestoneRoutes from './internal/milestone';
import orgRoutes from './internal/org';
import planRoutes from './internal/plan';
import projectRoutes from './internal/project';
import repoRoutes from './internal/repo';
import resultRoutes from './internal/result';
import roleRoutes from './internal/role';
import runRoutes from './internal/run';
import sharedStepRoutes from './internal/sharedStep';
import templateRoutes from './internal/template';
import tagRoutes from './internal/tag';
import userRoutes from './internal/user';
import ssoConfigRoutes from './internal/ssoConfig';
import scheduledTaskRoutes from './internal/scheduledTask';
import inviteRoutes from './internal/invite';

export const internalRoutes: Router[] = [
  accessTokenRoutes,
  attachmentRoutes,
  assistRoutes,
  commentRoutes,
  caseRoutes,
  configurationRoutes,
  customFieldRoutes,
  dashboardRoutes,
  defectRoutes,
  executionRoutes,
  folderRoutes,
  handleRoutes,
  integrationRoutes,
  workspaceRoutes,
  permissionsRoutes,
  milestoneRoutes,
  orgRoutes,
  planRoutes,
  projectRoutes,
  repoRoutes,
  resultRoutes,
  roleRoutes,
  runRoutes,
  sharedStepRoutes,
  userRoutes,
  templateRoutes,
  tagRoutes,
  ssoConfigRoutes,
  inviteRoutes,
  scheduledTaskRoutes,
];
