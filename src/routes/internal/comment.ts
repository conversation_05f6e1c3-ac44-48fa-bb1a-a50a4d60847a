import {
  createCommentValidator,
  deleteCommentValidators,
  getCommentsValidators,
  updateCommentValidators,
} from '@app/validators/comment';
import { setResource, tenantContext } from '@app/middlewares/handle';

import { auth } from '@ss-libs/ss-component-auth';
import commentController from '@app/controllers/comment';
import express from 'express';

const router = express.Router();
router.post(
  '/:handle/comments',
  tenantContext(),
  auth().authenticateHybrid,
  auth().authz(),
  createCommentValidator,
  commentController.createComment,
);
router.get(
  '/:handle/comments/:entityType/:entityUid',
  tenantContext(),
  auth().authenticateHybrid,
  getCommentsValidators,
  commentController.getComments,
);
router.put(
  '/:handle/comments/:id',
  setResource('comment'),
  tenantContext(),
  auth().authenticateHybrid,
  auth().authz(),
  updateCommentValidators,
  commentController.updateComment,
);
router.delete(
  '/:handle/comments/:id',
  tenantContext(),
  auth().authenticateHybrid,
  deleteCommentValidators,
  commentController.deleteComment,
);

export default router;
