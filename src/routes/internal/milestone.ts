import express, { Router } from 'express';
import { setResource, tenantContext } from '@app/middlewares/handle';
import {
  validateCreateMilestone,
  validateUpdateMilestone,
  validateMilestoneQuery,
  validateGetMilestoneRelations,
} from '@app/validators/milestone';

import { auth } from '@ss-libs/ss-component-auth';
import milestoneController from '@app/controllers/milestone';
import { permissions } from '@app/constants/auth';

const router: Router = express.Router();

const middlewares = (resource: string, ...permissions: string[]) => [
  setResource(resource),
  tenantContext(),
  auth().authenticateHybrid,
  auth().authz(...permissions),
];

router.post(
  '/:handle/projects/:key/milestones',
  ...middlewares('milestone', permissions.write_entity),
  validateCreateMilestone,
  milestoneController.createMilestone,
);
router.get(
  '/:handle/projects/:key/milestones',
  ...middlewares('milestone', permissions.read_entity),
  validateMilestoneQuery,
  milestoneController.getMilestones,
);

router.get(
  '/:handle/projects/:key/milestones/relations',
  ...middlewares('milestone', permissions.read_entity),
  validateGetMilestoneRelations,
  milestoneController.getMilestoneRelations,
);

router.get(
  '/:handle/projects/:key/milestones/search',
  ...middlewares('milestone', permissions.read_entity),
  milestoneController.searchMilestones,
);
router.get(
  '/:handle/projects/:key/milestones/:id',
  ...middlewares('milestone', permissions.read_entity),
  milestoneController.getMilestone,
);
router.patch(
  '/:handle/projects/:key/milestones/:id',
  ...middlewares('milestone', permissions.write_entity),
  validateUpdateMilestone,
  milestoneController.updateMilestone,
);
router.delete(
  '/:handle/projects/:key/milestones/:id',
  ...middlewares('milestone', permissions.delete_entity),
  milestoneController.deleteMilestone,
);

export default router;
