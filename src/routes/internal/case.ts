import express, { Router } from 'express';
import { setResource, tenantContext } from '@app/middlewares/handle';
import {
  validateCaseID,
  validateCaseQuery,
  validateCaseRelationQuery,
  validateCreateCase,
  validateCreateCases,
  validateDeleteCases,
  validateWriteCase,
} from '@app/validators/case';

import { auth } from '@ss-libs/ss-component-auth';
import caseController from '@app/controllers/case';
import { permissions } from '@app/constants/auth';
import { enforceStorageLimit } from '@app/middlewares/attachment';

const router: Router = express.Router();

const middlewares = (resource: string, ...permissions: string[]) => [
  setResource(resource),
  tenantContext(),
  auth().authenticateHybrid,
  auth().authz(...permissions),
];

router.post(
  '/:handle/projects/:key/cases/import',
  ...middlewares('case', permissions.write_entity),
  validate<PERSON>reate<PERSON><PERSON>,
  caseController.createCases,
);
router.post(
  '/:handle/projects/:key/cases',
  ...middlewares('case', permissions.write_entity),
  validateCreateCase,
  caseController.createCase,
);

router.delete(
  '/:handle/projects/:key/cases',
  ...middlewares('case', permissions.write_entity),
  validateDeleteCases,
  caseController.deleteCases,
);

router.patch(
  '/:handle/projects/:key/cases',
  ...middlewares('case', permissions.write_entity),
  caseController.updateTestCases,
);

router.patch(
  '/:handle/projects/:key/cases/:id',
  ...middlewares('case', permissions.write_entity),
  validateWriteCase,
  validateCaseID,
  caseController.updateTestCase,
);

router.get(
  '/:handle/projects/:key/cases/:id/executions',
  ...middlewares('case', permissions.read_activity),
  caseController.getExecutionsByCase,
);

// get cases related to project
router.get(
  '/:handle/projects/:key/cases',
  ...middlewares('project', permissions.read_entity),
  validateCaseQuery,
  caseController.getCases,
);

router.get(
  '/:handle/projects/:key/cases/relations',
  ...middlewares('project', permissions.read_entity),
  validateCaseRelationQuery,
  caseController.getCaseRelations,
);

router.get(
  '/:handle/projects/:key/cases/search',
  ...middlewares('case', permissions.read_entity),
  caseController.searchCases,
);

router.get(
  '/:handle/projects/:key/cases/count',
  ...middlewares('case', permissions.read_entity),
  caseController.getCasesCountByProject,
);

router.get(
  '/:handle/projects/:key/cases/:id',
  ...middlewares('case', permissions.read_entity),
  caseController.getCase,
);

router.get(
  '/:handle/projects/:key/cases/:id/versions/:versionId',
  ...middlewares('case', permissions.read_entity),
  caseController.getCaseByVersion,
);

router.delete(
  '/:handle/projects/:key/cases/:id',
  ...middlewares('case', permissions.delete_entity),
  caseController.deleteCase,
);

router.get(
  '/:handle/projects/:key/cases/:id/summary',
  ...middlewares('case', permissions.read_entity),
  caseController.getSummary,
);

router.post(
  '/:handle/projects/:key/cases/:id/attachments',
  ...middlewares('case', permissions.write_entity),
  enforceStorageLimit,
  caseController.uploadAttachment,
);

router.delete(
  '/:handle/projects/:key/cases/attachments/:id',
  ...middlewares('case', permissions.write_entity),
  caseController.deleteAttachment,
);
export default router;
