import { auth } from '@ss-libs/ss-component-auth';
import { tenantContext, setResource } from '@app/middlewares/handle';
import express, { Router } from 'express';
import rolesController from '@app/controllers/role';
import { permissions, excludedProjectPermissions } from '@app/constants/auth';

const router: Router = express.Router();
router.get(
  ['/:handle/permissions', '/:handle/projects/:key/permissions'],
  tenantContext(),
  auth().authenticateHybrid,
  rolesController.getPermissions,
);
router.get(
  '/orgs/:handle/projects/:key/authz',
  setResource('project'),
  tenantContext(),
  auth().authenticateHybrid,
  auth().authzPermissions(
    ...Object.values(permissions).filter(
      (p) => !excludedProjectPermissions.includes(p),
    ),
  ),
);

router.get(
  '/orgs/:handle/authz',
  tenantContext(),
  auth().authenticateHybrid,
  auth().authzPermissions(...Object.values(permissions)),
);

export default router;
