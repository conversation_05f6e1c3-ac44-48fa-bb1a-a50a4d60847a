import express, { Router } from 'express';
import {
  validateAccessTokenId,
  validateCreateAccessToken,
  validateUpdateAccessToken,
} from '@app/validators/accessToken';

import accessTokenController from '@app/controllers/accessToken';
import { auth } from '@ss-libs/ss-component-auth';
import { tenantContext } from '@app/middlewares/handle';

const middlewares = [tenantContext(), auth().authenticateHybrid];

const router: Router = express.Router();

router.post(
  '/:handle/accessTokens',
  ...middlewares,
  validateCreateAccessToken,
  accessTokenController.newAccessToken,
);
router.get(
  '/:handle/accessTokens',
  ...middlewares,
  accessTokenController.accessTokenIndex,
);
router.patch(
  '/:handle/accessTokens/:id',
  ...middlewares,
  validateUpdateAccessToken,
  validateAccessTokenId,
  accessTokenController.updateAccessToken,
);
router.delete(
  '/:handle/accessTokens/:id',
  ...middlewares,
  validateAccessTokenId,
  accessTokenController.deleteAccessToken,
);

export default router;
