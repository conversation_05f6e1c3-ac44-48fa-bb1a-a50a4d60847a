import express, { Router } from 'express';
import { setResource, tenantContext } from '@app/middlewares/handle';
import ssoConfigValidator from '@app/validators/ssoConfig';

import { auth } from '@ss-libs/ss-component-auth';
import ssoConfigController from '@app/controllers/ssoConfig';

const router: Router = express.Router();

const middlewares = (resource: string = 'ssoConfig') => [
  setResource(resource),
  tenantContext(),
  auth().authenticateHybrid,
];

router.get(
  '/:handle/sso/config',
  ...middlewares('ssoConfig'),
  ssoConfigController.getSSOConfig,
);

router.post(
  '/:handle/sso/config',
  ...middlewares('ssoConfig'),
  ssoConfigValidator.createSSOConfig,
  ssoConfigController.createSSOConfig,
);

router.patch(
  '/:handle/sso/config/:id',
  ...middlewares('ssoConfig'),
  ssoConfigValidator.updateSSOConfig,
  ssoConfigController.updateSSOConfig,
);

router.delete(
  '/:handle/sso/config/:id',
  ...middlewares('ssoConfig'),
  ssoConfigController.deleteSSOConfig,
);

export default router;
