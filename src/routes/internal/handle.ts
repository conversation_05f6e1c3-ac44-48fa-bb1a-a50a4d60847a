import * as handleValidator from '@app/validators/handle';
import {
  bindSharedModels,
  loadHandle,
  tenantContext,
} from '@app/middlewares/handle';
import express, { Router } from 'express';

import accessTokenController from '@app/controllers/accessToken';
import { auth } from '@ss-libs/ss-component-auth';
import handleController from '@app/controllers/handle';
import { permissions } from '@app/constants/auth';

const router: Router = express.Router();

router.patch(
  '/:handle/preferences',
  tenantContext(),
  auth().authenticateHybrid,
  auth().authz(permissions.write_setting),
  handleValidator.validatePreferenceUpdate,
  handleController.updatePreference,
);

router.get(
  '/:handle/preferences',
  tenantContext(),
  auth().authenticateHybrid,
  auth().authz(),
  handleController.getPreferences,
);

router.get(
  '/handle/:handle/profile',
  loadHandle(),
  bindSharedModels,
  auth().authenticateHybrid,
  auth().authz(),
  handleController.getHandleStatus,
);

router.get(
  '/handle/:handle',
  bindSharedModels,
  handleValidator.validatePotentialHandle,
  handleController.handleInUse,
);

router.patch(
  '/handle/:handle',
  tenantContext(),
  auth().authenticateHybrid,
  auth().authz(permissions.write_setting),
  handleValidator.validateHandle,
  handleController.updateHandle,
);

router.get(
  '/:handle/tokens',
  tenantContext(),
  auth().authenticateHybrid,
  auth().authz(),
  accessTokenController.accessTokenIndex,
);

export default router;
