import express, { Router } from 'express';
import { setResource, tenantContext } from '@app/middlewares/handle';
import {
  validateTemplateId,
  validateUpdateTemplate,
  validateWriteTemplate,
} from '@app/validators/template';

import { auth } from '@ss-libs/ss-component-auth';
import { permissions } from '@app/constants/auth';
import templateController from '@app/controllers/template';

const router: Router = express.Router();

const middlewares = (
  resource: string = 'template',
  ...permissions: string[]
) => [
  setResource(resource),
  tenantContext(),
  auth().authenticateHybrid,
  auth().authz(...permissions),
];

router.get(
  '/:handle/projects/:key/templates',
  ...middlewares('template'),
  templateController.getTemplates,
);

router.post(
  '/:handle/projects/:key/templates',
  ...middlewares('template', permissions.write_template),
  validateWriteTemplate,
  templateController.createTemplate,
);

router.get(
  '/:handle/projects/:key/templates/:id',
  ...middlewares('template'),
  validateTemplateId,
  templateController.getTemplate,
);

router.patch(
  '/:handle/projects/:key/templates/:id',
  ...middlewares('template', permissions.write_template),
  validateTemplateId,
  validateUpdateTemplate,
  templateController.updateTemplate,
);

router.delete(
  '/:handle/projects/:key/templates/:id',
  ...middlewares('template', permissions.delete_template),
  validateTemplateId,
  templateController.deleteTemplate,
);

export default router;
