import express, { Router } from 'express';
import { setResource, tenantContext } from '@app/middlewares/handle';
import {
  validateCustomFieldBody,
  validateCustomFieldId,
} from '@app/validators/customField';

import { auth } from '@ss-libs/ss-component-auth';
import customFieldController from '@app/controllers/customField';
import { permissions } from '@app/constants/auth';

const router: Router = express.Router();

const middlewares = (
  resource: string = 'custom_field',
  ...permissions: string[]
) => [
  setResource(resource),
  tenantContext(),
  auth().authenticateHybrid,
  auth().authz(...permissions),
];

router.get(
  '/:handle/projects/:key/customFields',
  ...middlewares('custom_field'),
  customFieldController.getCustomFields,
);

router.post(
  '/:handle/projects/:key/customFields',
  ...middlewares('custom_field', permissions.write_custom_field),
  validateCustomFieldBody(true),
  customFieldController.createCustomField,
);

router.patch(
  '/:handle/projects/:key/customFields/:id',
  ...middlewares('custom_field', permissions.write_custom_field),
  validateCustomFieldBody(false),
  validateCustomFieldId,
  customFieldController.updateCustomField,
);

router.delete(
  '/:handle/projects/:key/customFields/:id',
  ...middlewares('custom_field', permissions.delete_custom_field),
  validateCustomFieldId,
  customFieldController.deleteCustomField,
);

export default router;
