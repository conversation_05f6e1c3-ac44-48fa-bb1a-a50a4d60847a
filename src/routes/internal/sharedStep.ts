import express, { Router } from 'express';
import { setResource, tenantContext } from '@app/middlewares/handle';
import {
  validateSharedStepId,
  validateSharedStepIds,
  validateToogleArchiveSharedSteps,
  validateUpdateSharedTestStep,
  validateWriteSharedTestStep,
} from '@app/validators/sharedStep';

import { auth } from '@ss-libs/ss-component-auth';
import { permissions } from '@app/constants/auth';
import sharedStepController from '@app/controllers/sharedStep';

const router: Router = express.Router();

const middlewares = (resource: string = 'step', ...permissions: string[]) => [
  setResource(resource),
  tenantContext(),
  auth().authenticateHybrid,
  auth().authz(...permissions),
];

router.get(
  '/:handle/projects/:key/shared-steps',
  ...middlewares('step'),
  sharedStepController.getSharedTestSteps,
);

router.post(
  '/:handle/projects/:key/shared-steps',
  ...middlewares('step', permissions.write_step),
  validateWriteSharedTestStep,
  sharedStepController.createSharedStep,
);

router.delete(
  '/:handle/projects/:key/shared-steps/:id',
  ...middlewares('step', permissions.delete_step),
  validateSharedStepId,
  sharedStepController.deleteSharedStep,
);

router.delete(
  '/:handle/projects/:key/shared-steps',
  ...middlewares('step', permissions.delete_step),
  validateSharedStepIds,
  sharedStepController.deleteSharedSteps,
);

router.get(
  '/:handle/projects/:key/shared-steps/:id',
  ...middlewares('step', permissions.read_step),
  validateSharedStepId,
  sharedStepController.getSharedStep,
);

router.patch(
  '/:handle/projects/:key/shared-steps/:id',
  ...middlewares('step', permissions.write_step),
  validateSharedStepId,
  validateUpdateSharedTestStep,
  sharedStepController.updateSharedStep,
);

router.patch(
  '/:handle/projects/:key/shared-steps',
  ...middlewares('step', permissions.write_step),
  validateToogleArchiveSharedSteps,
  sharedStepController.updateSharedSteps,
);

export default router;
