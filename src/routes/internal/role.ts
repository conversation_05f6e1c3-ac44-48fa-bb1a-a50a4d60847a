import express, { Router } from 'express';
import { setResource, tenantContext } from '@app/middlewares/handle';
import {
  validateAddMembers,
  validateCreateRole,
  validateFindRole,
  validateGetMemberRoles,
  validateReAssignMembers,
  validateRoleIds,
  validateUpdateRole,
  validateGetRoles,
} from '@app/validators/role';

import { auth } from '@ss-libs/ss-component-auth';
import { permissions } from '@app/constants/auth';
import rolesController from '@app/controllers/role';

const router: Router = express.Router();

const baseMiddlewares = [
  tenantContext(),
  auth().authenticateHybrid,
];

router.post(
  ['/:handle/roles', '/:handle/projects/:key/roles'],
  ...baseMiddlewares,
  auth().authz(permissions.write_role),
  validateCreateRole,
  rolesController.createRole,
);

router.delete(
  ['/:handle/roles', '/:handle/projects/:key/roles'],
  setResource('role'),
  ...baseMiddlewares,
  auth().authz(permissions.write_role),
  validateRoleIds,
  rolesController.deleteRoles,
);

router.get(
  ['/:handle/roles', '/:handle/projects/:key/roles'],
  ...baseMiddlewares,
  auth().authz(permissions.read_role),
  validateGetRoles,
  rolesController.listRoles,
);

router.get(
  '/:handle/roles/:id',
  setResource('role'),
  ...baseMiddlewares,
  auth().authz(permissions.read_role),
  validateFindRole,
  rolesController.findRole,
);

router.patch(
  ['/:handle/roles/:id', '/:handle/projects/:key/roles/:id'],
  setResource('role'),
  ...baseMiddlewares,
  auth().authz(permissions.write_role),
  validateFindRole,
  validateUpdateRole,
  rolesController.updateRole,
);

router.post(
  '/:handle/roles/:id/members',
  setResource('role'),
  ...baseMiddlewares,
  auth().authz(permissions.write_member),
  validateFindRole,
  validateAddMembers,
  rolesController.addMembers,
);

router.delete(
  '/:handle/roles/:id/members',
  setResource('role'),
  ...baseMiddlewares,
  auth().authz(permissions.write_member),
  validateFindRole,
  validateAddMembers,
  rolesController.removeMembers,
);

router.get(
  '/:handle/members/:userUid/roles',
  ...baseMiddlewares,
  auth().authz(),
  validateGetMemberRoles,
  rolesController.getMemberRoles,
);

router.delete(
  ['/:handle/roles/:id', '/:handle/projects/:key/roles/:id'],
  setResource('role'),
  ...baseMiddlewares,
  auth().authz(permissions.delete_role),
  validateFindRole,
  rolesController.deleteRole,
);

router.post(
  ['/:handle/roles/:id/members/reassign', '/:handle/projects/:key/roles/:id/members/reassign'],
  setResource('role'),
  ...baseMiddlewares,
  auth().authz(permissions.write_member),
  validateFindRole,
  validateReAssignMembers,
  rolesController.reAssignRoleToMembers,
);

export default router;
