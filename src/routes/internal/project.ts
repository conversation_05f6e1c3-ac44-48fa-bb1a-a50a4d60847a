import express, { Router } from 'express';
import {
  loadProject,
  setResource,
  tenantContext,
} from '@app/middlewares/handle';
import projectValidator, {
  validateCountProjectEntity,
  validatePaginatedProjectsQuery,
  validateUpdateProject,
} from '@app/validators/project';

import { auth } from '@ss-libs/ss-component-auth';
import { permissions } from '@app/constants/auth';
import projectController from '@app/controllers/project';
import { enforceStorageLimit } from '@app/middlewares/attachment';

const router: Router = express.Router();

router.use(setResource('project'));

const baseMiddlewares = [tenantContext(), auth().authenticateHybrid];

router.post(
  '/:handle/projects',
  ...baseMiddlewares,
  auth().authz(permissions.write_project),
  projectValidator.createProject,
  projectController.createProject,
);
router.get(
  '/:handle/projects',
  ...baseMiddlewares,
  auth().authz(),
  validatePaginatedProjectsQuery,
  projectController.getProjects,
);
router.get(
  '/:handle/projects/search',
  ...baseMiddlewares,
  auth().authz(),
  projectController.searchProjects,
);

router.get(
  '/:handle/projects/keys/:key',
  ...baseMiddlewares,
  auth().authz(),
  projectController.keyInUse,
);
router.get(
  '/:handle/projects/:key',
  ...baseMiddlewares,
  auth().authz(),
  projectController.getProject,
);
router.delete(
  '/:handle/projects/:key',
  ...baseMiddlewares,
  auth().authz(permissions.delete_project),
  projectController.deleteProject,
);
router.patch(
  '/:handle/projects/:key',
  ...baseMiddlewares,
  auth().authz(permissions.write_project),
  validateUpdateProject,
  projectController.updateProject,
);

router.patch(
  '/:handle/projects/:key/unarchive',
  ...baseMiddlewares,
  auth().authz(permissions.write_project),
  projectController.unArchiveProject,
);
router.post(
  '/:handle/projects/:key/attachments',
  tenantContext(),
  ...baseMiddlewares,
  auth().authz(permissions.write_project),
  enforceStorageLimit,
  projectController.uploadAttachment,
);

router.delete(
  '/:handle/projects/:key/attachments/:id',
  ...baseMiddlewares,
  auth().authz(permissions.write_project),
  projectController.deleteAttachment,
);

router.get(
  '/:handle/projects/:key/users',
  ...baseMiddlewares,
  auth().authz(permissions.read_member),
  projectController.getProjectUsers,
);

router.get(
  '/:handle/projects/:key/entities/count',
  ...baseMiddlewares,
  validateCountProjectEntity,
  loadProject,
  auth().authz(permissions.read_entity),
  projectController.countEntity,
);

export default router;
