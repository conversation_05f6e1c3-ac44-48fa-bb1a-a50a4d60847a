import * as orgValidator from '@app/validators/org';

import express, { Router } from 'express';
import {
  loadHandle, setResource, tenantContext, bindSharedModels,
} from '@app/middlewares/handle';

import { auth } from '@ss-libs/ss-component-auth';
import orgController from '@app/controllers/org';
import { permissions } from '@app/constants/auth';
import { validateCreateAttachment } from '@app/validators/attachment';
import { enforceStorageLimit } from '@app/middlewares/attachment';

const router: Router = express.Router();

const baseMiddlewares = (...permissions: string[]) => [
  setResource('org'),
  tenantContext(),
  auth().authenticateHybrid,
  auth().authz(...permissions),
];

router.put(
  '/orgs/:handle/avatar',
  setResource('org'),
  tenantContext(),
  auth().authenticateHybrid,
  validateCreateAttachment,
  enforceStorageLimit,
  orgController.uploadAttachment,
);

router.post(
  '/orgs',
  auth().authenticateHybrid,
  orgValidator.validateNewOrg,
  orgController.newOrg,
);
router.patch(
  '/orgs/:handle',
  ...baseMiddlewares(permissions.write_member),
  orgValidator.validateUpdateOrg,
  orgController.updateOrg,
);

router.get(
  '/orgs/:handle',
  ...baseMiddlewares(permissions.read_member),
  orgController.getOrganization,
);

router.get(
  '/orgs/:handle/users',
  ...baseMiddlewares(permissions.read_member),
  orgController.getUsers,
);

router.patch(
  '/orgs/:handle/users',
  ...baseMiddlewares(permissions.write_member),
  orgValidator.validateBulkMemberUpdate,
  orgController.updateUsers,
);

router.get(
  '/orgs/:handle/users/count',
  ...baseMiddlewares(),
  orgController.countUsers,
);

router.delete(
  '/orgs/:handle/users/:userId',
  ...baseMiddlewares(permissions.delete_member),
  orgValidator.validateRemoveMember,
  orgController.removeMember,
);

router.delete(
  '/orgs/:handle/',
  ...baseMiddlewares(permissions.owner),
  orgValidator.validateDeleteOrg,
  orgController.deleteOrg,
);

router.get(
  '/orgs/:handle/sso',
  bindSharedModels,
  loadHandle(),
  orgController.initiateSSO,
);

export default router;
