import express, { Router } from 'express';
import { setResource, tenantContext } from '@app/middlewares/handle';

import assistController from '@app/controllers/assist';
import { auth } from '@ss-libs/ss-component-auth';
import { validateCaseCreation } from '@app/validators/assist';

const router: Router = express.Router();

const middlewares = () => [
  setResource('assist'),
  tenantContext(),
  auth().authenticateHybrid,
  auth().authz(),
];

router.post(
  '/:handle/assist',
  ...middlewares(),
  validateCaseCreation,
  assistController.handleGetAssistResponse,
);

export default router;
