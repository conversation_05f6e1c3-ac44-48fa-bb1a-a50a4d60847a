import {
  deleteResultValidators,
  getResultByExecutionValidators,
  updateResultValidators,
  validateCreateResult,
  validateExecUidParam,
} from '@app/validators/result';

import { auth } from '@ss-libs/ss-component-auth';
import express from 'express';
import { permissions } from '@app/constants/auth';
import resultController from '@app/controllers/result';
import { tenantContext } from '@app/middlewares/handle';
import { enforceStorageLimit } from '@app/middlewares/attachment';

const router = express.Router();

const baseMiddlewares = [tenantContext(), auth().authenticateHybrid];

router.post(
  '/:handle/projects/:key/executions/:id/results',
  ...baseMiddlewares,
  auth().authz(permissions.write_activity),
  validateCreateResult,
  validateExecUidParam,
  resultController.createResult,
);

router.get(
  '/:handle/projects/:key/executions/:executionUid/results',
  ...baseMiddlewares,
  auth().authz(permissions.read_activity),
  getResultByExecutionValidators,
  resultController.getResultsByExecution,
);
router.get(
  '/:handle/cases/:caseUid/results',
  ...baseMiddlewares,
  auth().authz(permissions.read_activity),
  resultController.getResultsByCase,
);
router.patch(
  '/:handle/projects/:key/results/:id',
  ...baseMiddlewares,
  auth().authz(permissions.write_activity),
  updateResultValidators,
  resultController.updateResult,
);
router.delete(
  '/:handle/projects/:key/results/:id',
  ...baseMiddlewares,
  auth().authz(permissions.write_activity),
  deleteResultValidators,
  resultController.deleteResult,
);

router.post(
  '/:handle/projects/:key/results/:id/attachments',
  ...baseMiddlewares,
  auth().authz(permissions.write_activity),
  enforceStorageLimit,
  resultController.uploadAttachment,
);

router.delete(
  '/:handle/projects/:key/results/attachments/:id',
  ...baseMiddlewares,
  auth().authz(permissions.write_activity),
  resultController.deleteAttachment,
);
export default router;
