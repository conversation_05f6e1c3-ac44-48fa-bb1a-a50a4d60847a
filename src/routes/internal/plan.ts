import express, { Router } from 'express';
import { setResource, tenantContext } from '@app/middlewares/handle';

import { auth } from '@ss-libs/ss-component-auth';
import { permissions } from '@app/constants/auth';
import planController from '@app/controllers/plan';
import plansValidator from '@app/validators/plan';

const router: Router = express.Router();

const middlewares = (resource: string, ...permissions: string[]) => [
  setResource(resource),
  tenantContext(),
  auth().authenticateHybrid,
  auth().authz(...permissions),
];

router.post(
  '/:handle/projects/:key/plans',
  ...middlewares('plan', permissions.write_entity),
  plansValidator.createPlan,
  planController.createPlan,
);
router.post(
  '/:handle/projects/:key/plans/duplicate',
  ...middlewares('plan', permissions.write_entity),
  plansValidator.duplicatePlan,
  planController.duplicatePlan,
);

router.get(
  '/:handle/projects/:key/plans',
  ...middlewares('plan', permissions.read_entity),
  plansValidator.getPlans,
  planController.getPlans,
);

router.get(
  '/:handle/projects/:key/plans/relations',
  ...middlewares('plan', permissions.read_entity),
  plansValidator.getPlanRelations,
  planController.getPlanRelations,
);

router.get(
  '/:handle/projects/:key/plans/:id/runs/count',
  ...middlewares('plan', permissions.read_entity),
  plansValidator.getPlan,
  planController.getRunsCountByPlan,
);

router.get(
  '/:handle/projects/:key/plans/:id/milestones/count',
  ...middlewares('plan', permissions.read_entity),
  plansValidator.getPlan,
  planController.getMilestonesCountByPlan,
);

router.patch(
  '/:handle/projects/:key/plans',
  ...middlewares('plan', permissions.write_entity),
  plansValidator.bulkUpdatePlans,
  planController.bulkUpdate,
);

router.get(
  '/:handle/projects/:key/plans/:id',
  ...middlewares('plan', permissions.read_entity),
  planController.getPlan,
  planController.getPlan,
);

router.patch(
  '/:handle/projects/:key/plans/:id',
  ...middlewares('plan', permissions.write_entity),
  plansValidator.getPlan,
  plansValidator.updatePlan,
  planController.updatePlan,
);

export default router;
