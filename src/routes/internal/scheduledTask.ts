import express, { Router } from 'express';
import { setResource, tenantContext } from '@app/middlewares/handle';
import { auth } from '@ss-libs/ss-component-auth';
import sharedStepController from '@app/controllers/scheduledTasks';

const router: Router = express.Router();

const middlewares = (resource: string = 'scheduledTask') => [
  setResource(resource),
  tenantContext(),
  auth().authenticateHybrid,
];

router.get(
  '/:handle/tasks/:id',
  ...middlewares('scheduledTask'),
  sharedStepController.getSchduledTask,
);
router.post(
  '/:handle/tasks/:id/run',
  ...middlewares('scheduledTask'),
  sharedStepController.runScheduledTask,
);
export default router;
