import express, { Router } from 'express';
import {
  validateCreateIntegration,
  validateOauthSetup,
  validateRemoveIntegrationErrors,
  validateUpdateIntegration,
} from '@app/validators/integration';

import { auth } from '@ss-libs/ss-component-auth';
import integrationController from '@app/controllers/integration';
import { permissions } from '@app/constants/auth';
import { tenantContext } from '@app/middlewares/handle';
import { enforceStorageLimit } from '@app/middlewares/attachment';

const router: Router = express.Router();
const middlewares = () => [tenantContext(), auth().authenticateHybrid];

router.post(
  '/:handle/integrations/:integrationUid/reauthenticate',
  ...middlewares(),
  auth().authz(permissions.write_integration),
  integrationController.reauthenticateToken,
);

router.get(
  '/:handle/integrations/jira/redirect',
  ...middlewares(),
  auth().authz(permissions.write_integration),
  integrationController.jiraRedirect,
);

router.get(
  '/:handle/integrations/:service/:integrationUid/personal/redirect',
  ...middlewares(),
  auth().authz(permissions.write_integration),
  integrationController.personalTokenRedirect,
);

router.get(
  '/app/oauth',
  validateOauthSetup,
  integrationController.handleOAuthResponse,
);

router.get(
  '/:handle/integrations/github/redirect',
  ...middlewares(),
  auth().authz(permissions.write_integration),
  integrationController.githubRedirect,
);

router.get(
  '/:handle/integrations/:integrationUid/data',
  ...middlewares(),
  auth().authz(permissions.read_integration),
  integrationController.getIntegrationData,
);

router.get(
  '/:handle/integrations/available',
  ...middlewares(),
  auth().authz(permissions.read_integration),
  integrationController.getAvailableIntegrations,
);

router.get(
  '/:handle/integrations',
  ...middlewares(),
  auth().authz(permissions.read_integration),
  integrationController.getIntegrations,
);

router.get(
  '/:handle/integrations/:integrationUid',
  ...middlewares(),
  auth().authz(permissions.read_integration),
  integrationController.getIntegration,
);

router.get(
  '/:handle/integrations/:integrationUid/projects',
  ...middlewares(),
  auth().authz(permissions.read_integration),
  integrationController.integrationServiceProjects,
);

router.get(
  '/:handle/integrations/:integrationUid/orgs',
  ...middlewares(),
  auth().authz(permissions.read_integration),
  integrationController.getOrganizations,
);

router.post(
  '/:handle/integrations',
  ...middlewares(),
  auth().authz(permissions.write_integration),
  validateCreateIntegration,
  integrationController.createIntegration,
);

router.patch(
  '/:handle/integrations/:integrationUid',
  ...middlewares(),
  auth().authz(permissions.write_integration),
  validateUpdateIntegration,
  integrationController.updateIntegration,
);

router.delete(
  '/:handle/integrations/:integrationUid',
  ...middlewares(),
  auth().authz(permissions.delete_integration),
  integrationController.deleteIntegration,
);

router.post(
  '/:handle/integrations/:integrationUid/attachments',
  ...middlewares(),
  auth().authz(permissions.write_integration),
  enforceStorageLimit,
  integrationController.uploadAttachment,
);

router.delete(
  '/:handle/integrations/:integrationUid/attachments/:id',
  ...middlewares(),
  auth().authz(permissions.write_integration),
  integrationController.deleteAttachment,
);

router.patch(
  '/:handle/integrations/:integrationUid/errors',
  ...middlewares(),
  auth().authz(permissions.write_integration),
  validateRemoveIntegrationErrors,
  integrationController.removeIntegrationErrors,
);

export default router;
