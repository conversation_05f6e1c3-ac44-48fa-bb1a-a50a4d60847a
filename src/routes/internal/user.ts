import { bindSharedModels, tenantContext } from '@app/middlewares/handle';
import express, { Router } from 'express';
import {
  validateUserSearch,
  validateUserUpdateProfile,
} from '@app/validators/user';

import { auth } from '@ss-libs/ss-component-auth';
import userController from '@app/controllers/user';
import { validateCreateAttachment } from '@app/validators/attachment';
import { enforceStorageLimit } from '@app/middlewares/attachment';

const router: Router = express.Router();

const middlewares = () => [
  bindSharedModels,
  auth().authenticateHybrid,
];

router.get(
  '/users/:id/orgs',
  ...middlewares(),
  userController.getOrgs,
);

router.put(
  '/avatar',
  ...middlewares(),
  validateCreateAttachment,
  enforceStorageLimit,
  userController.uploadAttachment,
);

router.put(
  '/profile',
  ...middlewares(),
  validateUserUpdateProfile,
  userController.updateProfile,
);

router.get(
  '/profile',
  ...middlewares(),
  userController.getProfile,
);

router.put(
  '/change-password',
  ...middlewares(),
  userController.changePassword,
);

router.get(
  '/users/search',
  ...middlewares(),
  validateUserSearch,
  userController.searchUsers,
);

router.delete(
  '/user/:id',
  ...middlewares(),
  userController.deleteUser,
);

router.post(
  '/user/authentication/create',
  ...middlewares(),
  userController.createAuthentication,
);

router.post(
  '/user/authentication/validation',
  ...middlewares(),
  userController.validateAuthentication,
);

router.delete(
  '/:handle/orgs/:orgHandle',
  tenantContext('orgHandle'),
  ...middlewares(),
  userController.leaveOrg,
);

export default router;
