import express, { Router } from 'express';
import { setResource, tenantContext } from '@app/middlewares/handle';
import { accessAttachment } from '@app/middlewares/attachment';
import {
  validateGetAttachment,
  validateGetAttachmentQuery,
  validateCleanupUploaded,
} from '@app/validators/attachment';

import attachmentController from '@app/controllers/attachment';
import { auth } from '@ss-libs/ss-component-auth';

const router: Router = express.Router();

const middlewares = (resource: string) => [
  setResource(resource),
  tenantContext(),
  auth().authenticateHybrid,
  accessAttachment(),
];

router.get(
  '/:handle/:type/attachments/:id/object',
  ...middlewares('project'),
  validateGetAttachment,
  validateGetAttachmentQuery,
  attachmentController.getAttachment,
);

router.get(
  '/:handle/attachments/size',
  ...middlewares('project'),
  attachmentController.checkPotentialMigrationSize,
);

router.delete(
  '/:handle/projects/:key/:type/attachments/:id/failed',
  ...middlewares('project'),
  validateCleanupUploaded,
  attachmentController.cleanupFailedUpload,
);

export default router;
