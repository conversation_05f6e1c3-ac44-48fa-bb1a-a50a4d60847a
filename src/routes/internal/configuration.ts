import express, { Router } from 'express';
import { setResource, tenantContext } from '@app/middlewares/handle';

import { auth } from '@ss-libs/ss-component-auth';
import configurationController from '@app/controllers/configuration';
import { permissions } from '@app/constants/auth';
import { validateCreateOrUpdateConfiguration } from '@app/validators/configuration';

const router: Router = express.Router();

const middlewares = (
  resource: string = 'configuration',
  ...permissions: string[]
) => [
  setResource(resource),
  tenantContext(),
  auth().authenticateHybrid,
  auth().authz(...permissions),
];

router.post(
  '/:handle/projects/:key/configurations',
  ...middlewares('tag', permissions.write_tag),
  validateCreateOrUpdateConfiguration,
  configurationController.createConfiguration,
);
router.get(
  '/:handle/projects/:key/configurations',
  ...middlewares('tag'),
  configurationController.getConfigurations,
);
router.get(
  '/:handle/projects/:key/configurations/:id',
  ...middlewares('tag'),
  configurationController.getConfiguration,
);
router.patch(
  '/:handle/projects/:key/configurations/:id',
  ...middlewares('tag', permissions.write_tag),
  validateCreateOrUpdateConfiguration,
  configurationController.updateConfiguration,
);
router.delete(
  '/:handle/projects/:key/configurations/:id',
  ...middlewares('tag', permissions.delete_tag),
  configurationController.deleteConfiguration,
);

export default router;
