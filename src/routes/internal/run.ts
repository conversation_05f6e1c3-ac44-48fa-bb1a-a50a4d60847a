import express, { Router } from 'express';
import { setResource, tenantContext } from '@app/middlewares/handle';
import {
  validateBulkUpdateRuns,
  validateCreateTestRun,
  validateDeleteRuns,
  validateDuplicateRuns,
  validateGetRunRelations,
  validateListRunsCasesQuery,
  validateListRunsQuery,
  validateTestRunUid,
  validateUpdateTestRun,
  validateUpdateTestRunCases,
} from '@app/validators/run';

import { auth } from '@ss-libs/ss-component-auth';
import { permissions } from '@app/constants/auth';
import runController from '@app/controllers/run';

const router: Router = express.Router();

const middlewares = (resource: string, ...permissions: string[]) => [
  setResource(resource),
  tenantContext(),
  auth().authenticateHybrid,
  auth().authz(...permissions),
];

router.post(
  '/:handle/projects/:key/runs',
  ...middlewares('run', permissions.read_entity),
  validate<PERSON>reateTestRun,
  runController.createTestRun,
);

router.post(
  '/:handle/projects/:key/runs/duplicate',
  ...middlewares('run', permissions.write_entity),
  validateDuplicateRuns,
  runController.duplicateTestRun,
);

router.patch(
  '/:handle/projects/:key/runs',
  ...middlewares('run', permissions.write_entity),
  validateBulkUpdateRuns,
  runController.updateTestRuns,
);

router.patch(
  '/:handle/projects/:key/runs',
  ...middlewares('run', permissions.write_entity),
  validateBulkUpdateRuns,
  runController.updateTestRuns,
);

router.patch(
  '/:handle/projects/:key/runs/:id',
  ...middlewares('run', permissions.write_entity),
  validateUpdateTestRun,
  validateTestRunUid,
  runController.updateTestRun,
);

router.get(
  '/:handle/projects/:key/runs/:id/cases/count',
  ...middlewares('run', permissions.read_entity),
  validateTestRunUid,
  runController.getCasesCountByRun,
);

router.get(
  '/:handle/projects/:key/runs',
  ...middlewares('run', permissions.read_entity),
  validateListRunsQuery,
  runController.getRuns,
);

router.get(
  '/:handle/projects/:key/runs/relations',
  ...middlewares('run', permissions.read_entity),
  validateGetRunRelations,
  runController.getRunRelations,
);

router.get(
  '/:handle/projects/:key/runs/count',
  ...middlewares('run', permissions.read_activity),
  runController.getRunsCountByProject,
);

router.get(
  '/:handle/projects/:key/runs/:id',
  ...middlewares('run', permissions.read_activity),
  validateTestRunUid,
  runController.getRun,
);

router.get(
  '/:handle/projects/:key/runs/:id/folders',
  ...middlewares('run', permissions.read_activity),
  validateTestRunUid,
  runController.getRunFolders,
);

router.get(
  '/:handle/projects/:key/runs/:id/executions',
  ...middlewares('run', permissions.read_activity),
  validateTestRunUid,
  runController.getRunExecutions,
);

router.delete(
  '/:handle/projects/:key/runs',
  ...middlewares('run', permissions.delete_entity),
  validateDeleteRuns,
  runController.deleteTestRuns,
);

router.get(
  '/:handle/projects/:key/runs/:id/cases',
  ...middlewares('run', permissions.read_entity),
  validateTestRunUid,
  validateListRunsCasesQuery,
  runController.getRunCases,
);

router.patch(
  '/:handle/projects/:key/runs/:id/cases',
  ...middlewares('run', permissions.read_entity),
  validateTestRunUid,
  validateUpdateTestRunCases,
  runController.updateTestRunCases,
);

// get test runs related to test case
// router.get(
//   '/:handle/projects/:key/cases/:id/runs',
//   ...middlewares('case', permissions.read_entity),
//   runController.getRunsRelatedToTestCase,
// );

// router.get(
//   '/:handle/projects/:key/cases/:caseId/executions/runs',
//   ...middlewares('run', permissions.read_run),
//   runController.getRunsByCaseThroughExecutions,
// );

export default router;
