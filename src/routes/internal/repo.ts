import express, { Router } from 'express';
import { setResource, tenantContext } from '@app/middlewares/handle';
import { validateCreateBranch, validateCreateRepo } from '@app/validators/repo';

import { auth } from '@ss-libs/ss-component-auth';
import { isUUID } from '@app/middlewares/validator';
import { permissions } from '@app/constants/auth';
import repoController from '@app/controllers/repo';

const router: Router = express.Router();
router.use(setResource('project'));

const middlewares = () => [
  tenantContext(),
  auth().authenticateHybrid,
];

router.post(
  '/:handle/projects/:key/repos',
  ...middlewares(),
  auth().authz(permissions.write_repo),
  validateCreateRepo,
  repoController.createRepo,
);

router.get(
  '/:handle/projects/:key/repos',
  ...middlewares(),
  auth().authz(permissions.write_repo),
  repoController.getRepos,
);

router.post(
  '/:handle/projects/:key/repos/:id/branches',
  ...middlewares(),
  auth().authz(permissions.write_branch),
  validateCreateBranch,
  isUUID('id'),
  repoController.createBranch,
);

router.get(
  '/:handle/projects/:key/repos/:id/branches',
  ...middlewares(),
  auth().authz(),
  isUUID('id'),
  repoController.getBranches,
);

export default router;
