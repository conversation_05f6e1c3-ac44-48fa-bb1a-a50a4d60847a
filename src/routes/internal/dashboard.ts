import {
  dashboardValidator,
  validateUpdateDashboard,
  validateUpdate<PERSON><PERSON>s,
  chartValidator,
  createValidator,
} from '@app/validators/dashboard';
import express, { Router } from 'express';
import { setResource, tenantContext, accessableProjects } from '@app/middlewares/handle';

import { auth } from '@ss-libs/ss-component-auth';
import dashboardController from '@app/controllers/dashboard';
import { permissions } from '@app/constants/auth';

const router: Router = express.Router();

const middlewares = (resource: string, requiresAccessibleProjectsCheck: boolean = false, ...permissions: string[]) => [
  setResource(resource),
  tenantContext(),
  auth().authenticateHybrid,
  ...(requiresAccessibleProjectsCheck ? [accessableProjects()] : [auth().authz(...permissions)]),
];

router.get(
  '/:handle/dashboards/overview',
  ...middlewares('dashboard', true),
  dashboardController.dashboardOverview,
);
router.get(
  '/:handle/projects/:key/dashboards/overview',
  ...middlewares('dashboard', false, permissions.read_dashboard),
  dashboardController.dashboardOverview,
);

router.get(
  '/:handle/dashboards/list',
  ...middlewares('dashboard', true),
  dashboardController.getDashboards,
);

router.get(
  '/:handle/projects/:key/dashboards/list',
  ...middlewares('dashboard', false, permissions.read_dashboard),
  dashboardController.getDashboards,
);

router.delete(
  '/:handle/dashboards/:uid',
  ...middlewares('dashboard', false, permissions.delete_dashboard),
  dashboardController.deleteDashboard,
);
router.get(
  '/:handle/dashboards/:uid?/chart/:chartId',
  ...middlewares('dashboard', true),
  chartValidator,
  dashboardController.fetchSingleDashboardChart,
);
router.get(
  '/:handle/projects/:key/dashboards/:uid?/chart/:chartId',
  ...middlewares('dashboard', false, permissions.read_dashboard),
  chartValidator,
  dashboardController.fetchSingleDashboardChart,
);

router.get(
  '/:handle/dashboards/:uid?',
  ...middlewares('dashboard', true),
  dashboardValidator,
  dashboardController.getDashboard,
);

router.get(
  '/:handle/projects/:key/dashboards/:uid?',
  ...middlewares('dashboard', false, permissions.read_dashboard),
  dashboardValidator,
  dashboardController.getDashboard,
);

router.post(
  '/:handle/dashboards',
  ...middlewares('dashboard', false, permissions.write_dashboard),
  createValidator,
  dashboardController.createDashboard,
);

router.patch(
  ['/:handle/dashboards/:uid/charts', '/:handle/projects/:key/dashboards/:uid/charts'],
  ...middlewares('dashboard', false, permissions.write_dashboard),
  validateUpdateCharts,
  dashboardController.updateCharts,
);

router.patch(
  '/:handle/dashboards/:uid',
  ...middlewares('dashboard', false, permissions.write_dashboard),
  validateUpdateDashboard,
  dashboardController.updateDashboard,
);

export default router;
