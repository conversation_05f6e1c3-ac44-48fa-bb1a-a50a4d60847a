import express, { Router } from 'express';

import { auth } from '@ss-libs/ss-component-auth';
import defectController from '@app/controllers/defect';
import { tenantContext } from '@app/middlewares/handle';
import { permissions } from '@app/constants/auth';
import defectValidator from '@app/validators/defect';
import { enforceStorageLimit } from '@app/middlewares/attachment';

const router: Router = express.Router();

const middlewares = (...permissions: string[]) => [
  tenantContext(),
  auth().authenticateHybrid,
  auth().authz(...permissions),
];

router.get(
  '/:handle/projects/:key/defects/priorities',
  ...middlewares(),
  defectController.getDefectPriorities,
);

router.get(
  '/:handle/projects/:key/defects/statuses',
  ...middlewares(),
  defectController.getDefectStatuses,
);

router.get(
  '/:handle/projects/:key/defects/statuses/scope',
  ...middlewares(),
  defectController.getDefectStatusByScope,
);

router.get(
  '/:handle/projects/:key/defects',
  ...middlewares(permissions.read_defect),
  defectController.getDefects,
);

router.get(
  '/:handle/projects/:key/defects/open/count',
  ...middlewares(permissions.read_defect),
  defectValidator.validateDefectOpenCountRequired,
  defectController.getDefectsOpenCount,
);

router.get(
  '/:handle/projects/:key/defects/closed/count',
  ...middlewares(permissions.read_defect),
  defectValidator.validateDefectClosedCountRequired,
  defectController.getDefectsClosedCount,
);

router.get(
  '/:handle/projects/:key/defects/:uid/attachments',
  ...middlewares(permissions.read_defect),
  defectController.getDefectAttachments,
);
router.get(
  '/:handle/projects/:key/defects/:uid/executions',
  ...middlewares(permissions.read_defect),
  defectController.getDefectExecutions,
);
router.get(
  '/:handle/projects/:key/defects/:uid/runs',
  ...middlewares(permissions.read_defect),
  defectController.getDefectRuns,
);

router.get(
  '/:handle/projects/:key/defects/count',
  ...middlewares(permissions.read_defect),
  defectController.getDefectsCountByProject,
);

router.get(
  '/:handle/projects/:key/defects/:uid',
  ...middlewares(permissions.read_defect),
  defectController.getDefect,
);

router.post(
  '/:handle/projects/:key/defects',
  ...middlewares(permissions.write_defect),
  defectController.createDefect,
);

router.patch(
  '/:handle/projects/:key/defects/:uid',
  ...middlewares(permissions.write_defect),
  defectController.updateDefect,
);

router.post(
  '/:handle/projects/:key/defects/:uid/link',
  ...middlewares(permissions.write_defect),
  defectController.linkDefectToExecution,
);

router.post(
  '/:handle/projects/:key/defects/:uid/attachments',
  ...middlewares(permissions.write_defect),
  enforceStorageLimit,
  defectController.uploadAttachment,
);

router.delete(
  '/:handle/projects/:key/defects/:uid/attachments/:attachmentUid',
  ...middlewares(permissions.write_defect),
  defectController.deleteAttachment,
);

export default router;
