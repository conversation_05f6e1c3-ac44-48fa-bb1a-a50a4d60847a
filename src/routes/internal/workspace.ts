import workspaceController from '@app/controllers/workspace';
import {
  accessableProjects,
  setResource,
  tenantContext,
} from '@app/middlewares/handle';
import workSpaceValidator from '@app/validators/workSpace';
import { auth } from '@ss-libs/ss-component-auth';
import express, { Router } from 'express';

const middlewares = () => [
  setResource('execution'),
  tenantContext(),
  auth().authenticateHybrid,
  accessableProjects(),
];
const router: Router = express.Router();

router.get(
  '/:handle/workspace/executions',
  ...middlewares(),
  workSpaceValidator.validateGetWorkspaceExecutionQuery,
  workspaceController.getWorkspaceExecutions,
);

router.get(
  '/:handle/workspace/overview',
  ...middlewares(),
  workspaceController.getWorkspaceOverview,
);

export default router;
