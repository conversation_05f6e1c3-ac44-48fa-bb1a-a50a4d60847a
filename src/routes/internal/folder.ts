import express, { Router } from 'express';
import { setResource, tenantContext } from '@app/middlewares/handle';
import {
  validateCreateFolder,
  validateQueryFolder,
  validateUpdateFolder,
} from '@app/validators/folder';

import { auth } from '@ss-libs/ss-component-auth';
import folderController from '@app/controllers/folder';
import { permissions } from '@app/constants/auth';

const router: Router = express.Router();

const middlewares = (authResource: string = 'folder') => [
  setResource(authResource),
  tenantContext(),
  auth().authenticateHybrid,
];

router.post(
  '/:handle/projects/:key/folders',
  ...middlewares(),
  auth().authz(permissions.write_entity),
  validateCreateFolder,
  folderController.createFolder,
);

router.get(
  '/:handle/projects/:key/folders',
  ...middlewares('project'),
  auth().authz(permissions.read_entity),
  validateQ<PERSON>y<PERSON>older,
  folderController.getProjectFolders,
);

router.get(
  '/:handle/projects/:key/folders/search',
  ...middlewares(),
  auth().authz(permissions.read_entity),
  folderController.searchFolders,
);

router.get(
  '/:handle/projects/:key/folders/:id',
  ...middlewares(),
  auth().authz(permissions.read_entity),
  folderController.getFolder,
);

router.patch(
  '/:handle/projects/:key/folders/:id',
  ...middlewares(),
  auth().authz(permissions.write_entity),
  validateUpdateFolder,
  folderController.updateFolder,
);

router.delete(
  '/:handle/projects/:key/folders/:id',
  ...middlewares(),
  auth().authz(permissions.delete_entity),
  folderController.deleteFolder,
);

export default router;
