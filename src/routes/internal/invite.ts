import express, { Router } from 'express';
import {
  setResource, tenantContext,
} from '@app/middlewares/handle';
import { auth } from '@ss-libs/ss-component-auth';
import inviteValidator from '@app/validators/invite';
import { permissions } from '@app/constants/auth';
import inviteController from '@app/controllers/invite';

const router: Router = express.Router();

const baseMiddlewares = (...permissions: string[]) => [
  tenantContext(),
  auth().authenticateHybrid,
  auth().authz(...permissions),
];

router.get(
  '/orgs/:handle/invite/:token',
  setResource('org'),
  tenantContext(),
  inviteValidator.findOne,
  inviteController.getInvite,
);

router.post(
  '/orgs/:handle/invite/:token/accept',
  tenantContext(),
  auth().authenticateHybrid,
  inviteController.acceptInvite,
);

router.post(
  '/orgs/:handle/invite/:token/decline',
  tenantContext(),
  auth().authenticateHybrid,
  inviteController.declineInvite,
);

router.post(
  ['/:handle/invite', '/:handle/projects/:key/invite'],
  ...baseMiddlewares(permissions.write_member),
  inviteValidator.validateInvite,
  inviteController.newInvite,
);

router.post(
  ['/:handle/invite/resend', '/:handle/projects/:key/invite/resend'],
  ...baseMiddlewares(permissions.write_member),
  inviteValidator.bulkResend,
  inviteController.resendInvite,
);

router.get(
  ['/:handle/invites', '/:handle/projects/:key/invites'],
  ...baseMiddlewares(permissions.read_member),
  inviteValidator.findAll,
  inviteController.getPendingInvites,
);

router.delete(
  ['/:handle/invite', '/:handle/projects/:key/invite'],
  ...baseMiddlewares(permissions.write_member),
  inviteController.deleteInviteByEmail,
);

router.patch(
  ['/:handle/invites', '/:handle/projects/:key/invites'],
  ...baseMiddlewares(permissions.write_member),
  inviteValidator.bulkUpdateInvites,
  inviteController.bulkUpdateInvite,
);

export default router;
