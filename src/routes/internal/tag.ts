import express, { Router } from 'express';
import { setResource, tenantContext } from '@app/middlewares/handle';
import {
  validateCreateTag,
  validateListTags,
  validateUpdateTag,
} from '@app/validators/tag';

import { auth } from '@ss-libs/ss-component-auth';
import { permissions } from '@app/constants/auth';
import tagController from '@app/controllers/tag';

const router: Router = express.Router();

const middlewares = (resource: string = 'tag', ...permissions: string[]) => [
  setResource(resource),
  tenantContext(),
  auth().authenticateHybrid,
  auth().authz(...permissions),
];

router.get(
  '/:handle/tags',
  ...middlewares('tag'),
  validateListTags,
  tagController.getTags,
);

router.post(
  '/:handle/tags',
  ...middlewares('tag', permissions.write_tag),
  validateCreateTag,
  tagController.createTag,
);

router.patch(
  '/:handle/tags/:id',
  ...middlewares('tag', permissions.write_tag),
  validateUpdateTag,
  tagController.updateTag,
);

router.delete(
  '/:handle/tags/:id',
  ...middlewares('tag', permissions.delete_tag),
  tagController.deleteTag,
);

router.get(
  '/:handle/projects/:key/tags',
  ...middlewares('tag'),
  tagController.getTags,
);

router.post(
  '/:handle/projects/:key/tags',
  ...middlewares('tag', permissions.write_tag),
  validateCreateTag,
  tagController.createTag,
);

router.patch(
  '/:handle/projects/:key/tags/:id',
  ...middlewares('tag', permissions.write_tag),
  validateUpdateTag,
  tagController.updateTag,
);

router.delete(
  '/:handle/projects/:key/tags/:id',
  ...middlewares('tag', permissions.delete_tag),
  tagController.deleteTag,
);

export default router;
