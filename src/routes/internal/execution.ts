import express, { Router } from 'express';
import {
  accessableProjects,
  setResource,
  tenantContext,
} from '@app/middlewares/handle';
import {
  validateBulkUpdateExecutions,
  validateDeleteSteps,
  validateExecutionUid,
  validateGetExecutions,
  validateUpdateExecution,
  validateGetMilestonesQuery,
  validateGetTestPlansQuery,
  validateGetTestRunsQuery,
  validateGetExecutionCountQuery,
  validateGetExecutionsRelationsQuery,
} from '@app/validators/execution';

import { auth } from '@ss-libs/ss-component-auth';
import executionController from '@app/controllers/execution';
import { permissions } from '@app/constants/auth';
import { enforceStorageLimit } from '@app/middlewares/attachment';

const router: Router = express.Router();

const middlewares = (resource: string, ...permissions: string[]) => [
  setResource(resource),
  tenantContext(),
  auth().authenticateHybrid,
  auth().authz(...permissions),
];

const generalMiddlewares = () => [
  setResource('execution'),
  tenantContext(),
  auth().authenticateHybrid,
  accessableProjects(),
];

router.get(
  '/:handle/executions/users',
  ...generalMiddlewares(),
  executionController.getExecutionsUsers,
);

router.get(
  '/:handle/executions/projects',
  ...generalMiddlewares(),
  executionController.getExecutionsProjects,
);

router.get(
  '/:handle/executions/count',
  ...generalMiddlewares(),
  validateGetExecutionCountQuery,
  executionController.getExecutionsCountByStatus,
);

router.get(
  '/:handle/executions/milestones',
  ...generalMiddlewares(),
  validateGetMilestonesQuery,
  executionController.getExecutionsMilestones,
);

router.get(
  '/:handle/executions/plans',
  ...generalMiddlewares(),
  validateGetTestPlansQuery,
  executionController.getExecutionsTestPlans,
);

router.get(
  '/:handle/executions/runs',
  ...generalMiddlewares(),
  validateGetTestRunsQuery,
  executionController.getExecutionsTestRuns,
);

router.get(
  '/:handle/executions/relations',
  ...generalMiddlewares(),
  validateGetExecutionsRelationsQuery,
  executionController.getExecutionsRelations,
);

router.post(
  '/:handle/projects/:key/executions',
  ...middlewares('execution', permissions.write_activity),
  executionController.createExecution,
);

router.get(
  '/:handle/projects/:key/executions',
  ...middlewares('execution', permissions.read_activity),
  validateGetExecutions,
  executionController.getExecutions,
);

router.patch(
  '/:handle/projects/:key/executions/:id',
  ...middlewares('execution', permissions.write_activity),
  validateUpdateExecution,
  executionController.updateExecution,
);
router.patch(
  '/:handle/projects/:key/executions',
  ...middlewares('execution', permissions.write_activity),
  validateBulkUpdateExecutions,
  executionController.updateExecutions,
);
router.get(
  '/:handle/projects/:key/executions/:id',
  ...middlewares('execution', permissions.read_activity),
  executionController.getExecution,
);

router.delete(
  '/:handle/projects/:key/executions/:id',
  ...middlewares('execution', permissions.delete_activity),
  validateExecutionUid,
  executionController.deleteExecution,
);

router.delete(
  '/:handle/projects/:key/executions/:id/steps',
  ...middlewares('execution', permissions.delete_activity),
  validateExecutionUid,
  validateDeleteSteps,
  executionController.deleteSteps,
);

router.post(
  '/:handle/projects/:key/executions/:id/attachments',
  ...middlewares('execution', permissions.write_activity),
  enforceStorageLimit,
  executionController.uploadAttachment,
);

router.delete(
  '/:handle/projects/:key/executions/attachments/:id',
  ...middlewares('execution', permissions.write_activity),
  executionController.deleteAttachment,
);

export default router;
