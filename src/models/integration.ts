import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import { Attachment } from './attachment';

export class Integration extends Model {
  uid: number;

  name: string;

  description: string;

  service: string;

  type: string;

  configuration: Record<string, any>;

  entityTypes: string[];

  projectUids: number[];

  archivedAt: Date | string;

  avatarAttachmentUid: string | null;

  status: string;

  errorCount: number;

  externalErrors: Record<string, any>[];

  syncedAt: Date | string;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'integrations';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['name', 'service', 'type', 'configuration'],
      properties: {
        uid: { type: 'integer' },
        name: { type: 'string' },
        description: { type: 'string' },
        service: { type: 'string' },
        type: { type: 'string' },
        configuration: { type: 'object' },
        entityTypes: { type: 'array', items: { type: 'string' } },
        projectUids: { type: 'array', items: { type: 'integer' } },
        avatarAttachmentUid: { type: ['string', 'null'] },
        status: { type: 'string', enum: ['active', 'inactive', 'error'] },
        errorCount: { type: 'integer' },
        externalErrors: { type: 'array', items: { type: 'object' } },
        archivedAt: { type: ['string', 'null'], format: 'date-time' },
        syncedAt: { type: ['string', 'null'], format: 'date-time' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: ['string', 'null'], format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      attachments: {
        relation: Model.ManyToManyRelation,
        modelClass: Attachment,
        join: {
          from: 'integrations.uid',
          through: {
            from: 'integrationAttachments.integrationUid',
            to: 'integrationAttachments.attachmentUid',
          },
          to: 'attachments.uid',
        },
      },
    };
  }
}

export type IntegrationModel = ModelClass<Integration>;
