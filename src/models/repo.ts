import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import { RepoBranch } from './repoBranch';
import { TestCase } from './testCase';

export class Repo extends Model {
  uid: string;

  externalId: string;

  source: string;

  name: string;

  link: string;

  customFields: Record<string, any>;

  projectUid: number;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'repos';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['externalId', 'source', 'name', 'projectUid'],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        projectUid: { type: 'integer' },
        externalId: { type: 'string', maxLength: 255 },
        source: { type: 'string', maxLength: 255 },
        name: { type: 'string', maxLength: 255 },
        link: { type: 'string', maxLength: 255 },
        customFields: { type: 'object' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      repoBranches: {
        relation: Model.HasManyRelation,
        modelClass: RepoBranch,
        join: {
          from: 'repos.uid',
          to: 'repoBranches.repoUid',
        },
      },
      testCases: {
        relation: Model.HasManyRelation,
        modelClass: TestCase,
        join: {
          from: 'repos.uid',
          to: 'testCases.repoUid',
        },
      },
    };
  }
}

export type RepoModel = ModelClass<Repo>;
