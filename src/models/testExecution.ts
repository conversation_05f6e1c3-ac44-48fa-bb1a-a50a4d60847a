import { ModelClass, ModelObject } from 'objection';

import { Model } from '@app/lib/model';
import { UpdateExecutionDTO } from '@app/types/execution';
import { Knex } from 'knex';
import _ from 'lodash';
import { Attachment } from './attachment';
import { Defect } from './defect';
import { Project } from './project';
import { RepoBranch } from './repoBranch';
import { Tag } from './tag';
import { TestCase } from './testCase';
import { TestExecutionStep } from './testExecutionStep';
import { TestRun } from './testRun';
import { TestMilestone } from './testMilestone';

export class TestExecution extends Model {
  uid: number;

  name?: string;

  /**
   * folder the execution belongs to
   */
  parentUid?: number;

  externalId: string;

  source: string;

  link: string;

  customFields: CustomFields;

  externalCreatedAt: Date | string;

  externalUpdatedAt: Date | string;

  lastAssignedAt: Date | string;

  dueAt: Date | string;

  result: string;

  status: number | null;

  testCaseRef: number;

  testRunUid: number;

  repoBranchUid: string;

  priority: number | null;

  assignedTo: string;

  testCaseUid: number;

  projectUid: number;

  templateFields?: TemplateField[];

  /**
   * general expected result for the execution.
   */
  expectedResult?: string;

  tags?: Tag[];

  attachments?: Attachment[];

  testCase?: TestCase;

  steps?: TestExecutionStep[];

  static jsonAttributes = ['steps', 'templateFields'];

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'testExecutions';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      properties: {
        uid: { type: 'integer' },
        externalId: { type: ['string', 'null'], maxLength: 255 },
        source: { type: ['string', 'null'] },
        link: { type: ['string', 'null'] },
        customFields: { type: ['object', 'null'] },
        externalCreatedAt: { type: ['string', 'null'], format: 'date-time' },
        externalUpdatedAt: { type: ['string', 'null'], format: 'date-time' },
        status: { type: 'integer' },
        testCaseRef: { type: 'integer' },
        testRunUid: { type: 'integer' },
        testRepoBranchUid: { type: 'string', format: 'uuid' },
        lastAssignedAt: { type: ['string', 'null'], format: 'date-time' },
        dueAt: { type: ['string', 'null'], format: 'date-time' },
        deletedAt: { type: ['string', 'null'], format: 'date-time' },
        priority: { type: 'integer' },
        assignedTo: { type: ['string', 'null'] },
        projectUid: { type: 'integer' },
      },
    };
  }

  static get relationMappings() {
    return {
      project: {
        relation: Model.BelongsToOneRelation,
        modelClass: Project,
        join: {
          from: 'testExecutions.projectUid',
          to: 'projects.uid',
        },
      },
      steps: {
        relation: Model.HasManyRelation,
        modelClass: TestExecutionStep,
        join: {
          from: 'testExecutions.uid',
          to: 'testExecutionSteps.testExecutionUid',
        },
      },
      testCase: {
        relation: Model.BelongsToOneRelation,
        modelClass: TestCase,
        join: {
          from: 'testExecutions.testCaseUid',
          to: 'testCases.uid',
        },
      },
      testRun: {
        relation: Model.BelongsToOneRelation,
        modelClass: TestRun,
        join: {
          from: 'testExecutions.testRunUid',
          to: 'tags.uid',
        },
      },
      repoBranches: {
        relation: Model.BelongsToOneRelation,
        modelClass: RepoBranch,
        join: {
          from: 'testExecutions.repoBranchUid',
          to: 'repoBranches.uid',
        },
      },
      attachments: {
        relation: Model.ManyToManyRelation,
        modelClass: Attachment,
        join: {
          from: 'testExecutions.uid',
          through: {
            from: 'executionAttachments.executionUid',
            to: 'executionAttachments.attachmentUid',
          },
          to: 'attachments.uid',
        },
      },
      defects: {
        relation: Model.ManyToManyRelation,
        modelClass: Defect,
        join: {
          from: 'testExecutions.uid',
          through: {
            from: 'defectExecutions.executionUid',
            to: 'defectExecutions.defectUid',
            extra: ['executionUrl'],
          },
          to: 'defects.uid',
        },
      },
      tags: {
        relation: Model.ManyToManyRelation,
        modelClass: Tag,
        join: {
          from: 'testExecutions.testCaseRef',
          through: {
            from: 'testCaseTags.testCaseRef',
            to: 'testCaseTags.tagUid',
          },
          to: 'tags.uid',
        },
      },
    };
  }

  /**
   * determines the progress from a list of executions
   * @param execs list of executions
   * @param statuses list of complete statuses
   * @param hasCompletedRuns indicates if the parent of these executions is marked as complete
   * @returns
   */
  static execProgress(
    execs: TestExecution[],
    statuses: number[],
    hasCompletedRuns: boolean,
  ) {
    const completedFreq = statuses.reduce((acc, c) => {
      acc.add(c);
      return acc;
    }, new Set<number>());

    const freq: Record<number, number> = {};
    let completedCount = 0;

    for (const e of execs) {
      if (!_.isNull(e.deletedAt)) continue;

      freq[e.status] = (freq[e.status] ?? 0) + 1;
      if (completedFreq.has(e.status)) {
        completedCount++;
      }
    }

    let progress = 0;
    if (execs.length > 0) {
      progress = Math.round((completedCount / execs.length) * 100);
    } else if (hasCompletedRuns) {
      progress = 100;
    }

    return { progress, execCount: execs.length, frequency: freq };
  }

  static async updateOne(
    trx: Knex.Transaction,
    uid: number,
    update: UpdateExecutionDTO,
  ) {
    const exec = await TestExecution.query(trx).findById(uid);
    if (!exec) return null;

    const patch: Partial<ModelObject<TestExecution>> = {};
    if (update.priority) patch.priority = update.priority;
    if (update.status) patch.status = update.status;
    if (update.dueAt) patch.dueAt = update.dueAt.toISOString();

    if (update.assignedTo && exec.assignedTo !== update.assignedTo) {
      patch.assignedTo = update.assignedTo;
      patch.lastAssignedAt = trx.fn.now() as any;
    }
    if (update.name) patch.name = update.name;

    if (update.templateFields) patch.templateFields = update.templateFields;

    const tagUids = new Set([
      ...(update.tagUids ?? []),
      ...(exec.customFields?.tags?.map((t) => t.uid) ?? []),
    ]);
    for (const r of update.tagReplacements) {
      if (r.existingTagUids?.length > 0) {
        r.existingTagUids.forEach((u) => tagUids.delete(u));
      }
      if (r.newTagUids?.length > 0) {
        r.newTagUids.forEach((u) => tagUids.add(u));
      }
    }

    patch.customFields = {
      ...exec.customFields,
      tags: Array.from(tagUids).map((uid) => ({ uid })),
      // we only update these fields for backward compatibility
      name: update.name ?? exec.customFields?.name,
      steps: update.steps ?? exec.customFields?.steps,
      templateFields:
        update.templateFields ?? exec.customFields?.templateFields,
    };

    await TestExecution.updateSteps(trx, exec.uid, update.steps ?? []);

    return TestExecution.query(trx)
      .patchAndFetchById(uid, patch)
      .returning('*');
  }

  static formatAttachment(exec: TestExecution, baseUrl: string, pathPrefix: string = 'cases') {
    if (_.has(exec, 'attachments')) {
      exec.attachments = exec.attachments.map(
        ({ uid, name, fileType }) => ({
          previewUrl: `${baseUrl}/${pathPrefix}/attachments/${uid}/object`,
          name,
          type: fileType,
          uid,
        }) as unknown as Attachment,
      );
    }
  }

  static async insertStep(
    trx: Knex.Transaction,
    step: any,
    position: number,
    execUid: number,
    parentStepUid?: string,
  ) {
    const savedStep = await TestExecutionStep.query(trx)
      .insert({
        position,
        testExecutionUid: execUid,
        description: step.description,
        expectedResult: step.expectedResult,
        title: step.title,
        sharedTestStepUid: step.sharedStepUid,
        ...(parentStepUid && { parentStepUid }),
      })
      .returning('*');
    if (step.children && step.children?.length > 0) {
      for (let i = 0; i < step.children.length; i++) {
        await TestExecution.insertStep(
          trx,
          step.children[i],
          i + 1,
          execUid,
          savedStep.uid,
        );
      }
    }
  }

  static async updateSteps(
    trx: Knex.Transaction,
    execUid: number,
    incoming: UpdateExecutionDTO['steps'],
  ) {
    const newSteps: UpdateExecutionDTO['steps'] = [];
    const existingSteps: Record<string, UpdateExecutionDTO['steps'][number]> = {};

    for (const s of incoming) {
      if (s.uid) existingSteps[s.uid] = s;
      else newSteps.push(s);
    }

    for (const ns of newSteps) {
      await TestExecution.insertStep(trx, ns, ns.position, execUid);
    }
    if (Object.keys(existingSteps).length === 0) return;

    const currentSteps = await TestExecutionStep.query(trx)
      .where({ testExecutionUid: execUid, deletedAt: null })
      .whereIn('uid', Object.keys(existingSteps));

    for (const currentVersion of currentSteps) {
      const newVersion = existingSteps[currentVersion.uid];

      const patch: Record<string, any> = {};

      for (const field of [
        'title',
        'description',
        'expectedResult',
        'position',
        'status',
      ]) {
        if (newVersion[field] !== currentVersion[field]) {
          patch[field] = newVersion[field];
        }
      }

      if (Object.keys(patch).length !== 0) {
        await currentVersion.$query(trx).patch(patch);
      }
    }
  }

  /**
   * retrieves a test Cases, and geneates a test execution (and its steps) from it
   * @param db
   * @param testCase
   */
  static async createFromTestCase(
    db: Knex.Transaction,
    testCase: TestCase | number,
    opts: { runUid?: number; status?: number; priority?: number },
  ): Promise<TestExecution> {
    let source: TestCase;
    if (typeof testCase === 'number') {
      source = await TestCase.query(db).findById(testCase);
    } else {
      source = testCase;
    }
    if (!source) return null;

    const exec = await TestExecution.query(db)
      .insert({
        ...(opts.status && { status: opts.status }),
        ...(opts.priority && { priority: opts.priority }),
        ...(opts.runUid && { testRunUid: opts.runUid }),
        name: source.name,
        testCaseRef: source.testCaseRef,
        testCaseUid: source.uid,
        projectUid: source.projectUid,
        parentUid: source.parentUid,
        templateFields: source.customFields?.templateFields,
        expectedResult: source.customFields?.expectedResult,
      })
      .returning('*');
    if (!exec) return null;

    if (source.steps && source.steps.length > 0) {
      for (let i = 0; i < source.steps.length; i++) {
        await TestExecution.insertStep(db, source.steps[i], i + 1, exec.uid);
      }
    }

    const attachmentUids = await db('caseAttachments')
      .where({ caseRef: source.testCaseRef })
      .select('attachmentUid');

    if (attachmentUids.length > 0) {
      await db('executionAttachments')
        .insert(attachmentUids.map((u) => ({
          attachmentUid: u.attachmentUid,
          executionUid: exec.uid,
        })));
    }

    return exec;
  }

  static async getTestRuns(db: Knex, executionUids: number[]) {
    const testRuns = await TestExecution.query(db)
      .whereIn('uid', executionUids)
      .withGraphFetched('testRun')
      .select('*') as any[];

    const result: Record<number, TestRun> = {};
    for (const tr of testRuns) {
      result[tr.uid] = tr.testRun;
    }
    return result;
  }

  static async getTestPlans(db: Knex, executionUids: number[]) {
    const testRuns = await TestExecution.query(db)
      .whereIn('uid', executionUids)
      .withGraphFetched('testRun.testPlans')
      .select('*') as any[];

    const result: Record<number, TestPlan[]> = {};
    for (const tr of testRuns) {
      result[tr.uid] = tr.testRun.testPlans;
    }
    return result;
  }

  static async getMilestones(trx: Knex, executionUids: number[]) {
    const testExecutions = await TestExecution.query(trx)
      .whereIn('uid', executionUids)
      .withGraphFetched('testRun.testPlans')
      .select('*') as any[];

    if (!testExecutions.length) return {};

    const result: Record<number, TestMilestone[]> = {};

    for (const execution of testExecutions) {
      const runMilestones = await TestMilestone.query(trx)
        .innerJoin(
          'testMilestoneRuns',
          'tags.uid',
          'testMilestoneRuns.milestoneUid',
        )
        .where('testMilestoneRuns.runUid', execution.testRun.uid)
        .whereNull('testMilestoneRuns.deletedAt')
        .select(
          'tags.uid as milestoneUid',
          'tags.name',
        ) as any[];

      const planMilestones = await TestMilestone.query(trx)
        .innerJoin(
          'testMilestonePlans',
          'tags.uid',
          'testMilestonePlans.milestoneUid',
        )
        .whereIn('testMilestonePlans.planUid', execution.testRun.testPlans.map((p) => p.uid))
        .whereNull('testMilestonePlans.deletedAt')
        .select(
          'tags.uid as milestoneUid',
          'tags.name',
        ) as any[];

      result[execution.uid] = _.uniqBy(
        [...planMilestones, ...runMilestones],
        'uid',
      );
    }

    return result;
  }

  static async getTags(db: Knex, executionUids: number[]) {
    const executions = await TestExecution.query(db)
      .whereIn('uid', executionUids)
      .select('uid', 'customFields') as any[];

    for (const execution of executions) {
      execution.tags = await Tag.findByIds(
        db,
        execution.customFields?.tags?.map((t) => t.uid),
      );
    }

    const result: Record<number, Tag[]> = {};
    for (const e of executions) {
      result[e.uid] = e.tags;
    }
    return result;
  }

  static async getProjects(db: Knex, executionUids: number[]) {
    const projects = await TestExecution.query(db)
      .whereIn('uid', executionUids)
      .withGraphFetched('project')
      .select('*') as any[];

    const result: Record<number, Project[]> = {};
    for (const tr of projects) {
      result[tr.uid] = tr.project;
    }
    return result;
  }
}

interface CustomFields {
  name?: string;
  /**
   * @todo swith to `tagUids` instead
   */
  tags?: { uid: number; name?: string }[];
  case?: any;
  steps?: any[];
  templateFields?: any;
  externalFields?: any;
}

interface TemplateField {
  id: string;
  name: string;
  options: string[];
  dataType: string;
  defaultValue: string;
  default_date?: string;
  value: string;
  [k: string]: any;
}

export type TestExecutionModel = ModelClass<TestExecution> &
  typeof TestExecution;
