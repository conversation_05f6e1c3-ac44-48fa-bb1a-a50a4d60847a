import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import { User } from './user';
import { Attachment } from './attachment';

const entityTypes = <const>['case', 'milestone', 'execution', 'result', 'run'];
type EntityType = (typeof entityTypes)[number];

export class Comment extends Model {
  uid: string;

  entityType: EntityType;

  entityUid: string;

  createdBy: string;

  body: string;

  static get tableName() {
    return 'comments';
  }

  static get idColumn() {
    return 'uid';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['entityType', 'entityUid', 'createdBy', 'body'],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        entityType: {
          type: 'string',
          enum: ['case', 'milestone', 'execution', 'result', 'run'],
        },
        entityUid: { type: 'number' },
        createdBy: { type: 'string', format: 'uuid' },
        body: { type: 'string' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      creator: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'comments.createdBy',
          to: 'users.uid',
        },
      },
      attachments: {
        relation: Model.ManyToManyRelation,
        modelClass: Attachment,
        join: {
          from: 'comments.uid',
          through: {
            from: 'commentAttachments.commentUid',
            to: 'commentAttachments.attachmentUid',
          },
          to: 'attachments.uid',
        },
      },
    };
  }
}

export type CommentModel = ModelClass<Comment>;
