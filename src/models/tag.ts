import { Model, CustomFields } from '@app/lib/model';
import { Knex } from 'knex';
import { ModelClass } from 'objection';

const systemTypes = <const>[
  'folder',
  'tag',
  'priority',
  'status',
  'plan',
  'milestone',
  'config',
  'config.option',
  'run',
];
type SystemType = (typeof systemTypes)[number];

export const entityTypes = <const>[
  'cases',
  'milestones',
  'executions',
  'results',
  'runs',
  'plans',
  'defects',
  'users',
  'roles',
];
export type EntityType = (typeof entityTypes)[number];

export class Tag<
  T extends CustomFields = any,
  U extends SystemType = SystemType,
> extends Model {
  uid: number;

  name: string;

  slug: string;

  description: string;

  systemType: U;

  customFields: T;

  entityTypes: EntityType[];

  archivedAt: Date | string;

  parentUid?: number;

  projectUid: number;

  externalCreatedAt: Date | string;

  externalUpdatedAt: Date | string;

  externalId?: string;

  source?: string;

  integrationUid?: number;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'tags';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['name'],
      properties: {
        uid: { type: 'integer' },
        name: { type: 'string' },
        description: { type: 'string' },
        entityTypes: { type: 'array', items: { type: 'string' } },
        customFields: { type: 'object' },
        systemType: { type: 'string', enum: <any>systemTypes },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        archivedAt: { type: ['string', 'null'], format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
        externalCreatedAt: { type: ['string', 'null'], format: 'date-time' },
        externalUpdatedAt: { type: ['string', 'null'], format: 'date-time' },
        externalId: { type: ['string', 'null'] },
        source: { type: ['string', 'null'] },
        integrationUid: { type: ['integer', 'null'] },
      },
    };
  }

  static get jsonAttributes() {
    return [];
  }

  static get modifiers() {
    return {
      isCaseTag(builder) {
        builder
          .where('testCaseTags.deletedAt', null)
          .where('tags.systemType', 'tag');
      },
      isCaseFolder(builder) {
        builder
          .where('testCaseTags.deletedAt', null)
          .where('tags.systemType', 'folder')
          .first('*');
      },
      isRoleTag(builder) {
        builder
          .where('roleTags.deletedAt', null)
          .where('tags.systemType', 'tag')
          .select(['uid', 'name']);
      },
      isConfigOption(builder) {
        builder.where({ deletedAt: null });
      },
    };
  }

  static findByIds(trx: Knex, uids: number[]) {
    if (!uids || uids.length === 0) return [];

    return Tag.query(trx)
      .whereNull('deletedAt')
      .whereIn('uid', uids)
      .select('uid', 'name');
  }

  static findByName(trx: Knex, name: string, entityTypes: EntityType[]) {
    if (!name) return [];
    return Tag.query(trx).whereRaw(
      'trim(lower(name))=? and "entityTypes" @> ?',
      [name.toLowerCase().trim(), entityTypes],
    );
  }

  protected static async count(
    trx: Knex,
    state: 'active' | 'archived',
    systemType: SystemType,
    projectId?: number,
  ) {
    const base = Tag.query(trx)
      .whereNull('deletedAt')
      .where('systemType', systemType);

    if (projectId) base.where('projectUid', projectId);

    if (state === 'active') {
      base.whereNull('archivedAt');
    } else {
      base.whereNotNull('archivedAt');
    }

    const [result] = (await base.count()) as any;
    return +result.count;
  }
}

export type TagModel = ModelClass<Tag> & typeof Tag;
