import { Model, ModelClass } from 'objection';

export class SSO<PERSON>ser extends Model {
  uid: string;

  provider: string;

  subject: string;

  userUid: string;

  createdAt: Date;

  updatedAt: Date;

  deletedAt: Date | null;

  static tableName: string = 'ssoUsers';

  static idColumn: string = 'uid';

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['provider', 'subject'],
      properties: {
        uid: { type: 'string', maxLength: 36 },
        provider: { type: 'string', maxLength: 255 },
        userUid: { type: 'string', format: 'uuid' },
        subject: { type: 'string', maxLength: 255 },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }
}

export type SSOUserModel = ModelClass<SSOUser> & typeof SSOUser;
