import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';

export class TestPlanConfiguration extends Model {
  planUid: string;

  configurationUid: string;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'testPlanConfigurations';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['planUid', 'configurationUid'],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        planUid: { type: 'string', format: 'uuid' },
        configurationUid: { type: 'string', format: 'uuid' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }
}

export type TestPlanConfigurationModel = ModelClass<TestPlanConfiguration>;
