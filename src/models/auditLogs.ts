import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';

export class AuditLog extends Model {
  id: string;

  tablename: string;

  action: string;

  input: string;

  output: string;

  createdAt: string;

  actor: string;

  requestIp: string;

  requestTraceId: string;

  userAgent: string;

  static get idColumn() {
    return 'id';
  }

  static get tableName() {
    return 'auditLogs';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['tablename', 'action', 'createdAt', 'actor'],
      properties: {
        id: { type: 'string', format: 'uuid' },
        tablename: { type: 'string' },
        action: { type: 'string' },
        input: { type: 'string' },
        output: { type: 'string' },
        createdAt: { type: 'string', format: 'date-time' },
        actor: { type: 'string' },
        requestIp: { type: 'string' },
        requestTraceId: { type: 'string' },
        userAgent: { type: 'string' },
      },
    };
  }
}

export type AuditLogModel = ModelClass<AuditLog>;
