import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import { Tenant } from './tenant';

export class Handle extends Model {
  uid: string;

  name: string;

  ownerUid: string;

  ownerType: 'org' | 'user';

  current: boolean;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'handles';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['name', 'ownerUid', 'current'],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        name: { type: 'string', maxLength: 255 },
        ownerUid: { type: 'string', maxLength: 255 },
        ownerType: { type: 'string', pattern: '^(user)|(org)$' },
        current: { type: 'boolean' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      tenant: {
        relation: Model.BelongsToOneRelation,
        modelClass: Tenant,
        join: {
          from: 'handles.ownerUid',
          to: 'tenants.tenantUid',
        },
      },
    };
  }
}

export type HandleModel = ModelClass<Handle>;
