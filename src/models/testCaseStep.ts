import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import { SharedTestStep } from './sharedTestStep';
import { TestCase } from './testCase';

export class TestCaseStep extends Model {
  uid: string;

  customFields: Record<string, any>;

  testCaseRef: number;

  sharedTestStepRef: number;

  testCaseAddedVersion: number[];

  testCaseRemovedVersion: number[] | null;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'testCaseSteps';
  }

  static jsonAttributes = ['testCaseAddedVersion', 'testCaseRemovedVersion'];

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['testCaseRef', 'sharedTestStepRef'],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        customFields: { type: 'object' },
        testCaseRef: { type: 'integer' },
        sharedTestStepRef: { type: 'integer' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      testCase: {
        relation: Model.BelongsToOneRelation,
        modelClass: TestCase,
        join: {
          from: 'testCaseSteps.testCaseRef',
          to: 'testCases.uid',
        },
      },
      sharedTestStep: {
        relation: Model.BelongsToOneRelation,
        modelClass: SharedTestStep,
        join: {
          from: 'testCaseSteps.sharedTestStepRef',
          to: 'sharedTestSteps.uid',
        },
      },
    };
  }
}

export type TestCaseStepModel = ModelClass<TestCaseStep>;
