import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import { Org } from './org';
import { Project } from './project';
import { Tag } from './tag';
import { User } from './user';
import { ProjectInvite } from './projectInvite';

const statuses = <const>['pending', 'delivered', 'invalid_email', 'canceled'];
type InviteStatus = (typeof statuses)[number];

export class Invite extends Model {
  uid: string;

  inviterUid: string;

  email: string;

  token: string;

  expiresAt: Date | string;

  accepted: boolean;

  acceptedAt: Date | string;

  roleUid: string | null; // Nullable for project invites

  status?: InviteStatus;

  tagUids: number[];

  projectUids: number[];

  projects?: Project;

  tags?: Tag[];

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'invites';
  }

  static jsonAttributes = ['tagUids'];

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['inviterUid', 'token', 'expiresAt', 'email'],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        inviterUid: { type: 'string', format: 'uuid' },
        email: { type: 'string' },
        token: { type: 'string' },
        expiresAt: { type: 'string' },
        tagUids: { type: 'array' },
        accepted: { type: 'boolean' },
        acceptedAt: { type: 'string', format: 'date-time' },
        roleUid: { type: 'string', maxLength: 255 },
        projectUids: { type: 'array', items: { type: 'integer' } },
        createdAt: { type: 'string', format: 'date-time' },
        status: { type: 'string', enum: <any>statuses },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      org: {
        relation: Model.BelongsToOneRelation,
        modelClass: Org,
        join: {
          from: 'invites.orgUid',
          to: 'orgs.uid',
        },
      },
      user: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'invites.inviterUid',
          to: 'users.uid',
        },
      },
      project: {
        relation: Model.BelongsToOneRelation,
        modelClass: Project,
        join: {
          from: 'invites.projectUid',
          to: 'projects.uid',
        },
      },
      projectInvite: {
        relation: Model.HasManyRelation,
        modelClass: ProjectInvite,
        join: {
          from: 'invites.uid',
          to: 'projectInvites.inviteUid',
        },
      },
    };
  }
}

export type InviteModel = ModelClass<Invite>;
