import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import { CustomFields } from '@app/types/templateCustomField';
import { User } from './user';

export class TestTemplate extends Model {
  uid: number;

  name: string;

  createdBy: string;

  customFields?: CustomFields;

  projectUid: number;

  isDefault: boolean;

  creator?: User;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'testTemplates';
  }

  static jsonAttributes = ['customFields'];

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['name', 'createdBy'],
      properties: {
        uid: { type: 'integer' },
        name: { type: 'string' },
        isDefault: { type: 'boolean' },
        customFields: { type: ['object', 'null'] },
        createdBy: { type: 'string', format: 'uuid' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      creator: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'testTemplates.createdBy',
          to: 'users.uid',
        },
      },
    };
  }
}

export type TestTemplateModel = ModelClass<TestTemplate>;
