import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import { Knex } from 'knex';
import { ApplicationError } from '@app/lib/http';
import { StatusCodes } from 'http-status-codes';
import errors from '@app/constants/errors';
import env from '@app/config/env';
import { Integration } from './integration';
import { TestExecution } from './testExecution';
import { Attachment } from './attachment';

export class Defect extends Model {
  uid: number;

  name: string;

  priority: number | null;

  status: number | null;

  externalId: string;

  integrationSourceUid: string | null;

  assignedTo: string;

  creator: string;

  customFields: Record<string, string>;

  projectUids: number[];

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'defects';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['name', 'externalId'],
      properties: {
        uid: { type: 'integer' },
        name: { type: 'string' },
        priority: { type: ['integer', 'null'] },
        status: { type: ['integer', 'null'] },
        externalId: { type: 'string' },
        integrationSourceUid: { type: ['string', 'null'] },
        assignedTo: { type: 'string' },
        creator: { type: 'string' },
        customFields: { type: 'object' },
        projectUids: { type: 'array', items: { type: 'integer' } },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: ['string', 'null'], format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      integration: {
        relation: Model.BelongsToOneRelation,
        modelClass: Integration,
        join: {
          from: 'defects.integrationSourceUid',
          to: 'integrations.uid',
        },
      },
      executions: {
        relation: Model.ManyToManyRelation,
        modelClass: TestExecution,
        join: {
          from: 'defects.uid',
          through: {
            from: 'defectExecutions.defectUid',
            to: 'defectExecutions.executionUid',
            extra: ['executionUrl'],
          },
          to: 'testExecutions.uid',
        },
      },
      attachments: {
        relation: Model.ManyToManyRelation,
        modelClass: Attachment,
        join: {
          from: 'defects.uid',
          through: {
            from: 'defectAttachments.defectUid',
            to: 'defectAttachments.attachmentUid',
          },
          to: 'attachments.uid',
        },
      },
    };
  }

  static async countOpenDefects(db: Knex, integrationService: string, projectUid: number): Promise<number> {
    const integrationUids = await Integration.query(db)
      .where('service', integrationService)
      .select('uid');
    const [result] = (await Defect.query(db)
      .whereIn('integrationSourceUid', integrationUids.map((integration) => integration.uid))
      .whereRaw('? = ANY("projectUids")', [projectUid])
      .whereNull('deletedAt')
      .whereNull('archivedAt')
      .count()) as any[];
    return +result.count;
  }

  static async countClosedDefects(db: Knex, integrationService: string, projectUid: number): Promise<number> {
    const integrationUids = await Integration.query(db)
      .where('service', integrationService)
      .select('uid');
    const [result] = (await Defect.query(db)
      .whereIn('integrationSourceUid', integrationUids.map((integration) => integration.uid))
      .whereRaw('? = ANY("projectUids")', [projectUid])
      .whereNotNull('archivedAt')
      .whereNull('deletedAt')
      .count()) as any[];
    return +result.count;
  }

  static async defectAttachments(db: Knex, defectUid: string, projectUid:number, handleName: string) {
    const defect = await Defect.query(db)
      .findById(defectUid)
      .withGraphFetched('attachments')
      .modifyGraph('attachments', (builder) => {
        builder
          .select(['attachments.uid', 'attachments.name', 'attachments.fileType'])
          .whereNull('attachments.deletedAt');
      })
      .whereRaw('? = ANY("projectUids")', [projectUid])
      .whereNull('defects.deletedAt');
    if (!defect) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, errors.DEFECT_NOT_FOUND);
    }
    const baseURL = `${env.BACKEND_URL}${env.API_INTERNAL_ROUTE}/${handleName}`;

    const transformedAttachments = (defect as any).attachments?.map((attachment: any) => ({
      uid: attachment.uid,
      name: attachment.name,
      fileType: attachment.fileType,
      previewUrl: `${baseURL}/defects/attachments/${attachment.uid}/object`,
    })) || [];

    return transformedAttachments;
  }
}

export type DefectModel = ModelClass<Defect> & typeof Defect;
