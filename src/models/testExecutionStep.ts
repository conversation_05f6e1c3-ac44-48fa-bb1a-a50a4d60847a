import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import _ from 'lodash';
import { Attachment } from './attachment';
import { SharedTestStep } from './sharedTestStep';
import { TestExecution } from './testExecution';

export class TestExecutionStep extends Model {
  uid: string;

  /**
   * represents the position of the step in it's level (first, second, third, ...)
   */
  position: number;

  /**
   * execution of the step
   */
  testExecutionUid: number;

  /**
   * optional uid of step in external integration
   */
  externalId?: string;

  /**
   * optional external source
   */
  source?: string;

  /**
   * link to excution according to external source
   */
  link?: string;

  /**
   * creation date on external platform
   */
  externalCreatedAt?: Date | string;

  /**
   * last update on external source before importing
   */
  externalUpdatedAt?: Date | string;

  /**
   * description of the step
   */
  description?: string;

  /**
   * expected result of the step
   */
  expectedResult?: string;

  /**
   * title of the step
   */
  title?: string;

  /**
   * status of the test step
   */
  status?: number;

  /**
   * shared step uid the step was originally derived from
   */
  sharedTestStepUid?: number;

  /**
   * parent execution step uid
   */
  parentStepUid?: string;

  /**
   * step children - used for building a tree of steps
   */
  children?: TestExecutionStep[];

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'testExecutionSteps';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['testExecutionUid'],
      properties: {
        uid: { type: 'integer' },
        testExecutionUid: { type: 'integer' },
        externalId: { type: 'string' },
        source: { type: 'string' },
        link: { type: 'string' },
        customFields: { type: 'object' },
        externalCreatedAt: { type: 'string', format: 'date-time' },
        externalUpdatedAt: { type: 'string', format: 'date-time' },
        description: { type: 'string' },
        testStepUid: { type: 'string', format: 'uuid' },
        expectedResult: { type: 'string' },
        title: { type: 'string' },
        sharedStepUid: { type: 'number' },
        parentStepUid: { type: 'string' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      testExecution: {
        relation: Model.BelongsToOneRelation,
        modelClass: TestExecution,
        join: {
          from: 'testExecutionSteps.testRunUid',
          to: 'testExecutions.uid',
        },
      },
      sharedTestStep: {
        relation: Model.BelongsToOneRelation,
        modelClass: SharedTestStep,
        join: {
          from: 'testExecutionSteps.sharedTestStepUid',
          to: 'sharedTestSteps.uid',
        },
      },
      attachments: {
        relation: Model.ManyToManyRelation,
        modelClass: Attachment,
        join: {
          from: 'testExecutionSteps.uid',
          through: {
            from: 'executionStepAttachments.executionStepUid',
            to: 'executionStepAttachments.attachmentUid',
          },
          to: 'attachments.uid',
        },
      },
    };
  }

  static buildTree(steps: TestExecutionStep[]) {
    const roots = steps.filter((i) => _.isNull(i.parentStepUid));
    for (const root of roots) TestExecutionStep.getChildren(root, steps);
    return roots;
  }

  static getChildren(node: TestExecutionStep, list: TestExecutionStep[]) {
    node.children = _.sortBy(
      list.filter((l) => l.parentStepUid === node.uid),
      (n) => n.position,
    );
    for (const c of node.children) TestExecutionStep.getChildren(c, list);
  }
}

export type TestExecutionStepModel = ModelClass<TestExecutionStep> &
  typeof TestExecutionStep;
