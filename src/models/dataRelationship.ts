import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import { Org } from './org';

export class DataRelationship extends Model {
  uid: string;

  primaryTable: string;

  primaryKey: string;

  secondaryTable: string;

  secondaryKey: string;

  source: string;

  direction: string;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'dataRelationships';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: [
        'primaryTable',
        'primaryKey',
        'secondaryTable',
        'secondaryKey',
        'source',
        'direction',
        'orgUid',
      ],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        primaryTable: { type: 'string', maxLength: 255 },
        primaryKey: { type: 'string', maxLength: 255 },
        secondaryTable: { type: 'string', maxLength: 255 },
        secondaryKey: { type: 'string', maxLength: 255 },
        source: { type: 'string', maxLength: 255 },
        direction: { type: 'string', maxLength: 255 },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      org: {
        relation: Model.BelongsToOneRelation,
        modelClass: Org,
        join: {
          from: 'dataRelationships.orgUid',
          to: 'orgs.uid',
        },
      },
    };
  }
}

export type DataRelationshipModel = ModelClass<DataRelationship>;
