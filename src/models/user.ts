import { K<PERSON> } from 'knex';
import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import { Attachment } from './attachment';
import { Handle } from './handle';
import { Invite } from './invite';

export class User extends Model {
  uid: string;

  firstName: string;

  lastName: string;

  email: string;

  passwordHash: string;

  preferences: Record<string, any>;

  lastSignInIp: string;

  lastSignInAt: Date | string;

  passwordResetToken: string;

  passwordResetAttemptsCount: number;

  avatar: {
    user: string | null;
    system: string;
  };

  stripeId: string;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'users';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['lastSignInIp'],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        firstName: { type: 'string', maxLength: 255 },
        lastName: { type: 'string' },
        email: { type: 'string' },
        passwordHash: { type: 'string', maxLength: 255 },
        preferences: { type: 'object' },
        lastSignInIp: { type: 'string', maxLength: 50 },
        passwordResetToken: { type: 'string', maxLength: 255 },
        passwordResetAttemptsCount: { type: 'number' },
        avatar: { type: 'object' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
        stripeId: { type: 'string', maxLength: 255 },
      },
    };
  }

  static get relationMappings() {
    return {
      invite: {
        relation: Model.HasOneRelation,
        modelClass: Invite,
        join: {
          from: 'users.uid',
          to: 'invites.inviterUid',
        },
      },
      handle: {
        relation: Model.HasOneRelation,
        modelClass: Handle,
        join: {
          from: 'users.uid',
          to: 'handles.ownerUid',
        },
      },
      attachments: {
        relation: Model.ManyToManyRelation,
        modelClass: Attachment,
        join: {
          from: 'users.uid',
          through: {
            from: 'userAttachments.userUid',
            to: 'userAttachments.attachmentUid',
          },
          to: 'attachments.uid',
        },
      },
    };
  }

  static async getOne(db: Knex, uid: string) {
    const [user] = await User.getByIds(db, [uid]);
    return user;
  }

  static async getByIds(db: Knex, uids: string[]): Promise<UserWithHandle[]> {
    return User.query(db)
      .join('handles', 'users.uid', '=', 'handles.ownerUid')
      .where((q) => {
        if (uids.length === 1) {
          q.where('users.uid', uids[0]);
        } else {
          q.whereIn('users.uid', uids);
        }
      })
      .where('handles.current', true)
      .where('handles.ownerType', 'user')
      .select({
        uid: 'users.uid',
        firstName: 'users.firstName',
        lastName: 'users.lastName',
        email: 'users.email',
        handle: 'handles.name',
      });
  }
}

interface UserWithHandle {
  uid: string;
  firstName: string;
  lastName: string;
  email: string;
  handle?: string;
}

export type UserModel = ModelClass<User> & typeof User;
