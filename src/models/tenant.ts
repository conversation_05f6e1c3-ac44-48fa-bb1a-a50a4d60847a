import { Knex } from 'knex';
import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import { DBServer } from './dbServer';
import { AppVersion } from './appVersion';

export class Tenant extends Model {
  uid: string;

  tenantUid: string; // also serves as the db name and openfga store name

  tenantType: 'user' | 'org';

  setupStatus: 'unset' | 'inProgress' | 'completed';

  openfgaStoreId: string;

  openfgaAuthModelId?: string;

  isMaintenance: boolean;

  dbServerUid?: number;

  dbServer: DBServer;

  frontendVersionUid?: number;

  backendVersionUid?: number;

  frontendVersion?: AppVersion;

  backendVersion?: AppVersion;

  config: Record<string, any>;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'tenants';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['tenantUid', 'tenantType', 'setupStatus'],
      properties: {
        tenantUid: { type: 'string', format: 'uuid' },
        isMaintenance: { type: 'boolean' },
        tenantType: { type: 'string' },
        setupStatus: { type: 'string' },
        openfgaStoreId: { type: 'string' },
        openfgaAuthModelId: { type: 'string' },
        dbServerUid: { type: 'number' },
        frontendVersion: { type: 'object' },
        backendVersion: { type: 'object' },
        config: { type: 'object' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      dbServer: {
        relation: Model.BelongsToOneRelation,
        modelClass: DBServer,
        join: {
          from: 'tenants.dbServerUid',
          to: 'dbServers.uid',
        },
      },
      frontendVersion: {
        relation: Model.BelongsToOneRelation,
        modelClass: AppVersion,
        join: {
          from: 'tenants.frontendVersionUid',
          to: 'appVersions.uid',
        },
      },
      backendVersion: {
        relation: Model.BelongsToOneRelation,
        modelClass: AppVersion,
        join: {
          from: 'tenants.backendVersionUid',
          to: 'appVersions.uid',
        },
      },
    };
  }

  static async findByTenantUids(db: Knex, tenantUids: string[]) {
    return Tenant.query(db)
      .whereIn('tenantUid', tenantUids)
      .whereNull('deletedAt');
  }

  static async findOne(db: Knex, tenantUid: string) {
    return Tenant.query(db).where({ tenantUid }).first();
  }
}

export type TenantModel = ModelClass<Tenant>;
