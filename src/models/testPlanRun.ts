import { ModelClass, ModelObject } from 'objection';

import { Knex } from 'knex';
import { Model } from '@app/lib/model';
import { TestPlan } from './testPlan';

export class TestPlanRun extends Model {
  planUid: number;

  runUid: number;

  testPlan?: TestPlan;

  static get tableName() {
    return 'testPlanRuns';
  }

  static get idColumn() {
    return ['planUid', 'runUid'];
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['planUid', 'runUid'],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        planUid: { type: 'integer' },
        runUid: { type: 'integer' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      testPlan: {
        relation: Model.BelongsToOneRelation,
        modelClass: TestPlan,
        join: { from: 'testPlanRuns.planUid', to: 'tags.uid' },
      },
    };
  }

  static async create(
    trx: Knex,
    details: Array<Partial<ModelObject<TestPlanRun>>>,
  ) {
    return TestPlanRun.query(trx)
      .insert(
        details.map((d) => ({
          runUid: d.runUid,
          planUid: d.planUid,
          createdAt: trx.fn.now(),
          updatedAt: trx.fn.now(),
          deletedAt: trx.raw('null'),
        })),
      )
      .onConflict(['runUid', 'planUid'])
      .merge()
      .returning('*');
  }

  static async deleteByRunUid(trx: Knex, runUid: number, planUids: number[]) {
    return TestPlanRun.query(trx)
      .where({ runUid, deletedAt: null })
      .whereIn('planUid', planUids)
      .patch({ deletedAt: trx.fn.now() })
      .returning('*');
  }

  static async deleteByPlanUid(trx: Knex, planUid: number, runUids?: number[]) {
    return TestPlanRun.query(trx)
      .where({ planUid, deletedAt: null })
      .where((q) => {
        if (runUids?.length > 0) q.whereIn('runUid', runUids);
      })
      .patch({ deletedAt: trx.fn.now() })
      .returning('*');
  }
}

export type TestPlanRunModel = ModelClass<TestPlanRun> & typeof TestPlanRun;
