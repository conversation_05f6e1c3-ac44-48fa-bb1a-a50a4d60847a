/* eslint-disable max-classes-per-file */
import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import { Knex } from 'knex';
import { Org } from './org';
import { Tag } from './tag';
import { User } from './user';

export class Role extends Model {
  uid: string;

  name: string;

  description: string;

  slug: string;

  system: boolean;

  assignees?: User[];

  projectUid?: number | null;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'roles';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      properties: {
        uid: { type: 'string', format: 'uuid' },
        name: { type: 'string' },
        description: { type: 'string' },
        slug: { type: 'string' },
        projectUid: { type: ['number', 'null'] },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get jsonAttributes() {
    return [];
  }

  static get relationMappings() {
    return {
      org: {
        relation: Model.BelongsToOneRelation,
        modelClass: Org,
        join: {
          from: 'roles.orgUid',
          to: 'orgs.uid',
        },
      },
      tags: {
        relation: Model.ManyToManyRelation,
        modelClass: Tag,
        join: {
          from: 'roles.uid',
          through: {
            from: 'roleTags.roleUid',
            to: 'roleTags.tagUid',
          },
          to: 'tags.uid',
        },
      },
    };
  }

  static async findWithProject(trx: Knex, roleUid: string) {
    return this.query(trx).withGraphFetched('tags(isRoleTag)')
      .leftJoin('projects', 'projects.uid', 'roles.projectUid')
      .select(
        'roles.*',
        'projects.name as projectName',
        'projects.key as projectKey',
        'projects.avatarAttachmentUid',
      )
      .where('roles.uid', roleUid)
      .whereNull('roles.deletedAt')
      .first();
  }
}

export class RoleTag extends Model {
  roleUid: string;

  tagUid: number;

  static get idColumn() {
    return null;
  }

  static get tableName() {
    return 'roleTags';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['roleUid', 'tagUid'],
      properties: {
        roleUid: { type: 'string', format: 'uuid' },
        tagUid: { type: 'number' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }
}

export type RoleModel = ModelClass<Role>;
export type RoleTagModel = ModelClass<RoleTag>;
