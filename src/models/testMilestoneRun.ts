import { ModelClass, ModelObject } from 'objection';

import { Knex } from 'knex';
import { Model } from '@app/lib/model';
import { TestMilestone } from './testMilestone';

export class TestMilestoneRun extends Model {
  uid: string;

  milestoneUid: number;

  runUid: number;

  testMilestone?: TestMilestone;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'testMilestoneRuns';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['milestoneUid', 'runUid'],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        milestoneUid: { type: 'integer' },
        runUid: { type: 'integer' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      testMilestone: {
        relation: Model.BelongsToOneRelation,
        modelClass: TestMilestone,
        join: { from: 'testMilestoneRuns.milestoneUid', to: 'tags.uid' },
      },
    };
  }

  static async create(
    trx: Knex,
    details: Array<Partial<ModelObject<TestMilestoneRun>>>,
  ) {
    return TestMilestoneRun.query(trx)
      .insert(
        details.map((d) => ({
          runUid: d.runUid,
          milestoneUid: d.milestoneUid,
          createdAt: trx.fn.now(),
          updatedAt: trx.fn.now(),
          deletedAt: trx.raw('null'),
        })),
      )
      .onConflict(['runUid', 'milestoneUid'])
      .merge();
  }

  static async deleteByRunUid(
    trx: Knex,
    runUid: number,
    milestoneUids: number[],
  ) {
    return TestMilestoneRun.query(trx)
      .where({ runUid, deletedAt: null })
      .whereIn('milestoneUid', milestoneUids)
      .patch({ deletedAt: trx.fn.now() })
      .returning('*');
  }

  static async deleteByMilestoneUid(
    trx: Knex,
    milestoneUid: number,
    runUids: number[],
  ) {
    return TestMilestoneRun.query(trx)
      .where({ milestoneUid, deletedAt: null })
      .whereIn('runUid', runUids)
      .patch({ deletedAt: trx.fn.now() })
      .returning('*');
  }
}

export type TestMilestoneRunModel = ModelClass<TestMilestoneRun>;
