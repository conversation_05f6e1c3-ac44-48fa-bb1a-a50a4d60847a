import { Role, RoleTag } from './role';

import { AccessToken } from './accessToken';
import { AppConfig } from './appConfig';
import { Attachment } from './attachment';
import { AuditLog } from './auditLogs';
import { Comment } from './comment';
import { Configuration } from './configuration';
import { CustomField } from './customField';
import { Dashboard } from './dashboard';
import { DataRelationship } from './dataRelationship';
import { Defect } from './defect';
import { DefectExecution } from './defectExecution';
import { ExternalEntity } from './externalEntity';
import { Folder } from './folder';
import { Handle } from './handle';
import { Integration } from './integration';
import { IntegrationToken } from './integrationToken';
import { Invite } from './invite';
import { MemberTag } from './memberTag';
import { Membership } from './membership';
import { Org } from './org';
import { Project } from './project';
import { ProjectInvite } from './projectInvite';
import { Repo } from './repo';
import { RepoBranch } from './repoBranch';
import { SSOConfig } from './ssoConfig';
import { SharedTestStep } from './sharedTestStep';
import { Subscription } from './subscription';
import { SubscriptionPlan } from './subscriptionPlan';
import { Tag } from './tag';
import { Tenant } from './tenant';
import { TestCase } from './testCase';
import { TestCaseStep } from './testCaseStep';
import { TestCaseTag } from './testCaseTag';
import { TestExecution } from './testExecution';
import { TestExecutionStep } from './testExecutionStep';
import { TestMilestone } from './testMilestone';
import { TestMilestonePlan } from './testMilestonePlan';
import { TestMilestoneRun } from './testMilestoneRun';
import { TestPlan } from './testPlan';
import { TestPlanConfiguration } from './testPlanConfiguration';
import { TestPlanRun } from './testPlanRun';
import { TestResult } from './testResult';
import { TestRun } from './testRun';
import { TestTemplate } from './testTemplate';
import { User } from './user';
import { SSOUser } from './ssoUser';
import { DBServer } from './dbServer';
import { AppNode } from './appNode';
import { AppVersion } from './appVersion';
import { ScheduledTask } from './scheduledTask';

export const sharedModels = {
  AccessToken,
  AppConfig,
  User,
  Org,
  Handle,
  Subscription,
  SubscriptionPlan,
  Tenant,
  Membership,
  SSOConfig,
  SSOUser,
  DBServer,
  AppNode,
  AppVersion,
  ScheduledTask,
};

export const tenantModels = {
  Attachment,
  AuditLog,
  Comment,
  CustomField,
  Dashboard,
  DataRelationship,
  Defect,
  ExternalEntity,
  Folder,
  Invite,
  MemberTag,
  IntegrationToken,
  Repo,
  RepoBranch,
  Role,
  RoleTag,
  Tag,
  TestTemplate,
  TestCase,
  TestCaseStep,
  TestCaseTag,
  TestExecution,
  TestExecutionStep,
  TestMilestone,
  TestMilestoneRun,
  TestPlanRun,
  Configuration,
  TestPlanConfiguration,
  TestPlan,
  Project,
  ProjectInvite,
  TestResult,
  TestRun,
  SharedTestStep,
  TestMilestonePlan,
  Integration,
  DefectExecution,
};

export const models = { ...sharedModels, ...tenantModels };

export const adminConfig = Object.values(models).map((M) => ({
  name: M.tableName,
  schema: M.jsonSchema,
}));
