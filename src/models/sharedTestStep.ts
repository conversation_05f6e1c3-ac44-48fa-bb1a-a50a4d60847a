import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import { SharedStepItem } from '@app/types/step';
import { Attachment } from './attachment';
import { TestCase } from './testCase';
import { TestExecution } from './testExecution';
import { User } from './user';
import { TestCaseStep } from './testCaseStep';

export class SharedTestStep extends Model {
  uid: number;

  name: string;

  externalId: string;

  source: string;

  link: string;

  customFields: Record<string, any>;

  externalCreatedAt: Date | string;

  externalUpdatedAt: Date | string;

  version: number;

  active: boolean;

  archivedAt: Date | string;

  steps: SharedStepItem[];

  projectUid: number;

  sharedTestStepRef: number;

  createdBy: string;

  creator?: User;

  expectedResultByStep?: boolean;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'sharedTestSteps';
  }

  static jsonAttributes = ['steps'];

  static get jsonSchema() {
    return {
      type: 'object',
      required: [
        'name',
        'steps',
        'projectUid',
        'sharedTestStepRef',
        'createdBy',
      ],
      properties: {
        uid: { type: 'integer' },
        projectUid: { type: 'integer' },
        sharedTestStepRef: { type: 'integer' },
        createdBy: { type: 'string', format: 'uuid' },
        name: { type: 'string' },
        externalId: { type: ['string', 'null'], maxLength: 255 },
        source: { type: ['string', 'null'], maxLength: 255 },
        link: { type: ['string', 'null'], maxLength: 255 },
        customFields: { type: ['object', 'null'] },
        externalCreatedAt: { type: 'string', format: 'date-time' },
        externalUpdatedAt: { type: 'string', format: 'date-time' },
        version: { type: 'integer' },
        active: { type: 'boolean' },
        archivedAt: { type: ['string', 'null'], format: 'date-time' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
        expectedResultByStep: { type: ['boolean', 'null'] },
      },
    };
  }

  static get relationMappings() {
    return {
      testCases: {
        relation: Model.ManyToManyRelation,
        modelClass: TestCase,
        join: {
          from: 'sharedTestSteps.uid',
          through: {
            from: 'testCaseSteps.sharedTestStepRef',
            to: 'testCaseSteps.testCaseRef',
          },
          to: 'testCases.uid',
        },
      },
      testExecutions: {
        relation: Model.ManyToManyRelation,
        modelClass: TestExecution,
        join: {
          from: 'sharedTestSteps.uid',
          through: {
            from: 'testExecutionSteps.sharedTestStepUid',
            to: 'testExecutionSteps.testExecutionUid',
          },
          to: 'testExecutions.uid',
        },
      },
      attachments: {
        relation: Model.ManyToManyRelation,
        modelClass: Attachment,
        join: {
          from: 'sharedTestSteps.uid',
          through: {
            from: 'stepAttachments.sharedTestStepUid',
            to: 'stepAttachments.attachmentUid',
          },
          to: 'attachments.uid',
        },
      },
      creator: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'sharedTestSteps.createdBy',
          to: 'users.uid',
        },
      },
      testCaseSteps: {
        relation: Model.HasManyRelation,
        modelClass: TestCaseStep,
        join: {
          from: 'sharedTestSteps.sharedTestStepRef',
          to: 'testCaseSteps.sharedTestStepRef',
        },
      },
    };
  }
}

export type SharedTestStepModel = ModelClass<SharedTestStep>;
