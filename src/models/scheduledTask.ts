import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';

export const scheduledTaskStatuses = <const>['completed', 'not_started', 'failed', 'error', 'running'];
type Statuses = typeof scheduledTaskStatuses[number];

export class ScheduledTask extends Model {
  uid: string;

  status: Statuses;

  message: string;

  queue: string;

  name: string;

  traceback: string;

  ownerUid: string;

  ownerType: 'org' | 'user';

  percentage: number;

  taskData: Record<string, any>;

  createdAt: string;

  completedAt: string;

  static get tableName() {
    return 'scheduledTasks';
  }

  static get idColumn() {
    return 'uid';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['status', 'name', 'percentage', 'taskData', 'queue', 'ownerUid'],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        status: {
          type: 'string',
          enum: [...scheduledTaskStatuses],
        },
        ownerUid: { type: 'string', format: 'uuid' },
        ownerType: { type: 'string', pattern: '^(user)|(org)$' },
        message: { type: 'string' },
        queue: { type: 'string' },
        name: { type: 'string' },
        traceback: { type: 'string' },
        percentage: { type: 'number' },
        taskData: { type: 'object' },
        createdAt: { type: 'string', format: 'date-time' },
      },
    };
  }
}

export type ScheduledTaskModel = ModelClass<ScheduledTask>;
