/* eslint-disable max-classes-per-file */

import {
  DuplicateRunOpts,
  ListRunsDTO,
  NewRunOpts,
  RunDTO,
  UpdateRunDTO,
} from '@app/types/run';
import { ModelClass, ModelObject } from 'objection';
import { Knex } from 'knex';
import _ from 'lodash';
import { cartesianProduct } from '@app/utils/arrays';
import { CompletedStatuses, StatusFreq } from './preferences';
import {
  CustomFields as _CustomFields,
  Model,
  paginated,
  PaginatedResult,
} from '../lib/model';

import { Tag } from './tag';
import { TestCase } from './testCase';
import { TestExecution } from './testExecution';
import { TestMilestone } from './testMilestone';
import { TestMilestoneRun } from './testMilestoneRun';
import { TestPlan } from './testPlan';
import { TestPlanRun } from './testPlanRun';
import { Attachment } from './attachment';
import { Folder } from './folder';

const execInheritables = [
  'externalId',
  'source',
  'link',
  'customFields',
  'externalCreatedAt',
  'externalUpdatedAt',
  'repoBranchUid',
  'testCaseUid',
  'testCaseRef',
];

export class TestRun extends Tag<CustomFields, 'run'> {
  // populated relations
  testMilestones?: TestMilestone[];

  testExecutions?: TestExecution[];

  testPlans?: TestPlan[];

  attachments?: any[];

  // DTO fields
  // fields that live in the `customFields` object but are later
  // promoted to root level after processing, for the sake of API responses
  configs?: { name: string; option: string }[];

  status?: number;

  priority?: number;

  tags?: Tag[];

  /**
   * populateConfig **mutates** the passed TestRun instance by setting it plan configuration
   * if it exist. It typically should be invoked within a transaction
   * @param trx
   * @param run
   * @returns
   */
  static async populateConfig(db: Knex, run: ModelObject<TestRun>) {
    run.configs = [];

    if (
      !run.customFields
      || !run.customFields.configs
      || run.customFields?.configs?.length <= 0
    ) {
      return;
    }
    const uids: number[] = [];
    const mappings: [number, number][] = [];

    for (const c of run.customFields.configs) {
      const ids = c
        .split('::')
        .filter((c) => _.isFinite(+c))
        .map((c) => +c);
      if (ids.length !== 2) continue;
      uids.push(...ids);
      mappings.push([+ids[0], +ids[1]]);
    }

    if (mappings.length === 0) return;

    const tags = (
      await Tag.query(db)
        .whereIn('systemType', ['config', 'config.option'])
        .whereIn('uid', uids)
        .select('uid', 'name')
    ).reduce(
      (m, c) => {
        m[`config-${c.uid}`] = c;
        return m;
      },
      {} as Record<string, Tag>,
    );

    for (const kv of mappings) {
      const config = tags[`config-${kv[0]}`];
      const option = tags[`config-${kv[1]}`];

      if (!config || !option) continue;
      run.configs.push({ name: config.name, option: option.name });
    }
  }

  static async populateTags(db: Knex, run: ModelObject<TestRun>) {
    run.tags = (await TestRun.relatedQuery('runTags', db)
      .for(run.uid)
      .select('uid', 'name')) as any[];
  }

  /**
   * create a new test run and it's executions based off an existing test run
   * @param trx
   * @param models
   * @param base
   */
  static async duplicate(
    trx: Knex.Transaction,
    run: RunDTO,
    opts: Partial<DuplicateRunOpts>,
  ) {
    const testRun = await TestRun.create(
      trx,
      run,
      undefined,
      opts.planUid ? [opts.planUid] : undefined,
    );

    const baseExecutions = await TestExecution.query(trx)
      .withGraphFetched('[testCase]')
      .where((q) => {
        if (opts.executionUids?.length > 0) {
          q.whereIn('uid', opts.executionUids);
        } else if (opts.executionUids?.length === 0) {
          q.whereRaw('1=2');
        } else {
          q.where('testRunUid', opts.sourceRunUid);
        }
      })
      .select(...execInheritables);

    const createExecs: Promise<TestExecution>[] = [];
    const folderUids: number[] = [];

    for (const e of baseExecutions) {
      createExecs.push(
        TestExecution.createFromTestCase(trx, e.testCase, {
          runUid: testRun.uid,
          priority: opts.execPriority,
          status: opts.execStatus,
        }),
      );
      if (e.testCase?.parentUid) folderUids.push(e.testCase.parentUid);
    }

    await TestRun.appendToFolderTree(trx, testRun.uid, folderUids);

    await TestRunTag.copyRunTags(trx, testRun.uid, opts.sourceRunUid);

    await TestRun.populateTags(trx, testRun);

    return { testRun, executions: await Promise.all(createExecs) };
  }

  static async newRun(
    trx: Knex.Transaction,
    run: RunDTO,
    opts: Partial<NewRunOpts>,
  ) {
    const testRun = await TestRun.create(trx, run, opts.milestoneUids);

    if (!opts.caseUids?.length || opts.caseUids.length === 0) {
      return { testRun, executions: [] };
    }

    const validCases = await TestCase.query(trx)
      .select('*')
      .whereIn('testCaseRef', opts.caseUids)
      .where('active', true)
      .whereNull('deletedAt');

    const createExecs: Promise<TestExecution>[] = [];
    const folderUids: number[] = [];
    for (const tc of validCases) {
      createExecs.push(
        TestExecution.createFromTestCase(trx, tc, {
          runUid: testRun.uid,
          priority: tc.priority ?? opts.execPriority,
          status: tc.status ?? opts.execStatus,
        }),
      );

      if (tc.parentUid) folderUids.push(tc.parentUid);
    }

    await TestRun.appendToFolderTree(trx, testRun.uid, folderUids);

    return { testRun, executions: await Promise.all(createExecs) };
  }

  private static async create(
    trx: Knex.Transaction,
    run: RunDTO,
    milestoneUids?: number[],
    planUids?: number[],
  ) {
    const testRun = await TestRun.query(trx)
      .insert({
        name: run.name ?? '',
        projectUid: run.projectUid,
        systemType: 'run',
        entityTypes: [],
        description: run.description ?? '',
        externalId: run.externalId,
        source: run.source,
        customFields: {
          frequency: null, // ensure frequency of executins is reset to zero
          progress: null, // reset progress to zero
          caseCount: null,
          dueAt: run.dueAt ?? null, // ensure dueAt always has a value
          configs: run.configs ?? [],
          link: run.link,
          priority: run.priority,
          status: run.status,
        } as CustomFields,
      })
      .returning('*');

    if (run.tagUids?.length > 0) {
      await TestRun.addTags(trx, [testRun.uid], run.tagUids);
    }

    if (milestoneUids?.length > 0) {
      await TestRun.addMilestones(trx, [testRun.uid], milestoneUids);
    }

    if (planUids?.length > 0) {
      await TestRun.addPlans(trx, [testRun.uid], planUids);
    }

    return testRun;
  }

  /**
   * @param tdb
   * @param latestRun
   * @param pref
   */
  static async updateProgress(
    tdb: Knex, // tenant db connection
    latestRun: TestRun,
    pref: CompletedStatuses,
  ) {
    await tdb.transaction(async (trx) => {
      const execs = await TestExecution.query(trx).where({
        testRunUid: latestRun.uid,
        deletedAt: null,
      });

      const hasCompletedRuns = pref.testRun.includes(
        latestRun.customFields?.status,
      );
      const { progress, execCount, frequency } = TestExecution.execProgress(
        execs,
        pref.testCase,
        hasCompletedRuns,
      );

      await TestRun.query(trx)
        .where('uid', latestRun.uid)
        .patch({
          customFields: {
            ...latestRun.customFields,
            progress: hasCompletedRuns ? 100 : progress,
            caseCount: execCount,
            frequency,
          },
        });
    });
  }

  static async deleteByIds(trx: Knex, ids: number[]) {
    const runs = await TestRun.query(trx)
      .whereIn('uid', ids)
      .whereNull('deletedAt')
      .patch({ deletedAt: trx.fn.now() })
      .returning('*');

    const runUids = runs.map((r) => r.uid);

    await TestExecution.query(trx)
      .whereIn('testRunUid', runUids)
      .patch({ deletedAt: trx.fn.now() });

    await TestMilestoneRun.query(trx)
      .whereIn('runUid', runUids)
      .patch({ deletedAt: trx.fn.now() });

    await TestPlanRun.query(trx)
      .whereIn('runUid', runUids)
      .patch({ deletedAt: trx.fn.now() });

    return runUids;
  }

  static get relationMappings() {
    return {
      testExecutions: {
        relation: Model.HasManyRelation,
        modelClass: TestExecution,
        join: {
          from: 'tags.uid',
          to: 'testExecutions.testRunUid',
        },
      },
      testMilestones: {
        relation: Model.ManyToManyRelation,
        modelClass: TestMilestone,
        join: {
          from: 'tags.uid',
          through: {
            from: 'testMilestoneRuns.runUid',
            to: 'testMilestoneRuns.milestoneUid',
          },
          to: 'tags.uid',
        },
      },
      testPlans: {
        relation: Model.ManyToManyRelation,
        modelClass: TestPlan,
        join: {
          from: 'tags.uid',
          through: {
            from: 'testPlanRuns.runUid',
            to: 'testPlanRuns.planUid',
            modify(builder) {
              builder.whereNull('testPlanRuns.deletedAt');
            },
          },
          to: 'tags.uid',
        },
      },
      runTags: {
        relation: Model.ManyToManyRelation,
        modelClass: Tag,
        join: {
          from: 'tags.uid',
          through: {
            from: 'testRunTags.runUid',
            to: 'testRunTags.tagUid',
            modify(builder) {
              builder.whereNull('testRunTags.deletedAt');
            },
          },
          to: 'tags.uid',
        },
      },
      attachments: {
        relation: Model.ManyToManyRelation,
        modelClass: Attachment,
        join: {
          from: 'tags.uid',
          through: {
            from: 'runAttachments.runUid',
            to: 'runAttachments.attachmentUid',
          },
          to: 'attachments.uid',
        },
      },
    };
  }

  static async findOne(trx: Knex, where: Partial<ModelObject<TestRun>>) {
    const run = await TestRun.query(trx)
      .where({ ...where, deletedAt: null, systemType: 'run' })
      .first();
    return run;
  }

  static async countRuns(
    trx: Knex,
    state: 'active' | 'archived',
    projectId?: number,
  ) {
    return TestRun.count(trx, state, 'run', projectId);
  }

  static async countCases(db: Knex, runUid: string) {
    const result = (await TestRun.query(db)
      .where({
        uid: runUid,
        deletedAt: null,
      })
      .select(db.raw('(tags."customFields"->>\'caseCount\')::int as count'))
      .first()) as any;

    return +result.count;
  }

  static async addMilestones(
    trx: Knex,
    runUids: number[],
    milestoneUids: number[],
  ) {
    const inserts = cartesianProduct([runUids, milestoneUids]).map((set) => {
      const [runUid, milestoneUid] = set;
      return { runUid, milestoneUid };
    });
    return TestMilestoneRun.create(trx, inserts);
  }

  static async removeMilestones(
    trx: Knex,
    runUids: number[],
    milestoneUids: number[],
  ) {
    const deleteRunMilestones = runUids.map((runUid) => TestMilestoneRun.deleteByRunUid(trx, runUid, milestoneUids));
    await Promise.all(deleteRunMilestones);
  }

  static async addPlans(trx: Knex, runUids: number[], planUids: number[]) {
    const inserts = cartesianProduct([runUids, planUids]).map((set) => {
      const [runUid, planUid] = set;
      return { runUid, planUid };
    });

    await TestPlanRun.create(trx, inserts);
  }

  static async removePlans(trx: Knex, runUids: number[], planUids: number[]) {
    const deleteRunPlans = runUids.map((runUid) => TestPlanRun.deleteByRunUid(trx, runUid, planUids));
    await Promise.all(deleteRunPlans);
  }

  static async addTags(trx: Knex, runUids: number[], tagUids: number[]) {
    const inserts = cartesianProduct([runUids, tagUids]).map((set) => {
      const [runUid, tagUid] = set;
      return { runUid, tagUid };
    });

    await TestRunTag.create(trx, inserts);
  }

  static async updateDueAt(trx: Knex, runUids: number[], dueAt: Date) {
    await TestRun.query(trx)
      .whereIn('uid', runUids)
      .where({ systemType: 'run', deletedAt: null })
      .patch(<any>{ 'customFields:dueAt': dueAt.toISOString() });
  }

  static async updateArchiveStatus(
    trx: Knex,
    runUids: number[],
    archive: boolean,
  ) {
    await TestRun.query(trx)
      .whereIn('uid', runUids)
      .where({ systemType: 'run', deletedAt: null })
      .patch({ archivedAt: archive ? trx.fn.now() : trx.raw('null') });
  }

  static findByIds(trx: Knex, uids: number[]) {
    if (!uids || uids.length === 0) return [];

    return TestRun.query(trx)
      .whereNull('deletedAt')
      .whereIn('uid', uids)
      .where('systemType', 'run');
  }

  static async updateOne(trx: Knex, uid: number, update: UpdateRunDTO) {
    let run = await TestRun.query(trx)
      .where({
        uid,
        deletedAt: null,
        systemType: 'run',
      })
      .first();
    if (!run) return null;

    const baseUpdate: Partial<ModelObject<TestRun>> = {
      customFields: {
        ...run.customFields,
        ...(update.status ? { status: update.status } : {}),
        ...(update.priority ? { priority: update.priority } : {}),
        ...(update.dueAt ? { dueAt: update.dueAt } : {}),
        ...(update.configs ? { configs: update.configs } : {}),
      },
      description: update.description ?? run.description ?? '',
      name: update.name ?? run.name,
      updatedAt: trx.fn.now() as any,
    };
    if (_.has(update, 'archive')) {
      baseUpdate.archivedAt = (
        update.archive ? trx.fn.now() : trx.raw('null')
      ) as any;
    }

    run = await TestRun.query(trx).patchAndFetchById(run.uid, baseUpdate);

    if (update.addTagUids?.length > 0) {
      await TestRun.addTags(trx, [run.uid], update.addTagUids);
    }
    if (update.removeTagUids?.length > 0) {
      await TestRunTag.deleteByRunUid(trx, run.uid, update.removeTagUids);
    }

    // update plans
    if (update.removePlanUids?.length > 0) {
      await TestPlanRun.deleteByRunUid(trx, run.uid, update.removePlanUids);
    }
    if (update.addPlanUids?.length > 0) {
      await TestRun.addPlans(trx, [run.uid], update.addPlanUids);
    }

    // update milestones
    if (update.removeMilestoneUids?.length > 0) {
      await TestMilestoneRun.deleteByRunUid(
        trx,
        run.uid,
        update.removeMilestoneUids,
      );
    }
    if (update.addMilestoneUids?.length > 0) {
      await TestRun.addMilestones(trx, [run.uid], update.addMilestoneUids);
    }

    // set executions : handle addition of new cases

    return run;
  }

  static async addCases(
    trx: Knex.Transaction,
    runUid: number,
    caseUids: number[],
    status?: number,
    priority?: number,
  ) {
    const validCases = await TestCase.query(trx)
      .select('*')
      .whereIn('uid', caseUids)
      .where('active', true)
      .whereNull('deletedAt');

    const folderUids: number[] = [];
    const createExecs: Array<Promise<TestExecution>> = [];
    for (const tc of validCases) {
      createExecs.push(
        TestExecution.createFromTestCase(trx, tc, {
          runUid,
          priority: priority || tc.priority,
          status,
        }),
      );

      if (tc.parentUid) {
        folderUids.push(tc.parentUid);
      }
    }

    await TestRun.appendToFolderTree(trx, runUid, folderUids);

    return Promise.all(createExecs);
  }

  static async removeExecs(
    trx: Knex.Transaction,
    runUid: number,
    execUids: number[],
  ) {
    const execs = await TestExecution.query(trx)
      .where('testRunUid', runUid)
      .whereIn('uid', execUids)
      .patch({ testRunUid: trx.raw('null') })
      .returning('*');

    return execs;
  }

  static async appendToFolderTree(
    trx: Knex.Transaction,
    runUid: number,
    folderUids: number[],
  ) {
    if (folderUids.length === 0) return;

    const runFolders = await Folder.getCompleteFolderTreeNodes(trx, folderUids);

    await TestRunFolder.create(
      trx,
      runFolders.map((rf) => ({ folderUid: rf.uid, testRunUid: runUid })),
    );
  }

  static async getFolders(trx: Knex, runUid: number) {
    return TestRunFolder.query(trx)
      .where('testRunUid', runUid)
      .innerJoin('tags', 'folderUid', 'tags.uid')
      .where({ 'tags.systemType': 'folder' })
      .select('tags.uid', 'tags.name', 'tags.parentUid');
  }

  static async findAll(
    trx: Knex,
    query: ListRunsDTO,
  ): Promise<PaginatedResult<TestRun>> {
    const sql = TestRun.query(trx)
      .where((builder) => {
        if (query.q) builder.whereILike('name', `%${query.q}%`);

        if (query.priorityUids?.length > 0) {
          builder.whereIn(
            trx.raw('(tags."customFields"->>\'priority\')::int'),
            query.priorityUids,
          );
        }

        if (query.statusUids?.length > 0) {
          builder.whereIn(
            trx.raw('(tags."customFields"->>\'status\')::int'),
            query.statusUids,
          );
        }

        if (query.configs?.length > 0) {
          builder.whereRaw('tags."customFields"->\'configs\' \\?| ?', [
            query.configs,
          ]);
        }

        // filter due dates
        if (query.fromDueDate) {
          builder.whereRaw(
            '(tags."customFields"->>\'dueAt\')::date >= ?',
            query.fromDueDate,
          );
        }
        if (query.toDueDate) {
          builder.whereRaw(
            '("customFields"->>\'dueAt\')::date <= ?',
            query.toDueDate,
          );
        }

        // filter creation date
        if (query.fromCreatedAt) {
          builder.where('createdAt', '>=', query.fromCreatedAt);
        }
        if (query.toCreatedAt) {
          builder.where('createdAt', '<=', query.toCreatedAt);
        }

        // filter by progress
        if (query.minProgress) {
          builder.whereRaw(
            '("customFields"->>\'progress\')::decimal >= ?',
            query.minProgress,
          );
        }

        if (query.maxProgress) {
          builder.whereRaw(
            '("customFields"->>\'progress\')::decimal <= ?',
            query.maxProgress,
          );
        }
      })
      .where({
        'tags.projectUid': query.projectUid,
        'tags.systemType': 'run',
        'tags.deletedAt': null,
      })
      .orderBy('updatedAt', 'DESC')
      .select('tags.*');

    // plans filters
    if (query.planUid) {
      sql.whereExists(
        TestRun.relatedQuery('testPlans', trx)
          .where('systemType', 'plan')
          .where('uid', query.planUid),
      );
    } else if (_.isNull(query.planUid)) {
      sql
        .leftJoin('testPlanRuns', 'testPlanRuns.runUid', 'tags.uid')
        .leftJoin(
          { testPlans: 'tags' } as any,
          'testPlanRuns.planUid',
          'testPlans.uid',
        )
        .groupBy('tags.uid')
        .havingRaw('count("testPlans".uid)=0');
    }

    if (query.milestoneUids) {
      sql.whereExists(
        TestRun.relatedQuery('testMilestones').whereIn(
          'testMilestones.uid',
          query.milestoneUids,
        ),
      );
    }

    if (query.tagUids?.length > 0) {
      sql.whereExists(
        TestRun.relatedQuery('runTags')
          .where('systemType', 'tag')
          .whereNull('testRunTags.deletedAt')
          .whereIn('runTags.uid', query.tagUids),
      );
    }

    return paginated(sql as any, query.limit, query.offset, trx, TestRun.toDTO);
  }

  /**
   * retrieves a key value mapping of runUid => milestone[]
   * @param trx
   * @param runUids
   */
  static async getMilestones(trx: Knex, runUids: number[]) {
    const milestoneRuns = await TestMilestoneRun.query(trx)
      .whereIn('runUid', runUids)
      .whereNull('deletedAt')
      .withGraphFetched('[testMilestone]')
      .modifyGraph('testMilestone', (builder) => builder.select('name', 'uid'));

    const map: Record<string, TestMilestone[]> = {};

    for (const milestoneRun of milestoneRuns) {
      const key = milestoneRun.runUid.toString();
      if (map[key]) map[key].push(milestoneRun.testMilestone);
      else map[key] = [milestoneRun.testMilestone];
    }

    return map;
  }

  /**
   * returns a  retrieves a key value mapping of runUid => tag[]
   * @param trx
   * @param runUids
   * @returns
   */
  static async getTags(trx: Knex, runUids: number[]) {
    const runTags = await TestRunTag.query(trx)
      .whereIn('runUid', runUids)
      .whereNull('deletedAt')
      .withGraphFetched('[tag]')
      .modifyGraph('tag', (builder) => builder.select('name', 'uid'));

    const map: Record<string, Tag[]> = {};

    for (const runTag of runTags) {
      const key = runTag.runUid.toString();
      if (map[key]) map[key].push(runTag.tag);
      else map[key] = [runTag.tag];
    }

    return map;
  }

  static async getConfigs(trx: Knex, runUids: number[]) {
    const runs = await TestRun.query(trx)
      .whereIn('uid', runUids)
      .select('uid', 'customFields');
    const configUids: any[] = [];

    for (const run of runs) {
      const uids = run.customFields.configs?.map((c) => c.split('::'));
      configUids.push(..._.flatten(uids));
    }

    const config = await Tag.findByIds(trx, configUids);

    const configGroups: Record<string, Tag> = config.reduce((acc, c) => {
      acc[c.uid.toString()] = c;
      return acc;
    }, {} as any);

    const map: Record<string, { name: string; option: string }[]> = {};

    for (const run of runs) {
      const k = run.uid.toString();
      map[k] = [];
      if (!run.customFields?.configs) continue;

      // eslint-disable-next-line no-unsafe-optional-chaining
      for (const config of run.customFields?.configs) {
        const [cfg, opt] = config.split('::');
        map[k].push({
          name: configGroups[cfg].name,
          option: configGroups[opt].name,
        });
      }
    }

    return map;
  }

  static toDTO(r: TestRun): TestRun {
    r.status = r.customFields?.status;
    r.priority = r.customFields?.priority;

    delete r.customFields?.priority;
    delete r.customFields?.status;
    delete r.customFields?.configs;
    delete r.entityTypes;
    delete r.parentUid;
    delete r.systemType;
    delete r.slug;

    return r;
  }
}

export class TestRunTag extends Model {
  runUid: number;

  tagUid: number;

  tag?: Tag;

  static get tableName() {
    return 'testRunTags';
  }

  static get idColumn() {
    return ['runUid', 'tagUid'];
  }

  static get relationMappings() {
    return {
      tag: {
        relation: Model.BelongsToOneRelation,
        modelClass: Tag,
        join: { from: 'testRunTags.tagUid', to: 'tags.uid' },
      },
    };
  }

  static async create(trx: Knex, details: Partial<ModelObject<TestRunTag>>[]) {
    const deletedAt: any = trx.raw('null');

    return TestRunTag.query(trx)
      .insert(
        details.map((d) => ({
          ...d,
          deletedAt,
        })),
      )
      .onConflict(['runUid', 'tagUid'])
      .merge();
  }

  static async copyRunTags(trx: Knex, destRunUid: number, srcRunUid: number) {
    const tags = await TestRunTag.query(trx)
      .where({ deletedAt: null, runUid: srcRunUid })
      .select(['tagUid']);

    if (tags.length > 0) {
      await TestRunTag.create(
        trx,
        tags.map((t) => ({
          runUid: destRunUid,
          tagUid: t.tagUid,
        })),
      );
    }
    return tags;
  }

  static async deleteByRunUid(trx: Knex, runUid: number, tagUids?: number[]) {
    return TestRunTag.query(trx)
      .where({ runUid, deletedAt: null })
      .where((q) => {
        if (tagUids?.length > 0) {
          q.whereIn('tagUid', tagUids);
        }
      })
      .patch({ deletedAt: trx.fn.now() })
      .returning('*');
  }
}

export class TestRunFolder extends Model {
  testRunUid: number;

  folderUid: number;

  static get idColumn() {
    return ['testRunUid', 'folderUid'];
  }

  static get tableName() {
    return 'testRunFolders';
  }

  static create(
    trx: Knex.Transaction,
    data: Partial<ModelObject<TestRunFolder>>[],
  ) {
    return TestRunFolder.query(trx)
      .insert(data)
      .onConflict(['testRunUid', 'folderUid'])
      .ignore();
  }
}

export type TestRunModel = ModelClass<TestRun> & typeof TestRun;

export interface CustomFields extends _CustomFields {
  /**
   * configuration options for the run
   * each element should be in the format <confiId>::<optionId>
   */
  configs?: string[];
  /**
   * tag uids for the run
   */
  tags?: string[];
  /**
   * total number of test cases attached to the run
   */
  caseCount?: number;
  /**
   * current run progress
   */
  progress?: number;
  /**
   * frequency of statuses
   */
  frequency?: StatusFreq;
  /**
   * current run status
   */
  status?: number;
  /**
   * current run priority
   */
  priority?: number;
  /**
   * due date for the test run
   */
  dueAt?: Date;
  /**
   * Uid of the testRun before migrating to tags
   */
  prevUid?: number;
  /**
   * integration link
   */
  link?: string;
  /**
   * integration sync timestamp
   */
  syncedAt?: Date;
  /**
   * plan id of the test run according to external source
   */
  externalPlanId?: string;
  /**
   * milestone id of the test run according to external source
   */
  externalMilestoneId?: string;
  /**
   * entryId of the test run according to external source
   */
  entryId?: string;
}
