import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import { Project } from './project';

export class Dashboard extends Model {
  uid: number;

  name: string;

  body: Record<string, any>;

  editable: boolean;

  systemDefault: boolean;

  createdBy: string;

  projectUid: number;

  entityType: string;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'dashboards';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['name', 'body', 'createdBy'],
      properties: {
        uid: { type: 'integer' },
        name: { type: 'string', maxLength: 255 },
        body: { type: 'object' },
        editable: { type: 'boolean' },
        systemDefault: { type: 'boolean' },
        createdBy: { type: 'string', format: 'uuid' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      project: {
        relation: Model.BelongsToOneRelation,
        modelClass: Project,
        join: {
          from: 'dashboards.projectUid',
          to: 'projects.uid',
        },
      },
    };
  }
}

export type DashboardModel = ModelClass<Dashboard>;
