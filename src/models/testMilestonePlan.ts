import { ModelClass, ModelObject } from 'objection';

import { Knex } from 'knex';
import { Model } from '@app/lib/model';
import { TestPlan } from './testPlan';

export class TestMilestonePlan extends Model {
  uid: string;

  milestoneUid: number;

  planUid: number;

  testPlan?: TestPlan;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'testMilestonePlans';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['milestoneUid', 'planUid'],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        milestoneUid: { type: 'integer' },
        planUid: { type: 'integer' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      testPlan: {
        relation: Model.BelongsToOneRelation,
        modelClass: TestPlan,
        join: { from: 'testMilestonePlans.planUid', to: 'tags.uid' },
      },
    };
  }

  static async create(
    trx: Knex,
    details: Array<Partial<ModelObject<TestMilestonePlan>>>,
  ) {
    return TestMilestonePlan.query(trx)
      .insert(
        details.map((d) => ({
          planUid: d.planUid,
          milestoneUid: d.milestoneUid,
          createdAt: trx.fn.now(),
          updatedAt: trx.fn.now(),
          deletedAt: trx.raw('null'),
        })),
      )
      .onConflict(['planUid', 'milestoneUid'])
      .merge();
  }

  static async deleteByPlanUid(
    trx: Knex,
    planUid: number,
    milestoneUids?: number[],
  ) {
    return TestMilestonePlan.query(trx)
      .where({ planUid, deletedAt: null })
      .where((q) => {
        if (milestoneUids?.length > 0) {
          q.whereIn('milestoneUid', milestoneUids);
        }
      })
      .patch({ deletedAt: trx.fn.now() })
      .returning('*');
  }
}

export type TestMilestonePlanModel = ModelClass<TestMilestonePlan>;
