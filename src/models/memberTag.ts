import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';

export class MemberTag extends Model {
  userUid: string;

  tagUid: number;

  static get tableName() {
    return 'memberTags';
  }

  static get idColumn() {
    return null;
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['userUid', 'tagUid'],
      properties: {
        userUid: { type: 'string', format: 'uuid' },
        tagUid: { type: 'integer' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }
}

export type MemberTagModel = ModelClass<MemberTag>;
