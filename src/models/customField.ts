import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';

export const types = <const>[
  'multi',
  'radio',
  'link',
  'text',
  'checkbox',
  'date',
  'file',
  'step',
  'dropdown',
  'integer',
];

export type CustomFieldTypes = (typeof types)[number] extends infer U ? U : never;

export class Custom<PERSON>ield extends Model {
  uid: string;

  name: string;

  type: CustomFieldTypes;

  slug: string;

  options: string[];

  source: string;

  externalId: string;

  ownerUid: string;

  ownerType: string;

  projectUid: number;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'customFields';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['name'],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        name: { type: 'string' },
        type: {
          type: 'string',
          enum: [...types],
        },
        slug: { type: 'string' },
        options: { type: 'array', items: { type: 'string' } },
        source: { type: 'string' },
        externalId: { type: 'string' },
        ownerUid: { type: 'string', format: 'uuid' },
        ownerType: { type: 'string' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get jsonAttributes() {
    return [];
  }
}

export type CustomFieldModel = ModelClass<CustomField>;
