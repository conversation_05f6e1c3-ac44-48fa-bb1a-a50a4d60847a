import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';

export const componentTypes = <const>['frontend', 'backend'];
export type ComponentTypes = (typeof componentTypes)[number];

export class AppVersion extends Model {
  uid: number;

  version: string;

  component: ComponentTypes;

  isDefault: boolean;

  isActive: boolean;

  appNodeUid?: number;

  static get tableName() {
    return 'appVersions';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['version', 'component'],
      properties: {
        uid: { type: 'number' },
        version: { type: 'string' },
        isDefault: { type: 'boolean' },
        isActive: { type: 'boolean' },
        component: { type: 'string', enum: <any>componentTypes },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }
}

export type AppVersionModel = ModelClass<AppVersion>;
