import {
  CreateMilestoneDTO,
  MilestoneQuery,
  UpdateMilestoneDTO,
} from '@app/types/milestone';
import { Knex } from 'knex';
import { Model, paginated } from '@app/lib/model';
import { ModelClass } from 'objection';
import _ from 'lodash';
import { Tag } from './tag';
import { TestPlan } from './testPlan';
import { TestRun } from './testRun';
import { TestMilestonePlan } from './testMilestonePlan';
import { TestMilestoneRun } from './testMilestoneRun';
import { CompletedStatuses } from './preferences';
import { TestExecution } from './testExecution';

interface MilestoneListItem {
  name: TestMilestone['name'];
  status: CustomFields['status'];
  testPlanCount: number;
  testRunCount: number;
  testCaseCount: number;
  description: CustomFields['description'];
  startDate: CustomFields['startDate'];
  dueAt: CustomFields['dueAt'];
  progress: CustomFields['progress'];
  archivedAt: TestMilestone['archivedAt'];
  uid: Tag['uid'];
  tagUids: CustomFields['tagUids'];
  externalFields?: any;
}

export class TestMilestone extends Tag<CustomFields, 'milestone'> {
  tags?: Tag[];

  status?: number;

  testPlans?: TestPlan[];

  testRuns?: TestRun[];

  testPlanCount?: number;

  testRunCount?: number;

  testCaseCount?: number;

  static get tableName() {
    return 'tags';
  }

  static async create(
    trx: Knex.Transaction,
    projectUid: number,
    details: CreateMilestoneDTO,
  ) {
    return TestMilestone.query(trx)
      .insert({
        name: details.name,
        description: details.description,
        projectUid,
        customFields: {
          status: details.status,
          dueAt: details.dueAt,
          startDate: details.startDate,
          ...(details.tagUids && { tagUids: details.tagUids }),
          syncedAt: new Date().toISOString(),
        },
        systemType: 'milestone',
      })
      .returning('*');
  }

  static async populateTags(trx: Knex, mstone: any) {
    const uids = mstone.customFields?.tagUids ?? mstone.tagUids ?? [];
    if (uids.length > 0) {
      mstone.tags = await Tag.query(trx)
        .whereIn('uid', uids)
        .select('uid', 'name');
    } else {
      mstone.tags = [];
    }
    return mstone;
  }

  static async updateOne(
    trx: Knex.Transaction,
    uid: number,
    update: UpdateMilestoneDTO,
  ) {
    const {
      archived,
      dueAt,
      startDate,
      planIds,
      runIds,
      status,
      tagUids,
      ...base
    } = update;

    let mstone = await TestMilestone.query(trx)
      .where({
        uid,
        systemType: 'milestone',
        deletedAt: null,
      })
      .first();
    if (!mstone) return null;

    const changes: Partial<TestMilestone> = {
      ...base,
      archivedAt: archived ? trx.fn.now() : <any>trx.raw('null'),
    };
    if (archived) changes.archivedAt = <any>trx.fn.now();
    const { customFields } = mstone;

    customFields.dueAt = dueAt ?? customFields.dueAt;
    customFields.startDate = startDate ?? customFields.startDate;
    customFields.status = status ?? customFields.status;
    customFields.tagUids = tagUids ?? customFields.tagUids;
    changes.customFields = customFields;

    [mstone] = await TestMilestone.query(trx)
      .where({ uid: mstone.uid })
      .patch(changes)
      .withGraphFetched('[testPlans, testRuns]')
      .modifyGraph('testPlans', (q) => {
        q.whereNull('testMilestonePlans.deletedAt');
      })
      .modifyGraph('testRuns', (q) => {
        q.whereNull('testMilestoneRuns.deletedAt');
      })
      .returning('*');

    if (planIds.length > 0) {
      const existing = mstone.testPlans.map((p) => p.uid);
      const creates = _.difference(planIds, existing);
      const deletes = _.difference(existing, planIds);

      if (deletes.length > 0) {
        await TestMilestonePlan.query(trx)
          .patch({ deletedAt: trx.fn.now() })
          .where('milestoneUid', mstone.uid)
          .whereIn('planUid', deletes);
      }

      if (creates.length > 0) {
        await TestMilestonePlan.query(trx)
          .insert(
            creates.map((c) => ({
              milestoneUid: mstone.uid,
              planUid: c,
              deletedAt: trx.raw('null'),
            })),
          )
          .onConflict(['planUid', 'milestoneUid'])
          .merge();
      }
    }

    if (runIds.length > 0) {
      const existing = mstone.testRuns.map((p) => p.uid);
      const creates = _.difference(runIds, existing);
      const deletes = _.difference(existing, runIds);

      if (deletes.length > 0) {
        await TestMilestoneRun.query(trx)
          .patch({ deletedAt: trx.fn.now() })
          .where('milestoneUid', mstone.uid)
          .whereIn('runUid', deletes);
      }

      if (creates.length > 0) {
        await TestMilestoneRun.query(trx)
          .insert(
            creates.map((c) => ({
              milestoneUid: mstone.uid,
              runUid: c,
              deletedAt: trx.raw('null'),
            })),
          )
          .onConflict(['runUid', 'milestoneUid'])
          .merge();
      }
    }

    return mstone;
  }

  static async findAll(trx: Knex, q: MilestoneQuery) {
    const mstones = await TestMilestone.query(trx)
      .where({
        'tags.systemType': 'milestone',
        'tags.deletedAt': null,
        'tags.projectUid': q.projectUid,
      })
      .withGraphFetched(
        '[testPlans.runs.testExecutions, testRuns.testExecutions]',
      )
      .modifyGraph('testPlans', (q) => {
        q.whereNull('testMilestonePlans.deletedAt');
      })
      .modifyGraph('testRuns', (q) => {
        q.whereNull('testMilestoneRuns.deletedAt');
      });

    const res: MilestoneListItem[] = [];
    for (const m of mstones) {
      const runs = [
        ...m.testRuns,
        ..._.flatten(m.testPlans.map((p) => p.runs)),
      ].filter((r) => _.isNil(r.deletedAt));

      const totalRuns = _.uniqBy(runs, (r) => r.uid).filter((p) => _.isNull(p.deletedAt)).length;

      const totalCases = _.uniqBy(
        _.flatten(
          runs.map((r) => r.testExecutions.filter((e) => _.isNull(e.deletedAt))),
        ),
        (e) => e.testCaseUid,
      ).length;
      res.push({
        name: m.name,
        status: m.customFields.status,
        testPlanCount: m.testPlans.filter((p) => _.isNull(p.deletedAt)).length,
        testRunCount: totalRuns,
        testCaseCount: totalCases,
        description: m.customFields.description,
        startDate: m.customFields.startDate,
        dueAt: m.customFields.dueAt,
        progress: m.customFields.progress,
        archivedAt: m.archivedAt,
        uid: m.uid,
        tagUids: m.customFields.tagUids,
      });
    }
    return res;
  }

  static async findPaginated(trx: Knex, q: MilestoneQuery) {
    const sql = TestMilestone.query(trx)
      .where({
        'tags.systemType': 'milestone',
        'tags.deletedAt': null,
        'tags.projectUid': q.projectUid,
      })
      .select('tags.*')
      .orderBy('tags.createdAt', 'asc')
      .where((builder) => {
        if (q.status) {
          builder.whereRaw('tags."customFields"->>\'status\' = ?', [q.status]);
        }
        if (q.tagUids && q.tagUids.length > 0) {
          builder.whereRaw(
            'TRANSLATE((tags."customFields"->\'tagUids\')::JSONB::TEXT,\'[]\',\'{}\')::TEXT[] && ?',
            [q.tagUids],
          );
        }
        return builder;
      });

    const page = await paginated(
      sql as any,
      q.limit,
      q.offset,
      trx,
      async (m: TestMilestone) => {
        const result = {
          name: m.name,
          status: m.customFields?.status,
          description: m.customFields?.description,
          startDate: m.customFields?.startDate,
          dueAt: m.customFields?.dueAt,
          progress: m.customFields?.progress,
          archivedAt: m.archivedAt,
          uid: m.uid,
          tagUids: m.customFields?.tagUids,
        } as any;

        await TestMilestone.populateTags(trx, result);
        return result;
      },
    );
    return page;
  }

  static async updateProgress(
    tdb: Knex,
    latestMilestone: TestMilestone,
    pref: CompletedStatuses,
  ) {
    const tdbIsTrx = tdb.isTransaction;
    const trx: Knex.Transaction = tdbIsTrx ? <any>tdb : await tdb.transaction();
    try {
      // get all plan.run and run execs
      const [plans, runs] = await Promise.all([
        latestMilestone
          .$relatedQuery('testPlans', trx)
          .whereNull('testMilestonePlans.deletedAt')
          .withGraphFetched('[runs.testExecutions]'),
        latestMilestone
          .$relatedQuery('testRuns', trx)
          .whereNull('testMilestoneRuns.deletedAt')
          .withGraphFetched('[testExecutions]'),
      ]);

      let hasCompleteRuns = false;

      const execs: TestExecution[] = [];

      for (const plan of plans) {
        if (!_.isNull(plan.deletedAt)) continue;
        for (const run of plan.runs) {
          if (!_.isNull(run.deletedAt)) continue;
          // if run is marked as complete, only consider the complete executions
          if (pref.testRun.includes(run.customFields?.status)) {
            hasCompleteRuns = true;
            execs.push(
              ...run.testExecutions.filter((e) => pref.testCase.includes(e.status)),
            );
          } else {
            execs.push(...run.testExecutions);
          }
        }
      }

      for (const r of runs) {
        if (!_.isNull(r.deletedAt)) continue;
        // if a run has been marked as completed, we only consider the completed executions
        if (pref.testRun.includes(r.customFields.status)) {
          hasCompleteRuns = true;
          execs.push(
            ...r.testExecutions.filter((e) => pref.testCase.includes(e.status)),
          );
        } else {
          execs.push(...r.testExecutions);
        }
      }

      const { progress } = TestExecution.execProgress(
        execs,
        pref.testCase,
        hasCompleteRuns,
      );
      await TestMilestone.query(trx)
        .findById(latestMilestone.uid)
        .patch({ customFields: { ...latestMilestone.customFields, progress } });

      if (!tdbIsTrx) await trx.commit();
    } catch (err) {
      if (!tdbIsTrx) await trx.rollback();
    }
  }

  static async deleteByIds(trx: Knex, ids: number[]) {
    const rows = await TestMilestone.query(trx)
      .whereIn('uid', ids)
      .where('systemType', 'milestone')
      .whereNull('deletedAt')
      .patch({ deletedAt: trx.fn.now() })
      .returning('*');
    return rows;
  }

  static async findOne(trx: Knex, where: Partial<TestMilestone>) {
    const milestone = await TestMilestone.query(trx)
      .where({ ...where, deletedAt: null, systemType: 'milestone' })
      .withGraphFetched(
        `[
          testRuns.[testExecutions],
          testPlans.[runs.[testExecutions]]
        ]`,
      )
      .modifyGraph('testPlans', (query) => {
        query.whereNull('testMilestonePlans.deletedAt');
      })
      .modifyGraph('testRuns', (query) => {
        query.whereNull('testMilestoneRuns.deletedAt');
      })
      .modifyGraph('testPlans.runs', (query) => {
        query.whereNull('testPlanRuns.deletedAt');
      })
      .modifyGraph('testRuns.testExecutions', (query) => {
        query.whereNull('testExecutions.deletedAt');
      })
      .modifyGraph('testPlans.runs.testExecutions', (query) => {
        query.whereNull('testExecutions.deletedAt');
      })
      .first();
    return milestone;
  }

  static async countRuns(db: Knex, milestoneUid: string): Promise<number> {
    const [result] = (await TestMilestone.query(db)
      .where('tags.uid', milestoneUid)
      .leftJoin('testMilestoneRuns', (builder) => {
        builder
          .on('tags.uid', '=', 'testMilestoneRuns.milestoneUid')
          .andOnNull('testMilestoneRuns.deletedAt');
      })
      .leftJoin('tags as run_tags', (builder) => {
        builder
          .on('testMilestoneRuns.runUid', '=', 'run_tags.uid')
          .andOnNull('run_tags.deletedAt');
      })
      .leftJoin('testMilestonePlans', (builder) => {
        builder
          .on('tags.uid', '=', 'testMilestonePlans.milestoneUid')
          .andOnNull('testMilestonePlans.deletedAt');
      })
      .leftJoin('testPlanRuns', (builder) => {
        builder
          .on('testMilestonePlans.planUid', '=', 'testPlanRuns.planUid')
          .andOnNull('testPlanRuns.deletedAt');
      })
      .leftJoin('tags as plan_run_tags', (builder) => {
        builder
          .on('testPlanRuns.runUid', '=', 'plan_run_tags.uid')
          .andOnNull('plan_run_tags.deletedAt');
      })
      .countDistinct(db.raw('COALESCE(run_tags.uid, plan_run_tags.uid)'), {
        as: 'count',
      })) as any[];

    return +result.count;
  }

  static async countPlans(db: Knex, milestoneUid: string): Promise<number> {
    const [result] = (await TestMilestone.query(db)
      .where('tags.uid', milestoneUid)
      .leftJoin('testMilestonePlans', (builder) => {
        builder
          .on('tags.uid', '=', 'testMilestonePlans.milestoneUid')
          .andOnNull('testMilestonePlans.deletedAt');
      })
      .leftJoin('tags as plan_tags', (builder) => {
        builder
          .on('testMilestonePlans.planUid', '=', 'plan_tags.uid')
          .andOnNull('plan_tags.deletedAt');
      })
      .countDistinct('plan_tags.uid', { as: 'count' })) as any[];

    return +result.count;
  }

  static async countCases(db: Knex, milestoneUid: string): Promise<number> {
    const [result] = (await TestMilestone.query(db)
      .where('tags.uid', milestoneUid)
      // Direct milestone runs
      .leftJoin('testMilestoneRuns', (builder) => {
        builder
          .on('tags.uid', '=', 'testMilestoneRuns.milestoneUid')
          .andOnNull('testMilestoneRuns.deletedAt');
      })
      .leftJoin('tags as run_tags', (builder) => {
        builder
          .on('testMilestoneRuns.runUid', '=', 'run_tags.uid')
          .andOnNull('run_tags.deletedAt');
      })
      .leftJoin('testMilestonePlans', (builder) => {
        builder
          .on('tags.uid', '=', 'testMilestonePlans.milestoneUid')
          .andOnNull('testMilestonePlans.deletedAt');
      })
      .leftJoin('testPlanRuns', (builder) => {
        builder
          .on('testMilestonePlans.planUid', '=', 'testPlanRuns.planUid')
          .andOnNull('testPlanRuns.deletedAt');
      })
      .leftJoin('tags as plan_run_tags', (builder) => {
        builder
          .on('testPlanRuns.runUid', '=', 'plan_run_tags.uid')
          .andOnNull('plan_run_tags.deletedAt');
      })
      .leftJoin('testExecutions', (builder) => {
        builder
          .on((builder) => {
            builder
              .on('testExecutions.testRunUid', '=', 'run_tags.uid')
              .orOn('testExecutions.testRunUid', '=', 'plan_run_tags.uid');
          })
          .andOnNull('testExecutions.deletedAt');
      })
      .countDistinct('testExecutions.testCaseUid', { as: 'count' })) as any[];

    return +result.count;
  }

  static get relationMappings() {
    return {
      testPlans: {
        relation: Model.ManyToManyRelation,
        modelClass: TestPlan,
        join: {
          from: 'tags.uid',
          through: {
            from: 'testMilestonePlans.milestoneUid',
            to: 'testMilestonePlans.planUid',
          },
          to: 'tags.uid',
        },
      },
      testRuns: {
        relation: Model.ManyToManyRelation,
        modelClass: TestRun,
        join: {
          from: 'tags.uid',
          through: {
            from: 'testMilestoneRuns.milestoneUid',
            to: 'testMilestoneRuns.runUid',
          },
          to: 'tags.uid',
        },
      },
    };
  }

  /**
   * @param instance
   */
  static toDTO(instance: TestMilestone) {
    instance.status = instance.customFields?.status;
    delete instance.customFields?.status;

    return instance;
  }

  static async countMilestones(
    trx: Knex,
    state: 'active' | 'archived',
    projectId?: number,
  ) {
    return TestMilestone.count(trx, state, 'milestone', projectId);
  }
}

interface CustomFields {
  status: number;
  prevUid?: number;
  dueAt?: string;
  startDate?: string;
  completedAt?: string;
  progress?: number;
  tagUids?: number[];
  description?: string;
  syncedAt?: string;
  link?: string;
}

export type TestMilestoneModel = ModelClass<TestMilestone> &
  typeof TestMilestone;
