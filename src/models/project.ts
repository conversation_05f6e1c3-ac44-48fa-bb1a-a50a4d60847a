import { DefaultTemplateData } from '@app/temporal/activities/template';
import { startWorkflow } from '@app/temporal/client';
import { CreateProjectDto } from '@app/types/project';
import { Knex } from 'knex';
import { kebabCase } from 'lodash';
import { ModelClass } from 'objection';
import { Model } from '../lib/model';
import { DefaultDashboardData } from '../temporal/activities/dashboard';
import { Defect } from './defect';
import { Role } from './role';
import { Tag } from './tag';
import { TestCase } from './testCase';
import { TestRun } from './testRun';

export class Project extends Model {
  uid: number;

  externalId: string;

  source: string;

  name: string;

  key: string;

  link: string;

  customFields: Record<string, any>;

  archivedAt: Date | string;

  avatarAttachmentUid: string;

  status?: 'pending' | 'active';

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'projects';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['name', 'key'],
      properties: {
        uid: { type: 'integer' },
        externalId: { type: 'string', maxLength: 255 },
        source: { type: 'string', maxLength: 255 },
        name: { type: 'string', maxLength: 255 },
        key: { type: 'string' },
        link: { type: 'string', maxLength: 255 },
        customFields: { type: 'object' },
        archivedAt: { type: ['string', 'null'], format: 'date-time' },
        avatarAttachmentUid: { type: 'string', maxLength: 255 },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      testCases: {
        relation: Model.HasManyRelation,
        modelClass: TestCase,
        join: {
          from: 'projects.uid',
          to: 'testCases.projectUid',
          modify(qb) {
            qb.whereNull('testCases.deletedAt');
          },
        },
      },
      roles: {
        relation: Model.HasManyRelation,
        modelClass: Role,
        join: {
          from: 'projects.uid',
          to: 'roles.projectUid',
          modify(qb) {
            qb.whereNull('roles.deletedAt');
          },
        },
      },
    };
  }

  static async countRuns(db: Knex, uid: number): Promise<number> {
    const [result] = (await TestRun.query(db)
      .where({
        systemType: 'run',
        deletedAt: null,
        projectUid: uid,
      })
      .count('uid')) as any[];
    return +result.count;
  }

  static async countCases(db: Knex, uid: number): Promise<number> {
    const [result] = (await TestCase.query(db)
      .where({
        deletedAt: null,
        projectUid: uid,
        active: true,
      })
      .count('uid')) as any[];
    return +result.count;
  }

  static async countDefects(db: Knex, uid: number): Promise<number> {
    const [result] = (await Defect.query(db)
      .where({
        deletedAt: null,
        projectUids: [uid],
      })
      .count('uid')) as any[];
    return +result.count;
  }

  static async newProject(
    projectDto: CreateProjectDto,
    ownerType: string,
    ownerUid: string,
    trx: Knex.Transaction,
  ) {
    const project = await this.query(trx)
      .insert({
        externalId: projectDto.externalId,
        source: projectDto.source,
        name: projectDto.name,
        key: projectDto.key.toUpperCase(),
        customFields: projectDto.customFields,
      })
      .returning('*');

    await Tag.query(trx).insert({
      name: project.name,
      slug: kebabCase(projectDto.name),
      projectUid: project.uid,
      customFields: { source: 'testfiesta' },
      entityTypes: ['cases'],
      systemType: 'folder',
    });

    const param: DefaultTemplateData = {
      ownerType,
      ownerUid,
      userId: ownerUid,
      projectId: project.uid,
    };

    await startWorkflow('templateWorkflow', {
      taskQueue: 'template-queue',
      workflowId: `${ownerUid}:template:${Date.now()}`,
      args: [param],
    });

    const param2: DefaultDashboardData = {
      projectUid: project.uid,
      ownerUid,
      projectName: `${project.name} Default Dashboard`,
      systemDefault: false,
      editable: true,
    };
    await startWorkflow('dashboardWorkflow', {
      taskQueue: 'dashboard-queue',
      workflowId: `${ownerUid}:dashboard:${Date.now()}`,
      args: [param2],
    });

    return project;
  }
}

export type ProjectModel = ModelClass<Project> & typeof Project;
