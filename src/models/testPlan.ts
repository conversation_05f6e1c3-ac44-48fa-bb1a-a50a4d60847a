import { Model, ModelClass, ModelObject } from 'objection';

import { CustomFields } from '@app/lib/model';
import { Knex } from 'knex';
import _, { clone, isEmpty } from 'lodash';
import {
  cartesianProduct,
  getDistinct,
  mergeAndGroup,
} from '@app/utils/arrays';
import {
  BulkUpdateAction,
  ConfigurationDTO,
  CreatePlanDTO,
  DuplicatePlanDTO,
} from '@app/types/plan';
import { slugify } from '@app/utils/string';
import {} from '@app/services';
import { Tag } from './tag';
import { TestMilestone } from './testMilestone';
import { TestMilestonePlan } from './testMilestonePlan';
import { TestRun } from './testRun';
import { TestExecution } from './testExecution';
import { CompletedStatuses, PreferenceMap, StatusFreq } from './preferences';
import { TestPlanRun } from './testPlanRun';
import { Attachment } from './attachment';

interface PlanCustomFields extends CustomFields {
  status: number;
  priority: number;
  /**
   * Uid of the testPlan before migrating to tags
   */
  prevUid?: number;
  progress?: number;
  // frequency of statuses
  frequency?: StatusFreq;
  tagUids?: number[];

  syncedAt?: string;
  link?: string;
}

export class TestPlan extends Tag<PlanCustomFields, 'plan'> {
  tags?: Tag[];

  runs?: TestRun[];

  milestones?: TestMilestone[];

  attachments?: any[];

  // utility fields
  testRunCount?: number;

  testMilestoneCount?: number;

  static get tableName() {
    return 'tags';
  }

  /**
   * finds a single test plan
   * @param trx db context
   * @param where where claise
   * @returns
   */
  static async findOne(
    trx: Knex,
    where: Partial<TestPlan>,
    withRuns = false,
  ): Promise<TestPlan> {
    const q = TestPlan.query(trx)
      .where({ ...where, systemType: 'plan' })
      .first();
    if (withRuns) {
      q.withGraphFetched('[runs,milestones]').modifyGraph('milestones', (q) => {
        q.whereNull('testMilestonePlans.deletedAt');
      });
    }

    const plan = await q;
    await TestPlan.populateTags(trx, plan);

    return plan;
  }

  static async populateTags(trx: Knex, plan: TestPlan | ModelObject<TestPlan>) {
    if (plan && plan.customFields?.tagUids?.length) {
      plan.tags = await Tag.query(trx)
        .whereIn('uid', plan.customFields.tagUids)
        .select('uid', 'name');
    }
    return plan;
  }

  static async findAll(
    where: Knex.QueryBuilder<TestPlan>,
    ctx = TestPlan.knex(),
  ) {
    return TestPlan.query(ctx)
      .where(where)
      .where({ systemType: 'plan', deletedAt: null });
  }

  static async countPlans(
    trx: Knex,
    state: 'active' | 'archived',
    projectId?: number,
  ) {
    return TestPlan.count(trx, state, 'plan', projectId);
  }

  static async destroyMany(
    trx: Knex,
    uids: number[],
    update: DestructiveUpdate,
    cascade: boolean,
  ) {
    const plans: TestPlan[] = await TestPlan.query(trx)
      .whereIn('uid', uids)
      .where('systemType', 'plan')
      .where((q) => {
        if (_.has(update, 'deletedAt')) {
          q.whereNull('deletedAt');
        }
        if (_.has(update, 'archivedAt')) {
          q.whereNull('archivedAt');
        }
        return q;
      })
      .patch(update)
      .returning('*');

    if (!cascade) return plans;

    // @todo: move thid to a worker ?
    for (const plan of plans) {
      // delete plan runs
      await TestMilestonePlan.deleteByPlanUid(trx, plan.uid);
      const planRuns = await TestPlanRun.deleteByPlanUid(trx, plan.uid);
      // delete runs
      await TestRun.deleteByIds(
        trx,
        planRuns.map((p) => p.runUid),
      );
    }

    return plans;
  }

  static async unarchive(trx: Knex, uids: number[]) {
    const plans: TestPlan[] = await TestPlan.query(trx)
      .whereNotNull('archivedAt')
      .whereIn('uid', uids)
      .where('systemType', 'plan')
      .patch({ archivedAt: trx.raw('null') })
      .returning('*');
    return plans;
  }

  static async bulkUpdateMilestones(
    trx: Knex.Transaction,
    uids: number[],
    milestoneUids: number[],
    action: BulkUpdateAction,
  ) {
    const plans: TestPlan[] = [];
    if (action === 'addMilestones') {
      plans.push(...(await TestPlan.addMilestones(trx, uids, milestoneUids)));
    } else if (action === 'removeMilestones') {
      plans.push(
        ...(await TestPlan.detachMilestones(trx, uids, milestoneUids)),
      );
    }
    return plans;
  }

  static async addMilestones(
    trx: Knex,
    uids: number[],
    milestoneUids: number[],
  ) {
    const inserts = cartesianProduct([uids, milestoneUids]).map((set) => {
      const [planUid, milestoneUid] = set;
      return { planUid, milestoneUid, deletedAt: trx.raw('null') };
    });

    const milestonePlans = await TestMilestonePlan.query(trx)
      .insert(inserts)
      .onConflict(['milestoneUid', 'planUid'])
      .merge()
      .returning('*')
      .withGraphFetched('[testPlan]');
    return milestonePlans.map((m) => m.testPlan);
  }

  static async detachMilestones(
    trx: Knex,
    uids: number[],
    milestoneUids: number[],
  ) {
    const milestonePlans = await TestMilestonePlan.query(trx)
      .whereIn('planUid', uids)
      .whereIn('milestoneUid', milestoneUids)
      .patch({ deletedAt: trx.fn.now() })
      .returning('*')
      .withGraphFetched('[testPlan]');
    return milestonePlans.map((m) => m.testPlan!);
  }

  static async bulkUpdateRuns(
    trx: Knex.Transaction,
    uids: number[],
    runUids: number[],
    action: BulkUpdateAction,
  ) {
    let plans: TestPlan[] = [];

    if (action === 'addRuns') {
      plans = await TestPlan.addRuns(trx, uids, runUids);
    } else if (action === 'removeRuns') {
      plans = await TestPlan.detachRuns(trx, uids, runUids);
    }

    return plans;
  }

  static async addRuns(trx: Knex, uids: number[], runUids: number[]) {
    const inserts = cartesianProduct([uids, runUids]).map((set) => {
      const [planUid, runUid] = set;
      return { planUid, runUid, deletedAt: trx.raw('null') };
    });

    const milestonePlans = await TestPlanRun.query(trx)
      .insert(inserts)
      .onConflict(['runUid', 'planUid'])
      .merge()
      .returning('*')
      .withGraphFetched('[testPlan]');
    return milestonePlans.map((m) => m.testPlan);
  }

  static async detachRuns(trx: Knex, uids: number[], runUids: number[]) {
    const planRuns = await TestPlanRun.query(trx)
      .whereIn('planUid', uids)
      .whereIn('runUid', runUids)
      .patch({ deletedAt: trx.fn.now() })
      .returning('*')
      .withGraphFetched('[testPlan]');
    return planRuns.map((m) => m.testPlan!);
  }

  static async updateMilestones(trx: Knex, plan: TestPlan, incoming: number[]) {
    const existing: number[] = [];
    if (plan.milestones) {
      existing.push(...plan.milestones.map((m) => m.uid));
    } else {
      const milestones = await TestMilestonePlan.query(trx).where({
        planUid: plan.uid,
        deletedAt: null,
      });
      existing.push(...milestones.map((m) => m.milestoneUid));
    }

    const toBeAdded = _.difference(incoming, existing);
    const toBeRemoved = _.difference(existing, incoming);

    if (toBeRemoved.length) {
      await TestPlan.detachMilestones(trx, [plan.uid], toBeRemoved);
    }

    if (toBeAdded.length) {
      await TestPlan.addMilestones(trx, [plan.uid], toBeAdded);
    }
  }

  static async newPlan(
    dto: CreatePlanDTO,
    projectUid: number,
    preferences: PreferenceMap,
    trx: Knex.Transaction,
  ) {
    const plan = await TestPlan.create(
      {
        ...dto,
        status: dto.status ?? preferences.testPlan?.status,
        priority: dto.priority ?? preferences.testPlan?.priority,
      },
      projectUid,
      trx,
    );

    const runUids = dto.testRuns.map((t) => t.uid);
    const testRuns = await TestRun.query(trx).whereIn('uid', runUids);

    const runs: TestRun[] = [];
    const executions: TestExecution[] = [];
    for (const run of testRuns) {
      const baseRun = {
        externalId: run.externalId,
        source: run.source,
        link: run.customFields?.link,
        testPlanUid: plan.uid,
        priority: preferences.testRun?.priority,
        status: preferences.testRun?.status,
        projectUid,
        name: run.name,
      };
      const customFields: TestRun['customFields'] = clone(
        run.customFields ?? <any>{},
      );

      const opts = getConfigOptions(
        dto.configuration,
        dto.testRuns.find((t) => t.uid === run.uid).configuration,
      );

      if (opts.length === 0) {
        const r = await TestRun.duplicate(
          trx,
          { ...baseRun, ...customFields },
          {
            planUid: plan.uid,
            execPriority: preferences.testCase?.priority,
            execStatus: preferences.testCase?.status,
            sourceRunUid: run.uid,
          },
        );
        runs.push(r.testRun);
        executions.push(...r.executions);
      } else {
        for (const opt of opts) {
          customFields.configs = opt;
          const r = await TestRun.duplicate(
            trx,
            { ...baseRun, ...customFields },
            {
              planUid: plan.uid,
              execPriority: preferences.testCase?.priority,
              execStatus: preferences.testCase?.status,
              sourceRunUid: run.uid,
            },
          );
          runs.push(r.testRun);
          executions.push(...r.executions);
        }
      }
    }
    return { plan, runs, executions };
  }

  static async duplicate(
    trx: Knex.Transaction,
    dtos: DuplicatePlanDTO[],
    projectUid: number,
    preferences: PreferenceMap,
  ) {
    const result: {
      plan: TestPlan;
      runs: TestRun[];
      executions: TestExecution[];
    }[] = [];

    for (const dto of dtos) {
      const sourcePlan = await TestPlan.findOne(trx, {
        uid: dto.uid,
      });
      if (!sourcePlan) continue;

      const plan = await TestPlan.create(
        {
          name: sourcePlan.name,
          description: sourcePlan.description ?? '',
          status: preferences.testPlan?.status,
          priority: preferences.testPlan?.priority,
          configuration: null,
          tagUids: sourcePlan.customFields?.tagUids,
        },
        projectUid,
        trx,
      );

      if (dto.runUids && dto.runUids.length === 0) {
        result.push({
          plan,
          runs: [],
          executions: [],
        });
        continue;
      }

      const validRuns = [];
      if (dto.runUids?.length > 0) {
        validRuns.push(
          ...(await TestRun.query(trx).whereIn('uid', dto.runUids)),
        );
      }

      const runs: TestRun[] = [];
      const executions: TestExecution[] = [];

      for (const run of validRuns) {
        const r = await TestRun.duplicate(
          trx,
          {
            ...run.customFields,
            name: run.name,
            priority: preferences.testRun?.priority,
            status: preferences.testRun?.status,
            projectUid,
            testPlanUid: plan.uid,
          },
          {
            planUid: plan.uid,
            sourceRunUid: run.uid,
            execPriority: preferences.testCase?.priority,
            execStatus: preferences.testCase?.status,
          },
        );
        runs.push(r.testRun);
        executions.push(...r.executions);
      }
      result.push({
        plan,
        runs: runs as TestRun[],
        executions: executions as TestExecution[],
      });
    }
    return result;
  }

  private static async create(
    dto: CreatePlanDTO,
    projectUid: number,
    trx: Knex,
  ) {
    const plan = await TestPlan.query(trx).insert({
      name: dto.name,
      description: dto.description,
      projectUid,
      customFields: {
        status: dto.status,
        priority: dto.priority,
        ...(dto.tagUids && { tagUids: dto.tagUids }),
        syncedAt: new Date().toISOString(),
      },
      systemType: 'plan',
      entityTypes: [],
      slug: slugify(`${dto.name}-${Date.now()}`),
    });

    if (dto.milestoneUids?.length > 0) {
      await TestMilestonePlan.create(
        trx,
        dto.milestoneUids.map((m) => ({
          planUid: plan.uid,
          milestoneUid: m,
        })),
      );
    }
    return plan;
  }

  static async updateProgress(
    tdb: Knex,
    latestPlan: TestPlan,
    pref: CompletedStatuses,
  ) {
    await tdb.transaction(async (trx) => {
      const runs = await TestRun.query(trx)
        .whereIn(
          'uid',
          TestPlan.relatedQuery('runs').for([latestPlan.uid]).select('uid'),
        )
        .withGraphFetched('[testExecutions]');
      const execs: TestExecution[] = [];
      let hasCompleteRuns = false;

      for (const r of runs) {
        // if a run has been marked as completed, we only consider the completed executions
        if (pref.testRun.includes(r.customFields?.status)) {
          hasCompleteRuns = true;
          execs.push(
            ...r.testExecutions.filter((e) => pref.testCase.includes(e.status)),
          );
        } else {
          execs.push(...r.testExecutions);
        }
      }

      const { progress, frequency } = TestExecution.execProgress(
        execs,
        pref.testCase,
        hasCompleteRuns,
      );

      await TestPlan.query(trx)
        .where('uid', latestPlan.uid)
        .patch({
          customFields: {
            ...latestPlan.customFields,
            progress: pref.testPlan.includes(latestPlan.customFields.status)
              ? 100
              : progress,
            frequency,
          },
        });
    });
  }

  static async deleteByIds(trx: Knex, ids: number[]) {
    const rows = await TestPlan.query(trx)
      .whereIn('uid', ids)
      .where('systemType', 'plan')
      .whereNull('deletedAt')
      .patch({ deletedAt: trx.fn.now() })
      .returning('*');
    return rows;
  }

  /**
   * gets the number of runs attached to a single plan
   * @param db
   * @param planUid
   * @returns
   */
  static async countRuns(db: Knex, planUid: number): Promise<number> {
    const [result] = (await TestPlanRun.query(db)
      .where({
        deletedAt: null,
        planUid,
      })
      .count('runUid')) as any[];
    return +result.count;
  }

  /**
   *
   * @param db gets the number of milestone attached to a single plan
   * @param planUid
   * @returns
   */
  static async countMilestones(db: Knex, planUid: string): Promise<number> {
    const [result] = (await TestMilestonePlan.query(db)
      .where({
        deletedAt: null,
        planUid,
      })
      .count('milestoneUid')) as any[];
    return +result.count;
  }

  /**
   * returns a mapping of planUid => numberOfMilestones
   * @param db
   * @param planUids
   */
  static async getMilestoneCount(db: Knex, planUids: number[]) {
    const result = planUids.reduce(
      (acc, uid) => {
        acc[uid.toString()] = 0;
        return acc;
      },
      {} as Record<string, number>,
    );

    const milestoneCounts = await TestMilestonePlan.query(db)
      .whereIn('planUid', planUids)
      .whereNull('deletedAt')
      .groupBy('planUid')
      .select('planUid', db.raw('count("milestoneUid")'));

    for (const { planUid, count } of milestoneCounts as any) {
      result[planUid.toString()] = +count;
    }
    return result;
  }

  /**
   * returns a mapping of planUid => numberOfRuns
   * @param db
   * @param planUids
   */
  static async getRunCount(db: Knex, planUids: number[]) {
    const result = planUids.reduce(
      (acc, uid) => {
        acc[uid.toString()] = 0;
        return acc;
      },
      {} as Record<string, number>,
    );

    const runCounts = await TestPlanRun.query(db)
      .whereIn('planUid', planUids)
      .whereNull('deletedAt')
      .groupBy('planUid')
      .select('planUid', db.raw('count("runUid")'));
    for (const { planUid, count } of runCounts as any) {
      result[planUid.toString()] = +count;
    }
    return result;
  }

  static async getTags(db: Knex, planUids: number[]) {
    const plans = await TestPlan.query(db)
      .whereIn('uid', planUids)
      .where('systemType', 'plan');

    const tagUids = _.flatten(plans.map((p) => p.customFields?.tagUids));

    const tags = (await Tag.findByIds(db, tagUids)).reduce((acc, tag) => {
      acc[tag.uid.toString()] = tag;
      return acc;
    }, {} as any);

    const result: { [key: string]: { name: string; uid: string }[] } = planUids.reduce((acc, p) => {
      acc[p] = [];
      return acc;
    }, {} as any);

    for (const plan of plans) {
      result[plan.uid.toString()] = plan.customFields.tagUids?.map((t) => {
        const tag = tags[t.toString()];
        return { name: tag.name, uid: tag.uid };
      });
    }

    return result;
  }

  static get relationMappings() {
    return {
      runs: {
        relation: Model.ManyToManyRelation,
        modelClass: TestRun,
        join: {
          from: 'tags.uid',
          through: {
            from: 'testPlanRuns.planUid',
            to: 'testPlanRuns.runUid',
            modify(qb) {
              qb.whereNull('testPlanRuns.deletedAt');
            },
          },
          to: 'tags.uid',
        },
      },
      milestones: {
        relation: Model.ManyToManyRelation,
        modelClass: TestMilestone,
        join: {
          from: 'tags.uid',
          through: {
            from: 'testMilestonePlans.planUid',
            to: 'testMilestonePlans.milestoneUid',
          },
          to: 'tags.uid',
        },
      },
      attachments: {
        relation: Model.ManyToManyRelation,
        modelClass: Attachment,
        join: {
          from: 'tags.uid',
          through: {
            from: 'planAttachments.planUid',
            to: 'planAttachments.attachmentUid',
          },
          to: 'attachments.uid',
        },
      },
    };
  }
}

export function getConfigOptions(
  general: ConfigurationDTO,
  specific: ConfigurationDTO,
) {
  const [first, second] = [clone(general), clone(specific)].map((obj) => {
    if (!obj || isEmpty(obj)) obj = { sets: [], type: 'matrix' };
    return obj;
  });

  if (first.type === 'matrix' && second.type === 'matrix') {
    return getDistinct([...(first.sets ?? []), ...(second.sets ?? [])]);
  }
  if (first.type === 'matrix' && second.type === 'simple') {
    // merge general with crossed version of specific
    const product = cartesianProduct(mergeAndGroup(second.sets ?? []));
    product.push(...(first.sets ?? []));
    return getDistinct(product);
  }
  if (first.type === 'simple' && second.type === 'matrix') {
    // cross general and merge with specific
    const product = cartesianProduct(mergeAndGroup(first.sets ?? []));
    product.push(...(second.sets ?? []));
    return getDistinct(product);
  }
  if (first.type === 'simple' && second.type === 'simple') {
    // merge both sets and get the cartesian product
    const cfg = mergeAndGroup([...(first.sets ?? []), ...(second.sets ?? [])]);
    return cartesianProduct(cfg);
  }
}

interface DestructiveUpdate {
  deletedAt?: Knex.Raw<string>;
  archivedAt?: Knex.Raw<string>;
}

export type TestPlanModel = ModelClass<TestPlan> & typeof TestPlan;
