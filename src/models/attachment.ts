import { Model } from '@app/lib/model';
import { ModelClass, Transaction } from 'objection';
import mimes from 'mime-types';
import { storageProvider } from '@ss-libs/ss-component-media';
import { v4 } from 'uuid';
import { Project } from './project';
import { TestExecution } from './testExecution';
import { TestExecutionStep } from './testExecutionStep';
import { TestResult } from './testResult';
import { User } from './user';
import { Defect } from './defect';
import { Integration } from './integration';

interface AttachmentDTO {
  name: string,
  size: number,
  fileType: string,
  mediaType: string,
  ownerUid?: string
  creatorUid?: string,
}

export class Attachment extends Model {
  uid: string;

  name: string;

  size: number;

  metadata: Record<string, string>;

  fileType: string;

  checksum: string;

  source: string;

  externalId: string;

  ownerUid: string;

  mediaType: string;

  creatorUid: string;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'attachments';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['size', 'fileType'],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        name: { type: 'string', maxLength: 255 },
        size: { type: 'number' },
        metadata: { type: 'object' },
        fileType: { type: 'string', maxLength: 255 },
        mediaType: { type: 'string', enum: ['attachment', 'profile-picture'] },
        checksum: { type: 'string', maxLength: 255 },
        source: { type: 'string' },
        ownerUid: { type: 'string', format: 'uuid' },
        creatorUid: { type: 'string', format: 'uuid' },
        externalId: { type: 'string' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      testExecutionSteps: {
        relation: Model.ManyToManyRelation,
        modelClass: TestExecutionStep,
        join: {
          from: 'attachments.uid',
          through: {
            from: 'executionStepAttachments.attachmentUid',
            to: 'executionStepAttachments.executionStepUid',
          },
          to: 'testExecutionSteps.uid',
        },
      },
      testExecutions: {
        relation: Model.ManyToManyRelation,
        modelClass: TestExecution,
        join: {
          from: 'attachments.uid',
          through: {
            from: 'executionAttachments.attachmentUid',
            to: 'executionAttachments.executionUid',
          },
          to: 'testExecutions.uid',
        },
      },
      testResults: {
        relation: Model.ManyToManyRelation,
        modelClass: TestResult,
        join: {
          from: 'attachments.uid',
          through: {
            from: 'resultAttachments.attachmentUid',
            to: 'resultAttachments.resultUid',
          },
          to: 'testResults.uid',
        },
      },
      project: {
        relation: Model.ManyToManyRelation,
        modelClass: Project,
        join: {
          from: 'attachments.uid',
          through: {
            from: 'projectAttachments.attachmentUid',
            to: 'projectAttachments.projectUid',
          },
          to: 'projects.uid',
        },
      },
      users: {
        relation: Model.ManyToManyRelation,
        modelClass: User,
        join: {
          from: 'attachments.uid',
          through: {
            from: 'userAttachments.attachmentUid',
            to: 'userAttachments.userUid',
            extra: ['attachmentUid', 'userUid'],
          },
          to: 'users.uid',
        },
      },
      defects: {
        relation: Model.ManyToManyRelation,
        modelClass: Defect,
        join: {
          from: 'attachments.uid',
          through: {
            from: 'defectAttachments.attachmentUid',
            to: 'defectAttachments.defectUid',
          },
          to: 'defects.uid',
        },
      },
      integrations: {
        relation: Model.ManyToManyRelation,
        modelClass: Integration,
        join: {
          from: 'attachments.uid',
          through: {
            from: 'integrationAttachments.attachmentUid',
            to: 'integrationAttachments.integrationUid',
          },
          to: 'integrations.uid',
        },
      },
    };
  }

  get key() {
    const knexInstance = (this.constructor as typeof Model).knex();
    const ownerUid = (knexInstance?.client?.config?.database as string);

    const prefix = this.mediaType === 'attachment' ? ownerUid : 'public';

    return `${prefix}/${this.uid}.${mimes.extension(this.fileType)}`;
  }

  async uploadURL(ownerUid: string) {
    const { key } = this;

    const {
      mediaType, fileType, size,
    } = this;

    const uploadURL = await storageProvider(ownerUid).upload(mediaType, key, fileType, size);

    return uploadURL;
  }

  static async createAttachment(attachment: AttachmentDTO, trx: Transaction) {
    try {
      const {
        name, creatorUid, size, fileType, mediaType, ownerUid,
      } = attachment;

      const uid = v4();

      const newAttachment = await Attachment.query(trx).insert({
        uid,
        name,
        creatorUid,
        size,
        fileType,
        mediaType,
      });

      const presignedUpload = await newAttachment.uploadURL(ownerUid);

      return {
        ...presignedUpload,
        uid,
      };
    } catch (err) {
      await trx.rollback();
      throw err;
    }
  }

  static async getTotalSize(trx: Transaction): Promise<number> {
    const result : Attachment & { totalSize? : number } = await this.query(trx).sum('size as totalSize').first();
    return Number((result?.totalSize as number)) || 0;
  }

  static async deleteAttachment(trx: Transaction, id: string, entityTable: string) {
    try {
      await trx(entityTable).where({ attachmentUid: id }).delete();
      await Attachment.query(trx).where({ uid: id }).delete();
      await trx.commit();
    } catch (err) {
      await trx.rollback();
      throw err;
    }
  }
}

export type AttachmentModel = ModelClass<Attachment>;
