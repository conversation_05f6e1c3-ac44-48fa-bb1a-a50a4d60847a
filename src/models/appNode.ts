import { Model, ModelClass } from 'objection';

export class AppNode extends Model {
  uid: number;

  name: string;

  region: string;

  config: object;

  isDefault: boolean;

  createdAt: Date;

  updatedAt: Date;

  deletedAt: Date | null;

  static get tableName() {
    return 'appNodes';
  }

  static idColumn: string = 'uid';

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['region', 'name'],
      properties: {
        uid: { type: 'number' },
        name: { type: 'string' },
        config: { type: 'object' },
        region: { type: 'string' },
        isDefault: { type: 'boolean' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }
}

export type AppNodeModel = ModelClass<AppNode>;
