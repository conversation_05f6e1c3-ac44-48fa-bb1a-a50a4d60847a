import { Model, ModelClass } from 'objection';
import { AppNode } from './appNode';

export class DBServer extends Model {
  uid: number;

  name: string;

  host: string;

  username: string;

  password: string;

  port: number;

  appNodeUid?: number;

  appNode: AppNode;

  isDefault: boolean;

  createdAt: Date;

  updatedAt: Date;

  deletedAt: Date | null;

  static get tableName() {
    return 'dbServers';
  }

  static idColumn: string = 'uid';

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['host', 'username', 'password', 'port'],
      properties: {
        uid: { type: 'number' },
        name: { type: 'string', maxLength: 36 },
        host: { type: 'string', maxLength: 36 },
        username: { type: 'string', maxLength: 36 },
        password: { type: 'string', maxLength: 36 },
        port: { type: 'number' },
        isDefault: { type: 'boolean' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      appNode: {
        relation: Model.BelongsToOneRelation,
        modelClass: AppNode,
        join: {
          from: 'dbServers.appNodeUid',
          to: 'appNodes.uid',
        },
      },
    };
  }
}

export type DBServerModel = ModelClass<DBServer>;
