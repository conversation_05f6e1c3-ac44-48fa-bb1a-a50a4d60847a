import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import { Tag } from './tag';

export class TestCaseTag extends Model {
  uid: number;

  testCaseRef: number;

  tagUid: number;

  testCaseAddedVersion: number[];

  testCaseRemovedVersion?: number[];

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'testCaseTags';
  }

  static jsonAttributes = ['testCaseAddedVersion', 'testCaseRemovedVersion'];

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['testCaseRef', 'tagUid'],
      properties: {
        uid: { type: 'integer', format: 'uuid' },
        testCaseRef: { type: 'integer' },
        tagUid: { type: 'integer' },
        testCaseAddedVersion: { type: 'array' },
        testCaseRemovedVersion: { type: 'array' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      tag: {
        relation: Model.HasOneRelation,
        modelClass: Tag,
        join: {
          from: 'testCaseTags.tagUid',
          to: 'tags.uid',
        },
      },
    };
  }
}

export type TestCaseTagModel = ModelClass<TestCaseTag>;
