import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import {
  encryptHybrid, newSymmetricKey, encryptRsa,
} from '@ss-libs/ss-component-encryption';
import * as forge from 'node-forge';
import { Knex } from 'knex';
import { IntegrationTokenDTO } from '@app/types/integrationToken';
import { KEY_MANAGER } from '@app/config/keyManagerLoader';

export class IntegrationToken extends Model {
  uid: string;

  accessToken: string;

  refreshToken: string;

  url: string;

  ownerUid: string;

  creatorUid: string;

  retrievalId: string;

  integrationUid: number;

  expiresAt: Date | string;

  retrieved: boolean;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'integrationTokens';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['accessToken', 'refreshToken', 'url', 'ownerUid'],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        accessToken: { type: 'string', maxLength: 4000 },
        refreshToken: { type: 'string', maxLength: 4000 },
        url: { type: 'string', maxLength: 255 },
        ownerUid: { type: 'string', format: 'uuid' },
        creatorUid: { type: 'string', format: 'uuid' },
        retrievalId: { type: 'string', maxLength: 255 },
        retrieved: { type: 'boolean' },
        integrationUid: { type: 'integer' },
        expiresAt: { type: 'string', format: 'date-time' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static async getEncryptedIntegrationToken(db: Knex, integrationToken: IntegrationTokenDTO) {
    const keyManager = KEY_MANAGER;
    const latestPublicKey = await keyManager.getLatestPublicKey(db, 'integration');
    if (!latestPublicKey) {
      throw new Error('Public key not found');
    }
    const symmetricKey = newSymmetricKey();
    const rsaEncryptedKey = encryptRsa({ publicKeyPEM: forge.util.decode64(latestPublicKey.key), data: symmetricKey });

    const encryptedAccessToken = encryptHybrid({
      data: integrationToken.accessToken,
      symmetricKey,
    });
    const encryptedRefreshToken = encryptHybrid({
      data: integrationToken.refreshToken,
      symmetricKey,
    });

    const encryptedIntegrationToken = {
      ...integrationToken,
      accessToken: forge.util.encode64(encryptedAccessToken.encryptedData),
      refreshToken: forge.util.encode64(encryptedRefreshToken.encryptedData),
    };

    const symmetricKeyData = {
      fieldMetadata: {
        accessToken: {
          iv: forge.util.encode64(encryptedAccessToken.iv),
          tag: forge.util.encode64(encryptedAccessToken.tag.bytes()),
        },
        refreshToken: {
          iv: forge.util.encode64(encryptedRefreshToken.iv),
          tag: forge.util.encode64(encryptedRefreshToken.tag.bytes()),
        },
      },
      version: latestPublicKey.version,
      key: forge.util.encode64(rsaEncryptedKey),
      service: 'integration',
      publicKeyUid: latestPublicKey.uid,
      entityId: '', // will be updated will inserting into db
    };
    return { encryptedIntegrationToken, symmetricKeyData };
  }

  static async createIntegrationToken(sharedDb: Knex, tenantDb: Knex, models, integrationToken: IntegrationTokenDTO) {
    const keyManager = KEY_MANAGER;
    const { encryptedIntegrationToken, symmetricKeyData } = await this.getEncryptedIntegrationToken(sharedDb, integrationToken);
    const token = await models.IntegrationToken.query().insert(encryptedIntegrationToken).returning('*');
    symmetricKeyData.entityId = token.uid;
    await keyManager.createSymmeticKey(tenantDb, symmetricKeyData);
    return token;
  }

  static async updateIntegrationToken(sharedDb: Knex, tenantDb: Knex, models, integrationToken: IntegrationTokenDTO, tokenUid: string) {
    const keyManager = KEY_MANAGER;
    const { encryptedIntegrationToken, symmetricKeyData } = await this.getEncryptedIntegrationToken(sharedDb, integrationToken);
    await models.IntegrationToken.query().findById(tokenUid).patch(encryptedIntegrationToken);
    symmetricKeyData.entityId = tokenUid;
    await keyManager.createSymmeticKey(tenantDb, symmetricKeyData);
  }
}

export type IntegrationTokenModel = ModelClass<IntegrationToken>;
