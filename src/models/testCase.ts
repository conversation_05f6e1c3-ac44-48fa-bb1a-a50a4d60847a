import { Model, getNextId } from '@app/lib/model';
import _ from 'lodash';

import { ApplicationError } from '@app/lib/http';
import { CreateCaseDTO } from '@app/types/case';
import { FGARawWrite } from '@ss-libs/ss-component-auth';
import { Knex } from 'knex';
import { ModelClass } from 'objection';
import { StatusCodes } from 'http-status-codes';
import { TestCaseStepItem } from '@app/types/step';
import errorConstants from '@app/constants/errors';
import { slugify } from '@app/utils/string';
import { Attachment } from './attachment';
import { PreferenceMap } from './preferences';
import { Repo } from './repo';
import { SharedTestStep } from './sharedTestStep';
import { Tag } from './tag';
import { TestCaseStep } from './testCaseStep';
import { TestCaseTag } from './testCaseTag';
import { TestExecution } from './testExecution';
import { TestTemplate } from './testTemplate';
import { User } from './user';

export class TestCase extends Model {
  uid: number;

  externalId: string;

  source: string;

  name: string;

  link: string;

  customFields: Record<string, any>;

  priority: number | null;

  status: number | null;

  testTemplateUid: string;

  repoUid: string;

  parentUid: number;

  projectUid: number;

  testCaseRef: number;

  version: number;

  active: boolean;

  externalCreatedAt: Date | string;

  externalUpdatedAt: Date | string;

  steps: TestCaseStepItem[];

  createdBy?: string;

  creator?: User;

  attachments?: any[];

  static jsonAttributes = ['steps'];

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'testCases';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['name', 'version', 'testCaseRef', 'projectUid'],
      properties: {
        uid: { type: 'integer' },
        parentUid: { type: 'integer' },
        projectUid: { type: 'integer' },
        externalId: { type: ['string', 'null'], maxLength: 255 },
        source: { type: ['string', 'null'], maxLength: 255 },
        name: { type: 'string', maxLength: 255 },
        link: { type: 'string', maxLength: 255 },
        customFields: { type: 'object' },
        priority: { type: ['integer', 'null'] },
        status: { type: ['integer', 'null'] },
        testTemplateUid: { type: 'integer' },
        repoUid: { type: 'string', format: 'uuid' },
        testCaseRef: { type: 'integer' },
        version: { type: 'integer' },
        active: { type: 'boolean' },
        externalCreatedAt: { type: 'string', format: 'date-time' },
        externalUpdatedAt: { type: 'string', format: 'date-time' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      testTemplate: {
        relation: Model.BelongsToOneRelation,
        modelClass: TestTemplate,
        join: {
          from: 'testCases.testTemplateUid',
          to: 'testTemplates.uid',
        },
      },

      repo: {
        relation: Model.BelongsToOneRelation,
        modelClass: Repo,
        join: {
          from: 'testCases.repoUid',
          to: 'repos.uid',
        },
      },

      testExecutions: {
        relation: Model.HasManyRelation,
        modelClass: TestExecution,
        join: {
          from: 'testCases.uid',
          to: 'testExecutions.testCaseRef',
        },
      },

      attachments: {
        relation: Model.ManyToManyRelation,
        modelClass: Attachment,
        join: {
          from: 'testCases.testCaseRef',
          through: {
            from: 'caseAttachments.caseRef',
            to: 'caseAttachments.attachmentUid',
          },
          to: 'attachments.uid',
        },
      },

      sharedSteps: {
        relation: Model.ManyToManyRelation,
        modelClass: SharedTestStep,
        join: {
          from: 'testCases.uid',
          through: {
            from: 'sharedTestCaseSteps.testCaseRef',
            to: 'sharedTestCaseSteps.sharedTestStepRef',
          },
          to: 'sharedTestSteps.uid',
        },
      },
      tags: {
        relation: Model.ManyToManyRelation,
        modelClass: Tag,
        join: {
          from: 'testCases.testCaseRef',
          through: {
            from: 'testCaseTags.testCaseRef',
            to: 'testCaseTags.tagUid',
          },
          to: 'tags.uid',
        },
      },
    };
  }

  static async findByIds(trx: Knex, caseUids: number[]) {
    return TestCase.query(trx).whereNull('deletedAt').whereIn('uid', caseUids);
  }

  static async newCases(
    casesDto: CreateCaseDTO[],
    preferenceMap: PreferenceMap,
    projectUid: number,
    ownerUid: string,
    ownerType: string,
    trx: Knex.Transaction,
  ) {
    const cases: Array<Partial<TestCase>> = [];
    const caseSteps: Array<Partial<TestCaseStep>> = [];
    const caseTags: Array<Partial<TestCaseTag>> = [];
    const fgaWrites: FGARawWrite[] = [];

    const tagsCache: Record<string, Tag> = {};

    for (const item of casesDto) {
      const sharedStepIds = new Set<number>();

      if (item.steps && item.steps.length > 0) {
        for (const step of item.steps) {
          if (step.sharedStepUid) {
            sharedStepIds.add(step.sharedStepUid);
          }
        }
      }
      const uid = await getNextId(trx, TestCase);

      if (item.parentId) {
        // check if it's a folder
        const folder = await Tag.query(trx)
          .where({ systemType: 'folder', uid: item.parentId, deletedAt: null })
          .first();
        if (!folder) {
          throw new ApplicationError(
            StatusCodes.UNPROCESSABLE_ENTITY,
            errorConstants.FOLDER_NOT_FOUND,
          );
        }
      }

      const nullValue: any = trx.raw('null');

      cases.push({
        externalId: item.externalId || '',
        source: item.source || '',
        name: item.name || '',
        customFields: item.customFields,
        projectUid,
        parentUid: item.parentId,
        repoUid: item.repoUID,
        priority: item.priority || preferenceMap?.testCase.priority,
        ...(item.templateId && { testTemplateUid: item.templateId }),
        ...(item.status && { status: item.status }),
        active: true,
        version: 1,
        createdBy: item.createdBy ?? ownerUid,
        uid,
        testCaseRef: uid,
        steps: item.steps,
        externalCreatedAt: item.externalCreatedAt ?? nullValue,
        externalUpdatedAt: item.externalUpdatedAt ?? nullValue,
      });

      const sharedStepsArray = [...sharedStepIds];

      if (sharedStepsArray?.length > 0) {
        caseSteps.push(
          ...sharedStepsArray.map((sharedStepId: number) => ({
            testCaseRef: uid,
            sharedTestStepRef: sharedStepId,
            testCaseAddedVersion: [1],
          })),
        );
      }

      if (item.tagIds && item.tagIds.length > 0) {
        for (const tagId of item.tagIds) {
          caseTags.push({
            tagUid: tagId,
            testCaseRef: uid,
            testCaseAddedVersion: [1],
          });
        }
      } else if (item.tags && item.tags.length > 0) {
        for (const t of item.tags) {
          const slug = slugify(t);
          if (tagsCache[slug]) {
            caseTags.push({
              tagUid: tagsCache[slug].uid,
              testCaseRef: uid,
              testCaseAddedVersion: [1],
            });
            continue;
          }

          const [existingTag] = await Tag.findByName(trx, t, ['cases']);

          if (existingTag) {
            tagsCache[slug] = existingTag;
          } else {
            tagsCache[slug] = await Tag.query(trx).insert({
              name: t,
              entityTypes: ['cases', 'executions'],
            });
          }

          caseTags.push({
            tagUid: tagsCache[slug].uid,
            testCaseRef: uid,
            testCaseAddedVersion: [1],
          });
        }
      }

      fgaWrites.push({
        objectType: 'case',
        objectId: uid,
        relation: 'owner',
        subjectType: ownerType,
        subjectId: ownerUid,
      });
    }
    const casesCreated = await TestCase.query(trx).insert(cases).returning('*');
    if (caseSteps.length > 0) {
      await TestCaseStep.query(trx).insert(caseSteps);
    }

    if (caseTags.length > 0) {
      await TestCaseTag.query(trx)
        .toKnexQuery()
        .insert(caseTags)
        .returning('*');
    }

    return {
      cases: casesCreated,
      fgaWrites,
    };
  }

  static formatAttachment(testCase: TestCase, baseUrl: string) {
    if (_.has(testCase, 'attachments')) {
      testCase.attachments = testCase.attachments.map(
        ({ uid, name, fileType }) => ({
          previewUrl: `${baseUrl}/cases/attachments/${uid}/object`,
          name,
          type: fileType,
          uid,
        }),
      );
    }
  }

  static async populateCreator(trx: Knex, testCase: TestCase) {
    const creator = await User.query(trx)
      .where('uid', testCase.createdBy)
      .select('firstName', 'lastName', 'email')
      .first();
    testCase.creator = creator;
  }

  static async getTags(trx: Knex, caseUids: number[]) {
    const caseTags = await TestCaseTag.query(trx)
      .whereIn('testCaseRef', caseUids)
      .withGraphFetched('[tag]')
      .modifyGraph('tag', (builder) => {
        builder.select('name', 'uid');
      })
      .whereNull('deletedAt');

    const groups = _.groupBy(caseTags, (ct) => ct.testCaseRef);
    if (_.isEmpty(groups)) return {};

    // eslint-disable-next-line guard-for-in
    for (const k in groups) {
      groups[k] = groups[k].map((g: any) => g.tag);
    }

    return groups;
  }

  static async getCreators(trx: Knex, sharedKnex: Knex, caseUids: number[]) {
    const cases = await TestCase.query(trx)
      .whereIn('testCaseRef', caseUids)
      .select('testCaseRef', 'createdBy');

    // map of caseUid -> creatorUid
    const casesMap = cases.reduce((acc, c) => {
      if (c.createdBy) acc[c.testCaseRef.toString()] = c.createdBy;
      return acc;
    }, {} as any);

    const users = await User.getByIds(sharedKnex, Object.values(casesMap));
    // map of creatorUid -> creatorDetails
    const userMap = users.reduce((acc, u) => {
      acc[u.uid] = u;
      return acc;
    }, {});

    // eslint-disable-next-line guard-for-in
    for (const c in casesMap) casesMap[c] = userMap[casesMap[c]];

    return casesMap;
  }
}

export type TestCaseModel = ModelClass<TestCase> & typeof TestCase;
