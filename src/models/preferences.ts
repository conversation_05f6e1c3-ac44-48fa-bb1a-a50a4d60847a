import DEFAULT_PREFERENCE from '@app/constants/preference';
import { Org } from '@app/models/org';
import { User } from '@app/models/user';
import { Knex } from 'knex';
import _ from 'lodash';

class Preference {
  async findOne(db: Knex, type: HandleType, uid: string) {
    const Model: any = type === 'org' ? Org : User;

    const owner = await Model.query(db).findById(uid);
    const preferences: HandlePreference = _.isEmpty(owner.preferences)
      ? DEFAULT_PREFERENCE
      : owner.preferences;

    return preferences;
  }

  async getDefaults(db: Knex, type: HandleType, uid: string) {
    const preferences = await this.findOne(db, type, uid);
    return this.filterDefaults(preferences);
  }

  async getCompleted(db: Knex, type: HandleType, uid: string) {
    const preferences = await this.findOne(db, type, uid);
    return this.filterCompleted(preferences);
  }

  async getDefaultsAndCompleted(db: Knex, type: HandleType, uid: string) {
    const preferences = await this.findOne(db, type, uid);
    return {
      completed: this.filterCompleted(preferences),
      defaults: this.filterDefaults(preferences),
    };
  }

  private filterCompleted(preferences: HandlePreference) {
    // initialise to empty arrays
    const statuses: CompletedStatuses = entityTypes.reduce(
      (map, type) => {
        map[type] = [];
        return map;
      },
      <any>{},
    );

    for (const status of preferences.statusColors) {
      if (status.isCompleted) {
        if (statuses[status.entityType]) {
          statuses[status.entityType].push(status.id);
        } else {
          statuses[status.entityType] = [status.id];
        }
      }
    }

    return statuses;
  }

  public filterDefaults(preferences: HandlePreference) {
    const defaults: PreferenceMap = <any>{};

    for (const status of preferences.statusColors) {
      if (status.isDefault) {
        defaults[status.entityType] = { status: status.id, priority: null };
      }
    }
    for (const priority of preferences.priorityColors) {
      if (priority.isDefault) {
        defaults[priority.entityType].priority = priority.id;
      }
    }

    return defaults;
  }

  async getMappings(db: Knex, type: HandleType, uid: string) {
    const preferences = await this.findOne(db, type, uid);
    const mappings: Mappings = <any>{};

    for (const e of entityTypes) mappings[e] = { priority: {}, status: {} };

    for (const s of preferences.statusColors) {
      mappings[s.entityType].status[s.id] = s;
    }

    for (const p of preferences.priorityColors) {
      mappings[p.entityType].priority[p.id] = p;
    }

    return mappings;
  }

  async save(
    db: Knex,
    type: HandleType,
    uid: string,
    prefs: HandlePreference,
  ): Promise<Org | User> {
    const Model: any = type === 'org' ? Org : User;

    return Model.query(db).findById(uid).patch({ preferences: prefs });
  }
}

const entityTypes = <const>[
  'defect',
  'testRun',
  'testPlan',
  'testCase',
  'milestone',
];
export type EntityType = (typeof entityTypes)[number];

type HandleType = 'org' | 'user';

export type Status = {
  id: number;
  color: string;
  entityType: EntityType;
  name: string;
  isDefault: boolean;
  isCompleted: boolean;
  isFailure: boolean;
  isSuccess: boolean;
  aliases: string[];
};

export type Priority = {
  id: number;
  color: string;
  entityType: EntityType;
  name: string;
  isDefault: boolean;
};

interface HandlePreference {
  statusColors: Status[];
  priorityColors: Priority[];
}

export type PreferenceMap = Partial<
Record<
EntityType,
{
  status: number;
  priority: number;
}
>
>;

export type CompletedStatuses = Record<EntityType, number[]>;

export type StatusFreq = Record<number, number>;

type Mappings = Record<
EntityType,
{ status: { [k: number]: Status }; priority: { [k: number]: Priority } }
>;

const preferences = new Preference();
export default preferences;
