import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';

export class Membership extends Model {
  uid: string;

  userUid: string;

  accountUid: string;

  accountType: 'org' | 'user';

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'memberships';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['userUid', 'accountUid', 'accountType'],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        userUid: { type: 'string', format: 'uuid' },
        accountUid: { type: 'string', format: 'uuid' },
        accountType: { type: 'string', enum: ['user', 'org'] },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }
}

export type MembershipModel = ModelClass<Membership>;
