import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import { Invite } from './invite';

export class ProjectInvite extends Model {
  uid: string;

  projectUid: number;

  roleUid: string;

  inviteUid: string;

  createdAt: Date;

  updatedAt: Date;

  static tableName: string = 'projectInvites';

  static idColumn: string = 'uid';

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['projectUid', 'roleUid'],
      properties: {
        uid: { type: 'string', maxLength: 36 },
        projectUid: { type: 'number' },
        roleUid: { type: 'string', maxLength: 36 },
        inviteUid: { type: 'string', maxLength: 36 },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      // define relation with invites table
      invite: {
        relation: Model.BelongsToOneRelation,
        modelClass: Invite,
        join: {
          from: 'projectInvites.inviteUid',
          to: 'invites.uid',
        },
      },
    };
  }
}

export type ProjectInviteModel = ModelClass<ProjectInvite>;
