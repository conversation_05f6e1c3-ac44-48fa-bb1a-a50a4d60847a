import { ModelClass, ModelObject } from 'objection';

import { CreateAccessTokenDTO } from '@app/types/accessToken';
import { Knex } from 'knex';
import { Model } from '@app/lib/model';
import { accessTokens } from '@ss-libs/ss-component-auth';
import { User } from './user';

export class AccessToken extends Model {
  uid: string;

  accessTokenHash: string;

  name: string;

  ownerType: 'user' | 'org';

  ownerUid: string;

  expiresAt: string | Date;

  createdAt: Date | string;

  createdBy: string;

  updatedAt: Date | string;

  deletedAt: Date | string;

  lastUsedAt: Date | string;

  secretKey?: string;

  permissions?: string[];

  creator?: User;

  $beforeUpdate() {
    this.updatedAt = new Date().toISOString();
  }

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'accessTokens';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['accessTokenHash', 'name', 'ownerUid', 'expiresAt'],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        accessTokenHash: { type: 'string', maxLength: 255 },
        name: { type: 'string', maxLength: 255 },
        ownerType: { type: 'string', enum: ['user', 'org'] },
        ownerUid: { type: 'string', format: 'uuid' },
        expiresAt: { type: 'string', format: 'date-time' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static relationMappings() {
    return {
      creator: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: { from: 'accessTokens.createdBy', to: 'users.uid' },
      },
    };
  }

  static async createOne(db: Knex, dto: CreateAccessTokenDTO) {
    const keyPair = await accessTokens().newKeyPair(dto.days);
    const accessToken = await AccessToken.query(db)
      .insert({
        ownerType: dto.ownerType,
        ownerUid: dto.ownerUid,
        accessTokenHash: keyPair.hash,
        expiresAt: keyPair.expiresAt.toISOString(),
        name: dto.name,
        lastUsedAt: db.raw('null'),
        createdBy: dto.createdBy,
      })
      .returning('*');
    delete accessToken.accessTokenHash;

    accessToken.secretKey = accessTokens().assembleSecretKey(
      dto.prefix,
      accessToken.uid,
      keyPair.key,
    );

    return accessToken;
  }

  static async findAll(db: Knex, where: Partial<ModelObject<AccessToken>>) {
    return AccessToken.query(db)
      .where({ ...where, deletedAt: null })
      .withGraphFetched('[creator]')
      .modifyGraph('creator', (q) => {
        q.whereNull('deletedAt').select(
          'uid',
          'firstName',
          'lastName',
          'email',
        );
      })
      .select('name', 'expiresAt', 'uid', 'lastUsedAt')
      .orderBy('createdAt', 'desc');
  }

  static async delete(db: Knex, where: Partial<ModelObject<AccessToken>>) {
    return AccessToken.query(db)
      .where({ ...where, deletedAt: null })
      .patch({ deletedAt: db.fn.now() });
  }
}

export type AccessTokenModel = ModelClass<AccessToken> & typeof AccessToken;
