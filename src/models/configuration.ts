/* eslint-disable max-classes-per-file */
import { Model, ModelClass } from 'objection';

import { ConfigurationDTO } from '@app/types/configuration';
import { Knex } from 'knex';
import { Tag } from './tag';

interface CustomField {
  previousUid?: number;
}

export class Configuration extends Tag<CustomField, 'config'> {
  options?: ConfigurationOption[];

  static async create(trx: Knex.Transaction, dto: ConfigurationDTO) {
    const config = await Configuration.query(trx)
      .insert({
        name: dto.name,
        description: dto.description,
        projectUid: dto.projectUid,
        systemType: 'config',
      })
      .returning('*');
    if (dto.options?.length > 0) {
      config.options = await Configuration.setOpts(trx, config, dto.options);
    }

    return config;
  }

  static async update(
    trx: Knex.Transaction,
    uid: number,
    dto: ConfigurationDTO,
  ) {
    const patch: Partial<Configuration> = {
      name: dto.name,
    };
    if (dto.description) patch.description = dto.description;

    const [config] = await Configuration.query(trx)
      .where({
        systemType: 'config',
        uid,
      })
      .patch(patch)
      .withGraphFetched('[options(isConfigOption)]')
      .returning('*');
    if (!config) return null;

    config.options = await Configuration.setOpts(trx, config, dto.options);

    await ConfigurationOption.query(trx)
      .patch({ deletedAt: trx.fn.now() })
      .where({ parentUid: config.uid, systemType: 'config.option' })
      .whereNotIn(
        'uid',
        config.options.map((o) => o.uid),
      );
    return config;
  }

  private static async setOpts(
    trx: Knex.Transaction,
    config: Configuration,
    options: ConfigurationDTO['options'],
  ) {
    const newOpts = options.map(async (opt) => {
      let option: ConfigurationOption;
      if (opt.uid) {
        [option] = await ConfigurationOption.query(trx)
          .where({
            uid: opt.uid,
            parentUid: config.uid,
            systemType: 'config.option',
            deletedAt: null,
          })
          .patch({ name: opt.name })
          .returning('*');
      } else {
        option = await ConfigurationOption.query(trx)
          .insert({
            systemType: 'config.option',
            parentUid: config.uid,
            name: opt.name,
            projectUid: config.projectUid,
          })
          .returning('*');
      }
      return option;
    });

    return Promise.all(newOpts);
  }

  static get relationMappings() {
    return {
      options: {
        relation: Model.HasManyRelation,
        modelClass: ConfigurationOption,
        join: {
          from: 'tags.uid',
          to: 'tags.parentUid',
        },
      },
    };
  }
}

export class ConfigurationOption extends Tag<null, 'config.option'> {}

export type ConfigurationModel = ModelClass<Configuration> &
  typeof Configuration;
