import { CreateResultDTO } from '@app/types/result';
import { Knex } from 'knex';
import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import { Attachment } from './attachment';
import { Tag } from './tag';
import { TestExecution } from './testExecution';
import { TestExecutionStep } from './testExecutionStep';
import { User } from './user';

export class TestResult extends Model {
  uid: string;

  externalId: string;

  source: string;

  link: string;

  customFields: CustomFields;

  comment: string;

  status?: number;

  externalCreatedAt: Date | string;

  /**
   * if set, it means the test result is for a test step
   */
  testExecutionStepUid?: string;

  testExecutionUid: number;

  reporterUid: string;

  attachments?: Attachment[];

  reporter?: User;

  tags?: Tag[];

  testExecution?: TestExecution;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'testResults';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      properties: {
        uid: { type: 'integer' },
        externalId: { type: 'string', maxLength: 255 },
        source: { type: 'string', maxLength: 255 },
        link: { type: 'string', maxLength: 255 },
        customFields: { type: 'object' },
        comment: { type: 'string' },
        status: { type: 'integer' },
        externalCreatedAt: { type: 'string', format: 'date-time' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
        testExecutionStepUid: { type: 'string', format: 'uuid' },
        testExecutionUid: { type: 'integer' },
        reporterUid: { type: 'string', maxLength: 36, format: 'uuid' },
      },
    };
  }

  static get relationMappings() {
    return {
      testExecutionStep: {
        relation: Model.BelongsToOneRelation,
        modelClass: TestExecutionStep,
        join: {
          from: 'testResults.testExectionStepUid',
          to: 'testExecutionSteps.uid',
        },
      },
      testExecution: {
        relation: Model.BelongsToOneRelation,
        modelClass: TestExecution,
        join: {
          from: 'testResults.testExecutionUid',
          to: 'testExecutions.uid',
        },
      },
      attachments: {
        relation: Attachment.ManyToManyRelation,
        modelClass: Attachment,
        join: {
          from: 'testResults.uid',
          through: {
            from: 'resultAttachments.resultUid',
            to: 'resultAttachments.attachmentUid',
          },
          to: 'attachments.uid',
        },
      },
    };
  }

  static async create(trx: Knex.Transaction, dto: CreateResultDTO) {
    const result = await TestResult.query(trx)
      .insert({
        comment: dto.comment,
        externalId: dto.externalId,
        reporterUid: dto.reporterUid,
        link: dto.link,
        source: dto.source,
        status: dto.status,
        testExecutionStepUid: dto.stepUid,
        testExecutionUid: dto.executionUid,
        customFields: {
          tagUids: dto.tagUids,
        },
      })
      .returning('*');
    // update test execution step status, if stepuid is specified, else update the test execution status
    if (dto.stepUid) {
      await TestExecutionStep.query(trx)
        .where({ uid: dto.stepUid, testExecutionUid: dto.executionUid })
        .patch({
          status: dto.status,
          updatedAt: trx.fn.now(),
        });
    } else {
      [result.testExecution] = await TestExecution.query(trx)
        .where({ uid: dto.executionUid })
        .patch({
          status: dto.status,
          updatedAt: trx.fn.now(),
        })
        .returning('*');
    }

    return result;
  }
}

interface CustomFields {
  tagUids: number[];
  externalFields?: any;
}

export type TestResultModel = ModelClass<TestResult>;
