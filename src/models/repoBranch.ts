import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import { Repo } from './repo';
import { TestExecution } from './testExecution';

export class RepoBranch extends Model {
  uid: string;

  externalId: string;

  source: string;

  name: string;

  link: string;

  repoUid: string;

  customFields: Record<string, any>;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'repoBranches';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['externalId', 'name'],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        externalId: { type: 'string', maxLength: 255 },
        name: { type: 'string', maxLength: 255 },
        link: { type: 'string', maxLength: 255 },
        customFields: { type: 'object' },
        repoUid: { type: 'string', format: 'uuid' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }

  static get relationMappings() {
    return {
      repo: {
        relation: Model.BelongsToOneRelation,
        modelClass: Repo,
        join: {
          from: 'repoBranches.repoUid',
          to: 'repos.uid',
        },
      },
      testExecutions: {
        relation: Model.HasManyRelation,
        modelClass: TestExecution,
        join: {
          from: 'repoBranches.uid',
          to: 'testExecutions.repoBranchUid',
        },
      },
    };
  }
}

export type RepoBranchModel = ModelClass<RepoBranch>;
