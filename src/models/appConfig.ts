import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';

export class AppConfig extends Model {
  uid: string;

  config: object;

  name: string;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'appConfigs';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['config'],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        config: { type: 'object' },
        name: { type: 'string', maxLength: 255 },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }
}

export type AppConfigModel = ModelClass<AppConfig>;
