import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';

export class Subscription extends Model {
  uid: string;

  stripeCustomerId: string;

  stripeSubscriptionId: string;

  stripePriceId: string;

  name: string;

  qty: number;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'subscriptions';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: [
        'stripeCustomerId',
        'stripeSubscriptionId',
        'stripePriceId',
        'name',
        'qty',
      ],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        stripeCustomerId: { type: 'string', maxLength: 255 },
        stripeSubscriptionId: { type: 'string', maxLength: 255 },
        stripePriceId: { type: 'string', maxLength: 255 },
        name: { type: 'string', maxLength: 255 },
        qty: { type: 'integer' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }
}

export type SubscriptionModel = ModelClass<Subscription>;
