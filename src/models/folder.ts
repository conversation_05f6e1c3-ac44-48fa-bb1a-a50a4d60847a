import { CreateFolderDto, UpdateFolderDto } from '@app/types/folder';
import { CustomFields, Model } from '@app/lib/model';

import { ApplicationError } from '@app/lib/http';
import { Knex } from 'knex';
import { ModelClass, ModelObject } from 'objection';
import { StatusCodes } from 'http-status-codes';
import errors from '@app/constants/errors';
import { kebabCase } from 'lodash';
import { Tag } from './tag';

interface FolderCustomFields extends CustomFields {
  source?: string;
  externalId?: string;
}

const selectables = [
  'uid',
  'name',
  'parentUid',
  'createdAt',
  'updatedAt',
  'customFields',
  'slug',
];

export class Folder extends Tag<FolderCustomFields, 'folder'> {
  children?: Folder[]; // to be populated if needed

  static async create(
    trx: Knex,
    folderDto: CreateFolderDto,
    projectId: number,
  ) {
    const slug = kebabCase(folderDto.name);

    // check if an identical folder exists in the same directory
    const existing = await this.query(trx)
      .where({
        slug,
        systemType: 'folder',
        parentUid: folderDto.parentId ?? null,
      })
      .first();

    if (existing) {
      throw new ApplicationError(StatusCodes.CONFLICT, errors.FOLDER_EXISTS);
    }

    // validate parent folder if specified
    if (folderDto.parentId) {
      const parent = await this.query(trx)
        .where({
          uid: folderDto.parentId,
          systemType: 'folder',
          projectUid: projectId,
        })
        .first();

      if (!parent) {
        throw new ApplicationError(
          StatusCodes.UNPROCESSABLE_ENTITY,
          errors.INVALID_FOLDER_UID,
        );
      }
    }

    // create the folder
    const folder = await this.query(trx)
      .insert({
        name: folderDto.name,
        slug,
        source: folderDto.source,
        externalId: folderDto.externalId,
        parentUid: folderDto.parentId,
        projectUid: projectId,
        entityTypes: ['cases'],
        systemType: 'folder',
      })
      .returning('*');

    return folder;
  }

  static async update(trx: Knex, folderId: number, folderDto: UpdateFolderDto) {
    const folderParams = {
      ...folderDto,
      slug: '',
    };
    let folder = await Folder.query(trx).findById(folderId);
    if (!folder || folder?.systemType !== 'folder') {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        errors.FOLDER_NOT_FOUND,
      );
    }

    if (folderDto.name) {
      folderParams.slug = kebabCase(folderDto.name);
      // check if an identical folder exists in the same directory as incoming folder
      const existing = await Folder.query(trx)
        .where({
          slug: folderParams.slug,
          systemType: 'folder',
          parentUid: folderDto.parentId ?? folder.parentUid,
        })
        .whereNot('uid', folderId)
        .first();
      if (existing) {
        throw new ApplicationError(StatusCodes.CONFLICT, errors.FOLDER_EXISTS);
      }
    }
    folder = await folder.$query().patchAndFetch(folderParams);
    return folder;
  }

  static async findChildren(trx: Knex, parentUid: number): Promise<Folder[]> {
    return Folder.query(trx)
      .where({
        deletedAt: null,
        systemType: 'folder',
        parentUid,
      })
      .select(selectables);
  }

  static findMany(
    trx: Knex,
    where: Partial<ModelObject<Folder>>,
    opts?: { limit?: number },
  ) {
    const query = Folder.query(trx)
      .select(selectables)
      .where({ systemType: 'folder', deletedAt: null })
      .where(where)
      .orderBy('name', 'ASC');
    if (opts?.limit) {
      query.limit(opts.limit);
    }

    return query;
  }

  static async findOne(trx: Knex, uid: number): Promise<Folder> {
    return Folder.query(trx)
      .where({
        uid,
        deletedAt: null,
        systemType: 'folder',
      })
      .select(selectables)
      .first();
  }

  /**
   * Populates the `parentId` field for each test case object based on the folder structure.
   *
   * - If a test case has a `parentName` but no `parentId`, it attempts to find a folder with a matching slug.
   * - If no matching folder is found, a new folder is created under the parent folder.
   * - Throws an error if the parent folder does not exist.
   *
   * @param trx - The database transaction object.
   * @param cases - An array of test case objects to populate.
   * @param projectUid - The unique identifier of the project.
   * @returns The updated array of test case objects with populated `parentId` fields.
   * @throws {ApplicationError} If the parent folder does not exist.
   */
  static async populateFolders(trx: Knex, cases, projectUid) {
    const allFolders = await Folder.query(trx).where({
      systemType: 'folder',
      projectUid,
    });

    const rootFolder = allFolders.find((folder) => folder.parentUid == null);

    if (!rootFolder) {
      throw new ApplicationError(
        StatusCodes.UNPROCESSABLE_ENTITY,
        errors.PARENT_FOLDER_NOT_EXISTS,
      );
    }

    for (const caseObject of cases) {
      if (!caseObject.parentId && caseObject.parentName) {
        const found = allFolders.find(
          (folder) => folder.slug === kebabCase(caseObject.parentName),
        );

        if (found) {
          caseObject.parentId = found.uid;
        } else {
          const newFolder = await Folder.create(
            trx,
            { name: caseObject.parentName, parentId: rootFolder.uid },
            projectUid,
          );

          allFolders.push(newFolder);

          caseObject.parentId = newFolder.uid;
        }
      }
    }

    return cases;
  }

  static get relationMappings() {
    return {
      children: {
        relation: Model.HasManyRelation,
        modelClass: Folder,
        join: {
          from: 'tags.uid',
          to: 'tags.parentUid',
        },
      },
      parent: {
        relation: Model.BelongsToOneRelation,
        modelClass: Folder,
        join: {
          from: 'tags.parentUid',
          to: 'tags.uid',
        },
      },
    };
  }

  static async getCompleteFolderTreeNodes(trx: Knex, nodes: number[]) {
    const { rows: treeNodes } = await trx.raw(
      `
WITH RECURSIVE folders as (
SELECT tags.uid, tags."parentUid" from tags WHERE "systemType"=? AND "deletedAt" IS NULL AND uid IN (??)
UNION
SELECT tags.uid, tags."parentUid" from tags INNER JOIN folders ON folders."parentUid"=tags.uid
) SELECT * from folders;
`,
      ['folder', nodes],
    );

    return treeNodes as { uid: number; parentUid?: number }[];
  }
}

export type FolderModel = ModelClass<Folder> & typeof Folder;
