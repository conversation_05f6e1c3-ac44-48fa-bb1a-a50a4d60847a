import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';
import { Dashboard } from './dashboard';
import { DataRelationship } from './dataRelationship';
import { Handle } from './handle';
import { Invite } from './invite';
import { User } from './user';

export class Org extends Model {
  uid: string;

  name: string;

  createdBy: string;

  preferences: any;

  avatarUrl: string;

  stripeId: string;

  handle?: Handle;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'orgs';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['name', 'createdBy'],
      properties: {
        uid: { type: 'string', format: 'uuid' },
        name: { type: 'string', maxLength: 255 },
        createdBy: { type: 'string', format: 'uuid' },
        preferences: { type: 'object' },
        avatarUrl: { type: 'string', maxLength: 255 },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
        stripeId: { type: 'string', maxLength: 255 },
      },
    };
  }

  static get relationMappings() {
    return {
      dataRelationships: {
        relation: Model.HasManyRelation,
        modelClass: DataRelationship,
        join: {
          from: 'orgs.orgUid',
          to: 'dataRelationships.orgUid',
        },
      },
      invites: {
        relation: Model.HasManyRelation,
        modelClass: Invite,
        join: {
          from: 'orgs.uid',
          to: 'invites.orgUid',
        },
      },
      dashboards: {
        relation: Model.HasOneRelation,
        modelClass: Dashboard,
        join: {
          from: 'orgs.uid',
          to: 'dashboards.orgUid',
        },
      },
      creator: {
        relation: Model.BelongsToOneRelation,
        modelClass: User,
        join: {
          from: 'orgs.createdBy',
          to: 'users.uid',
        },
      },
      handle: {
        relation: Model.HasOneRelation,
        modelClass: Handle,
        join: {
          from: 'orgs.uid',
          to: 'handles.ownerUid',
        },
      },
    };
  }
}

export type OrgModel = ModelClass<Org>;
