import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';

export const billingTypes = <const>['arrears', 'advance'];
type BillingType = (typeof billingTypes)[number];

export const entityTypes = <const>['user', 'org'];
type EntityType = (typeof entityTypes)[number];

export const statuses = <const>['active', 'inactive'];
type Status = (typeof statuses)[number];

interface Feature {
  title: string;
  description: string;
  key: 'maxUsers' | 'maxTestCases' | 'maxGbStorage';
  value: number;
}

export class SubscriptionPlan extends Model {
  stripePriceId: string;

  name: string;

  amount: number; // amount in cents

  billingType: BillingType;

  entityType: EntityType;

  description: string;

  status: Status;

  features: Feature[];

  static get idColumn() {
    return 'stripePriceId';
  }

  static get tableName() {
    return 'subscriptionPlans';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['stripePriceId', 'name', 'amount', 'billingType', 'features'],
      properties: {
        stripePriceId: { type: 'string', maxLength: 255 },
        name: { type: 'string' },
        amount: { type: 'number' },
        billingType: { type: 'string', enum: <any>billingTypes },
        entityType: { type: 'string', enum: <any>entityTypes },
        status: { type: 'string', enum: <any>statuses },
        features: { type: 'array' },
        description: { type: 'string' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }
}

export type SubscriptionPlanModel = ModelClass<SubscriptionPlan>;
