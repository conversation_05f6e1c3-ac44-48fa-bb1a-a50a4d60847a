import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';

export class DefectExecution extends Model {
  uid: number;

  defectUid: number;

  executionUid: number;

  executionUrl: string;

  static get idColumn() {
    return 'uid';
  }

  static get tableName() {
    return 'defectExecutions';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['defectUid', 'executionUid'],
      properties: {
        uid: { type: 'integer' },
        defectUid: { type: 'integer' },
        executionUid: { type: 'integer' },
        executionUrl: { type: 'string' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: ['string', 'null'], format: 'date-time' },
      },
    };
  }
}
export type DefectExecutionModel = ModelClass<DefectExecution>;
