import { Model } from '@app/lib/model';
import { ModelClass } from 'objection';

export class ExternalEntity extends Model {
  uid: number;

  source: string;

  sourceId: string;

  entityUid: string;

  entityType: string;

  customFields: object;

  static get tableName() {
    return 'externalEntities';
  }

  static get idColumn() {
    return 'uid';
  }

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['source', 'sourceId', 'entityUid', 'entityType'],
      properties: {
        uid: { type: 'integer' },
        source: { type: 'string', maxLength: 255 },
        sourceId: { type: 'string', maxLength: 255 },
        entityUid: { type: 'string' },
        entityType: { type: 'string' },
        customFields: { type: 'object' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }
}

export type ExternalEntityModel = ModelClass<ExternalEntity>;
