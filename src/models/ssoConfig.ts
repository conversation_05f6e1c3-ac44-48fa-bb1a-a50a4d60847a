import { Model, ModelClass } from 'objection';

export const SSO_PROVIDERS = ['oidc', 'saml2', 'oauth2'];

export type SSOProvider = typeof SSO_PROVIDERS[number];

interface ConfigValue {
  url: string;
  key: string;
  secret: string;
  allowedOrigins: string[];
  groupMappings?: Record<string, string>;
  allowOnlyInvitedAccounts: boolean;
  defaultRole?: string;
}

export type Config = Record<SSOProvider, ConfigValue>;

export const DEFAULT_OIDC_SCOPE: string[] = ['openid', 'profile', 'email', 'groups'];

export class SSOConfig extends Model {
  uid: string;

  orgUid: string;

  config: Config;

  isActive: boolean;

  createdAt: Date;

  updatedAt: Date;

  deletedAt: Date | null;

  static tableName: string = 'ssoConfigs';

  static idColumn: string = 'uid';

  static get jsonSchema() {
    return {
      type: 'object',
      required: ['orgUid', 'config'],
      properties: {
        uid: { type: 'string', maxLength: 36 },
        orgUid: { type: 'string', maxLength: 36 },
        config: { type: 'object' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        deletedAt: { type: 'string', format: 'date-time' },
      },
    };
  }
}

export type SSOConfigModel = ModelClass<SSOConfig>;
