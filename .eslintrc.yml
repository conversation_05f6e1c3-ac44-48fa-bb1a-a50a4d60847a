env:
  browser: true
  es2021: true
extends:
  - airbnb-base
  - airbnb-typescript/base
  - eslint:recommended
  - plugin:@typescript-eslint/recommended
parser: '@typescript-eslint/parser'
parserOptions:
  ecmaVersion: latest
  sourceType: module
  project: ./tsconfig.json
plugins:
  - '@typescript-eslint'
rules:
  consistent-return: off
  max-len: off
  class-methods-use-this: off
  import/prefer-default-export: off
  no-param-reassign: off
  no-underscore-dangle: off
  no-console:
    - error
  no-await-in-loop: off
  no-restricted-syntax: off
  no-continue: off
  no-plusplus: off
  no-tabs: off
  radix: off
  'import/no-cycle': off
  '@typescript-eslint/quotes':
    - error
    - single
  '@typescript-eslint/no-explicit-any': off
  '@typescript-eslint/no-non-null-assertion': off
  '@typescript-eslint/no-use-before-define': off
  '@typescript-eslint/no-shadow': off
